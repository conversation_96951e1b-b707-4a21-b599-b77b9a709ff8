-- DROP SCHEMA job_repo;

create schema IF NOT EXISTS job_repo;

-- DROP TYPE job_repo."_batch_job_execution";

CREATE TYPE job_repo."_batch_job_execution" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_execution,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_job_execution_context";

CREATE TYPE job_repo."_batch_job_execution_context" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_execution_context,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_job_execution_params";

CREATE TYPE job_repo."_batch_job_execution_params" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_execution_params,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_job_instance";

CREATE TYPE job_repo."_batch_job_instance" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_instance,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_job_tracking";

CREATE TYPE job_repo."_batch_job_tracking" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_tracking,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_job_tracking_history";

CREATE TYPE job_repo."_batch_job_tracking_history" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_job_tracking_history,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_step_execution";

CREATE TYPE job_repo."_batch_step_execution" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_step_execution,
	DELIMITER = ',');

-- DROP TYPE job_repo."_batch_step_execution_context";

CREATE TYPE job_repo."_batch_step_execution_context" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 8,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.batch_step_execution_context,
	DELIMITER = ',');

-- DROP TYPE job_repo."_job_cd_type";

CREATE TYPE job_repo."_job_cd_type" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 4,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.job_cd_type,
	DELIMITER = ',');

-- DROP TYPE job_repo."_job_status_type";

CREATE TYPE job_repo."_job_status_type" (
	INPUT = array_in,
	OUTPUT = array_out,
	RECEIVE = array_recv,
	SEND = array_send,
	ANALYZE = array_typanalyze,
	ALIGNMENT = 4,
	STORAGE = any,
	CATEGORY = A,
	ELEMENT = job_repo.job_status_type,
	DELIMITER = ',');

-- DROP TYPE job_repo.job_cd_type;

CREATE TYPE job_repo.job_cd_type AS ENUM (
	'recon',
	'settlement');

-- DROP TYPE job_repo.job_status_type;

CREATE TYPE job_repo.job_status_type AS ENUM (
	'success',
	'failed',
	'rerun');

-- DROP SEQUENCE job_repo.batch_job_execution_seq;

CREATE SEQUENCE job_repo.batch_job_execution_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE job_repo.batch_job_seq;

CREATE SEQUENCE job_repo.batch_job_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE job_repo.batch_job_tracking_history_id_seq;

CREATE SEQUENCE job_repo.batch_job_tracking_history_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE job_repo.batch_job_tracking_id_seq;

CREATE SEQUENCE job_repo.batch_job_tracking_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE job_repo.batch_step_execution_seq;

CREATE SEQUENCE job_repo.batch_step_execution_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;-- job_repo.batch_job_instance definition

-- Drop table

-- DROP TABLE job_repo.batch_job_instance;

CREATE TABLE job_repo.batch_job_instance (
	job_instance_id int8 NOT NULL,
	"version" int8 NULL,
	job_name varchar(100) NOT NULL,
	job_key varchar(32) NOT NULL,
	CONSTRAINT batch_job_instance_pkey PRIMARY KEY (job_instance_id),
	CONSTRAINT job_inst_un UNIQUE (job_name, job_key)
);


-- job_repo.batch_job_execution definition

-- Drop table

-- DROP TABLE job_repo.batch_job_execution;

CREATE TABLE job_repo.batch_job_execution (
	job_execution_id int8 NOT NULL,
	"version" int8 NULL,
	job_instance_id int8 NOT NULL,
	create_time timestamp NOT NULL,
	start_time timestamp NULL,
	end_time timestamp NULL,
	status varchar(10) NULL,
	exit_code varchar(2500) NULL,
	exit_message varchar(2500) NULL,
	last_updated timestamp NULL,
	job_configuration_location varchar(2500) NULL,
	CONSTRAINT batch_job_execution_pkey PRIMARY KEY (job_execution_id),
	CONSTRAINT job_inst_exec_fk FOREIGN KEY (job_instance_id) REFERENCES job_repo.batch_job_instance(job_instance_id)
);


-- job_repo.batch_job_execution_context definition

-- Drop table

-- DROP TABLE job_repo.batch_job_execution_context;

CREATE TABLE job_repo.batch_job_execution_context (
	job_execution_id int8 NOT NULL,
	short_context varchar(2500) NOT NULL,
	serialized_context text NULL,
	CONSTRAINT batch_job_execution_context_pkey PRIMARY KEY (job_execution_id),
	CONSTRAINT job_exec_ctx_fk FOREIGN KEY (job_execution_id) REFERENCES job_repo.batch_job_execution(job_execution_id)
);


-- job_repo.batch_job_execution_params definition

-- Drop table

-- DROP TABLE job_repo.batch_job_execution_params;

CREATE TABLE job_repo.batch_job_execution_params (
	job_execution_id int8 NOT NULL,
	type_cd varchar(6) NOT NULL,
	key_name varchar(100) NOT NULL,
	string_val varchar(250) NULL,
	date_val timestamp NULL,
	long_val int8 NULL,
	double_val float8 NULL,
	identifying bpchar(1) NOT NULL,
	CONSTRAINT job_exec_params_fk FOREIGN KEY (job_execution_id) REFERENCES job_repo.batch_job_execution(job_execution_id)
);


-- job_repo.batch_job_tracking definition

-- Drop table

-- DROP TABLE job_repo.batch_job_tracking;

CREATE TABLE job_repo.batch_job_tracking (
	id int4 NOT NULL GENERATED ALWAYS AS IDENTITY,
	job_execution_id int8 NOT NULL,
	job_type job_repo.job_cd_type NOT NULL,
	processed_file_name varchar(200) NOT NULL,
	run_date timestamp NOT NULL,
	status job_repo.job_status_type NOT NULL,
	CONSTRAINT batch_job_tracking_pkey PRIMARY KEY (id),
	CONSTRAINT batch_job_tracking_job_execution_id_fkey FOREIGN KEY (job_execution_id) REFERENCES job_repo.batch_job_execution(job_execution_id)
);


-- job_repo.batch_job_tracking_history definition

-- Drop table

-- DROP TABLE job_repo.batch_job_tracking_history;

CREATE TABLE job_repo.batch_job_tracking_history (
	id int4 NOT NULL GENERATED ALWAYS AS IDENTITY,
	batch_job_tracking_id int8 NOT NULL,
	job_type job_repo.job_cd_type NOT NULL,
	processed_file_name varchar(200) NOT NULL,
	run_date timestamp NOT NULL,
	status job_repo.job_status_type NOT NULL,
	CONSTRAINT batch_job_tracking_history_pkey PRIMARY KEY (id),
	CONSTRAINT batch_job_tracking_history_batch_job_tracking_id_fkey FOREIGN KEY (batch_job_tracking_id) REFERENCES job_repo.batch_job_tracking(id)
);


-- job_repo.batch_step_execution definition

-- Drop table

-- DROP TABLE job_repo.batch_step_execution;

CREATE TABLE job_repo.batch_step_execution (
	step_execution_id int8 NOT NULL,
	"version" int8 NOT NULL,
	step_name varchar(100) NOT NULL,
	job_execution_id int8 NOT NULL,
	start_time timestamp NOT NULL,
	end_time timestamp NULL,
	status varchar(10) NULL,
	commit_count int8 NULL,
	read_count int8 NULL,
	filter_count int8 NULL,
	write_count int8 NULL,
	read_skip_count int8 NULL,
	write_skip_count int8 NULL,
	process_skip_count int8 NULL,
	rollback_count int8 NULL,
	exit_code varchar(2500) NULL,
	exit_message varchar(2500) NULL,
	last_updated timestamp NULL,
	CONSTRAINT batch_step_execution_pkey PRIMARY KEY (step_execution_id),
	CONSTRAINT job_exec_step_fk FOREIGN KEY (job_execution_id) REFERENCES job_repo.batch_job_execution(job_execution_id)
);


-- job_repo.batch_step_execution_context definition

-- Drop table

-- DROP TABLE job_repo.batch_step_execution_context;

CREATE TABLE job_repo.batch_step_execution_context (
	step_execution_id int8 NOT NULL,
	short_context varchar(2500) NOT NULL,
	serialized_context text NULL,
	CONSTRAINT batch_step_execution_context_pkey PRIMARY KEY (step_execution_id),
	CONSTRAINT step_exec_ctx_fk FOREIGN KEY (step_execution_id) REFERENCES job_repo.batch_step_execution(step_execution_id)
);
