package com.peoples.banking.batch.reconsettle.config;

import com.peoples.banking.batch.reconsettle.batch.ReconJobTypeDecider;
import com.peoples.banking.batch.reconsettle.batch.model.ClientSettlementReportEntry;
import com.peoples.banking.batch.reconsettle.batch.model.InteracReconEntry;
import com.peoples.banking.batch.reconsettle.batch.model.ReconciliationReportEntry;
import com.peoples.banking.batch.reconsettle.batch.writer.*;
import com.peoples.banking.batch.reconsettle.dao.model.*;
import com.peoples.banking.batch.reconsettle.dao.model.InteracTransaction;
import com.peoples.banking.batch.reconsettle.service.AmazonClient;
import com.peoples.banking.batch.reconsettle.service.ExecutionHistoryService;
import com.peoples.banking.batch.reconsettle.service.ReconService;
import com.peoples.banking.batch.reconsettle.utils.Consts;
import com.peoples.banking.batch.reconsettle.batch.model.exception.ExceptionFileXml;
import com.peoples.banking.batch.reconsettle.batch.processor.ExceptionFileProcessor;
import com.peoples.banking.batch.reconsettle.batch.processor.InteracReconProcessor;
import com.peoples.banking.batch.reconsettle.batch.reader.InteracTransactionConverter;
import com.peoples.banking.batch.reconsettle.batch.reader.ResultsReader;
import com.peoples.banking.batch.reconsettle.dao.baas.model.BaasTransaction;
import com.peoples.banking.batch.reconsettle.dao.baas.repository.BaasRepository;
import com.peoples.banking.batch.reconsettle.dao.repository.MainRepository;
import com.peoples.banking.batch.reconsettle.utils.Utils;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.thoughtworks.xstream.security.AnyTypePermission;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FileUtils;
import org.hibernate.SessionFactory;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.*;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.job.flow.support.SimpleFlow;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.HibernateCursorItemReader;
import org.springframework.batch.item.database.builder.HibernateCursorItemReaderBuilder;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.transform.DelimitedLineAggregator;
import org.springframework.batch.item.xml.StaxEventItemReader;
import org.springframework.batch.item.xml.StaxEventItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.oxm.xstream.XStreamMarshaller;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.sql.DataSource;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;

import static com.peoples.banking.batch.reconsettle.dao.model.DataModelFields.*;
import static com.peoples.banking.batch.reconsettle.dao.model.DataModelFields.BaasTransaction.*;
import static com.peoples.banking.batch.reconsettle.dao.model.DataModelFields.ClientSettlementReportEntry.*;
import static com.peoples.banking.batch.reconsettle.dao.model.DataModelFields.ReconciliationReportEntry.*;


@Configuration
@Log4j2
@Profile({Consts.RECON_JOB_NAME, Consts.INTERAC_R_AND_S_JOB_NAME, Consts.SETTLE_SA_REPORTS_JOB_NAME})
@Getter
public class ReconJobConfig {

  public static final String EXCEPTION_FILE_SUFFIX = "_exception.xml";
  public static final String NO_INTERAC = "nointerac";

  @Autowired
  private MainRepository mainRepository;

  @Autowired
  private BaasRepository baasRepository;

  @PersistenceContext(unitName = Consts.MAIN_PERSISTENCE_CONTEXT)
  private EntityManager mainEntityManager;

  @PersistenceContext(unitName = Consts.BAAS_PERSISTENCE_CONTEXT)
  private EntityManager baasEntityManager;

  @Autowired
  public JobBuilderFactory jobBuilderFactory;

  @Autowired
  private StepBuilderFactory stepBuilderFactory;

  @Autowired
  private PlatformTransactionManager dataTransactionManager;

  @Autowired
  private InteracTransactionConverter interacTransactionConverter;

  @Autowired
  @Qualifier(Consts.BAAS_TRANSACTION_MANAGER)
  private PlatformTransactionManager baasTransactionManager;

  @Autowired
  private DateConfig dateConfig;

  @Autowired
  private ReconService reconService;

  @Autowired
  private ExecutionHistoryService executionHistoryService;

  @Autowired
  private ReconJobTypeDecider reconJobTypeDecider;

  @Value("${INTERAC_FILE_LOCATION}")
  private String interacFileLocation;

  @Value("${EXCEPTION_FILE_LOCATION}")
  private String exceptionFileLocation;
  @Value("${REPORT_FILE_LOCATION}")
  private String reportFileLocation;
  @Value("${sys.pn.interac.peoples.fiid}")
  private String fiId;

  @Autowired
  private AmazonClient amazonClient;

  private String exceptionFilePath;

  @Bean
  public Job reconJob(JobRepository jobRepository, Step readBaasStep, Step reconInteracStep, Step reconBaasStep, Step exceptionFileStep,
      DataSource datasource, @Qualifier(Consts.RECON_SCHEMA_NAME) String schemaName) {
    return this.jobBuilderFactory.get(Consts.RECON_JOB_NAME)
        .repository(jobRepository)
        .start(decideJobTypeFlow(readBaasStep, reconInteracStep, reconBaasStep, exceptionFileStep))
        .end()
        .listener(reconJobListener(datasource, schemaName))
        .build();

  }

  @Bean
  public JobExecutionListener reconJobListener(DataSource datasource, String schemaName) {
    return new JobExecutionListener() {

      /***
       * if s3 is enabled, we will download the file from s3 bucket
       */
      @SneakyThrows
      @Override
      public void beforeJob(JobExecution jobExecution) {
        downloadReconFilesFromAWSIfEnabled();
      }

      /***
       * This is the extra after job steps it will do schema rename and aws s3 upload
       * @param jobExecution
       */
      @Override
      public void afterJob(JobExecution jobExecution) {
        executionHistoryService.logExecution(jobExecution, isFlowWithoutInteracReconFile(jobExecution) ? null : interacFileLocation);
        if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
          uploadReconFilesToAWSIfEnabled(jobExecution);
          if (isFlowWithoutInteracReconFile(jobExecution)) {
            ConfigUtil.dropSchema(datasource, schemaName);
          } else {
            ConfigUtil.renameSchema(datasource, dateConfig, schemaName, Consts.RECONCILIATION_SCHEMA_PREFIX);
          }
        }
      }
    };
  }

  public boolean isFlowWithoutInteracReconFile(JobExecution jobExecution) {
    return ReconConstant.INTERAC_FILE_ABSENT_FLOW.equals(jobExecution.getExecutionContext().get(ReconConstant.RECON_JOB_TYPE_PARAM));
  }



  public void uploadReconFilesToAWSIfEnabled(JobExecution jobExecution) {
    if (jobExecution.getStatus() == BatchStatus.COMPLETED && dateConfig.isS3enable()) {
      //We need one more steps upload the file to S3 bucket
      if (exceptionFilePath != null && Files.exists(Paths.get(exceptionFilePath))) {
        amazonClient.uploadFile(exceptionFilePath, ReconConstant.EXCEPTION_FOLDER);
      }

      //Also we will upload the report file into s3 report folder
      File[] files = new File(reportFileLocation).listFiles();
      for (File file : files) {
        amazonClient.uploadFile(file.getPath(), ReconConstant.REPORT_FOLDER);
      }
    }
  }

  public void downloadReconFilesFromAWSIfEnabled() throws IOException {
    if (dateConfig.isS3enable()) {
      log.info("Clean the report folder for reconJob=" + reportFileLocation);
      try {
        //Before start we clean all local directory
        File reportFileLocationDir = new File(reportFileLocation);
        FileUtils.cleanDirectory(reportFileLocationDir);
      } catch (Exception ex) {
        log.error(ex.getMessage(), ex);
      }

      //First steps download the file from s3
      String reconFile = amazonClient.downloadS3File(ReconConstant.AWS_S3_RECON_NAME_PREFIX, ReconConstant.AWS_S3_RECON_FOLDER);
      log.info("download the recon file from AWS s3=" + reconFile);
      interacFileLocation = reconFile;
      reconJobTypeDecider.setReconFileLocation(reconFile);
    }
  }

  @Bean
  public Flow decideJobTypeFlow(Step readBaasStep, Step reconInteracStep, Step reconBaasStep, Step exceptionFileStep) {
    return new FlowBuilder<SimpleFlow>(Consts.RECON_JOB_FLOW)
            .start(reconJobTypeDecider)
              .on(ReconConstant.INTERAC_FILE_PRESENT_FLOW).to(reconWithInteracFlow(readBaasStep, reconInteracStep, reconBaasStep, exceptionFileStep))
            .from(reconJobTypeDecider)
              .on(ReconConstant.INTERAC_FILE_ABSENT_FLOW).to(reconWithoutInteracFlow())
            .end();
  }

  @Bean
  public Flow reconWithInteracFlow(Step readBaasStep, Step reconInteracStep, Step reconBaasStep, Step exceptionFileStep) {
    return new FlowBuilder<SimpleFlow>(Consts.RECON_WITH_INTERAC_FLOW)
            .start(readInteracFileFlow())
            .next(readBaasDbFlow(readBaasStep))
            .next(reconInteracStep)
            .next(reconBaasStep)
            .next(processResultsFlow(exceptionFileStep))
            .next(markReconciledBaasFlow())
            .end();
  }

  @Bean
  public Flow reconWithoutInteracFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.RECON_WITHOUT_INTERAC_FLOW)
            .start(generateReconciledReport())
            .next(clientSettlementReportStep())
            .end();
  }

  @Bean
  public Flow processResultsFlow(Step exceptionFileStep) {
    return new FlowBuilder<SimpleFlow>(Consts.PROCESS_RESULTS_FLOW)
        .split(taskExecutor())
        .add(createExceptionFileFlow(exceptionFileStep), generateConsolidateReconciliationReportFlow())
        .end();
  }

  @Bean
  public Flow markReconciledBaasFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.MARK_BAAS_FLOW)
        .start(markReconciledBaasStep())
        .end();
  }

  @Bean
  public Flow createExceptionFileFlow(Step exceptionFileStep) {
    return new FlowBuilder<SimpleFlow>(Consts.EXCEPTION_FILE_FLOW)
        .start(exceptionFileStep)
        .end();
  }

  @Bean
  public Flow generateConsolidateReconciliationReportFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.CONSOLIDATED_REPORT_FILE_FLOW)
            .start(generateConsolidatedReconciledReportStep())
            .end();
  }

  @Bean
  public Flow generateClientSettlementReportFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.CLIENT_SETTLEMENT_REPORT_FILE_FLOW)
            .start(clientSettlementReportStep())
            .end();
  }

  @Bean
  public Flow generateReconciliationReportFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.REPORT_FILE_FLOW)
        .start(generateReconciledReport())
        .end();
  }

  @Bean
  public Flow readInteracFileFlow() {
    return new FlowBuilder<SimpleFlow>(Consts.READ_INTERAC_FILE_FLOW)
        .start(readInteracFileStep())
        .end();
  }

  @Bean
  public Flow readBaasDbFlow(Step readBaasStep) {
    return new FlowBuilder<SimpleFlow>(Consts.READ_BAAS_DB_FLOW)
        .start(readBaasStep)
        .end();
  }

  @Bean
  public TaskExecutor taskExecutor() {
    return new SimpleAsyncTaskExecutor();
  }

  @Bean
  public Step generateConsolidatedReconciledReportWithoutInteracStep() {
    return this.stepBuilderFactory.get(Consts.CONSOLIDATED_WITHOUT_INTERAC_RECONCILED_REPORT_STEP_NAME)
            .transactionManager(baasTransactionManager)
            .<BaasTransaction, ReconciliationReportEntry>chunk(500)
            .reader(baasDbInboundOutboundReader())
            .processor((Function<BaasTransaction, ReconciliationReportEntry>) ReconciliationReportEntry::new)
            .writer(csvConsolidatedReportWriter())
            .build();
  }

  @Bean
  public Step generateReconciledReport() {
    return this.stepBuilderFactory.get(Consts.RECONCILED_REPORT_WITHOUT_INTERAC_STEP_NAME)
            .transactionManager(baasTransactionManager)
            .<BaasTransaction, ReconciliationReportEntry>chunk(500)
            .reader(baasDbInboundOutboundReader())
            .processor((Function<BaasTransaction, ReconciliationReportEntry>) b-> new ReconciliationReportEntry(b).additionalClientReconProcessing(fiId))
            .writer(csvReportWriter())
            .build();
  }

  @Bean
  public Step markReconciledBaasStep() {
    return this.stepBuilderFactory.get(Consts.MARK_BAAS_STEP_NAME)
        .transactionManager(baasTransactionManager)
        .<ReconResult, ReconResult>chunk(500)
        .reader(reconciledResultsReader())
        .writer(l -> baasRepository.markBaasEntryAsReconciled(l))
        .build();
  }

  @Bean
  public Step generateConsolidatedReconciledReportStep() {
    return this.stepBuilderFactory.get(Consts.CONSOLIDATED_RECONCILED_REPORT_STEP_NAME)
            .transactionManager(dataTransactionManager)
            .<ReconResult, ReconciliationReportEntry>chunk(500)
            .reader(allResultsReader())
            .processor((Function<ReconResult, ReconciliationReportEntry>) ReconciliationReportEntry::new)
            .writer(csvConsolidatedReportWriter())
            .build();
  }

  @Bean
  public Step reconBaasStep() {
    return this.stepBuilderFactory.get(Consts.RECON_BAAS_STEP_NAME)
        .transactionManager(dataTransactionManager)
        .<BaasTransaction, ReconResult>chunk(500)
        .reader(baasWithoutResultsTableReader())
        .processor((Function<BaasTransaction, ReconResult>) b -> new ReconResult(null, b, false))
        .writer(mainRepository::persistEntityList)
        .build();
  }

  @Bean
  public Step reconInteracStep(InteracReconProcessor processor) {
    return this.stepBuilderFactory.get(Consts.RECON_INTERAC_STEP_NAME)
        .transactionManager(dataTransactionManager)
        .<InteracReconEntry, ReconResult>chunk(500)
        .reader(interacTableReader())
        .processor(processor)
        .writer(mainRepository::persistEntityList)
        .build();
  }

  @Bean
  public Step exceptionFileStep(ResultsReader resultsReader, ExceptionFileProcessor processor) {
    return this.stepBuilderFactory.get(Consts.EXCEPTION_FILE_STEP_NAME)
        .transactionManager(dataTransactionManager)
        .<List<ReconResult>, ExceptionFileXml>chunk(1)
        .reader(resultsReader)
        .processor(processor)
        .writer(exceptionFileWriter())
        .build();
  }

  @Bean
  public Step clientSettlementReportStep() {
    return this.stepBuilderFactory.get(Consts.CLIENT_SETTLEMENT_REPORT_STEP_NAME)
            .<String, ClientSettlementReportEntry>chunk(Integer.MAX_VALUE)
            .reader(serviceAccountsReader())
            .processor((Function<String, ClientSettlementReportEntry>)a->reconService.createClientSettlementReportEntry(a, dateConfig))
            .writer(clientSettlementReportCsvWriter())
            .build();
  }

  @Bean
  public Step readBaasStep(ItemReader<BaasTransaction> baasDbInboundOutboundReader) {
    return this.stepBuilderFactory.get(Consts.READ_BAAS_DB_STEP_NAME)
        .transactionManager(dataTransactionManager)
        .<BaasTransaction, BaasTransaction>chunk(500)
        .reader(baasDbInboundOutboundReader)
        .writer(mainRepository::persistEntityList)
        .build();
  }

  @Bean
  public FlatFileItemWriter<ClientSettlementReportEntry> clientSettlementReportCsvWriter() {
    ClientSettlementFlatFileWriter writer = new ClientSettlementFlatFileWriter(reconService, reportFileLocation, dateConfig);
    writer.setAppendAllowed(true);
    DelimitedLineAggregator<ClientSettlementReportEntry> lineAggregator = new DelimitedLineAggregator<>();
    lineAggregator.setDelimiter(",");
    FormattedAmountBeanWrapperFieldExtractor<ClientSettlementReportEntry> fieldExtractor = new FormattedAmountBeanWrapperFieldExtractor<>();
    fieldExtractor.setDecimalFormattedFields(INCOMING_TOTAL, INCOMING_MATCHED_TOTAL);
    String[] fieldNames = {DataModelFields.ClientSettlementReportEntry.SERVICE_ACCOUNT_REF_ID, INCOMING_COUNT, INCOMING_TOTAL, OUTGOING_COUNT, OUTGOING_TOTAL};
    String[] headers = {CsvHeaders.SERVICE_ACCOUNT_REF_ID, CsvHeaders.INBOUND_COUNT, CsvHeaders.INBOUND_TOTAL, CsvHeaders.OUTBOUND_COUNT, CsvHeaders.OUTBOUND_TOTAL};
    fieldExtractor.setNames(fieldNames);
    lineAggregator.setFieldExtractor(fieldExtractor);
    writer.setHeaderCallback(w -> w.write(String.join(",", headers)));
    writer.setLineAggregator(lineAggregator);
    return writer;
  }


  @Bean
  public FlatFileItemWriter<ReconciliationReportEntry> csvConsolidatedReportWriter() {
    FlatFileItemWriter writer = new ConsolidatedReconciliationFlatFileWriter(reportFileLocation, dateConfig);
    writer.setAppendAllowed(true);
    DelimitedLineAggregator<ReconciliationReportEntry> lineAggregator = new DelimitedLineAggregator<>();
    lineAggregator.setDelimiter(",");
    FormattedAmountBeanWrapperFieldExtractor<ReconciliationReportEntry> fieldExtractor = new FormattedAmountBeanWrapperFieldExtractor<>();
    String[] fieldNames = {MATCHED, PARENT_ID, DataModelFields.BaasTransaction.SERVICE_ACCOUNT_REF_ID, CUSTOMER_REF_ID, DEBTOR_NAME, EXTERNAL_REF_ID, TYPE, AMOUNT, ACCOUNT_NAME, ACCOUNT_NUMBER,
            NETWORK_REF_ID, NETWORK_PAYMENT_REF_ID, NETWORK_PAYMENT_URL, NETWORK_PAYMENT_TYPE, STATUS, SETTLEMENT_DATE, CREATED_ON, UPDATED_ON, NETWORK_CREATED_DATE,
            INTERAC_CURRENCY, INTERAC_FI_REF_NO, INTERAC_ORIG_FI_USER_ID, INTERAC_PMT_REF_NO, INTERAC_AMOUNT, INTERAC_TRANSACTION_TYPE,
            INTERAC_CHANNEL_INDICATOR, INTERAC_TRAN_DATE, INTERAC_LOCAL_TRAN_DATE, INTERAC_INTERAC_DATE, INTERAC_LOCAL_INTERAC_DATE, DataModelFields.InteracTransaction.INTERAC_INITIATED
    };
    String[] headerNames = {CsvHeaders.MATCHED, CsvHeaders.PARENT_ID, CsvHeaders.SERVICE_ACCOUNT_REF_ID, CsvHeaders.CUSTOMER_REF_ID, CsvHeaders.DEBTOR_NAME, CsvHeaders.EXTERNAL_REF_ID, CsvHeaders.TYPE, CsvHeaders.AMOUNT,
            CsvHeaders.ACCOUNT_NAME, CsvHeaders.ACCOUNT_NUMBER,
            CsvHeaders.NETWORK_REF_ID, CsvHeaders.NETWORK_PAYMENT_REF_ID, CsvHeaders.NETWORK_PAYMENT_URL, CsvHeaders.NETWORK_PAYMENT_TYPE, CsvHeaders.STATUS, CsvHeaders.SETTLEMENT_DATE,
            CsvHeaders.CREATED_ON, CsvHeaders.UPDATED_ON, CsvHeaders.NETWORK_CREATED_DATE,
            CsvHeaders.CURRENCY, CsvHeaders.FI_REF_NO, CsvHeaders.ORIG_FI_USER_ID,  CsvHeaders.PMT_REF_NO, CsvHeaders.AMOUNT, CsvHeaders.TRANSACTION_TYPE, CsvHeaders.CHANNEL_INDICATOR, CsvHeaders.TRAN_DATE,
            CsvHeaders.LOCAL_TRAN_DATE, CsvHeaders.INTERAC_DATE, CsvHeaders.LOCAL_INTERAC_DATE, CsvHeaders.INTERAC_INITIATED
    };
    fieldExtractor.setNames(fieldNames);
    lineAggregator.setFieldExtractor(fieldExtractor);
    writer.setHeaderCallback(w -> w.write(",BAAS Data,,,,,,,,,,,,,,,,,Interac Data\n" + String.join(",", headerNames)));
    writer.setLineAggregator(lineAggregator);
    return writer;
  }


  @Bean
  public ItemWriter<ReconciliationReportEntry> csvReportWriter() {
    return Utils.csvReportWriter(baasRepository, reportFileLocation, dateConfig, this::getQueryParams, () -> baasRepository.loadServiceAccountIds(), "");
  }

  private Map<String, Object> getQueryParams() {
    Map<String, Object> params = dateConfig.getQueryParams();
    params.put(Consts.SA_SETTLED_STATUS, true);
    return params;
  }

  @Bean
  public ItemReader<ReconResult> reconciledResultsReader() {
    return new HibernateCursorItemReaderBuilder<ReconResult>()
        .name(Consts.READER_RECONCILED_RESULTS_TABLE)
        .sessionFactory(mainEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
        .fetchSize(500)
        .currentItemCount(0)
        .queryName(NamedQueriesConsts.RECONCILED_RECON_RESULTS)
        .useStatelessSession(true)
        .build();
  }

  @Bean
  @Scope("prototype")
  public ItemReader<ReconResult> allResultsReader() {
    return new HibernateCursorItemReaderBuilder<ReconResult>()
        .name(Consts.READER_RESULTS_TABLE)
        .sessionFactory(mainEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
        .fetchSize(500)
        .currentItemCount(0)
        .queryName(NamedQueriesConsts.ALL_RECON_RESULTS)
        .useStatelessSession(true)
        .build();
  }

  @Bean
  @Scope("prototype")
  public ItemReader<ReconResult> allInboundOutboundResultsReader() {
    return new HibernateCursorItemReaderBuilder<ReconResult>()
            .name(Consts.READER_INBOUND_OUTBOUND_RESULTS)
            .sessionFactory(mainEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
            .fetchSize(500)
            .currentItemCount(0)
            .queryName(NamedQueriesConsts.ALL_INBOUND_OUTBOUND_RECON_RESULTS)
            .useStatelessSession(true)
            .build();
  }

  @Bean
  public ItemReader<BaasTransaction> baasWithoutResultsTableReader() {
    return new HibernateCursorItemReaderBuilder<BaasTransaction>()
        .name(Consts.READER_BAAS_WITHOUT_RESULTS_TABLE)
        .sessionFactory(mainEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
        .fetchSize(500)
        .currentItemCount(0)
        .queryName(NamedQueriesConsts.BAAS_TRANSACTIONS_WITHOUT_RECON_RESULTS)
        .useStatelessSession(true)
        .build();
  }


  @Bean
  public ItemReader<String> serviceAccountsReader() {
    return new HibernateCursorItemReaderBuilder<String>()
            .name(Consts.READER_SERVICE_ACCOUNTS)
            .sessionFactory(baasEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
            .fetchSize(500)
            .currentItemCount(0)
            .queryName(NamedQueriesConsts.ALL_SERVICE_ACCOUNTS)
            .useStatelessSession(true)
            .build();
  }

  @Bean
  public ItemReader<InteracReconEntry> interacTableReader() {
    return new HibernateCursorItemReaderBuilder<InteracReconEntry>()
        .name(Consts.READER_INTERAC_TABLE)
        .sessionFactory(mainEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
        .fetchSize(500)
        .currentItemCount(0)
        .queryName(NamedQueriesConsts.ALL_INTERAC_TRANSACTIONS)
        .useStatelessSession(true)
        .build();
  }

  @Bean
  @StepScope
  public HibernateCursorItemReader<BaasTransaction> baasDbInboundOutboundReader() {
    return new HibernateCursorItemReaderBuilder<BaasTransaction>()
            .name(Consts.READER_BAAS_INBOUND_OUTBOUND)
            .sessionFactory(baasEntityManager.getEntityManagerFactory().unwrap(SessionFactory.class))
            .fetchSize(500)
            .currentItemCount(0)
            .queryName(NamedQueriesConsts.ALL_BAAS_INBOUND_OUTBOUND_TRANSACTIONS)
            .parameterValues(dateConfig.getQueryParams())
            .useStatelessSession(true)
            .build();
  }

  @Bean
  public Step readInteracFileStep() {
    return this.stepBuilderFactory.get(Consts.READ_INTERAC_FILE_STEP_NAME)
        .transactionManager(dataTransactionManager)
        .<InteracTransaction, InteracTransaction>chunk(500)
        .reader(interacReader())
        .writer(mainRepository::persistEntityList)
        .build();
  }


  @Bean
  @StepScope
  public StaxEventItemReader<InteracTransaction> interacReader() {
    Map<String, Class> aliases = new HashMap<>();
    aliases.put(InteracXmlFields.PAYMENT, InteracTransaction.class);
    aliases.put(InteracXmlFields.INTERACPAYMENT, InteracTransaction.class);
    aliases.put(InteracXmlFields.HEADER, InteracHeader.class);
    XStreamMarshaller unmarshaller = new XStreamMarshaller();
    unmarshaller.setAliases(aliases);
    unmarshaller.setConverters(interacTransactionConverter);
    unmarshaller.setTypePermissions(AnyTypePermission.ANY);
    StaxEventItemReader<InteracTransaction> reader = new StaxEventItemReader<>();
    reader.setResource(new FileSystemResource(interacFileLocation));
    reader.setFragmentRootElementNames(new String[]{InteracXmlFields.PAYMENT, InteracXmlFields.INTERACPAYMENT, InteracXmlFields.HEADER});
    reader.setUnmarshaller(unmarshaller);
    return reader;
  }

  @Bean
  public StaxEventItemWriter<ExceptionFileXml> exceptionFileWriter() {
    StaxEventItemWriter<ExceptionFileXml> writer = new NoRootHeaderStaxWriter<>();
    writer.setResource(
        new FileSystemResource(Paths.get(exceptionFileLocation, dateConfig.getExceptionFileNameDate() + EXCEPTION_FILE_SUFFIX)));
    writer.setRootTagName(InteracXmlFields.INTERACDISCREPANCY);
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    jaxb2Marshaller.setClassesToBeBound(ExceptionFileXml.class);
    writer.setMarshaller(jaxb2Marshaller);
    return writer;
  }

  public ReconJobTypeDecider getReconJobTypeDecider() {
    return reconJobTypeDecider;
  }
}
