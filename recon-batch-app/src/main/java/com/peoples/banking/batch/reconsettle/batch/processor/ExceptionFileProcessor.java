package com.peoples.banking.batch.reconsettle.batch.processor;

import com.peoples.banking.batch.reconsettle.dao.model.InteracHeader;
import com.peoples.banking.batch.reconsettle.utils.Consts;
import com.peoples.banking.batch.reconsettle.utils.ReconciliationBatchException;
import com.peoples.banking.batch.reconsettle.utils.Utils;
import com.peoples.banking.batch.reconsettle.dao.baas.model.BaasTransaction;
import com.peoples.banking.batch.reconsettle.dao.model.InteracTransaction;
import com.peoples.banking.batch.reconsettle.dao.model.ReconResult;
import com.peoples.banking.batch.reconsettle.dao.repository.MainRepository;
import com.peoples.banking.batch.reconsettle.batch.model.exception.*;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Profile({Consts.RECON_JOB_NAME, Consts.SETTLE_SA_REPORTS_JOB_NAME, Consts.INTERAC_R_AND_S_JOB_NAME})
@Component
public class ExceptionFileProcessor implements ItemProcessor<List<ReconResult>, ExceptionFileXml> {

    public static final String CAD = "CAD";
    private static final Map<NetworkPaymentType, Integer> TRANSACTION_TYPE_MAP = new HashMap<>();
    static {
        TRANSACTION_TYPE_MAP.put(NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT, 1);
    }

    @Value("${sys.pn.interac.peoples.fiid}")
    private String fiId;

    @Autowired
    private MainRepository mainRepository;

    @Override
    public ExceptionFileXml process(List<ReconResult> results) throws Exception {
        ExceptionFileXml result = new ExceptionFileXml();
        result.setHeader(createHeader());
        result.setDiscrepancy(createDiscrepancy(results));
        result.setMissingAtFi(createMissingAtFi(results));
        result.setMissingAtInterac(createMissingAtInterac(results));
        result.setTrailer(createFooter(results));
        return result;
    }

    private ExceptionMissingAtInterac createMissingAtInterac(List<ReconResult> results) {
        List<ReconResult> toProcess = results.stream().filter(r -> r.getInteracTransaction() == null && r.getBaasTransaction() != null).collect(Collectors.toList());
        if (!toProcess.isEmpty()) {
            ExceptionMissingAtInterac res = new ExceptionMissingAtInterac();
            List<ReconResult> interacInitiated = toProcess;
            if (!CollectionUtils.isEmpty(interacInitiated)) {
                res.setInitiatedByInterac(interacInitiated.stream().map(ReconResult::getBaasTransaction).map(this::processMissingPayment).collect(Collectors.toList()));
            }
            return res;
        }
        return null;
    }

    private ExceptionSupplementedPayment processSupplementedPayments(BaasTransaction baasTransaction) {
        ExceptionSupplementedPayment supplementedPayment = new ExceptionSupplementedPayment();
        supplementedPayment.setPayment(processMissingPayment(baasTransaction));
        supplementedPayment.setSupplement(processSupplement(baasTransaction));
        return supplementedPayment;
    }

    private ExceptionSupplement processSupplement(BaasTransaction baasTransaction) {
        ExceptionSupplement res = new ExceptionSupplement();
        res.setCustomerId(baasTransaction.getCustomerRefId());
        res.setName(baasTransaction.getAccountName());
        res.setFiUserId(baasTransaction.getCustomerRefId());
        return res;
    }

    private ExceptionMissingAtFi createMissingAtFi(List<ReconResult> results) {
        List<ReconResult> toProcess = results.stream().filter(r -> r.getBaasTransaction() == null && r.getInteracTransaction() != null).collect(Collectors.toList());
        if (!toProcess.isEmpty()) {
            ExceptionMissingAtFi res = new ExceptionMissingAtFi();
            Map<Boolean, List<ReconResult>> partitioned = toProcess.stream().collect(Collectors.partitioningBy(r -> r.getInteracTransaction().isInteracInitiated()));
            List<ReconResult> interacInitiated = partitioned.get(Boolean.TRUE);
            if (!CollectionUtils.isEmpty(interacInitiated)) {
                res.setInitiatedByInterac(interacInitiated.stream().map(ReconResult::getInteracTransaction).map(this::processMissingPayment).collect(Collectors.toList()));
            }
            return res;
        }
        return null;
    }

    private ExceptionMissingPayment processMissingPayment(BaasTransaction baasTransaction) {
        ExceptionMissingPayment res = new ExceptionMissingPayment();
        res.setAmount(baasTransaction.getAmount());
        res.setCurrency(CAD);
        res.setFiRefNo(baasTransaction.getExternalRefId());
        res.setOrigFiUserId(baasTransaction.getCustomerRefId());
        res.setPmRefNo(baasTransaction.getNetworkPaymentRefId());
        res.setTransactionDate(Utils.convertToUTCZone(baasTransaction.getSettlementDate()));
        res.setLocalTransactionDate(baasTransaction.getSettlementDate());
        res.setTransactionType(getTransactionType(baasTransaction.getNetworkPaymentType()));
        return res;
    }

    private Integer getTransactionType(String networkPaymentType) {
        return TRANSACTION_TYPE_MAP.get(NetworkPaymentType.valueOf(networkPaymentType));
    }

    private ExceptionMissingPayment processMissingPayment(InteracTransaction interacTransaction) {
        ExceptionMissingPayment res = new ExceptionMissingPayment();
        res.setAmount(interacTransaction.getAmount());
        res.setCurrency(interacTransaction.getCurrency());
        res.setFiRefNo(interacTransaction.getFiRefNo());
        res.setChannel(interacTransaction.getChannelIndicator() != null ? interacTransaction.getChannelIndicator().toString() : null);
        res.setPmRefNo(interacTransaction.getPmtRefNo());
        res.setTransactionDate(interacTransaction.getTranDate());
        res.setLocalTransactionDate(interacTransaction.getLocalTranDate());
        res.setOrigFiUserId(interacTransaction.getOrigFiUserId());
        res.setTransactionType(interacTransaction.getTransactionType());
        return res;
    }

    private ExceptionDiscrepancy createDiscrepancy(List<ReconResult> results) {
        List<ReconResult> toProcess = results.stream().filter(r -> r.getBaasTransaction() != null && r.getInteracTransaction() != null).collect(Collectors.toList());
        if (!toProcess.isEmpty()) {
            ExceptionDiscrepancy res = new ExceptionDiscrepancy();
            Map<Boolean, List<ReconResult>> partitioned = toProcess.stream().collect(Collectors.partitioningBy(r -> r.getInteracTransaction().isInteracInitiated()));
            List<ReconResult> interacInitiated = partitioned.get(Boolean.TRUE);
            if (!CollectionUtils.isEmpty(interacInitiated)) {
                res.setInitiatedByInterac(interacInitiated.stream().map(this::processDiscrepancyInteracEntry).collect(Collectors.toList()));
            }
            return res;
        }
        return null;
    }

    private ExceptionDiscrepancyFiElement processDiscrepancyFiEntry(ReconResult r) {
        ExceptionDiscrepancyFiElement el = new ExceptionDiscrepancyFiElement();
        el.setFiPayment(convertDiscrepancyFiPayment(r.getBaasTransaction()));
        el.setInteracPayment(convertDiscrepancyFiPayment(r.getInteracTransaction()));
        return el;
    }

    private ExceptionDiscrepancyFiPayment convertDiscrepancyFiPayment(InteracTransaction interacTransaction) {
        ExceptionDiscrepancyFiPayment res = new ExceptionDiscrepancyFiPayment();
        res.setAmount(interacTransaction.getAmount());
        res.setCurrency(interacTransaction.getCurrency());
        res.setFiRefNo(interacTransaction.getFiRefNo());
        res.setChannel(interacTransaction.getChannelIndicator().toString());
        res.setPmRefNo(interacTransaction.getPmtRefNo());
        res.setTransactionDate(interacTransaction.getTranDate());
        res.setLocalTransactionDate(interacTransaction.getLocalTranDate());
        res.setOrigFiUserId(interacTransaction.getOrigFiUserId());
        res.setTransactionType(interacTransaction.getTransactionType());
        return res;
    }

    private ExceptionDiscrepancyFiPayment convertDiscrepancyFiPayment(BaasTransaction baasTransaction) {
        ExceptionDiscrepancyFiPayment res = new ExceptionDiscrepancyFiPayment();
//        res.setAmount(baasTransaction.getAmount());
//        res.setCurrency(CAD);
//        res.setFiRefNo(baasTransaction.getNetworkRefId());
//        res.setOrigFiUserId(baasTransaction.getCustomerRefId());
//        res.setPmRefNo(baasTransaction.getNetworkPaymentRefId());
//        res.setTransactionDate(baasTransaction.getCreatedOn());
//        res.setLocalTransactionDate(baasTransaction.getCreatedOn());
//        res.setTransactionType(TRANSACTION_TYPE);
        return res;
    }

    private ExceptionDiscrepancyInteracElement processDiscrepancyInteracEntry(ReconResult r) {
        ExceptionDiscrepancyInteracElement el = new ExceptionDiscrepancyInteracElement();
        el.setFiPayment(convertDiscrepancyPayment(r.getBaasTransaction()));
        el.setFiPayment(convertDiscrepancyPayment(r.getInteracTransaction()));
        return el;
    }

    private ExceptionDiscrepancyInteracPayment convertDiscrepancyPayment(InteracTransaction interacTransaction) {
        ExceptionDiscrepancyInteracPayment res = new ExceptionDiscrepancyInteracPayment();
        res.setAmount(interacTransaction.getAmount());
        res.setCurrency(interacTransaction.getCurrency());
        res.setFiRefNo(interacTransaction.getFiRefNo());
        res.setChannel(interacTransaction.getChannelIndicator().toString());
        res.setFiUserId(interacTransaction.getFiUserId());
        res.setPmRefNo(interacTransaction.getPmtRefNo());
        res.setInteracLocalTransactionDate(interacTransaction.getLocalInteracDate());
        res.setInteracTransactionDate(interacTransaction.getInteracDate());
        res.setTransactionDate(interacTransaction.getTranDate());
        res.setLocalTransactionDate(interacTransaction.getLocalTranDate());
        return res;
    }

    private ExceptionDiscrepancyInteracPayment convertDiscrepancyPayment(BaasTransaction baasTransaction) {
        ExceptionDiscrepancyInteracPayment res = new ExceptionDiscrepancyInteracPayment();
        res.setAmount(baasTransaction.getAmount());
        res.setCurrency(CAD);
        res.setFiRefNo(baasTransaction.getExternalRefId());
        res.setFiUserId(baasTransaction.getCustomerRefId());
        res.setPmRefNo(baasTransaction.getNetworkPaymentRefId());
        res.setTransactionDate(Utils.convertToUTCZone(baasTransaction.getSettlementDate()));
        res.setLocalTransactionDate(baasTransaction.getSettlementDate());
        res.setTransactionType(getTransactionType(baasTransaction.getNetworkPaymentType()));
        return res;
    }

    private ExceptionFooter createFooter(List<ReconResult> results) {
        ExceptionFooter footer = new ExceptionFooter();
        footer.setTotal(results.size());
        return footer;
    }

    private ExceptionHeader createHeader() {
        InteracHeader interacHeader = mainRepository.getHeader();
        if (interacHeader == null) {
            throw new ReconciliationBatchException("No reconciliation header found in database");
        }

        ExceptionHeader header = new ExceptionHeader();
        header.setFiid(interacHeader.getFiId());
        header.setVersion(interacHeader.getVersion());
        header.setRunNo(interacHeader.getRunNo());
        header.setType(interacHeader.getType());
        header.setRunDate(Utils.formatDate(interacHeader.getRunDate()));
        header.setFileId(interacHeader.getFileId());
        header.setIndirectConnectorId(interacHeader.getInteracConnectorId());
        return header;
    }
}
