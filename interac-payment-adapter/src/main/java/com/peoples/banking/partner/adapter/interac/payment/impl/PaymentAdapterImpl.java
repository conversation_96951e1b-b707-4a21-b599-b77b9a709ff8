package com.peoples.banking.partner.adapter.interac.payment.impl;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.payment.PaymentAdapter;
import com.peoples.banking.partner.adapter.interac.payment.config.PaymentAdapterConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.AuthenticatePayment;
import com.peoples.banking.partner.domain.interac.deposit.model.DeclinePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.IncomingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.OutgoingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentOptionResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentsCancelPostRequestModel;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentBeginRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentBeginResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentCommitRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivedPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceAdviceResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceLocationResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ReversePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.io.UnsupportedEncodingException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Adapter implementation for Interac's <i>Payment Processing</i> service domain.
 */
@Service
public class PaymentAdapterImpl extends InteracRestAdapter<ErrorModel> implements PaymentAdapter {


  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(PaymentAdapterImpl.class);

  /**
   * <i>Interac</i> service domain.
   */
  private static final String SERVICE_DOMAIN = "PaymentProcessing";

  /**
   * <i>Interac</i> Initiate Payment endpoint URL.
   */
  private String initiatePaymentEndpoint = null;

  /**
   * <i>Interac</i> Submit Payment endpoint URL.
   */
  private String submitPaymentEndpoint = null;

  /**
   * <i>Interac</i> Stop Payment endpoint URL.
   */
  private String stopPaymentEndpoint = null;

  /**
   * <i>Interac</i> Payment Options endpoint URL.
   */
  private String paymentOptionsEndpoint = null;

  /**
   * <i>Interac</i> Cancel Payment endpoint URL.
   */
  private String cancelPaymentEndpoint = null;

  /**
   * <i>Interac</i> Retrieve Payment endpoint URL.
   */
  private String getPaymentEndpoint = null;

  /**
   * <i>Interac</i> Outgoing Transfers endpoint URL.
   */
  private String getOutgoingTransfersEndpoint = null;

  /**
   * <i>Interac</i> Received Transfer endpoint endpoint URL.
   */
  private String getReceivedTransfersEndpoint = null;

  /**
   * <i>Interac</i> Retrieve Payment endpoint URL.
   */
  private String getIncomingPaymentEndpoint = null;

  /**
   * <i>Interac</i> Authenticate Payment endpoint URL.
   */
  private String authenticatePaymentEndpoint = null;

  private String remittanceLocationEndpoint = null;

  private String remittanceAdviceEndpoint = null;

  /**
   * <i>Interac</i> Decline Payment endpoint URL.
   */
  private String declinePaymentEndpoint = null;


  /**
   * <i>Interac</i> Complete Payment Begin endpoint URL.
   */
  private String completePaymentBeginEndpoint = null;

  /**
   * <i>Interac</i> Complete Payment Commit endpoint URL.
   */
  private String completePaymentCommitEndpoint = null;

  /**
   * <i>Interac</i> Complete Payment Rollback endpoint URL.
   */
  private String completePaymentRollbackEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public InitiatePaymentResponse initiatePayment(String enrolmentId, InitiatePaymentRequest request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || request == null) {
      LOGGER.warn("required parameters: enrolmentId={}, request={}", enrolmentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getInitiatePaymentEndpoint();

    // build path parameters -- N/A

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, isRetry.toString());

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.POST, InteracRequestType.INITIATE_PAYMENT,
            enrolmentId, indirectConnectorId, request, InitiatePaymentResponse.class, null, headerParams);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public SubmitPaymentResponse submitPayment(String enrolmentId, String paymentTransactionToken, SubmitPaymentRequest request,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentTransactionToken == null || paymentTransactionToken.isBlank()
        || request == null) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentTransactionToken={}, request={}",
          enrolmentId, paymentTransactionToken, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getSubmitPaymentEndpoint();

    // build path parameters -- N/A

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_PAYMENT_TRANSACTION_TOKEN, paymentTransactionToken);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.PUT, InteracRequestType.SUBMIT_PAYMENT,
            enrolmentId, indirectConnectorId, request, SubmitPaymentResponse.class, null, headerParams);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ReversePaymentResponse reversePayment(String enrolmentId, String participantTransactionId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || participantTransactionId == null || participantTransactionId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}, participantTransactionId={}", enrolmentId, participantTransactionId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getStopPaymentEndpoint();

    // Build the final URI based on user input
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.QUERY_PARTICIPANT_TRANSACTION_ID, participantTransactionId);

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this.execute(builder.toUriString(), HttpMethod.DELETE, InteracRequestType.REVERSE_PAYMENT, enrolmentId, indirectConnectorId,
        null, ReversePaymentResponse.class, null, null);

  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public PaymentOptionResponse paymentOptions(String enrolmentId, String depositType, String depositHandle, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException, UnsupportedEncodingException {
    PaymentOptionResponse response;

    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || depositType == null || depositType.isBlank() || depositHandle == null
        || depositHandle.isBlank()) {
      LOGGER.warn("payment Options : enrolmentId={}, depositType={}, depositHandle={}", enrolmentId, depositType, depositHandle);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getPaymentOptionsEndpoint();

    final String uri = UriComponentsBuilder.fromHttpUrl(endpointUrl)
            .queryParam(PaymentAdapterConstant.QUERY_PRODUCT_CODE, PaymentAdapterConstant.PRODUCT_CODE)
            .queryParam(PaymentAdapterConstant.QUERY_DEPOSIT_TYPE, depositType)
            .queryParam(PaymentAdapterConstant.QUERY_DEPOSIT_HANDLE, depositHandle)
            .toUriString()
            .replaceAll("\\+", "%2B");

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    response = this.execute(uri, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId,
        null, PaymentOptionResponse.class, null, null);

    return response;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean cancelPayment(String enrolmentId, String paymentId, PaymentsCancelPostRequestModel request, Boolean isRetry,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank() || request == null) {
      LOGGER.warn("cancel Payment parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, paymentId, request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getCancelPaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, isRetry.toString());

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.CANCEL_PAYMENT, enrolmentId, indirectConnectorId, request,
        PaymentsCancelPostRequestModel.class, pathParams, headerParams);

    return true;
  }


  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public GetPaymentStatusResponse getPayment(String enrolmentId, String paymentId, GetPaymentStatusRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentId={}", enrolmentId, paymentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getGetPaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    return this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.GET_PAYMENT, enrolmentId, indirectConnectorId, request,
        GetPaymentStatusResponse.class, pathParams, null);
  }

  /**
   * @return
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public List<OutgoingPayment> getOutgoingTransfers(String enrolmentId, OffsetDateTime fromDate, OffsetDateTime toDate,
      int offset, int max_response_items, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}", enrolmentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getOutgoingTransfersEndpoint();

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.nnnnnnnnnX");

    // Build the final URI based on user input
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.FROM_DATE, formatter.format(fromDate))
        .queryParam(PaymentAdapterConstant.TO_DATE, formatter.format(toDate))
        .queryParam(PaymentAdapterConstant.OFFSET, offset)
        .queryParam(PaymentAdapterConstant.MAX_RESPONSE_ITEMS, max_response_items);

    OutgoingPayment[] outgoingPayments = this.execute(builder.toUriString(), HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId,
        indirectConnectorId, null, OutgoingPayment[].class, null, null);

    return Arrays.asList(outgoingPayments.clone());
  }

  /**
   * @return
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public List<ReceivedPayment> getReceivedTransfers(String enrolmentId, OffsetDateTime fromDate, OffsetDateTime toDate,
      int offset, int max_response_items, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}", enrolmentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getReceivedTransfersEndpoint();

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.nnnnnnnnnX");

    // Build the final URI based on user input
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.FROM_DATE, formatter.format(fromDate))
        .queryParam(PaymentAdapterConstant.TO_DATE, formatter.format(toDate))
        .queryParam(PaymentAdapterConstant.OFFSET, offset)
        .queryParam(PaymentAdapterConstant.MAX_RESPONSE_ITEMS, max_response_items);

    ReceivedPayment[] receivedPayments = this.execute(builder.toUriString(), HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId,
        indirectConnectorId, null,
        ReceivedPayment[].class, null, null);

    return Arrays.asList(receivedPayments.clone());
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public IncomingPayment getIncomingPayment(String enrolmentId, String paymentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentId={}", enrolmentId, paymentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getGetIncomingPaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    return this.execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
        IncomingPayment.class, pathParams, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean authenticatePayment(String enrolmentId, String paymentId, AuthenticatePayment request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank() || request == null) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentId={}", enrolmentId, paymentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getAuthenticatePaymentEndpoint();
    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    this.execute(endpointUrl, HttpMethod.PATCH, InteracRequestType.AUTHENTICATE_PAYMENT, enrolmentId, indirectConnectorId, request,
        AuthenticatePayment.class, pathParams, null);

    return true;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean declinePayment(final String enrolmentId, final String paymentId, final DeclinePaymentRequest request,
      final Boolean isRetry, String indirectConnectorId) throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank() || request == null) {
      LOGGER.warn("decline Payment parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, paymentId, request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getDeclinePaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, isRetry.toString());

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.DECLINE_PAYMENT, enrolmentId, indirectConnectorId, request,
        DeclinePaymentRequest.class, pathParams, headerParams);

    return true;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public RemittanceAdviceResponse getRemittanceAdvice(final String clearingReference, final String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(clearingReference, enrolmentId)) {
      LOGGER.warn("required parameters: clearingReference={}, remittanceId={}", clearingReference, enrolmentId);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getRemittanceAdviceEndpoint();

    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.CLEARING_SYSTEM_REFERENCE, clearingReference);

    return this.execute(builder.toUriString(), HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId,
        indirectConnectorId, null, RemittanceAdviceResponse.class, null, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public RemittanceLocationResponse getRemittanceLocation(final String clearingReference, final String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(clearingReference, enrolmentId)) {
      LOGGER.warn("required parameters: clearingReference={}, remittanceId={}", clearingReference, enrolmentId);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getRemittanceLocationEndpoint();

    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.CLEARING_SYSTEM_REFERENCE, clearingReference);

    return this.execute(builder.toUriString(), HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId,
        indirectConnectorId, null, RemittanceLocationResponse.class, null, null);
  }


  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ReceivePaymentBeginResponse completePaymentBegin(String enrolmentId, String paymentId,
      ReceivePaymentBeginRequest request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank()) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentId={}", enrolmentId, paymentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getCompletePaymentBeginEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, isRetry.toString());

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.COMPLETE_PAYMENT_BEGIN, enrolmentId, indirectConnectorId, request,
        ReceivePaymentBeginResponse.class, pathParams, headerParams);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean completePaymentCommit(final String enrolmentId, final String paymentId, final String paymentTransactionToken,
      final ReceivePaymentCommitRequest request, final Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TimeoutException, TBDException {
    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || paymentId == null || paymentId.isBlank() || paymentTransactionToken == null
        || paymentTransactionToken.isBlank()
        || request == null) {
      LOGGER.warn("required parameters: enrolmentId={}, paymentId={}, paymentTransactionToken={}, request={}",
          enrolmentId, paymentId, paymentTransactionToken, request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getCompletePaymentCommitEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, isRetry.toString());
    headerParams.put(InteracRestAdapterConstant.HEADER_RECEIVE_PAYMENT_TRANSACTION_TOKEN, paymentTransactionToken);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    this.execute(endpointUrl, HttpMethod.PUT, InteracRequestType.COMPLETE_PAYMENT_COMMIT, enrolmentId, indirectConnectorId, request,
        ReceivePaymentCommitRequest.class, pathParams, headerParams);

    return true;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean reverseCompletePayment(String enrolmentId, String paymentId, String ptcPaymentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(enrolmentId, ptcPaymentId, paymentId)) {
      LOGGER.warn("required parameters: enrollmentId={}, ptcPaymentId={}, networkPaymentId={}", enrolmentId, ptcPaymentId, paymentId);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(PaymentAdapterConstant.PAYMENT_ID, paymentId);

    // endpoint URL
    String endpointUrl = getReverseCompletePaymentEndpoint();
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(endpointUrl)
        .queryParam(PaymentAdapterConstant.QUERY_PARTICIPANT_TRANSACTION_ID, ptcPaymentId);

    // build path parameters
    this.execute(builder.buildAndExpand(pathParams).toUri().toString(), HttpMethod.DELETE, InteracRequestType.DEFAULT, enrolmentId,
        indirectConnectorId, null,
        String.class, null, null);

    return true;
  }

  /**
   * @inheritDoc
   */
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams)
      throws AdapterException {
    // x-et-retry-indicator applies to INITIATE PAYMENT or SUBMIT PAYMENT or CANCEL_PAYMENT or COMPLETE_PAYMENT_BEGIN or COMPLETE_PAYMENT_COMMIT
    if (interacRequestType.equals(InteracRequestType.INITIATE_PAYMENT)
        || interacRequestType.equals(InteracRequestType.SUBMIT_PAYMENT)
        || interacRequestType.equals(InteracRequestType.CANCEL_PAYMENT)
        || interacRequestType.equals(InteracRequestType.COMPLETE_PAYMENT_BEGIN)
        || interacRequestType.equals(InteracRequestType.COMPLETE_PAYMENT_COMMIT)
        || interacRequestType.equals(InteracRequestType.DECLINE_PAYMENT)) {
      String retryIndicator = (String) headerParams.get(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR);

      // sanity check
      if (retryIndicator == null || retryIndicator.isBlank()) {
        LOGGER.debug("defaulting retry indicator to false");
        retryIndicator = "false";
      }

      // must add x-et-retry-indicator
      httpHeaders.add(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, retryIndicator);

      LOGGER
          .info("{}={}", InteracRestAdapterConstant.HEADER_RETRY_INDICATOR,
              httpHeaders.get(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR));
    }

    // x-payment-transaction-token applies to SUBMIT PAYMENT and SUBMIT RECEIVE PAYMENT
    if (interacRequestType.equals(InteracRequestType.SUBMIT_PAYMENT) || interacRequestType
        .equals(InteracRequestType.COMPLETE_PAYMENT_COMMIT)) {
      String headerName = null;
      if (interacRequestType.equals(InteracRequestType.SUBMIT_PAYMENT)) {
        headerName = InteracRestAdapterConstant.HEADER_PAYMENT_TRANSACTION_TOKEN;
      } else {
        headerName = InteracRestAdapterConstant.HEADER_RECEIVE_PAYMENT_TRANSACTION_TOKEN;
      }

      String paymentTransactionToken = (String) headerParams.get(headerName);

      // sanity check
      if (paymentTransactionToken == null || paymentTransactionToken.isBlank()) {
        throw new AdapterException("paymentTransactionToken not supplied", ErrorCode.UNEXPECTED_EXCEPTION);
      }

      // must add x-payment-transaction-token
      httpHeaders.add(headerName, paymentTransactionToken);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      LOGGER.info("{}={}", headerName, httpHeaders.get(headerName));
    }
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return SERVICE_DOMAIN;
  }

  /**
   * Helper utility to construct <i>InitiatePayment</i> endpoint URL.
   *
   * @return InitiatePayment endpoint URL
   */
  private String getInitiatePaymentEndpoint() {
    if (initiatePaymentEndpoint == null) {
      initiatePaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.INITIATE_PAYMENT;
    }
    return initiatePaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>SubmitPayment</i> endpoint URL.
   *
   * @return SubmitPayment endpoint URL
   */
  private String getSubmitPaymentEndpoint() {
    if (submitPaymentEndpoint == null) {
      submitPaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.SUBMIT_PAYMENT;
    }
    return submitPaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>StopPayment</i> endpoint URL.
   *
   * @return StopPayment endpoint URL
   */
  private String getStopPaymentEndpoint() {
    if (stopPaymentEndpoint == null) {
      stopPaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.STOP_PAYMENT;
    }
    return stopPaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>PaymentOptions</i> endpoint URL.
   *
   * @return PaymentOptions endpoint URL
   */
  private String getPaymentOptionsEndpoint() {
    if (paymentOptionsEndpoint == null) {
      paymentOptionsEndpoint = this.getBaseUrl() + PaymentAdapterConstant.PAYMENT_OPTIONS;
    }
    return paymentOptionsEndpoint;
  }

  /**
   * Helper utility to construct <i>CancelPayment</i> endpoint URL.
   *
   * @return CancelPayment endpoint URL
   */
  private String getCancelPaymentEndpoint() {
    if (cancelPaymentEndpoint == null) {
      cancelPaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.CANCEL_PAYMENT;
    }
    return cancelPaymentEndpoint;
  }


  /**
   * Helper utility to construct <i>GetPayment</i> endpoint URL.
   *
   * @return GetPayment endpoint URL
   */
  private String getGetPaymentEndpoint() {
    if (getPaymentEndpoint == null) {
      getPaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.GET_PAYMENT;
    }
    return getPaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>GetOutgoingPayment</i> endpoint URL.
   *
   * @return getOutgoingTransfers endpoint URL
   */
  private String getOutgoingTransfersEndpoint() {
    if (getOutgoingTransfersEndpoint == null) {
      getOutgoingTransfersEndpoint = this.getBaseUrl() + PaymentAdapterConstant.GET_OUT_GOING_TRANSFERS;
    }
    return getOutgoingTransfersEndpoint;
  }

  /**
   * Helper utility to construct <i>GetReceivedPayment</i> endpoint URL.
   *
   * @return getReceivedTransfers endpoint URL
   */
  private String getReceivedTransfersEndpoint() {
    if (getReceivedTransfersEndpoint == null) {
      getReceivedTransfersEndpoint = this.getBaseUrl() + PaymentAdapterConstant.GET_RECEIVED_TRANSFERS;
    }
    return getReceivedTransfersEndpoint;
  }

  /**
   * Helper utility to construct <i>GetPayment</i> endpoint URL.
   *
   * @return GetPayment endpoint URL
   */
  private String getGetIncomingPaymentEndpoint() {
    if (getIncomingPaymentEndpoint == null) {
      getIncomingPaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.INCOMING_TRANSFER;
    }
    return getIncomingPaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>AuthenticatePayment</i> endpoint URL.
   *
   * @return AuthenticatePayment endpoint URL
   */
  private String getAuthenticatePaymentEndpoint() {
    if (authenticatePaymentEndpoint == null) {
      authenticatePaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.AUTHENTICATE_PAYMENT;
    }
    return authenticatePaymentEndpoint;
  }

  private String getRemittanceLocationEndpoint() {
    if (remittanceLocationEndpoint == null) {
      remittanceLocationEndpoint = this.getBaseUrl() + PaymentAdapterConstant.REMITTANCE_LOCATION;
    }
    return remittanceLocationEndpoint;
  }

  private String getRemittanceAdviceEndpoint() {
    if (remittanceAdviceEndpoint == null) {
      remittanceAdviceEndpoint = this.getBaseUrl() + PaymentAdapterConstant.REMITTANCE_ADVICE;
    }
    return remittanceAdviceEndpoint;
  }

  /**
   * Helper utility to construct <i>DeclinePayment</i> endpoint URL.
   *
   * @return CancelPayment endpoint URL
   */
  private String getDeclinePaymentEndpoint() {
    if (declinePaymentEndpoint == null) {
      declinePaymentEndpoint = this.getBaseUrl() + PaymentAdapterConstant.DECLINE_PAYMENT;
    }
    return declinePaymentEndpoint;
  }

  /**
   * Helper utility to construct <i>CancelPayment</i> endpoint URL.
   *
   * @return CancelPayment endpoint URL
   */
  private String getCompletePaymentBeginEndpoint() {
    if (completePaymentBeginEndpoint == null) {
      completePaymentBeginEndpoint = this.getBaseUrl() + PaymentAdapterConstant.INITIATE_RECEIVE_PAYMENT;
    }
    return completePaymentBeginEndpoint;
  }

  /**
   * Helper utility to construct <i>CommitReceivePayment</i> endpoint URL.
   *
   * @return CommitReceivePayment endpoint URL
   */
  private String getCompletePaymentCommitEndpoint() {
    if (completePaymentCommitEndpoint == null) {
      completePaymentCommitEndpoint = this.getBaseUrl() + PaymentAdapterConstant.INITIATE_RECEIVE_PAYMENT;
    }
    return completePaymentCommitEndpoint;
  }

  /**
   * Helper utility to construct <i>CancelPayment</i> endpoint URL.
   *
   * @return ReverseCompletePayment endpoint URL
   */
  private String getReverseCompletePaymentEndpoint() {
    if (completePaymentRollbackEndpoint == null) {
      completePaymentRollbackEndpoint = this.getBaseUrl() + PaymentAdapterConstant.REVERSE_RECEIVE_PAYMENT;
    }
    return completePaymentRollbackEndpoint;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }
}
