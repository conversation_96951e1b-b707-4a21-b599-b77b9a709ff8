package com.peoples.banking.partner.adapter.interac.payment.config;

/**
 * Constants for Interac's <i>Payment Processing</i> service domain.
 */
public final class PaymentAdapterConstant {

  /**
   * <i>Interac</i> root context path (incl. version)
   */
  public static final String PAYMENT_ROOT_CONTEXT = "/payment-api/v3.5.0";

  private static final String PAYMENT_OPERATION = "/payments";
  public static final String PAYMENT_ID = "id";
  public static final String FROM_DATE = "from_date";
  public static final String OFFSET = "offset";
  public static final String MAX_RESPONSE_ITEMS = "max_response_items";
  public static final String TO_DATE = "to_date";
  public static final String CLEARING_SYSTEM_REFERENCE = "clearing_system_reference";
  public static final String ID = "id";
  public static final String PAYMENT_WITH_ID_OPERATION = PAYMENT_OPERATION + "/{" + PAYMENT_ID + "}";
  public static final String TRANSACTION = "/transaction";

  public static final String INITIATE_PAYMENT = PAYMENT_ROOT_CONTEXT + PAYMENT_OPERATION + TRANSACTION;
  public static final String SUBMIT_PAYMENT = INITIATE_PAYMENT;
  public static final String STOP_PAYMENT = INITIATE_PAYMENT;

  public static final String GET_OUT_GOING_TRANSFERS = PAYMENT_ROOT_CONTEXT + PAYMENT_OPERATION;

  public static final String GET_RECEIVED_TRANSFERS = PAYMENT_ROOT_CONTEXT + PAYMENT_OPERATION + "/receive";

  public static final String PAYMENT_OPTIONS = PAYMENT_ROOT_CONTEXT + PAYMENT_OPERATION + "/options";

  public static final String CANCEL_PAYMENT = PAYMENT_ROOT_CONTEXT + PAYMENT_WITH_ID_OPERATION + "/cancel";

  public static final String GET_PAYMENT = PAYMENT_ROOT_CONTEXT + PAYMENT_OPERATION + "/details";

  public static final String INCOMING_TRANSFER = PAYMENT_ROOT_CONTEXT + PAYMENT_WITH_ID_OPERATION + "/receive";

  public static final String REMITTANCE_ADVICE = PAYMENT_ROOT_CONTEXT + "/remittance-advice";

  public static final String REMITTANCE_LOCATION = PAYMENT_ROOT_CONTEXT + "/remittance-location";

  public static final String AUTHENTICATE_PAYMENT = PAYMENT_ROOT_CONTEXT + PAYMENT_WITH_ID_OPERATION + "/authenticate";

  public static final String DECLINE_PAYMENT = INCOMING_TRANSFER + "/decline";

  public static final String INITIATE_RECEIVE_PAYMENT = INCOMING_TRANSFER + TRANSACTION;

  public static final String REVERSE_RECEIVE_PAYMENT = INCOMING_TRANSFER + "/transaction";

  /**
   * Query parameters
   */
  public static final String QUERY_PRODUCT_CODE = "product_code";
  public static final String QUERY_DEPOSIT_TYPE = "deposit_type";
  public static final String QUERY_DEPOSIT_HANDLE = "deposit_handle";
  public static final String QUERY_PARTICIPANT_TRANSACTION_ID = "participant_transaction_id";
  public static final String PRODUCT_CODE = "DOMESTIC";

  // TODO rename static constants so a reorg on this class doesn't ruin the flow

  private PaymentAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

}
