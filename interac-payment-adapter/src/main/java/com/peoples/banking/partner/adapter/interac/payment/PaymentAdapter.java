package com.peoples.banking.partner.adapter.interac.payment;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.interac.deposit.model.AuthenticatePayment;
import com.peoples.banking.partner.domain.interac.deposit.model.DeclinePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.IncomingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.OutgoingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentOptionResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentsCancelPostRequestModel;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentBeginRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentBeginResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivePaymentCommitRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReceivedPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceAdviceResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceLocationResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ReversePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.io.UnsupportedEncodingException;
import java.time.OffsetDateTime;
import java.util.List;

/**
 * Adapter for Interac's <i>Payment Processing</i> service domain.
 */
public interface PaymentAdapter {

  /**
   * Initiate a new e-Transfer. This service does not send the actual transfer. It is only the first step in a 2-step process, and it must be followed
   * either by a Submit Payment call (to actually send the payment) or by a Reverse Payment call (to stop the payment).
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param isRetry             indicator whether this is a retry attempt
   * @param request             request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  InitiatePaymentResponse initiatePayment(
      String enrolmentId, InitiatePaymentRequest request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Submit a payment that was initiated (set up) using an Initiate Payment service.
   *
   * @param enrolmentId             unique customer identifier on Interac system
   * @param paymentTransactionToken One-time token received from the previous Payment initiate (sent / receive/ cancel ) response.
   * @param request                 request body payload
   * @param indirectConnectorId     indirect connector to pass if it's not null
   * @return
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  @PerfLogger
  SubmitPaymentResponse submitPayment(String enrolmentId, String paymentTransactionToken, SubmitPaymentRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Stop a payment that was initiated (set up) using an Initiate Payment service.
   *
   * @param enrolmentId              unique customer identifier on Interac system
   * @param participantTransactionId TransactionIdentification reference of the payment or payment request. ( participant transaction id ), Present if
   *                                 ClrSysRef is missing
   * @param indirectConnectorId      indirect connector to pass if it's not null
   * @return
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  @PerfLogger
  ReversePaymentResponse reversePayment(String enrolmentId, String participantTransactionId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Look up a registered service and to inquire about the transfer options available for a specific Contact/Recipient belonging to a Customer. It is
   * normally invoked before creating and submitting a transfer to that Contact/Recipient.
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param depositType         Flag indicating the type of the Account Alias registration
   * @param depositHandle       Handle associated with the deposit type
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  @PerfLogger
  PaymentOptionResponse paymentOptions(String enrolmentId, String depositType, String depositHandle, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException, UnsupportedEncodingException;

  /**
   * Cancels an existing for payment.
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param paymentId           send Money clearingSystemReference id
   * @param isRetry             indicator whether this is a retry attempt
   * @param request             request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return boolean
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  @PerfLogger
  Boolean cancelPayment(String enrolmentId, String paymentId, PaymentsCancelPostRequestModel request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the details of an existing payment.
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param paymentId           unique request identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return GetPaymentStatusResponse - payment details
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  GetPaymentStatusResponse getPayment(String enrolmentId, String paymentId, GetPaymentStatusRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the a list of the outgoing transfers for one of their Customer
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param fromDate            Start date/time in UTC
   * @param toDate              End date/time in UTC
   * @param offset              offset is starting point of received payment filter; if offset is not provided it would be defaulted to zero;
   * @param max_response_items  Maximum number of response items to be returned. All items are returned if this field is absent.
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return List of OutgoingPayment
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  List<OutgoingPayment> getOutgoingTransfers(String enrolmentId, OffsetDateTime fromDate, OffsetDateTime toDate, int offset,
      int max_response_items, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the a list of the received transfers for one of their Customer
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param fromDate            Start date/time in UTC
   * @param toDate              End date/time in UTC
   * @param offset              offset is starting point of received payment filter; if offset is not provided it would be defaulted to zero;
   * @param max_response_items  Maximum number of response items to be returned. All items are returned if this field is absent.
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return List of ReceivedPayment
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  List<ReceivedPayment> getReceivedTransfers(String enrolmentId, OffsetDateTime fromDate, OffsetDateTime toDate,
      int offset, int max_response_items, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the details of an incoming payment.
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param paymentId           unique request identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return IncomingPayment incoming payment if exists in interac
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  @PerfLogger
  IncomingPayment getIncomingPayment(String enrolmentId, String paymentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Authenticate a received payment (regular transfer).
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param paymentId           unique request identifier on Interac system
   * @param request             request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   */
  @PerfLogger
  Boolean authenticatePayment(String enrolmentId, String paymentId, AuthenticatePayment request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Declines an existing payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param paymentId   send Money clearingSystemReference id
   * @param isRetry     indicator whether this is a retry attempt
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return boolean if operation was successful
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  @PerfLogger
  Boolean declinePayment(String enrolmentId, String paymentId, DeclinePaymentRequest request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * get remittance advice
   *
   * @param paymentId     send Money clearingSystemReference id
   * @param enrolmentId  unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return ReceivePaymentBeginResponse ReceivePaymentBeginResponse on Interac system
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  RemittanceAdviceResponse getRemittanceAdvice(String paymentId, String enrolmentId, String indirectConnectorId)
          throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * get remittance location
   *
   * @param paymentId     send Money clearingSystemReference id
   * @param enrolmentId  unique customer identifier on Interac system
   ** @param indirectConnectorId indirect connector to pass if it's not null
   * @return ReceivePaymentBeginResponse ReceivePaymentBeginResponse on Interac system
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  RemittanceLocationResponse getRemittanceLocation(String paymentId, String enrolmentId, String indirectConnectorId)
          throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * begin the Retrieve Payment
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param paymentId   unique request identifier on Interac system
   * @param request     ReceivePaymentBeginRequest on Interac system
   * @param isRetry     indicator whether this is a retry attempt
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return ReceivePaymentBeginResponse ReceivePaymentBeginResponse on Interac system
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  ReceivePaymentBeginResponse completePaymentBegin(String enrolmentId, String paymentId,
      ReceivePaymentBeginRequest request, Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * commit the Retrieve Payment
   *
   * @param enrolmentId             unique customer identifier on Interac system
   * @param paymentTransactionToken unique request identifier on Interac system
   * @param request                 ReceivePaymentCommitRequest on Interac system
   * @param isRetry                 indicator whether this is a retry attempt
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  @PerfLogger
  Boolean completePaymentCommit(String enrolmentId, String paymentId, String paymentTransactionToken, ReceivePaymentCommitRequest request,
      Boolean isRetry, String indirectConnectorId)
      throws AdapterException, ResponseException, TimeoutException, TBDException;

  /**
   * @param enrolmentId  - enrollment id
   * @param paymentId    - interac payment id
   * @param ptcPaymentId - PTC payment id
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return - true if request was successful
   * @throws AdapterException, ResponseException, TBDException, TimeoutException - if something go wrong
   */
  Boolean reverseCompletePayment(String enrolmentId, String paymentId, String ptcPaymentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;
}
