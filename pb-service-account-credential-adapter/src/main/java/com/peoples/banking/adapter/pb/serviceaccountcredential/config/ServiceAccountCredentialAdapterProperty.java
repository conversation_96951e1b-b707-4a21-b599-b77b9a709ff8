package com.peoples.banking.adapter.pb.serviceaccountcredential.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class ServiceAccountCredentialAdapterProperty {

  /**
   * service account credential api URL (protocol://host:port).
   */
  @Value("${sys.pb.serviceaccountcredential.url.base}")
  private String serviceAccountCredentialUrlBase;

  /**
   * Time to live (maximum age) of a request (in milliseconds).
   * <pre>defaults to 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.time-to-live:30000}")
  private Integer timeToLive;

  /**
   * HTTP connection keepalive interval (in milliseconds).
   * <pre>default is 20 seconds = 20000 ms</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.connection.keep-alive:20000}")
  private Integer connectionKeepAlive;

  /**
   * Maximum number of HTTP connections.
   * <pre>default is 25</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.connection.max:25}")
  private Integer maxHttpConnections;

  /**
   * Delay before reaper removes idle HTTP connections (in milliseconds).
   * <pre>default is 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.connection.wait-time:30000}")
  private Integer waitTimeIdleConnection;

  /**
   * Connection timeout for requesting a connection from connection manager (in milliseconds).
   * <pre>defaults to 5 seconds = 5000 ms.</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.connection-request-timeout:5000}")
  private Integer connectionReqTimeout;

  /**
   * Timeout waiting until a connection is established (in milliseconds).
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.connection.timeout:15000}")
  private Integer connectTimeout;

  /**
   * Read timeout for requests to customer api.
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pb.serviceaccountcredential.read.timeout:15000}")
  private Integer readTimeout;
}
