package com.peoples.banking.adapter.pb.serviceaccountcredential;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;

public interface ServiceAccountCredentialAdapter {

  /**
   * retrieve service account by api token
   *
   * @param createServiceAccountCredentialRequest - service account credential object
   * @return - service account mapped to ServiceAccountResponse
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  void createServiceAccountCredential(CreateServiceAccountCredentialRequest createServiceAccountCredentialRequest)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
   *
   * @param response       response body from <i>Interac</i>
   * @param httpStatusCode HTTP status code from downstream system
   * @throws ResponseException response exception from Interac
   */
  void throwResponseException(String response, Integer httpStatusCode) throws ResponseException;
}
