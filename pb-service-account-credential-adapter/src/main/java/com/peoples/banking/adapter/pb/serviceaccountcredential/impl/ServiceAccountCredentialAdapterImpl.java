package com.peoples.banking.adapter.pb.serviceaccountcredential.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.pb.serviceaccountcredential.ServiceAccountCredentialAdapter;
import com.peoples.banking.adapter.pb.serviceaccountcredential.config.ServiceAccountCredentialAdapterConstant;
import com.peoples.banking.adapter.pb.serviceaccountcredential.config.ServiceAccountCredentialAdapterProperty;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.domain.serviceaccountcredential.model.ErrorResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.util.Collections;
import lombok.extern.log4j.Log4j2;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

@Log4j2
@Service
public class ServiceAccountCredentialAdapterImpl implements ServiceAccountCredentialAdapter {

  @Autowired
  private ServiceAccountCredentialAdapterProperty serviceAccountAdapterProperty;

  @Autowired
  @Qualifier("serviceAccountCredentialManagementRestTemplate")
  private RestTemplate restTemplate;

  @Autowired
  protected ObjectMapper objectMapper;

  /**
   * service account endpoint URL.
   */
  private String createServiceAccountCredentialEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public void createServiceAccountCredential(CreateServiceAccountCredentialRequest createServiceAccountCredentialRequest)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (createServiceAccountCredentialRequest == null) {
      throw new AdapterException("invalid input arguments for createServiceAccountCredentialRequest");
    }

    // endpoint URL
    String endpointUrl = getCreateServiceAccountCredentialEndpoint();

    try {
      // populate HTTP headers
      log.debug("populating HTTP headers");
      HttpHeaders headers = new HttpHeaders();
      // default HTTP httpHeaders
      headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
      headers.setContentType(MediaType.APPLICATION_JSON);
      HttpEntity<CreateServiceAccountCredentialRequest> httpEntity = new HttpEntity<>(createServiceAccountCredentialRequest, headers);

      restTemplate.exchange(endpointUrl, HttpMethod.POST, httpEntity, CreateServiceAccountCredentialRequest.class);

    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)

      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side-effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }
  }

  /**
   * @inheritDoc
   */
  // TODO clean this up, not elegant; make part of abstract class
  @Override
  public void throwResponseException(String response, Integer httpStatusCode) throws ResponseException {

    // sanity check
    if (response != null && !response.isBlank()) {
      ErrorResponse responseModel;
      try {
        responseModel = this.convertResponseToDomain(response, ErrorResponse.class);
      } catch (TBDException e) {
        log.warn("cannot convert Customer Management error response into something meaningful");
        throw new ResponseException(httpStatusCode, null, null);
      }

      throw new ResponseException(httpStatusCode,
          responseModel.getError().get(0).getCode(),
          responseModel.getError().get(0).getAdditionalInformation());
    } else {
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>       the generic object type that the response is converted to
   * @param payload   payload to be converted
   * @param classType the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String payload, Class<T> classType) throws TBDException {
    T response = null;

    // sanity check
    if (payload != null && !payload.isBlank()) {
      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }

  /**
   * Returns the <i>system</i> domain endpoint URL.
   */
  protected String getBaseUrl() {
    return serviceAccountAdapterProperty.getServiceAccountCredentialUrlBase();
  }

  /**
   * Helper utility to construct <i>AccountByApiToken</i> endpoint URL.
   *
   * @return
   */
  public String getCreateServiceAccountCredentialEndpoint() {
    if (createServiceAccountCredentialEndpoint == null) {
      createServiceAccountCredentialEndpoint = this.getBaseUrl() + ServiceAccountCredentialAdapterConstant.CREATE_SERVICE_ACCOUNT_CREDENTIAL;
    }
    return createServiceAccountCredentialEndpoint;
  }

}
