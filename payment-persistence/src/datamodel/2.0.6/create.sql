-- Schema for Banking as a Service
CREATE SCHEMA IF NOT EXISTS transaction;

DO $$ BEGIN
	-- ENUM for PAYMENTS.TYPE_CD (code)
  CREATE type transaction.payment_cd_type AS ENUM('REQUEST', 'INBOUND', 'OUTBOUND');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- PAYMENTS, listing payments a customer has initiated or has been initiated on their behalf
CREATE table IF NOT EXISTS transaction.payments (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    parent_id                       INTEGER, -- internal unique identifier to refer to parent (in case of fulfilment, links to the initial request)
      -- NOT USED YET, until we implement deposit services

    service_account_ref_id          CHARACTER VARYING NOT NULL, -- unique identifier for the partner organization owning the customer
      -- same value as customers.service_account_ref_id (so we can find all transactions for a partner, without joins)

    end_to_end_id                   CHARACTER VARYING, -- unique end to end identifier
      -- same value as request.end_to_end_id; if not provided, keep this null

    customer_ref_id                 CHARACTER VARYING NOT NULL, -- unique identifier for the customer
      -- from JSON request.customer_id
      -- must verify the customer exists first, via API call to Customer API

    external_ref_id                 CHARACTER VARYING NOT NULL, -- external identifier (used by service provider) to identify the payment
      -- generated by US as soon as we receive the request, this is NOT the Interac payment reference number.
      -- from JSON, response.request_ref_id

    type_cd                         transaction.payment_cd_type NOT NULL, -- identifies the type of payment (request payment, outbound or inbound payment)
      -- for request payment, this is REQUEST


    contact_json                    JSONB, -- recipient the payment or request payment is intended for
      -- from JSON request.contact (entire JSON object)
      -- in future once we support contact ID, we will still populate this with a snapshot of the contact object
    contact_json_ver                SMALLINT NOT NULL, -- JSON object version for POJO conversion

    amount                          NUMERIC(7,2) NOT NULL, -- amount of the request/payment
    account_name                    CHARACTER VARYING,
      -- from JSON, request.account_name; mandatory for request money, not necessarily for all payments though
    account_number                  CHARACTER VARYING,
      -- from JSON, request.account_number; this can be a token, or an account number

    account_transaction_ref_id      CHARACTER VARYING, -- account transaction reference id returned by accounting system

    account_reverse_ref_id          CHARACTER VARYING, -- account transaction reference id returned by accounting system

    option_json                     JSONB, -- optional payment options
      -- from JSON request.options (entire JSON object)
    option_json_ver                 SMALLINT NOT NULL, -- JSON object version for POJO conversion

    remittance_json                 JSONB, -- optional remittance information
      -- from JSON request.remittance_information (entire JSON object)
    remittance_json_ver             SMALLINT NOT NULL, -- JSON object version for POJO conversion

    network_ref_id                  CHARACTER VARYING NOT NULL, -- unique identifier for the payment network used for this payment
      -- this should identify INTERAC, so maybe we use INTERAC_EMT?
    network_payment_ref_id          CHARACTER VARYING, -- unique identifier for the payment, from the payment network
      -- this is the ID returned from Interac for the payment
      -- NOT returned to the client
    network_message_id              CHARACTER VARYING, -- unique network message identifier
    network_transaction_id          CHARACTER VARYING, -- unique network transaction identifier
    network_payment_url             CHARACTER VARYING, -- URL for the request from the payment network
      -- applicable only for request money, this is the response.request_url we get back from Interac
    network_payment_type            CHARACTER VARYING, -- type of payment (based on Payment Networks definition)

    network_enrollment_id  CHARACTER VARYING , -- enrollment id on the payment network
    network_fraud_result_json       JSONB, -- network fraud result
    network_fraud_result_json_ver   SMALLINT NOT NULL, -- JSON object version for POJO conversion
    network_created_date            TIMESTAMP, -- the transaction created timestamp at network side

    status                          CHARACTER VARYING NOT NULL DEFAULT 'INITIATED', -- status of the payment or request
      -- defaults to INITIATED as soon as we create the record, and then we call Interac, update status based on Interac response

    status_reason_json              JSONB, -- status reason
    status_reason_json_ver          SMALLINT NOT NULL, -- JSON object version for POJO conversion

    external_fi_account_info_json                JSONB, -- debtor information from interac settlement file
    external_fi_account_info_json_ver            SMALLINT DEFAULT 1, -- JSON object version for POJO conversion

    expiry_date                     TIMESTAMP, -- date and time payment expires (if applicable)
      -- this is the expiry time we send Interac, which is the current date and time + expiry # of days provided by customer
      -- if customer does not provide this, then we use default
      -- from JSON, response.expiry_date

    settlement_date                 TIMESTAMP, -- date and time for settlement
      -- for request payment, this is the time from OUR side; for incoming payments, this is interac's timestamp
    network_settled                 BOOLEAN NOT NULL DEFAULT FALSE, -- to indicate the payment has been identified during recon and settlement, and has been settled from the networks perspective (eg. its included in the Interac recon and settlement files).
      -- default to false
    sa_settled                      BOOLEAN NOT NULL DEFAULT FALSE, -- to indicate the payment has been included in the client recon report which was generated, and has been included in the PTC-service account settlement (eg. its been included in a report and an EFT instruction has been created to send the partner the funds associated with the transfer/deposit
      -- default to false

    created_on                      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- date and time record was created on
    updated_on                      TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT                      uq_network_payment_ref_id UNIQUE(external_ref_id, network_ref_id, network_payment_ref_id) -- guarantee external_ref_id and network_payment_ref_id  are unique to a service account
);

CREATE INDEX IF NOT exists ix_external_ref_id ON transaction.payments USING btree (external_ref_id);

CREATE INDEX IF NOT exists ix_network_payment_ref_id_and_status ON transaction.payments USING btree (network_ref_id, network_payment_ref_id, status);

CREATE INDEX IF NOT exists ix_network_payment_ref_id_and_network_transaction_id ON transaction.payments USING btree (network_payment_ref_id, network_transaction_id);

CREATE INDEX IF NOT exists ix_created_on_andcustomer_ref_id ON transaction.payments USING btree (created_on, customer_ref_id);

-- PAYMENTS_STATUS_HISTORY, history of all status transitions for the payment
-- always create a record in here, as this should also show the current status of the payment
-- for the first record, the previous_status is null.
CREATE table IF NOT EXISTS transaction.payment_status_history (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    payment_id                      INTEGER NOT NULL REFERENCES transaction.payments(id), -- FK to PAYMENTS.ID

    status                          CHARACTER VARYING NOT NULL, -- latest status of the payment as of this record
    status_reason_json              JSONB, -- status reason
    status_reason_json_ver          SMALLINT NOT NULL, -- JSON object version for POJO conversion
    previous_status                 CHARACTER VARYING, -- previous status before latest status
    end_to_end_id                   CHARACTER VARYING, -- current end_to_end_id
    created_on                      TIMESTAMP NOT NULL -- date and time record was created on
);

-- DEVICE_INFO -- information about the end customers device
CREATE table IF NOT EXISTS transaction.device_info (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    payment_id                      INTEGER NOT NULL REFERENCES transaction.payments(id), -- FK to PAYMENTS.ID
    authentication_method           CHARACTER VARYING NOT NULL, -- customer authentication method
    ip_address                      CHARACTER VARYING NOT NULL, -- latest status of the payment as of this record
    fingerprint                 	  CHARACTER VARYING NOT NULL, -- previous status before latest status
    created_on                      TIMESTAMP NOT NULL -- date and time record was created on
);

CREATE INDEX IF NOT exists ix_device_info_payment_id ON transaction.device_info USING btree (payment_id);

-- PAYMENT reference ID sequence
CREATE SEQUENCE  IF NOT EXISTS transaction.payment_ref_id_seq
	INCREMENT BY 1
	MINVALUE 1000000000000
	MAXVALUE 9999999999999
	START 1000000000000
	CACHE 1
	NO CYCLE;

-- PAYMENT_AUTHENTICATION -- information about payment authentication
CREATE table IF NOT EXISTS transaction.payment_authentications (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    payment_id                      INTEGER NOT NULL REFERENCES transaction.payments(id), -- FK to PAYMENTS.ID
    hash_type                       CHARACTER VARYING NOT NULL, -- payment hash type
    hash_salt                       CHARACTER VARYING NOT NULL, -- payment hash salt
    created_on                      TIMESTAMP NOT NULL -- date and time record was created on
);

CREATE INDEX IF NOT exists ix_payment_authentications_payment_id ON transaction.payment_authentications USING btree (payment_id);

CREATE INDEX concurrently IF NOT exists payment_status_history_payment_id ON transaction.payment_status_history(payment_id);
CREATE INDEX IF NOT exists ix_parent_id ON transaction.payments(parent_id);
