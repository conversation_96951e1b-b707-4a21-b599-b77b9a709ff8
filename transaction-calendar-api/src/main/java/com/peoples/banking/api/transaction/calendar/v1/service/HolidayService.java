package com.peoples.banking.api.transaction.calendar.v1.service;

import com.peoples.banking.domain.transactioncalendar.model.AddHolidayRequest;
import com.peoples.banking.domain.transactioncalendar.model.DeleteHolidayRequest;
import com.peoples.banking.domain.transactioncalendar.model.HolidaysListResponse;
import com.peoples.banking.persistence.calendar.entity.Holidays;
import com.peoples.banking.persistence.calendar.repository.HolidaysRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.logger.annotation.PerfLogger;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class HolidayService {

  private final Set<LocalDate> holidays = new HashSet<>();

  @Autowired
  private HolidaysRepository holidaysRepository;

  @PostConstruct
  protected void init() {
    List<Holidays> holidayList = holidaysRepository.findAll();
    holidays.addAll(holidayList.stream().map(Holidays::getHolidayDate).collect(Collectors.toList()));
  }

  @PerfLogger
  public HolidaysListResponse fetchHolidays() {
    HolidaysListResponse response = new HolidaysListResponse();
    response.setHolidays(holidays.stream().sorted().collect(Collectors.toList()));
    return response;
  }

  public Set<LocalDate> findHolidays() {
    return holidays;
  }

  @PerfLogger
  public boolean addHoliday(AddHolidayRequest addHolidayRequest) {
    LocalDate holidayDate = addHolidayRequest.getHolidayDate();
    Optional<Holidays> holidayByHolidayDate = holidaysRepository.findHolidayByHolidayDate(holidayDate);
    if (holidayByHolidayDate.isPresent()) {
      throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
    }

    Holidays holiday = new Holidays();
    holiday.setHolidayDate(holidayDate);
    holiday.setDescription(addHolidayRequest.getDescription());
    final LocalDateTime now = DateUtil.nowEst();
    holiday.setCreatedOn(now);
    holiday.setUpdatedOn(now);

    holidaysRepository.save(holiday);
    boolean result = holidays.add(holidayDate);
    log.info("Added new holiday {}, result is {}", addHolidayRequest, result);
    return result;
  }

  @PerfLogger
  public boolean removeHoliday(DeleteHolidayRequest deleteHolidayRequest) {
    LocalDate holidayDate = deleteHolidayRequest.getHolidayDate();
    Optional<Holidays> holidayByHolidayDate = holidaysRepository.findHolidayByHolidayDate(holidayDate);
    if (holidayByHolidayDate.isEmpty()) {
      return false;
    }
    holidaysRepository.delete(holidayByHolidayDate.get());
    boolean result = holidays.remove(holidayDate);
    log.info("removed holiday {}, result is {}", deleteHolidayRequest, result);
    return result;
  }
}
