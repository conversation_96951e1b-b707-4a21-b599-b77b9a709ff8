package com.peoples.banking.api.transaction.calendar.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Transaction Calendar API filter configuration
 */
@Configuration
public class TransactionCalendarFilterConfig {

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> transactionCalenderAPILoggingFilter() {
    LoggingFilter transactionCalendarAPILoggingFilter = new LoggingFilter();
    transactionCalendarAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    transactionCalendarAPILoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    transactionCalendarAPILoggingFilter.setSystemName(TransactionCalendarConstant.TRANSACTION_CALENDER_API_SYSTEM_NAME);
    registrationBean.setFilter(transactionCalendarAPILoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);

    return registrationBean;
  }


  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
