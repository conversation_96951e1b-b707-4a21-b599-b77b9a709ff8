package com.peoples.banking.api.transaction.calendar.v1.config;

public final class TransactionCalendarConstant {

  private TransactionCalendarConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

  public static final String ROOT_SERVICE_URL = "/v1/transaction-calendar";

  public static final String GET_POSTING_DATE_URL = ROOT_SERVICE_URL;
  public static final String CALENDAR_HOLIDAYS_URL = ROOT_SERVICE_URL + "/holidays";

  public static final String TRANSACTION_CALENDER_API_SYSTEM_NAME = "TransactionCalenderAPI";

 }
