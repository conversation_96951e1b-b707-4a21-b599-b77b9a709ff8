package com.peoples.banking.api.transaction.calendar.v1.service;

import com.peoples.banking.api.transaction.calendar.v1.config.TransactionCalendarValidationMessage;
import com.peoples.banking.domain.transactioncalendar.model.PaymentTransactionType;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarRequest;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarResponse;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.service.APIService;
import com.peoples.banking.util.logger.annotation.PerfLogger;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import static java.time.temporal.TemporalAdjusters.lastDayOfYear;

@Log4j2
@Service
public class TransactionCalendarService extends APIService {

  public static final int LATE_HOUR = 12;
  public static final int WIRES_HOUR = 13;
  public static final int EARLY_HOUR = 1;

  public static final int POSTING_MINUTES = 30;

  private final ZoneId easternTimeZone = ZoneId.of("America/Toronto", ZoneId.SHORT_IDS);
  private final ZoneId UTC_ZONE = ZoneId.of("UTC");
  private static final LocalDate MIN_DATE = LocalDate.of(2021, 12, 1);
  private static final LocalDate MAX_DATE = LocalDate.of(2022, 12, 31);

  @Autowired
  private HolidayService holidayService;

  @PerfLogger
  public TransactionCalendarResponse calculatePostingDate(TransactionCalendarRequest transactionCalendarRequest) {
    LocalDate transactionDate = transactionCalendarRequest.getTransactionDate();
    PaymentTransactionType transactionType = transactionCalendarRequest.getTransactionType();

    if (transactionDate == null) {
      return null;
    }
    LocalDate maxDate = findMaxDate();
    //check if date is in range
    if (transactionDate.isBefore(MIN_DATE) || transactionDate.isAfter(maxDate.with(lastDayOfYear()))) {
      throw new ValidationException(ErrorProperty.INVALID_INPUT.name(), TransactionCalendarValidationMessage.JSON_PATH_TRANSACTION_DATE);
    }

    TransactionCalendarResponse result = new TransactionCalendarResponse();
    result.setFiDueDate(DateUtil.toOffsetGmtDateTime(calculateWiresDate(transactionDate)));
    result.setSaDueDate(DateUtil.toOffsetGmtDateTime(calculateEtransferDate(transactionDate, transactionType)));

    return result;
  }

  private LocalDateTime calculateEtransferDate(LocalDate transactionDate, PaymentTransactionType transactionType) {
    switch (transactionType) {
      case DEBIT:
        return calculateDebitPostingDate(transactionDate);
      case CREDIT:
        return calculateCreditPostingDate(transactionDate);
      default:
        throw new ValidationException(ErrorProperty.INVALID_INPUT.name());
    }
  }

  private LocalDateTime calculateWiresDate(LocalDate transactionDate) {
    if (isBusinessDay(transactionDate)) {
      return findWiresNextBusinessDate(nextDay(LocalDateTime.of(transactionDate, LocalTime.MIN)));
    }
    //Every transaction settled on a non-business day (Saturday, Sunday & provincial holiday) should be settled on the next + 1 business day.
    //return findWiresNextBusinessDate(nextDay(nextDay(LocalDateTime.of(transactionDate, LocalTime.MIN))));
    return findWiresNextBusinessDate(nextDay(findWiresNextBusinessDate(nextDay(LocalDateTime.of(transactionDate, LocalTime.MIN)))));
  }

  private boolean isBusinessDay(LocalDate transactionDate) {
    return !isDateHoliday(transactionDate) && !isDateWeekend(transactionDate);
  }

  private LocalDate findMaxDate() {
    return holidayService.findHolidays().stream().max(LocalDate::compareTo).stream().findFirst().orElse(MAX_DATE);
  }

  private LocalDateTime calculateDebitPostingDate(LocalDate transactionDate) {
    LocalDateTime nextDay = nextDay(LocalDateTime.of(transactionDate, LocalTime.MIN));
    LocalDateTime result = findNextBusinessDate(nextDay);
    if (nextDay.getDayOfMonth() != result.getDayOfMonth()) {
      result = result.withHour(EARLY_HOUR).atZone(easternTimeZone).withZoneSameInstant(UTC_ZONE).toLocalDateTime();
    }
    return result;
  }

  private LocalDateTime calculateCreditPostingDate(LocalDate transactionDate) {
    LocalDateTime result = findNextBusinessDate(nextDay(LocalDateTime.of(transactionDate, LocalTime.MIN)));
    if (isDateWeekend(transactionDate) || isDateHoliday(transactionDate)) {
      result = findNextBusinessDate(nextDay(result));
    }
    return result;
  }

  boolean isDateHoliday(LocalDate date) {
    return holidayService.findHolidays().contains((date));
  }

  boolean isDateWeekend(LocalDate dateTime) {
    DayOfWeek dayOfWeek = dateTime.getDayOfWeek();
    return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;
  }

  public LocalDateTime findNextBusinessDate(LocalDateTime localDateTime) {
    if (isDateHoliday(localDateTime.toLocalDate()) || isDateWeekend(localDateTime.toLocalDate())) {
      return findNextBusinessDate(nextDay(localDateTime));
    }
    return localDateTime.withHour(LATE_HOUR).withMinute(POSTING_MINUTES).atZone(easternTimeZone).withZoneSameInstant(UTC_ZONE)
        .toLocalDateTime();
  }

  public LocalDateTime findWiresNextBusinessDate(LocalDateTime localDateTime) {
    if (isDateHoliday(localDateTime.toLocalDate()) || isDateWeekend(localDateTime.toLocalDate())) {
      return findWiresNextBusinessDate(nextDay(localDateTime));
    }
    return localDateTime.withHour(WIRES_HOUR).withMinute(0).atZone(easternTimeZone).withZoneSameInstant(UTC_ZONE).toLocalDateTime();
  }

  public LocalDateTime nextDay(LocalDateTime localDateTime) {
    return localDateTime.plusDays(1);
  }

  /**
   * implement the customer validation error and assign corresponding error code
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    ErrorEntity error = new ErrorEntity();

    if (annotation instanceof NotNull) {
      error.setErrorCode(ErrorProperty.MISSING_FIELD.name());

    } else if (annotation instanceof NotBlank) {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());

    } else {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());
    }
    return error;
  }

}
