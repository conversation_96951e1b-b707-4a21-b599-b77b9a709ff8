package com.peoples.banking.api.transaction.calendar.v1.controller;

import com.peoples.banking.api.transaction.calendar.v1.config.TransactionCalendarConstant;
import com.peoples.banking.api.transaction.calendar.v1.config.TransactionCalendarProperty;
import com.peoples.banking.api.transaction.calendar.v1.service.HolidayService;
import com.peoples.banking.api.transaction.calendar.v1.service.TransactionCalendarService;
import com.peoples.banking.domain.transactioncalendar.model.AddHolidayRequest;
import com.peoples.banking.domain.transactioncalendar.model.DeleteHolidayRequest;
import com.peoples.banking.domain.transactioncalendar.model.HolidaysListResponse;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarRequest;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarResponse;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.controller.InternalAPIController;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * The Rest controller for Transaction Calendar API.
 */
@RestController
public class TransactionCalendarController extends InternalAPIController {

  @Autowired
  private TransactionCalendarService service;

  @Autowired
  private HolidayService holidayService;

  @Autowired
  private TransactionCalendarProperty properties;

  /**
   * Calculate transaction calendar posting date operation.
   *
   * @param transactionCalendarRequest        the transaction calendar request
   * @return PostingDateResponse Response with posting date
   */
  @PerfLogger
  @PostMapping(value = TransactionCalendarConstant.GET_POSTING_DATE_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<TransactionCalendarResponse> calculatePostingDate(
      @Valid @RequestBody TransactionCalendarRequest transactionCalendarRequest)  {

    // call service for business check and process
    TransactionCalendarResponse response = service.calculatePostingDate(transactionCalendarRequest);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders("interactionId",false);
    // return response
    return new ResponseEntity<>(response, headers, HttpStatus.OK);
  }


  /**
   * Fetch current holidays
   *
   * @return HolidayListResponse with all current holidays
   */
  @PerfLogger
  @GetMapping(value = TransactionCalendarConstant.CALENDAR_HOLIDAYS_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<HolidaysListResponse> fetchHolidays(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime) {

    // call service for business check and process
    HolidaysListResponse result = holidayService.fetchHolidays();

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders("interactionId",false);

    // return response
    return new ResponseEntity<>(result, headers, HttpStatus.OK);
  }

  /**
   * Add holiday to list
   *
   * @return 204 NO_CONTENT if holiday add was successful
   */
  @PerfLogger
  @PutMapping(value = TransactionCalendarConstant.CALENDAR_HOLIDAYS_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> addHoliday(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @Valid @RequestBody AddHolidayRequest request) {

    // call service for business check and process
    boolean result = holidayService.addHoliday(request);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders("interactionId",false);

    // return response
    return new ResponseEntity<>(result, headers, HttpStatus.NO_CONTENT);
  }

  /**
   * delete holiday
   *
   * @return 204 NO_CONTENT if remove was successful
   */
  @PerfLogger
  @DeleteMapping(value = TransactionCalendarConstant.CALENDAR_HOLIDAYS_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> removeHoliday(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @Valid @RequestBody DeleteHolidayRequest request) {

    // call service for business check and process
    boolean result = holidayService.removeHoliday(request);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders("interactionId",false);

    // return response
    return new ResponseEntity<>(result, headers, HttpStatus.NO_CONTENT);
  }

  /**
   * return Transaction calendar API's time to live value
   */
  @Override
  protected int getApiTimeToLiveValue() {
    return properties.getTimeToLive();
  }
}
