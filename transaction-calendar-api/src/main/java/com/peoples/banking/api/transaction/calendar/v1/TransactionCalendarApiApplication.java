package com.peoples.banking.api.transaction.calendar.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.peoples.banking.api.transaction",
    "com.peoples.banking.util",
    "com.peoples.banking.persistence.calendar",
})
/**
 * Spring Boot main application.
 */
public class TransactionCalendarApiApplication {

  public static void main(String[] args) {
    SpringApplication.run(TransactionCalendarApiApplication.class, args);
  }
}
