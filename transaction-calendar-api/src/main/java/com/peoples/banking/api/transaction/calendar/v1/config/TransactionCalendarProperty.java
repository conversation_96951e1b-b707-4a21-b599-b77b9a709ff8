package com.peoples.banking.api.transaction.calendar.v1.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class TransactionCalendarProperty {
  
  /**
   * Transaction Calendar API System Name.
   */
  @Value("${transaction.calendar.api.system.name:TransactionCalendarAPI}")
  private String systemName;

  /**
   * Transaction Calendar API time to live value.
   */
  @Value("${transaction.calendar.api.timetolive:30000}")
  private int timeToLive;
  
}
