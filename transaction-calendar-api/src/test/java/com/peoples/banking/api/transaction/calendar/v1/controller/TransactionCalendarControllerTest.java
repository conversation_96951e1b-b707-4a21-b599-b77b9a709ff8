package com.peoples.banking.api.transaction.calendar.v1.controller;

import static com.peoples.banking.api.transaction.calendar.v1.service.TransactionCalendarService.POSTING_MINUTES;
import static com.peoples.banking.api.transaction.calendar.v1.service.TransactionCalendarServiceTest.EVENING_GMT_SUMMER_HOUR;
import static com.peoples.banking.api.transaction.calendar.v1.service.TransactionCalendarServiceTest.EVENING_GMT_WINTER_HOUR;
import static com.peoples.banking.api.transaction.calendar.v1.service.TransactionCalendarServiceTest.WIRES_EVENING_GMT_SUMMER_HOUR;
import static com.peoples.banking.api.transaction.calendar.v1.util.TransactionCalendarTestUtil.buildHolidaysList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.transaction.calendar.v1.config.TransactionCalendarConstant;
import com.peoples.banking.api.transaction.calendar.v1.service.HolidayService;
import com.peoples.banking.api.transaction.calendar.v1.util.TransactionCalendarTestUtil;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.transactioncalendar.model.ErrorResponse;
import com.peoples.banking.domain.transactioncalendar.model.PaymentTransactionType;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarRequest;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarResponse;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TransactionCalendarControllerTest {

  @LocalServerPort
  private int port;

  @Autowired
  private TestRestTemplate template;

  private HttpHeaders headers;

  private String finalUrl;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private HolidayService holidayService;

  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() throws Exception {
    URL base = new URL("http://localhost:" + port);
    headers = getHttpHeader();
    serviceAccountResponse = TransactionCalendarTestUtil
        .createServiceAccountResponse(TransactionCalendarTestUtil.SERVICE_ACCOUNT_API_TOKEN,
            TransactionCalendarTestUtil.SERVICE_ACCOUNT_REF_ID);
    finalUrl = base + TransactionCalendarConstant.ROOT_SERVICE_URL;
    lenient().doReturn(buildHolidaysList()).when(holidayService).findHolidays();

  }

  @AfterEach
  public void tearDown() {
    template = null;
    headers = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, TransactionCalendarTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "123456");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }


  @ParameterizedTest
  @ValueSource(strings = {
      "2021-12-27",
      "2021-12-28",
      "2021-12-29"
  })
  public void getCreditPostingDate_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(OffsetDateTime.of(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, POSTING_MINUTES), ZoneOffset.UTC),
        responseEntity.getBody().getSaDueDate());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2022-02-19",
      "2022-02-20",
      "2022-02-21"
  })
  public void getCreditPostingDateFor2022_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, POSTING_MINUTES).atOffset(ZoneOffset.UTC),
        responseEntity.getBody().getSaDueDate());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2021-12-27",
      "2021-12-28",
      "2021-12-29"
  })
  public void getDebitPostingDate_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, POSTING_MINUTES).atOffset(ZoneOffset.UTC),
        responseEntity.getBody().getSaDueDate());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2022-02-20",
      "2022-02-21",
      "2022-02-22"
  })
  public void getDebitPostingDateFor2022_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, POSTING_MINUTES).atOffset(ZoneOffset.UTC),
        responseEntity.getBody().getSaDueDate());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2022-05-22",
      "2022-05-23",
      "2022-05-24"
  })
  public void getDebitPostingDateFor2022_summer_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(LocalDateTime.of(2022, 5, 25, EVENING_GMT_SUMMER_HOUR, POSTING_MINUTES).atOffset(ZoneOffset.UTC),
        responseEntity.getBody().getSaDueDate());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2021-11-30",
      "2099-12-01",
      "2099-12-26"
  })
  public void getDebitPostingDateFor_outside_of_calendar(String dateTime) {
    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<ErrorResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        ErrorResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertNotNull(responseEntity.getBody());
    assertNotNull(ErrorProperty.INVALID_INPUT.name(), responseEntity.getBody().getError().get(0).getCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "2022-05-22",
      "2022-05-23",
      "2022-05-24"
  })
  public void getWiresDateFor2022_summer_success(String dateTime) throws Exception {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    TransactionCalendarRequest request = new TransactionCalendarRequest();
    request.setTransactionDate(LocalDate.parse(dateTime));
    request.setTransactionType(PaymentTransactionType.CREDIT);
    ResponseEntity<TransactionCalendarResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        new HttpEntity<>(request, headers),
        TransactionCalendarResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.OK);
    assertNotNull(responseEntity.getBody());
    assertEquals(LocalDateTime.of(2022, 5, 25, WIRES_EVENING_GMT_SUMMER_HOUR, 0).atOffset(ZoneOffset.UTC),
        responseEntity.getBody().getFiDueDate());
  }

}
