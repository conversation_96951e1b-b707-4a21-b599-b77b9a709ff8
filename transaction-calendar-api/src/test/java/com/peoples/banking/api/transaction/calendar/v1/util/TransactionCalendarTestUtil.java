package com.peoples.banking.api.transaction.calendar.v1.util;


import com.google.common.collect.Sets;
import com.peoples.banking.adapter.base.type.ServiceAccountEndpointType;
import com.peoples.banking.domain.serviceaccount.model.ApiEndpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.transactioncalendar.model.PaymentTransactionType;
import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarRequest;
import com.peoples.banking.util.api.common.DateUtil;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * The class to provide some basic TransactionCalendarApi entity
 */
@UtilityClass
public class TransactionCalendarTestUtil {

  public static final String SERVICE_ACCOUNT_API_TOKEN = "AxYQ1GGl0";
  public static final String SERVICE_ACCOUNT_REF_ID = "ABC123";

  public static ServiceAccountResponse createServiceAccountResponse(String serviceAccountApiToken, String serviceAccountRefId) {
    ServiceAccountResponse serviceAccountResponse = new ServiceAccountResponse();
    serviceAccountResponse.setRefId(serviceAccountRefId);
    serviceAccountResponse.setInboundApiToken(serviceAccountApiToken);
    serviceAccountResponse.setOutboundApiToken("80a2b00743214abb8e7d10710814e005");
    serviceAccountResponse.setName("Test ServiceAccount");
    serviceAccountResponse.setLimitGroupId("PRODVAL");

    Map<String, ApiEndpoint> apiEndpointMap = new HashMap<>();
    ApiEndpoint elgty = new ApiEndpoint();
    elgty.setUrl("http://localhost:8080/account/{account_num}/eligibility");
    apiEndpointMap.put(ServiceAccountEndpointType.ELIGIBILITY.toString(), elgty);

    ApiEndpoint trns = new ApiEndpoint();
    trns.setUrl("http://localhost:8080/account/{account_num}/transaction");
    apiEndpointMap.put(ServiceAccountEndpointType.TRANSACTION.toString(), trns);

    ApiEndpoint rvs = new ApiEndpoint();
    rvs.setUrl("http://localhost:8080/account/{account_num}/{transaction_id}/reversal");
    apiEndpointMap.put(ServiceAccountEndpointType.REVERSAL.toString(), rvs);

    serviceAccountResponse.setApiEndpoints(apiEndpointMap);
    serviceAccountResponse.setCreatedDate(DateUtil.getCurrentUTCDateTime());
    serviceAccountResponse.setUpdatedDate(DateUtil.getCurrentUTCDateTime());
    return serviceAccountResponse;
  }

  public static TransactionCalendarRequest createTransactionCalendarRequest(LocalDate transactionDate, PaymentTransactionType transactionType) {
    TransactionCalendarRequest result = new TransactionCalendarRequest();
    result.setTransactionDate(transactionDate);
    result.setTransactionType(transactionType);
    return result;
  }

  public static HashSet<LocalDate> buildHolidaysList() {
    return Sets.newHashSet(
        LocalDate.of(2021, 12, 27), LocalDate.of(2021, 12, 28), LocalDate.of(2022,1, 3),
        LocalDate.of(2022, 2, 21), LocalDate.of(2022, 4, 15), LocalDate.of(2022, 5, 23), LocalDate.of( 2022, 7, 1),
        LocalDate.of(2022, 8, 1), LocalDate.of(2022, 9, 5), LocalDate.of(2022, 9, 30),
        LocalDate.of(2022, 10, 10), LocalDate.of(2022, 11, 11), LocalDate.of(2022, 12, 26),
        LocalDate.of(2022, 12, 27),
        LocalDate.of(2023, 11, 16));
  }
}
