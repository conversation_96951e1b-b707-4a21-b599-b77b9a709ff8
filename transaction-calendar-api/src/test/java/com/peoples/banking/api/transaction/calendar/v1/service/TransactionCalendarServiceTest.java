package com.peoples.banking.api.transaction.calendar.v1.service;

import static com.peoples.banking.api.transaction.calendar.v1.util.TransactionCalendarTestUtil.buildHolidaysList;
import static com.peoples.banking.api.transaction.calendar.v1.util.TransactionCalendarTestUtil.createTransactionCalendarRequest;
import static com.peoples.banking.domain.transactioncalendar.model.PaymentTransactionType.CREDIT;
import static com.peoples.banking.domain.transactioncalendar.model.PaymentTransactionType.DEBIT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.lenient;

import com.peoples.banking.domain.transactioncalendar.model.TransactionCalendarRequest;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public
class TransactionCalendarServiceTest {

  public static final int EVENING_GMT_WINTER_HOUR = 17;
  public static final int EVENING_GMT_SUMMER_HOUR = 16;
  public static final int WIRES_EVENING_GMT_SUMMER_HOUR = 17;
  public static final int WIRES_EVENING_GMT_WINTER_HOUR = 18;

  public static final int MORNING_GMT_WINTER_HOUR = 6;
  public static final int MORNING_GMT_SUMMER_HOUR = 5;

  @Mock
  private HolidayService holidayService;

  @InjectMocks
  private TransactionCalendarService transactionCalendarService;

  @BeforeEach
  public void init () {
    Set<LocalDate> holidays = buildHolidaysList();
    lenient().doReturn(holidays).when(holidayService).findHolidays();
  }

  @Test
  void calculatePostingDate_is_null() {
    TransactionCalendarRequest request = createTransactionCalendarRequest(null, CREDIT);
    assertNull(transactionCalendarService.calculatePostingDate(request));
  }

  @Test
  void calculatePostingDate_is_too_early() {
    LocalDate transactionDate = LocalDate.of(2021, 11, 30);
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertThrows(ValidationException.class, () -> transactionCalendarService.calculatePostingDate(request));
  }


  @Test
  void calculateCreditPostingDateMonday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 6);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 7, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateTuesday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 7);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 8, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateWednesday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 8);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 9, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateThursday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 9);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 10, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateFriday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 10);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 13, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateSaturday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 11);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 14, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateSunday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 12);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 14, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateMonday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 6);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 7, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateTuesday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 7);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 8, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateWednesday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 8);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 9, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateThursday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 9);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 10, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateFriday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 10);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 13, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateSaturday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 11);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 13, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateSunday_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 12);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 13, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDate0102_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 2);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 4, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDate1802_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 18);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 22, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDate1902_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 19);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 22, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDate2002_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 20);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 22, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDate2102_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 21);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }


  //Starting debit holidays checks
  @Test
  void calculateDebitPostingDateThursday23_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 23);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 24, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateFriday24_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 24);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateSaturday25_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 25);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateSunday26_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 26);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateMonday27_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 27);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, MORNING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateTuesday28_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 28);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateWednesday29_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 29);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateThursday30_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 30);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 31, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  //Starting credit holidays checks
  @Test
  void calculateCreditPostingDateThursday23_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 23);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 24, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateFriday24_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 24);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 29, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateSaturday25_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 25);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateSunday26_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 26);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateMonday27_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 27);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateTuesday28_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 28);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateWednesday29_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 29);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 30, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateThursday30_success() {
    LocalDate transactionDate = LocalDate.of(2021, 12, 30);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2021, 12, 31, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  //start checks for new 2022 year
  @Test
  void calculateCreditPostingDateSaturday0101_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 1);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 5, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateSunday0201_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 2);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 5, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateMonday0301_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 3);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 5, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateTuesday0401_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 4);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 5, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDateWednesday0501_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 5);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 6, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1802_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 18);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 22, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1902_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 19);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate2002_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 20);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate2102_success() {
    LocalDate transactionDate = LocalDate.of(2022, 2, 21);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 2, 23, EVENING_GMT_WINTER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1404_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 14);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 18, EVENING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1504_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 15);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 19, EVENING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1604_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 16);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 19, EVENING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1704_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 17);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 19, EVENING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateCreditPostingDate1804_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 18);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 19, EVENING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, CREDIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateDebitPostingDateMonday23_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 21);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 24, MORNING_GMT_SUMMER_HOUR, 30));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getSaDueDate());
  }

  @Test
  void calculateWiresPostingDateMondayJan10_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 10);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 11, WIRES_EVENING_GMT_WINTER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateMonday16_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 16);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 17, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateTuesday17_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 17);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 18, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateWednesday18_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 18);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 19, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateThursday19_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 19);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 20, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateFriday20_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 20);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 24, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateSaturday21_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 21);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 25, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateSunday22_success() {
    LocalDate transactionDate = LocalDate.of(2022, 5, 22);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 5, 25, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateHolidayMonday0103_success() {
    LocalDate transactionDate = LocalDate.of(2022, 1, 3);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 1, 5, WIRES_EVENING_GMT_WINTER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateHolidayFriday0415_success() {
    LocalDate transactionDate = LocalDate.of(2022, 4, 15);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2022, 4, 19, WIRES_EVENING_GMT_SUMMER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

  @Test
  void calculateWiresPostingDateHolidayFriday1116_success() {
    LocalDate transactionDate = LocalDate.of(2023, 11, 16);
    OffsetDateTime postingDate = DateUtil.toOffsetDateTime(LocalDateTime.of(2023, 11, 20, WIRES_EVENING_GMT_WINTER_HOUR, 0));
    TransactionCalendarRequest request = createTransactionCalendarRequest(transactionDate, DEBIT);
    assertEquals(postingDate, transactionCalendarService.calculatePostingDate(request).getFiDueDate());
  }

}