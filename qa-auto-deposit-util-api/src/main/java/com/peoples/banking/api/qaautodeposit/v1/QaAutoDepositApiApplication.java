package com.peoples.banking.api.qaautodeposit.v1;

import com.peoples.banking.persistence.customer.config.DataConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@ComponentScan(basePackages = {
		"com.peoples.banking.api.qaautodeposit",
		"com.peoples.banking.util",
		"com.peoples.banking.api.query.v1.config",
		"com.peoples.banking.persistence.customer",
		"com.peoples.banking.persistence.payment"
}, excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = { DataConfig.class })})
@EnableJpaRepositories(entityManagerFactoryRef = "autoDepositEntityManagerFactory", basePackages = {"com.peoples.banking.persistence.customer"})
public class QaAutoDepositApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(QaAutoDepositApiApplication.class, args);
	}

}
