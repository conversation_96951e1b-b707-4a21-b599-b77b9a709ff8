package com.peoples.banking.api.qaautodeposit.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class QaAutoDepositProperty {

  /**
   * QA::Customer API System Name.
   */
  @Value("${qa.auto.deposit.api.system.name}")
  private String systemName;

  /**
   * QA::Customer API time to live value.
   */
  @Value("${qa.auto.deposit.api.timetolive:30000}")
  private int timeToLive;

  /**
   * QA::Customer expiry days
   */
  @Value("${qa.auto.deposit.api.request.expiry.days:45}")
  private int expiryDays;


}
