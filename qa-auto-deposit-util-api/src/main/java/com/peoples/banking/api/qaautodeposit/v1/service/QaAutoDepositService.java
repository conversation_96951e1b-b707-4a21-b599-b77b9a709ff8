package com.peoples.banking.api.qaautodeposit.v1.service;


import static com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter.SIGNATURE_TYPE;

import com.peoples.banking.api.qaautodeposit.v1.model.PaymentType;
import com.peoples.banking.api.qaautodeposit.v1.model.TriggerAutoDepositRequest;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountIdentification4Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyAndAmount;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyCode;
import com.peoples.banking.partner.domain.interac.deposit.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.deposit.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.deposit.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3ChoiceOneOf;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemMemberIdentification2;
import com.peoples.banking.partner.domain.interac.deposit.model.Contact4;
import com.peoples.banking.partner.domain.interac.deposit.model.CountryCode;
import com.peoples.banking.partner.domain.interac.deposit.model.CreditTransferTransaction39;
import com.peoples.banking.partner.domain.interac.deposit.model.CustomerType;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalPaymentTransactionStatus1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.FIToFICustomerCreditTransferV08;
import com.peoples.banking.partner.domain.interac.deposit.model.FIToFIPaymentStatusReportV10;
import com.peoples.banking.partner.domain.interac.deposit.model.FinancialInstitutionIdentification18;
import com.peoples.banking.partner.domain.interac.deposit.model.FraudCheckResult;
import com.peoples.banking.partner.domain.interac.deposit.model.FraudCheckResult.ActionEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericAccountIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericFinancialIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericOrganisationIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader91;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader93;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2ChoiceOneOf.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.OrganisationIdentification29;
import com.peoples.banking.partner.domain.interac.deposit.model.OriginalGroupInformation29;
import com.peoples.banking.partner.domain.interac.deposit.model.OriginalTransactionReference28;
import com.peoples.banking.partner.domain.interac.deposit.model.Party38Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.Party40Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.PartyIdentification135;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentIdentification7;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTypeInformation28;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementInstruction7;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementMethod1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.TransactionAuthorization;
import com.peoples.banking.partner.domain.interac.deposit.model.TransactionAuthorization.AuthorizationTypeEnum;
import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Enrollments;
import com.peoples.banking.persistence.customer.entity.Products;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.customer.repository.EnrollmentsRepository;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.service.APIService;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import io.micrometer.core.instrument.util.StringUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * The qa customer service class for retrieve payment from DB for QA quick and easy test
 */
@Service
@Log4j2
public class QaAutoDepositService extends APIService {

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private CustomersRepository customerRepository;

  @Autowired
  private EnrollmentsRepository enrollmentsRepository;

  @Autowired
  private AliasesRepository aliasesRepository;

  private final RestTemplate restTemplate = new RestTemplate();

  @Value("${interac.deposit.api.url}")
  private String interacDepositPaymentApiUrl;

  public static final String FI_ID_PTC = "CA000621";
  public static final String FI_ID_BMO = "CA000001";
  public static final String BICFI = "XYZGBXXX";
  public static final String DEP_PAYMENT_EXTERNAL_REF_ID = "YZfiyo05lmZJWvAW";

  /**
   * Retrieve all registered customer aliases, including our alias ID as well as the network alias ID
   *
   * @param triggerAutoDepositRequest trigger auto deposit request
   * @return
   */
  @SneakyThrows
  @PerfLogger
  public void triggerAutoDeposit(TriggerAutoDepositRequest triggerAutoDepositRequest) {
    Optional<Customers> receiverCustomer = customerRepository.findByExternalRefId(triggerAutoDepositRequest.getReceiverCustomerId());
    if (receiverCustomer.isEmpty()) {
      log.warn("Failed to found receiver customer by id {}", triggerAutoDepositRequest.getReceiverCustomerId());
      throw new ValidationException(ErrorProperty.INVALID_CUSTOMER.name());
    }

    Customers customers = receiverCustomer.get();

    Optional<Aliases> optionalAliases = aliasesRepository.findByCustomerIdAndExternalRefId(customers.getId(),
        triggerAutoDepositRequest.getReceiverAliasRefId());

    if (optionalAliases.isEmpty()) {
      log.warn("Failed to found alias by external ref id {} and  customerId  {}", triggerAutoDepositRequest.getReceiverAliasRefId(),
          customers.getId());
      throw new ValidationException(ErrorProperty.INVALID_INPUT.name());
    }
    String clearingSystemReference = IdGeneratorUtil.secureRandomString(8);
    String paymentRefId = triggerAutoDepositRequest.getPaymentRefId();
    if (StringUtils.isNotEmpty(paymentRefId)) {
      Optional<Payments> optionalPayments = paymentsRepository.findByExternalRefId(paymentRefId);
      if (optionalPayments.isPresent()) {
        clearingSystemReference = optionalPayments.get().getNetworkPaymentRefId();;
      }
    }
    String depositId = clearingSystemReference;

    String networkAliasRefId = optionalAliases.get().getNetworkAliasRefId();
    String enrolmentId = findNetworkEnrollmentId(customers);
    String displayName = getDisplayName(customers);
    String messageIdentification = UUID.randomUUID().toString().substring(0, 15);
    DepositPaymentRequest depositPaymentRequest = buildDepositPaymentRequest(triggerAutoDepositRequest, enrolmentId, networkAliasRefId,
        displayName, messageIdentification, depositId, clearingSystemReference);

    String req = JsonUtil.toString(depositPaymentRequest);
    HttpHeaders headers = buildRequestHeaders(enrolmentId);

    HttpEntity<Object> request = new HttpEntity<>(req, headers);
    ResponseEntity<DepositPaymentResponse> depositPaymentResponseEntity = restTemplate.postForEntity(buildApiUrl(depositId),
        request, DepositPaymentResponse.class);
    if (depositPaymentResponseEntity.getStatusCode() != HttpStatus.CREATED) {
      log.warn("Failed to send depositPaymentResponse");
      throw new ApplicationException("Failed to send depositPaymentResponse", new IllegalArgumentException("bad response from interac"), ErrorProperty.UNEXPECTED_ERROR.name());
    }
    DepositPaymentResponse depositPaymentResponse = depositPaymentResponseEntity.getBody();
    log.info("Got response from interac {}", depositPaymentResponse);

    String accountServicerReference = depositPaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getAccountServicerReference();
    SubmitDepositRequest submitDepositRequest = createSubmitDepositRequest(enrolmentId, messageIdentification, accountServicerReference,
        triggerAutoDepositRequest.getReceiverAccountNumber(), depositId);
    HttpEntity<Object> submitDepositHttpRequest = new HttpEntity<>(submitDepositRequest, headers);
    ResponseEntity<SubmitDepositResponse> submitDepositResponseResponseEntity = restTemplate.exchange(buildApiUrl(depositId), HttpMethod.PUT,
        submitDepositHttpRequest, SubmitDepositResponse.class);
    if (submitDepositResponseResponseEntity.getStatusCode() != HttpStatus.OK) {
      log.warn("Failed to send SubmitDepositResponse");
      throw new ApplicationException("Failed to send SubmitDepositResponse", new IllegalArgumentException("bad response from interac"), ErrorProperty.UNEXPECTED_ERROR.name());
    }

  }

  private String buildApiUrl(String depositId) {
    return interacDepositPaymentApiUrl.replace("{deposit_id}", depositId);
  }

  private String getDisplayName(Customers customers) {
    String result;
    Map<String, Object> map;
    try {
      map = JsonUtil.toObject(customers.getNameJson(), Map.class);
      result = (String) Optional.ofNullable(map.get("displayName")).orElse(null);
    } catch (Exception e) {
      return null;
    }
    return result;
  }

  private DepositPaymentRequest buildDepositPaymentRequest(TriggerAutoDepositRequest triggerAutoDepositRequest, String networkEnrollmentId,
      String networkAliasRefId, String displayName, String messageIdentification, String depositId, String clearingSystemReference) {
    DepositPaymentRequest depositPaymentRequest = new DepositPaymentRequest();
    // group header
    GroupHeader93 groupHeader = new GroupHeader93();
    groupHeader.setMessageIdentification(messageIdentification);
    groupHeader.setCreationDatetime(OffsetDateTime.now(ZoneOffset.UTC));
    BranchAndFinancialInstitutionIdentification6 instructedAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 clearingSystemMemberIdentification = new ClearingSystemMemberIdentification2();
    clearingSystemMemberIdentification.setMemberIdentification(FI_ID_PTC);
    financialInstitutionIdentification.setClearingSystemMemberIdentification(clearingSystemMemberIdentification);
    financialInstitutionIdentification.setName("NOT_PROVIDED");
    financialInstitutionIdentification.setBicfi(BICFI);
    GenericFinancialIdentification1 other2 = new GenericFinancialIdentification1();
    other2.setIdentification("NOT_PROVIDED");
    financialInstitutionIdentification.setOther(other2);
    instructedAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
    groupHeader.setInstructedAgent(instructedAgent);
    groupHeader.setNumberOfTransactions("1");
    SettlementInstruction7 settlementInformation = new SettlementInstruction7();
    settlementInformation.setSettlementMethod(SettlementMethod1Code.CLRG);
    ClearingSystemIdentification3Choice clearingSystem = new ClearingSystemIdentification3Choice();
    clearingSystem.setProprietary(ClearingSystemIdentification3ChoiceOneOf.ProprietaryEnum.ETR);
    settlementInformation.setClearingSystem(clearingSystem);
    groupHeader.setSettlementInformation(settlementInformation);
    BranchAndFinancialInstitutionIdentification6 instructingAgent = new BranchAndFinancialInstitutionIdentification6();
    instructingAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
    groupHeader.setInstructingAgent(instructingAgent);

    depositPaymentRequest.setFiAccountId(FI_ID_PTC);

    FraudCheckResult fraudCheckResult = new FraudCheckResult();
    fraudCheckResult.setScore(0);
    fraudCheckResult.setAction(ActionEnum.ALLOW);
    fraudCheckResult.setReason("reason");
    depositPaymentRequest.setFraudCheckResult(fraudCheckResult);

    depositPaymentRequest.setCustomerType(CustomerType.RETAIL);
    TransactionAuthorization transactionAuthorization = new TransactionAuthorization();
    //filling transaction authorization
    transactionAuthorization.setAuthorizationType(getAuthorizationType(triggerAutoDepositRequest.getPaymentType()));
    //filling participant authorization token
    transactionAuthorization.setParticipantAuthorizationToken(triggerAutoDepositRequest.getReceiverAliasRefId());
    //filling interac authorization token
    transactionAuthorization.setInteracAuthorizationToken(networkAliasRefId);

    depositPaymentRequest.setTransactionAuthorization(transactionAuthorization);
    FIToFICustomerCreditTransferV08 fiToFiCustomerCreditTransfer = new FIToFICustomerCreditTransferV08();
    fiToFiCustomerCreditTransfer.setGroupHeader(groupHeader);
    CreditTransferTransaction39 creditTransferTransaction39 = new CreditTransferTransaction39();
    PartyIdentification135 creditor = new PartyIdentification135();
    Party38Choice identification = new Party38Choice();
    OrganisationIdentification29 organisationIdentification = new OrganisationIdentification29();
    GenericOrganisationIdentification1 organisationIdentification1 = new GenericOrganisationIdentification1();
    //Filling identification with networkEnrollmentId
    organisationIdentification1.setIdentification(networkEnrollmentId);

    CashAccount38 creditorAccount = new CashAccount38();
    AccountIdentification4Choice accountIdentification4Choice = new AccountIdentification4Choice();
    GenericAccountIdentification1 other = new GenericAccountIdentification1();
    //filling identification
    other.setIdentification(triggerAutoDepositRequest.getReceiverAccountNumber());
    other.setIssuer("NOT_PROVIDED");
    accountIdentification4Choice.setOther(other);
    creditorAccount.setIdentification(accountIdentification4Choice);
    creditTransferTransaction39.setCreditorAccount(creditorAccount);

    BranchAndFinancialInstitutionIdentification6 debtorAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 financialInstitutionIdentification1 = new FinancialInstitutionIdentification18();
    GenericFinancialIdentification1 otherIdentification = new GenericFinancialIdentification1();
    otherIdentification.setIdentification(FI_ID_PTC);
    financialInstitutionIdentification1.setOther(otherIdentification);
    financialInstitutionIdentification1.setName("********");
    ClearingSystemMemberIdentification2 clearingSystemMemberIdentification1 = new ClearingSystemMemberIdentification2();
    clearingSystemMemberIdentification1.setMemberIdentification("30800");
    financialInstitutionIdentification1.setClearingSystemMemberIdentification(clearingSystemMemberIdentification1);
    financialInstitutionIdentification1.setBicfi(BICFI);
    debtorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification1);
    creditTransferTransaction39.setDebtorAgent(debtorAgent);

    PaymentIdentification7 paymentIdentification = new PaymentIdentification7();
    paymentIdentification.setClearingSystemReference(clearingSystemReference);
    paymentIdentification.setTransactionIdentification(depositId);
    paymentIdentification.setEndToEndIdentification(depositId);
    paymentIdentification.setInstructionIdentification(depositId);
    creditTransferTransaction39.setPaymentIdentification(paymentIdentification);
    creditTransferTransaction39.setInterbankSettlementDate(LocalDate.now());

    ActiveCurrencyAndAmount interbankSettlementAmount = new ActiveCurrencyAndAmount();
    //Filling interbank settlement amount with payment amount
    interbankSettlementAmount.setAmount(triggerAutoDepositRequest.getPaymentAmount());
    interbankSettlementAmount.setCurrency(ActiveCurrencyCode.CAD);
    creditTransferTransaction39.setInterbankSettlementAmount(interbankSettlementAmount);
    BranchAndFinancialInstitutionIdentification6 previousInstructingAgent1 = new BranchAndFinancialInstitutionIdentification6();

    previousInstructingAgent1.setFinancialInstitutionIdentification(financialInstitutionIdentification1);
    creditTransferTransaction39.setPreviousInstructingAgent1(previousInstructingAgent1);

    organisationIdentification.setOther(List.of(organisationIdentification1));
    identification.setOrganisationIdentification(organisationIdentification);
    creditor.setIdentification(identification);
    //set creditor name
    creditor.setName(displayName);
    creditor.setCountryOfResidence(CountryCode.CA);
    Contact4 contactDetails = new Contact4();
    //filling contact email address
    contactDetails.setEmailAddress(triggerAutoDepositRequest.getReceiverEmail());
    contactDetails.setPhoneNumber("******-219-0343");
    contactDetails.setFaxNumber("******-219-0343");
    contactDetails.setMobileNumber("******-219-0343");
    contactDetails.setName("John");
    creditor.setContactDetails(contactDetails);

    BranchAndFinancialInstitutionIdentification6 creditorAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 financialInstitutionIdentification2 = new FinancialInstitutionIdentification18();
    financialInstitutionIdentification2.setName("NOT_PROVIDED");
    GenericFinancialIdentification1 other1 = new GenericFinancialIdentification1();
    other1.setIdentification("NOT_PROVIDED");
    financialInstitutionIdentification2.setOther(other1);
    ClearingSystemMemberIdentification2 clearingSystemMemberIdentification2 = new ClearingSystemMemberIdentification2();
    clearingSystemMemberIdentification2.setMemberIdentification(FI_ID_PTC);
    financialInstitutionIdentification2.setClearingSystemMemberIdentification(clearingSystemMemberIdentification2);
    creditorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification2);
    creditTransferTransaction39.setCreditorAgent(creditorAgent);


    creditTransferTransaction39.setCreditor(creditor);
    creditTransferTransaction39.setChargeBearer(ChargeBearerType1Code.SLEV);
    PartyIdentification135 debtor = new PartyIdentification135();
    //filling debtor name
    debtor.setName(triggerAutoDepositRequest.getSenderName());
    debtor.setCountryOfResidence(CountryCode.CA);
    Contact4 debtorContactDetails = new Contact4();
    debtorContactDetails.setName("John Wick");
    debtorContactDetails.setFaxNumber("******-219-0343");
    debtorContactDetails.setPhoneNumber("******-219-0343");
    debtorContactDetails.setMobileNumber("******-219-0343");
    //filling debtor email
    debtorContactDetails.setEmailAddress(triggerAutoDepositRequest.getSenderEmail());
    debtor.setContactDetails(debtorContactDetails);

    creditTransferTransaction39.setDebtor(debtor);
    PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28();
    LocalInstrument2Choice localInstrument = new LocalInstrument2Choice();

    //Filing proprietary with PaymentType
    localInstrument.setProprietary(getProprietary(triggerAutoDepositRequest.getPaymentType()));

    paymentTypeInformation.setLocalInstrument(localInstrument);

    RemittanceInformation16 remittanceInformation = new RemittanceInformation16();
    remittanceInformation.setUnstructured(List.of(triggerAutoDepositRequest.getMemo()));
    creditTransferTransaction39.setRemittanceInformation(remittanceInformation);

    creditTransferTransaction39.setPaymentTypeInformation(paymentTypeInformation);
    List<CreditTransferTransaction39> creditTransferTransactionInformation = List.of(creditTransferTransaction39);
    fiToFiCustomerCreditTransfer.setCreditTransferTransactionInformation(creditTransferTransactionInformation);
    depositPaymentRequest.setFiToFiCustomerCreditTransfer(fiToFiCustomerCreditTransfer);

    CashAccount38 debtorAccount = new CashAccount38();
    creditTransferTransaction39.setDebtorAccount(debtorAccount);

    GenericAccountIdentification1 otherIdentification1 = new GenericAccountIdentification1();
    otherIdentification1.setIdentification("ACCOUNT_NUMBER");
    otherIdentification1.setIssuer("issuer");
    AccountIdentification4Choice identification1 = new AccountIdentification4Choice();
    identification1.setOther(otherIdentification1);
    debtorAccount.setIdentification(identification1);
//
//    OriginalTransactionReference28 originalTransactionReference = fiToFiPaymentStatusReport.getTransactionInformationAndStatus().get(0)
//        .getOriginalTransactionReference();
//    originalTransactionReference.setDebtorAccount(debtorAccount);

    return depositPaymentRequest;
  }

  private ProprietaryEnum getProprietary(PaymentType paymentType) {

    switch (paymentType) {
      case ALIAS_REALTIME: return ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT;
      case ALIAS_AUTODEPOSIT: return ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT;
      default : return ProprietaryEnum.ACCOUNT_DEPOSIT_PAYMENT;
    }

  }

  private AuthorizationTypeEnum getAuthorizationType(PaymentType paymentType) {
    if (paymentType == PaymentType.ALIAS_AUTODEPOSIT) {
      return AuthorizationTypeEnum.ACCOUNT_ALIAS_PAYMENT;
    }
    return AuthorizationTypeEnum.REQUEST_FOR_PAYMENT;
  }

  private HttpHeaders buildRequestHeaders(String enrollmentId) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, FI_ID_PTC);
    headers.add(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, enrollmentId);
    headers.add(InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, null);
    headers.add(InteracRestAdapterConstant.HEADER_REQUEST_ID, java.util.UUID.randomUUID().toString());
    headers.add(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, "true");
    headers.add(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, "ETRANSFER_SYSTEM");
    headers.add(InteracRestAdapterConstant.HEADER_SIGNATURE, "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
    headers.add(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SIGNATURE_TYPE);
    headers.add(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString());
    headers.add(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
    return headers;
  }

  private SubmitDepositRequest createSubmitDepositRequest(String participantUserId, String originalMessageIdentification, String accountServicerReference,
      String creditorAuthentication, String depositId) {
    SubmitDepositRequest request = new SubmitDepositRequest();
    FIToFIPaymentStatusReportV10 fiToFiPaymentStatusReport = createFiToFIPaymentStatusReportV10(participantUserId, originalMessageIdentification,
        accountServicerReference, creditorAuthentication, depositId);
    request.setFiToFiPaymentStatusReport(fiToFiPaymentStatusReport);
    return request;
  }

  private FIToFIPaymentStatusReportV10 createFiToFIPaymentStatusReportV10(String participantUserId, String originalMessageIdentification, String accountServicerReference,
    String creditorAuthentication, String depositId) {
    FIToFIPaymentStatusReportV10 fiToFiPaymentStatusReport = new FIToFIPaymentStatusReportV10();

    // group header
    GroupHeader91 groupHeader = new GroupHeader91();
    groupHeader.setMessageIdentification(UUID.randomUUID().toString().substring(0, 12));
    groupHeader.setCreationDatetime(OffsetDateTime.now(ZoneOffset.UTC));

    // instructing agent
    BranchAndFinancialInstitutionIdentification6 instructingAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 instructingAgentFI = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 instructingAgentCSMI = new ClearingSystemMemberIdentification2();
    instructingAgentCSMI.setMemberIdentification(FI_ID_BMO);
    instructingAgentFI.setClearingSystemMemberIdentification(instructingAgentCSMI);
    instructingAgent.setFinancialInstitutionIdentification(instructingAgentFI);
    groupHeader.setInstructingAgent(instructingAgent);

    // instructed agent
    BranchAndFinancialInstitutionIdentification6 instructedAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 instructedAgentFI = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 instructedAgentCSMI = new ClearingSystemMemberIdentification2();
    instructedAgentCSMI.setMemberIdentification(FI_ID_PTC);
    instructedAgentFI.setClearingSystemMemberIdentification(instructedAgentCSMI);
    instructedAgent.setFinancialInstitutionIdentification(instructedAgentFI);
    groupHeader.setInstructedAgent(instructedAgent);

    // transaction information and status
    List<PaymentTransaction110> transactionInformationAndStatusList = new ArrayList<>();
    PaymentTransaction110 transactionInfoAndStatus = new PaymentTransaction110();

    // original group information
    OriginalGroupInformation29 originalGroupInfo = new OriginalGroupInformation29();
    originalGroupInfo.setOriginalMessageIdentification(originalMessageIdentification);
    originalGroupInfo.setOriginalMessageNameIdentification("pacs.002.001.10");
    transactionInfoAndStatus.setOriginalGroupInformation(originalGroupInfo);

    transactionInfoAndStatus.setOriginalEndToEndIdentification("NOTPROVIDED");
    transactionInfoAndStatus.setOriginalTransactionIdentification(DEP_PAYMENT_EXTERNAL_REF_ID);

    transactionInfoAndStatus.setTransactionStatus(ExternalPaymentTransactionStatus1Code.PDNG);
    transactionInfoAndStatus.setAccountServicerReference(accountServicerReference);
    transactionInfoAndStatus.setClearingSystemReference(depositId);

    // original transaction reference
    OriginalTransactionReference28 originalTransactionReference = new OriginalTransactionReference28();

    // creditor
    Party40Choice creditor = new Party40Choice();
    PartyIdentification135 party = new PartyIdentification135();
    party.setName("AWick");
    Party38Choice creditorId = new Party38Choice();

    OrganisationIdentification29 creditorOrganizationId = new OrganisationIdentification29();
    List<GenericOrganisationIdentification1> otherList = new ArrayList<>();
    GenericOrganisationIdentification1 other = new GenericOrganisationIdentification1();
    other.setIdentification(participantUserId);
    otherList.add(other);
    creditorOrganizationId.setOther(otherList);
    creditorId.setOrganisationIdentification(creditorOrganizationId);
    party.setIdentification(creditorId);
    creditor.setParty(party);
    originalTransactionReference.setCreditor(creditor);

    // creditor account
    CashAccount38 creditorAccount = new CashAccount38();
    AccountIdentification4Choice creditorAccountId = new AccountIdentification4Choice();
    GenericAccountIdentification1 creditorAccountOther = new GenericAccountIdentification1();
    creditorAccountOther.setIdentification(creditorAuthentication);
    creditorAccountId.setOther(creditorAccountOther);

    creditorAccount.setIdentification(creditorAccountId);
    originalTransactionReference.setCreditorAccount(creditorAccount);

    transactionInfoAndStatus.setOriginalTransactionReference(originalTransactionReference);

    transactionInformationAndStatusList.add(transactionInfoAndStatus);

    fiToFiPaymentStatusReport.setGroupHeader(groupHeader);
    fiToFiPaymentStatusReport.setTransactionInformationAndStatus(transactionInformationAndStatusList);
    return fiToFiPaymentStatusReport;
  }
  /**
   * implementation of constructing the request payment's validation error
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    return new ErrorEntity();
  }

  private String findNetworkEnrollmentId(Customers oneCustomer) {
    String result = null;
    for (Enrollments enrollments : oneCustomer.getEnrollments()) {
      Products products = enrollments.getProducts();
      // need improve this part later deserialize string to object check if this is etransfer
      if (products.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
        result = enrollments.getNetworkEnrollmentId();
        break;
      }
    }
    return result;
  }

}
