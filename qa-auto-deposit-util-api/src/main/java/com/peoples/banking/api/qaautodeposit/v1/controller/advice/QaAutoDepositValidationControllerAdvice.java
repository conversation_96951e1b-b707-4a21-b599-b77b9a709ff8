package com.peoples.banking.api.qaautodeposit.v1.controller.advice;

import com.peoples.banking.api.qaautodeposit.v1.converter.ErrorResponseConverter;
import com.peoples.banking.domain.customer.model.Error;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.util.api.common.HttpHeadersUtil;
import com.peoples.banking.util.api.common.controller.advice.APIValidationControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.ControllerAdvice;

/**
 * Validation controller advice to handle business validation exceptions
 *
 */
@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class QaAutoDepositValidationControllerAdvice extends APIValidationControllerAdvice {

  @Autowired
  private ErrorResponseConverter errorConverter;

  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(ErrorEntity validationError) {
    ErrorResponse errorResponse = convertValidationToErrorResponse(validationError);

    ErrorResponseEntity<ErrorResponse> errorResponseEntity = new ErrorResponseEntity<>(errorResponse);
    return errorResponseEntity;
  }

  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(List<ErrorEntity> validationErrorList) {

    List<Error> errorList = new ArrayList<>();
    for (ErrorEntity validationError : validationErrorList) {
      Error error = convertValidationErrorToError(validationError);
      errorList.add(error);
    }

    ErrorResponse errorResponse = errorConverter.buildErrorResponse(errorList);

    ErrorResponseEntity<ErrorResponse> errorResponseEntity = new ErrorResponseEntity<>(errorResponse);
    return errorResponseEntity;
  }

  /**
   * to convert validation error to error response
   * 
   * @param validationError
   * @return ErrorResponse the error response
   */
  private ErrorResponse convertValidationToErrorResponse(ErrorEntity validationError) {
    Error error = convertValidationErrorToError(validationError);

    ErrorResponse errorResponse = errorConverter.buildErrorResponse(Arrays.asList(error));

    return errorResponse;

  }


  /**
   * convert validation error to error
   * 
   * @param validationError the validation error
   * @return Error the error detail
   */
  private Error convertValidationErrorToError(ErrorEntity validationError) {
    Error error = errorConverter.buildError(validationError.getErrorCode(), validationError.getAdditionalInformation());

    return error;

  }

  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return HttpHeadersUtil.buildResponseHttpHeaders(request);
  }

}
