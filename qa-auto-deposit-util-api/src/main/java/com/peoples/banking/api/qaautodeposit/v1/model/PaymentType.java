package com.peoples.banking.api.qaautodeposit.v1.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * ALIAS_AUTODEPOSIT         - Auto-Deposit payment
 * ALIAS_REALTIME            - Real-time Auto-Deposit payment
 */
public enum PaymentType {

  ALIAS_AUTODEPOSIT("ALIAS_AUTODEPOSIT"),
  
  ALIAS_REALTIME("ALIAS_REALTIME");

  private String value;

  PaymentType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static PaymentType fromValue(String value) {
    for (PaymentType b : PaymentType.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

