package com.peoples.banking.api.qaautodeposit.v1.controller;

import com.peoples.banking.api.qaautodeposit.v1.config.QaAutoDepositConstant;
import com.peoples.banking.api.qaautodeposit.v1.model.TriggerAutoDepositRequest;
import com.peoples.banking.api.qaautodeposit.v1.service.QaAutoDepositService;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.time.Instant;
import javax.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * The Rest controller for QA Auto Deposit.
 */

@RestController
public class QaAutoDepositController {

  private static final Logger perf = LogManager.getLogger(APICommonUtilConstant.LOGGER_NAME_PERFORMANCE);

  private static final String OPERATION_TRIGGER_AUTO_DEPOSIT = "triggerAutoDeposit()";

  @Autowired
  private QaAutoDepositService service;

  /**
   * trigger auto deposit
   *
   * @param interactionId the unique id per request
   * @param triggerAutoDepositRequest  trigger auto deposit request
   * @return Payments in DB
   */
  @PerfLogger
  @PostMapping(value = QaAutoDepositConstant.OPERATION_TRIGGER_AUTO_DEPOSIT, produces = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<?> triggerAutoDeposit(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId,
      @Valid @RequestBody TriggerAutoDepositRequest triggerAutoDepositRequest) {

    Instant startTime = Instant.now();

    service.triggerAutoDeposit(triggerAutoDepositRequest);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId);

    Instant endTime = Instant.now();

    // log performance
    perf.info("{} - start [{}], end [{}], total [{}]ms", OPERATION_TRIGGER_AUTO_DEPOSIT,
        startTime, endTime, endTime.toEpochMilli() - startTime.toEpochMilli());

    return new ResponseEntity<>(null, headers, HttpStatus.CREATED);
  }

  /**
   * construct http headers to include interaction id sent in the request and application generated correspondent id
   *
   * @param interactionId - interaction id to use in headers
   * @return HttpHeaders - constructed http headers object
   */
  protected HttpHeaders createResponseHttpHeaders(String interactionId) {
    HttpHeaders headers = new HttpHeaders();

    // correspondentId is generated in the logger filter and stored in request context holder
    String correspondentId = (String) RequestContextHolder.getRequestAttributes().getAttribute(
        APICommonUtilConstant.LOGGING_FIELD_GUIID, RequestAttributes.SCOPE_REQUEST);

    headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.set(APICommonUtilConstant.HEADER_CORRESPONDENT_ID, correspondentId);

    return headers;
  }
}
