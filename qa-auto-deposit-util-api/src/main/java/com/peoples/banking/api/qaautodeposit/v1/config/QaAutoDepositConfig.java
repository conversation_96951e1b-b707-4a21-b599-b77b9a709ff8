package com.peoples.banking.api.qaautodeposit.v1.config;

import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import java.util.Properties;
import javax.sql.DataSource;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.web.context.request.RequestContextListener;

/**
 * QA Auto Deposit Configuration
 *
 */
@Configuration
public class QaAutoDepositConfig {

  private final String PROPERTY_SHOW_SQL = "hibernate.jpa.show-sql";
  private final String PROPERTY_DIALECT = "hibernate.dialect";

  @Autowired
  Environment environment;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  @Autowired
  private QaAutoDepositProperty qaAutoDepositProperty;
  
  /**
   * add logging filter
   * 
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> qaAutoDepositAPILoggingFilter() {
    LoggingFilter qaAutoDepositPaymentAPILoggingFilter = new LoggingFilter();
    qaAutoDepositPaymentAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    qaAutoDepositPaymentAPILoggingFilter.setSystemName(qaAutoDepositProperty.getSystemName());
    registrationBean.setFilter(qaAutoDepositPaymentAPILoggingFilter);
    registrationBean.addUrlPatterns("/*");

    return registrationBean;
  }


  /**
   * create request context listener. It is required for Request Context Holder
   * 
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener qaRequestContextListener() {
    return new RequestContextListener();
  }

  @Bean
  @Primary
  LocalContainerEntityManagerFactoryBean autoDepositEntityManagerFactory(DataSource dataSource) {
    LocalContainerEntityManagerFactoryBean lfb = new LocalContainerEntityManagerFactoryBean();
    lfb.setDataSource(dataSource);
    lfb.setPersistenceProviderClass(HibernatePersistenceProvider.class);
    lfb.setPackagesToScan("com.peoples.banking.persistence.customer", "com.peoples.banking.persistence.payment");
    lfb.setJpaProperties(hibernateProps());
    return lfb;
  }

  Properties hibernateProps() {
    Properties properties = new Properties();
    properties.setProperty(PROPERTY_DIALECT, environment.getProperty(PROPERTY_DIALECT));
    properties.setProperty(PROPERTY_SHOW_SQL, environment.getProperty(PROPERTY_SHOW_SQL));
    return properties;
  }
}
