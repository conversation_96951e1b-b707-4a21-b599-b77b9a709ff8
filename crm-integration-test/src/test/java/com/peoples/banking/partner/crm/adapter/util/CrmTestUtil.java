package com.peoples.banking.partner.crm.adapter.util;

import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequestData;

/**
 * Utility class to aid in the generation of <i>Interac</i> schema generation, for <i>Request Management</i> domain.
 */
public class CrmTestUtil {

  /**
   * Build base CreateSafCaseRequest request for <i>CRM.</i>
   *
   * @return pre initialized CreateSafCaseRequest
   */
  public static CreateSafCaseRequest buildCreateSafCaseRequest() {

    CreateSafCaseRequest request = new CreateSafCaseRequest();
    CreateSafCaseRequestData createSafCaseRequestData = new CreateSafCaseRequestData();
    createSafCaseRequestData.setCrmUniqueId("*********");

    createSafCaseRequestData.setPaymentType("ALIAS_AUTODEPOSIT");
    createSafCaseRequestData.setServiceAcctRefId("service_account_ref_id");
    createSafCaseRequestData.setPtcCustomerId("1111");
    createSafCaseRequestData.setCustomerType("INDIVIDUAL");
    createSafCaseRequestData.setPtcPaymentId("111111");
    createSafCaseRequestData.setPtcAssociatedId("asdfasdf");
    createSafCaseRequestData.setInteracPaymentId("1sfsaaa");
    createSafCaseRequestData.setInteracAssociatedId("");
    createSafCaseRequestData.setAmount("500");
    createSafCaseRequestData.setDateInitiated("2021-09-14");
    createSafCaseRequestData.setDateDeposited("2021-09-15");
    createSafCaseRequestData.setDateExpires("2021-09-24");
    createSafCaseRequestData.setPaymentStatus("ACCEPTED");
    createSafCaseRequestData.setPreviousFraudStatus("SUSPICIOUS");
    createSafCaseRequestData.setFiFraudStatus("Confirmed_Legit_By_FI");
    createSafCaseRequestData.setFraudType("FP_FRAUD");

    //filling recipient data information
    createSafCaseRequestData.setRecipientName("Tester user");
    createSafCaseRequestData.setRecipientEmail("<EMAIL>");
    createSafCaseRequestData.setRecipientPhoneNum("678911220");
    createSafCaseRequestData.setRecipientAcctNum("67890");
    createSafCaseRequestData.setRecipientIp("***********");
    createSafCaseRequestData.setRecipientFinInstId("fin_inst_id");
    //filling sender data information
    createSafCaseRequestData.setSenderName("Tester user");
    createSafCaseRequestData.setSenderEmail("<EMAIL>");
    createSafCaseRequestData.setSenderPhoneNum("678911220");
    createSafCaseRequestData.setSenderAcctNum("67890");
    createSafCaseRequestData.setSenderIp("***********");
    createSafCaseRequestData.setSenderFinInstId("fin_inst_id");

    request.setData(createSafCaseRequestData);
//    {
//      "data":{
//      "reference_id":"11345", "fi_fraud_status":"Confirmed_Legit_By_FI",
//          "recipient_acct_num":"67890", "fi_reference":"", "ext_name":"Tester user", "ext_email":"<EMAIL>", "ext_ip":
//      "***********", "amount":"500", "date_initiated":"2021-09-14", "date_expires":"2021-09-14", "date_deposited":
//      "2021-09-14", "ptc_customer_id":"1111", "interac_customer_id":"2222", "ptc_payment_id":"111111", "interac_payment_id":
//      "", "ptc_associated_id":"", "interac_associated_id":"", "crm_unique_id":"*********"
//    }
//    }

    return request;
  }

}