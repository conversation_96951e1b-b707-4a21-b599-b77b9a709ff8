package com.peoples.banking.partner.crm.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.crm.adapter.CrmRestAdapter;
import com.peoples.banking.partner.crm.adapter.config.CrmAdapterProperty;
import com.peoples.banking.partner.crm.adapter.config.CrmHttpClientConfig;
import com.peoples.banking.partner.crm.adapter.config.CrmRestTemplateConfig;
import com.peoples.banking.partner.crm.adapter.domain.SuccessModel;
import com.peoples.banking.partner.crm.adapter.util.CrmTestUtil;

import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequestData;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    CrmRestTemplateConfig.class,
    CrmHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
@Log4j2
class CrmAdapterIntegrationTest {

  @SpyBean
  ObjectMapper mapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  CrmAdapterProperty crmProperties;

  // Initialize and inject mocks into UUT
  @InjectMocks
  CrmRestAdapter uut;


  /**
   * Unit under test is CrmRestAdapter::createFasCase
   * <pre>successful request</pre>
   */
  @Order(1)
  @Test
  public void createFasCase_all_data_present_success() {

    //create the customer request
    CreateSafCaseRequest request = CrmTestUtil
        .buildCreateSafCaseRequest();

    // sanity check
    assertNotNull(request);

    // unit under test
    SuccessModel response = null;
    try {
      response = uut.createFasCase(request);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(200, response.getStatusCode());
    assertNotNull(response.getMessage());
  }

  /**
   * Unit under test is CrmRestAdapter::createFasCase
   * <pre>successful request</pre>
   */
  @Order(2)
  @Test
  public void createFasCase_only_required_data_present_success() {

    //create the customer request
    CreateSafCaseRequest request = CrmTestUtil
        .buildCreateSafCaseRequest();

    // sanity check
    assertNotNull(request);
    //setting not required fields to null
    CreateSafCaseRequestData requestData = request.getData();
    assertNotNull(requestData);
    requestData.setPtcPaymentId(null);
    requestData.setPtcAssociatedId(null);
    requestData.setInteracAssociatedId(null);
    requestData.setDateDeposited(null);
    requestData.setPreviousFraudStatus(null);
    requestData.setFraudType(null);
    requestData.setNotes(null);
    //set recipient to nulls
    requestData.setRecipientAcctNum(null);
    requestData.setRecipientEmail(null);
    requestData.setRecipientFinInstId(null);
    requestData.setRecipientPhoneNum(null);
    requestData.setRecipientIp(null);
    requestData.setRecipientName(null);
    //set sender non-mandatory to nulls
    requestData.setSenderIp(null);
    requestData.setSenderAcctNum(null);
    requestData.setSenderPhoneNum(null);
    requestData.setSenderEmail(null);

    // unit under test
    SuccessModel response = null;
    try {
      response = uut.createFasCase(request);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(200, response.getStatusCode());
    assertNotNull(response.getMessage());
  }

  /**
   * Unit under test is CrmRestAdapter::createFasCase
   * <pre>unsuccessful case - bad request because of bad fi fraud status</pre>
   */
  @Order(3)
  @Test
  public void createFasCase_bad_fraud_status_fail() {

    //create the customer request
    CreateSafCaseRequest request = CrmTestUtil
        .buildCreateSafCaseRequest();

    // sanity check
    assertNotNull(request);
    assertNotNull(request.getData());
    request.getData().setFiFraudStatus("bad_status");
    // unit under test
    SuccessModel response = null;
    try {
      response = uut.createFasCase(request);
    } catch (ResponseException responseException) {
      assertEquals(400, responseException.getHttpStatusCode());
      assertEquals("Invalid fi_fraud_status!", responseException.getResponseText());
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNull(response);
  }

}