package com.peoples.banking.partner.adapter.interac.registration;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.CreateAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.GetAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.UpdateAccountAliasRegistrationRequest;
import com.peoples.banking.partner.domain.interac.registration.model.ViewAccountAliasRegistration;

/**
 * Adapter for Interac's <i>Registration Management</i> service domain.
 */
public interface RegistrationAdapter {

  /**
   * Registers an account alias for a customer with the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  CreateAccountAliasRegistrationResponse registerAlias(
      String enrolmentId, AccountAliasRegistration request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieves list of account alias registrations (
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  GetAccountAliasRegistrationResponse retrieveRegisteredAlias(String enrolmentId, int maxResponseItems, int offset, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the account alias details of an existing registration.
   *
   * @param enrolmentId    unique customer identifier on Interac system
   * @param registrationId unique registration identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  ViewAccountAliasRegistration retrieveRegisteredAlias(String enrolmentId, String registrationId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Update the account alias details an existing registration.
   *
   * @param enrolmentId    unique customer identifier on Interac system
   * @param registrationId unique registration identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @param request        request body payload
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean updateRegisteredAlias(
      String enrolmentId, String registrationId, UpdateAccountAliasRegistrationRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Removes an existing account alias registration.
   *
   * @param enrolmentId    unique customer identifier on Interac system
   * @param registrationId unique registration identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean removeRegisteredAlias(String enrolmentId, String registrationId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;
}
