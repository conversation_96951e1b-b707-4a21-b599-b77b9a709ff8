package com.peoples.banking.partner.adapter.interac.registration.impl;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.registration.RegistrationAdapter;
import com.peoples.banking.partner.adapter.interac.registration.config.RegistrationAdapterConstant;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.CreateAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.registration.model.GetAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.UpdateAccountAliasRegistrationRequest;
import com.peoples.banking.partner.domain.interac.registration.model.ViewAccountAliasRegistration;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

/**
 * Adapter implementation for Interac's <i>Registration Management</i> service domain.
 */
@Log4j2
@Service
public class RegistrationAdapterImpl extends InteracRestAdapter<ErrorModel> implements RegistrationAdapter {

  /**
   * <i>Interac</i> service domain.
   */
  private static final String SERVICE_DOMAIN = "RegistrationManagement";

  /**
   * <i>Interac</i> Create Account-Alias Registration endpoint URL.
   */
  private String createAccountAliasRegEndpoint = null;

  /**
   * <i>Interac</i> Get Account-Alias Registrations (many) endpoint URL.
   */
  private String retrieveAccountAliasRegsEndpoint = null;

  /**
   * <i>Interac</i> Get Account-Alias Registration (one) endpoint URL.
   */
  private String retrieveAccountAliasRegEndpoint = null;

  /**
   * <i>Interac</i> Update Account-Alias Registration endpoint URL.
   */
  private String updateAccountAliasRegEndpoint = null;

  /**
   * <i>Interac</i> Delete Account-Alias Registration endpoint URL.
   */
  private String deleteAccountAliasRegEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public CreateAccountAliasRegistrationResponse registerAlias(String enrolmentId, AccountAliasRegistration request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, request={}", enrolmentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = createAccountAliasRegEndpoint();

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.POST, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
            CreateAccountAliasRegistrationResponse.class, null, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public GetAccountAliasRegistrationResponse retrieveRegisteredAlias(String enrolmentId, int maxResponseItems, int offset, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank()) {
      log.warn("required parameters: enrolmentId={}", enrolmentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = retrieveAccountAliasRegsEndpoint(maxResponseItems, offset);

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
            GetAccountAliasRegistrationResponse.class, null, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ViewAccountAliasRegistration retrieveRegisteredAlias(String enrolmentId, String registrationId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || registrationId == null || registrationId.isBlank()) {
      log.warn("required parameters: enrolmentId={}, registrationId={}", enrolmentId, registrationId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = retrieveAccountAliasRegEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RegistrationAdapterConstant.REGISTRATION_ID, registrationId);

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
            ViewAccountAliasRegistration.class, pathParams, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean updateRegisteredAlias(String enrolmentId, String registrationId, UpdateAccountAliasRegistrationRequest request,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || registrationId == null || registrationId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, registrationId={}, request={}", enrolmentId, registrationId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = updateAccountAliasRegEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RegistrationAdapterConstant.REGISTRATION_ID, registrationId);

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this
        .execute(endpointUrl, HttpMethod.PUT, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
            String.class, pathParams, null);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean removeRegisteredAlias(String enrolmentId, String registrationId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || registrationId == null || registrationId.isBlank()) {
      log.warn("required parameters: enrolmentId={}, registrationId={}", enrolmentId, registrationId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = deleteAccountAliasRegEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RegistrationAdapterConstant.REGISTRATION_ID, registrationId);

    // build path parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this
        .execute(endpointUrl, HttpMethod.DELETE, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
            String.class, pathParams, null);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams) {
    // no implementation needed
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return SERVICE_DOMAIN;
  }

  /**
   * Helper utility to construct <i>RegisterAccountAlias</i> endpoint URL.
   *
   * @return RegisterAccountAlias endpoint URL
   */
  private String createAccountAliasRegEndpoint() {
    if (createAccountAliasRegEndpoint == null) {
      createAccountAliasRegEndpoint = this.getBaseUrl() + RegistrationAdapterConstant.REGISTRATION;
    }
    return createAccountAliasRegEndpoint;
  }

  /**
   * Helper utility to construct <i>RetrieveAccountAliases</i> endpoint URL.
   *
   * @return RetrieveAccountAliases endpoint URL
   */
  private String retrieveAccountAliasRegsEndpoint(int maxItems, int offset) {
    if (retrieveAccountAliasRegsEndpoint == null) {
      retrieveAccountAliasRegsEndpoint = this.getBaseUrl() + RegistrationAdapterConstant.RETRIEVE_REGISTRATIONS;
    }
    String result = retrieveAccountAliasRegsEndpoint;
    if (maxItems > 0 && offset > 0) {
      result +=
          "?" + RegistrationAdapterConstant.MAX_RESPONSE_ITEMS + "=" + maxItems + "&" + RegistrationAdapterConstant.OFFSET + "=" + offset;
    }
    return result;
  }

  /**
   * Helper utility to construct <i>RetrieveAccountAlias</i> endpoint URL.
   *
   * @return RetrieveAccountAliase endpoint URL
   */
  private String retrieveAccountAliasRegEndpoint() {
    if (retrieveAccountAliasRegEndpoint == null) {
      retrieveAccountAliasRegEndpoint = this.getBaseUrl() + RegistrationAdapterConstant.RETRIEVE_REGISTRATION;
    }
    return retrieveAccountAliasRegEndpoint;
  }

  /**
   * Helper utility to construct <i>UpdateAccountAlias</i> endpoint URL.
   *
   * @return UpdateAccountAlias endpoint URL
   */
  private String updateAccountAliasRegEndpoint() {
    if (updateAccountAliasRegEndpoint == null) {
      updateAccountAliasRegEndpoint = this.getBaseUrl() + RegistrationAdapterConstant.UPDATE_REGISTRATION;
    }
    return updateAccountAliasRegEndpoint;
  }

  /**
   * Helper utility to construct <i>DeleteAccountAlias</i> endpoint URL.
   *
   * @return DeleteAccountAlias endpoint URL
   */
  private String deleteAccountAliasRegEndpoint() {
    if (deleteAccountAliasRegEndpoint == null) {
      deleteAccountAliasRegEndpoint = this.getBaseUrl() + RegistrationAdapterConstant.DELETE_REGISTRATION;
    }
    return deleteAccountAliasRegEndpoint;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }
}
