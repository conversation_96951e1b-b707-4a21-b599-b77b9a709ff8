package com.peoples.banking.partner.adapter.interac.registration.config;

/**
 * Constants for Interac's <i>Request Management</i> service domain.
 */
public final class RegistrationAdapterConstant {

  /**
   * <i>Interac</i> root context path (incl. version)
   */
  public static final String REGISTRATION_ROOT_CONTEXT = "/registration-api/v3.5.0";

  private static final String REGISTRATION_OPERATION = "/account-alias-registrations";
  public static final String REGISTRATION_ID = "id";
  public static final String REGISTRATION_WITH_ID_OPERATION = REGISTRATION_OPERATION + "/{" + REGISTRATION_ID + "}";

  public static final String REGISTRATION = REGISTRATION_ROOT_CONTEXT + REGISTRATION_OPERATION;
  public static final String UPDATE_REGISTRATION = REGISTRATION_ROOT_CONTEXT + REGISTRATION_WITH_ID_OPERATION;
  public static final String RETRIEVE_REGISTRATION = UPDATE_REGISTRATION;
  public static final String DELETE_REGISTRATION = UPDATE_REGISTRATION;
  public static final String RETRIEVE_REGISTRATIONS = REGISTRATION;
  public static final String MAX_RESPONSE_ITEMS = "max-response-items";
  public static final String OFFSET = "offset";

  /**
   * Query parameters.
   */
  // TBD add query parameters

  // TODO rename static constants so a reorg on this class doesn't ruin the flow
  private RegistrationAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
