package com.peoples.banking.partner.adapter.interac.customer;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.partner.domain.interac.customer.model.GetCustomerRegistrationResponse;

/**
 * Adapter for Interac's <i>Customer Management</i> service domain.
 */
public interface CustomerAdapter {

  /**
   * Register a new Customer with the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean addCustomer(String enrolmentId, AddCustomerRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the registration details of an existing Customer that is registered on the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  GetCustomerRegistrationResponse getCustomer(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Update the profile details of an existing customer that is registered with the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean updateCustomer(String enrolmentId, Customer request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Disable a previously disabled existing Customer registered on the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean disableCustomer(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException;

  /**
   * Retrieve various incoming and outgoing limits and amounts (current, per transfer, daily cumulative) for a specific Customer on the
   * Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean enableCustomer(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException;

  // TODO add customer limits API
}
