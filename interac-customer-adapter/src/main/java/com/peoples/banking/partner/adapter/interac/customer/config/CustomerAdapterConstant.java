package com.peoples.banking.partner.adapter.interac.customer.config;

/**
 * Constants for Interac's <i>Customer Management</i> service domain.
 */
public final class CustomerAdapterConstant {
  /**
   * <i>Interac</i> root context path (incl. version)
   */
  public static final String CUSTOMER_ROOT_CONTEXT = "/customer-api/v3.5.0";

  private static final String CUSTOMER_OPERATION = "/customers";
  public static final String CUSTOMER_ID = "id"  ;
  private static final String CUSTOMER_WITH_ID_OPERATION = CUSTOMER_OPERATION + "/{" + CUSTOMER_ID + "}";

  public static final String ADD_CUSTOMER = CUSTOMER_ROOT_CONTEXT + CUSTOMER_OPERATION;
  public static final String GET_CUSTOMER = CUSTOMER_ROOT_CONTEXT + CUSTOMER_WITH_ID_OPERATION;
  public static final String UPDATE_CUSTOMER = CUSTOMER_ROOT_CONTEXT + CUSTOMER_WITH_ID_OPERATION;
  public static final String ENABLE_CUSTOMER = CUSTOMER_ROOT_CONTEXT + CUSTOMER_WITH_ID_OPERATION  + "/enable";
  public static final String DISABLE_CUSTOMER = CUSTOMER_ROOT_CONTEXT + CUSTOMER_WITH_ID_OPERATION + "/disable";
  public static final String GET_CUSTOMER_AMOUNTS = CUSTOMER_ROOT_CONTEXT + CUSTOMER_WITH_ID_OPERATION + "​/amount-limits";

  // TODO rename static constants so a reorg on this class doesn't ruin the flow

  private CustomerAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
