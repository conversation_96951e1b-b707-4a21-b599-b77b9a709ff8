package com.peoples.banking.partner.adapter.interac.customer.impl;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.customer.CustomerAdapter;
import com.peoples.banking.partner.adapter.interac.customer.config.CustomerAdapterConstant;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.partner.domain.interac.customer.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.customer.model.GetCustomerRegistrationResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

/**
 * Adapter implementation for Interac's <i>Customer Management</i> service domain.
 */
@Log4j2
@Service
public class CustomerAdapterImpl extends InteracRestAdapter<ErrorModel> implements CustomerAdapter {

  /**
   * <i>Interac</i> service domain.
   */
  private static final String SERVICE_DOMAIN = "CustomerManagement";

  /**
   * <i>Interac</i> AddCustomer endpoint URL.
   */
  private String addCustomerEndpoint = null;

  /**
   * <i>Interac</i> UpdateCustomer endpoint URL.
   */
  private String updateCustomerEndpoint = null;

  /**
   * <i>Interac</i> GetCustomer endpoint URL.
   */
  private String getCustomerEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean addCustomer(String enrolmentId, AddCustomerRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, request={}", enrolmentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getAddCustomerEndpoint();

    // build path parameters -- N/A

    // build header parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
        String.class, null, null);

    return Boolean.TRUE;
  }


  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public GetCustomerRegistrationResponse getCustomer(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank()) {
      log.warn("required parameters: enrolmentId={}", enrolmentId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getGetCustomerEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(CustomerAdapterConstant.CUSTOMER_ID, enrolmentId);

    // build header parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
            GetCustomerRegistrationResponse.class, pathParams, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean updateCustomer(String enrolmentId, Customer request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // TODO add performance logging

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, request={}", enrolmentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getUpdateCustomerEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(CustomerAdapterConstant.CUSTOMER_ID, enrolmentId);

    // build header parameters -- N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.PUT, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
        String.class, pathParams, null);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean disableCustomer(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException {
    throw new AdapterException(ErrorCode.NOT_IMPLEMENTED);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean enableCustomer(String enrolmentId, String connectorId)
      throws AdapterException, ResponseException, TBDException {
    throw new AdapterException(ErrorCode.NOT_IMPLEMENTED);
  }

  /**
   * @inheritDoc
   */
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams) {
    // no implementation needed
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return SERVICE_DOMAIN;
  }

  /**
   * Helper utility to construct <i>AddCustomer</i> endpoint URL.
   *
   * @return AddCustomer endpoint URL
   */
  private String getAddCustomerEndpoint() {
    if (addCustomerEndpoint == null) {
      addCustomerEndpoint = this.getBaseUrl() + CustomerAdapterConstant.ADD_CUSTOMER;
    }
    return addCustomerEndpoint;
  }


  /**
   * Helper utility to construct <i>UpdateCustomer</i> endpoint URL.
   *
   * @return UpdateCustomer endpoint URL
   */
  private String getUpdateCustomerEndpoint() {
    if (updateCustomerEndpoint == null) {
      updateCustomerEndpoint = this.getBaseUrl() + CustomerAdapterConstant.UPDATE_CUSTOMER;
    }
    return updateCustomerEndpoint;
  }

  /**
   * Helper utility to construct <i>GetCustomer</i> endpoint URL.
   *
   * @return GetCustomer endpoint URL
   */
  private String getGetCustomerEndpoint() {
    if (getCustomerEndpoint == null) {
      getCustomerEndpoint = this.getBaseUrl() + CustomerAdapterConstant.GET_CUSTOMER;
    }
    return getCustomerEndpoint;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }
}
