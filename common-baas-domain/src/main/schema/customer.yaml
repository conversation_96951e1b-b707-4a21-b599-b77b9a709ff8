openapi: '3.0.0'
info:
  version: '2.1.6'
  title: eTransfer - Customer
  description: Customer management for an individual or organization
  x-last-updated-date: '13-November-2024'
servers:
  - description: Staging Environment
    url: http://banking-api-staging.peoplespayments.com
tags:
  - name: customer
    description: An individual or an organization
  - name: alias registration
    description: An alias associated with the customer, allowing automatic deposit without any intervention
paths:
  ##################################
  ############ Customer ############
  ##################################
  ############ New customer (individual or organization) ############
  /v1/customer:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    post:
      summary: New customer
      operationId: createCustomer
      tags:
        - customer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCustomerResponse'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  ############ Retrieve customer (individual or organization) ############
  /v1/customer/{customer_id}:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/CustomerId'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    get:
      summary: Retrieve an existing customer
      operationId: retrieveCustomer
      tags:
        - customer
      responses:
        '200':
          description: Customer retrieved successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveCustomerResponse'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
    ############ Update customer (individual or organization) ############
    put:
      summary: Update customer information
      operationId: updateCustomer
      tags:
        - customer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '204':
          description: Customer updated successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  ############ Enable customer status ############
  /v1/customer/{customer_id}/enable:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/CustomerId'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    patch:
      summary:  Enable existing customer
      operationId: enableCustomerStatus
      tags:
        - customer
      responses:
        '204':
          description: Customer enabled successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  ############ Disable customer status ############
  /v1/customer/{customer_id}/disable:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/CustomerId'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    patch:
      summary:  Disable existing customer
      operationId: disableCustomerStatus
      tags:
        - customer
      responses:
        '204':
          description: Customer disabled successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  ##################################
  ############## Alias #############
  ##################################
  ############ New alias registration ############
  /v1/customer/{customer_id}/alias:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/CustomerId'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    post:
      summary:  Register a new deposit alias
      operationId: addAlias
      tags:
        - alias registration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterAliasRequest'
      responses:
        '201':
          description: Aliases registered successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterAliasResponse'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, alias etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
    ############ Retrieve all alias registration ############
    get:
      summary:  Retrieve all registered deposit aliases
      operationId: retrieveAliases
      parameters:
      - in: query
        name: max_items
        description: Maximum number of records returned.
        schema:
          type: integer
          minimum: 5
          maximum: 500
      - in: query
        name: offset
        description: Starting record to return
        schema:
          type: integer
          minimum: 0
      tags:
        - alias registration
      responses:
        '200':
          description: Alias retrieved successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveAliasesResponse'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, alias etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  ############ Retrieve alias registration ############
  /v1/customer/{customer_id}/alias/{alias_id}:
    parameters:
      - $ref: 'common/common.yaml#/components/parameters/ServiceAccount'
      - $ref: 'common/common.yaml#/components/parameters/InteractionId'
      - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
      - $ref: 'common/common.yaml#/components/parameters/CustomerId'
      - $ref: 'common/common.yaml#/components/parameters/AliasId'
      - $ref: 'common/common.yaml#/components/parameters/Signature'
      - $ref: 'common/common.yaml#/components/parameters/SignatureKeyId'
      - $ref: 'common/common.yaml#/components/parameters/SignatureType'
    get:
      summary: Retrieve registered deposit alias
      operationId: retrieveAlias
      tags:
        - alias registration
      responses:
        '200':
          description: Aliases retrieved successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveAliasResponse'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, alias, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
    ############ Remove alias registration ############
    delete:
      summary:  Remove a registered deposit alias
      operationId: removeDepositAlias
      tags:
        - alias registration
      responses:
        '204':
          description: Alias removed successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
        '400':
          description: Unsuccessful
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found (service account, customer, alias, etc.)
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '504':
          description: Gateway Timeout -- Aws api gateway timeout
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-interaction-id'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/x-pg-correspondent-id'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
##############################################################################
components:
  schemas:
    ############ Root data type ############
    CreateCustomerRequest:
      allOf:
        - $ref: '#/components/schemas/Customer'
    CreateCustomerResponse:
      type: object
      required:
        - customer_id
      properties:
        customer_id:
          description: customer id
          $ref: 'common/common.yaml#/components/schemas/CustomerId'
    RetrieveCustomerResponse:
      allOf:
        - $ref: '#/components/schemas/Customer'
        - properties:
            active:
              description: Indicates whether the account is active (true) or disabled (false)
              type: boolean
              example: true
            created_date:
              description: The time the customer account was created
              type: string
              format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
              example: '2020-05-29T17:19:26.951000Z'
            updated_date:
              description: The time the customer account was last updated
              type: string
              format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
              example: '2020-05-29T17:19:26.951000Z'
            enrollments:
              type: array
              minItems: 0
              items:
                $ref: '#/components/schemas/Enrollment'
    UpdateCustomerRequest:
      type: object
      required:
        - name
        - language
        - email
      properties:
        name:
          type: object
          $ref: 'common/common.yaml#/components/schemas/CustomerName'
        language:
          $ref: 'common/common.yaml#/components/schemas/LanguageEnum'
        date_of_birth:
          description: Customer's date of birth
          type: string
          format: date ##YYYY-MM-DD
          example: '2020-05-29'
        address:
          type: array
          maxItems: 2
          items:
            $ref: '#/components/schemas/Address'
        email:
          type: array
          minItems: 1
          maxItems: 2
          items:
            $ref: '#/components/schemas/Email'
        phone:
          type: array
          minItems: 1
          maxItems: 4
          items:
            $ref: '#/components/schemas/Phone'
    RegisterAliasRequest:
      type: object
      required:
        - alias
        - account_number
        - account_open_date
      properties:
        alias:
          $ref: '#/components/schemas/ContactEmailPhone'
        account_number:
          $ref: 'common/common.yaml#/components/schemas/AccountNumber'
        account_open_date: # TBC
          $ref: 'common/common.yaml#/components/schemas/DateType'
    RegisterAliasResponse:
      allOf:
        - $ref: '#/components/schemas/AliasProfile'
    RetrieveAliasesResponse:
      type: object
      required:
        - aliases
      properties:
        aliases:
          type: array
          items:
            $ref: '#/components/schemas/AliasProfile'
    RetrieveAliasResponse:
      allOf:
        - $ref: '#/components/schemas/AliasProfile'
    ########## Complex data types ###########
    Address:
      description: Postal address information.
      type: object
      required:
        - type
        - address1
        - region
        - subdivision
        - postal_code
      properties:
        id:
          description: PTC generated unique id
          $ref: '#/components/schemas/IdType'
        type:
          description: Address priority
          $ref: '#/components/schemas/PriorityType'
        address1:
          description: Part 1 of the address
          $ref: '#/components/schemas/AddressLineType'
        address2:
          description: Part 2 of the address
          $ref: '#/components/schemas/AddressLineType'
        region:
          description: City of residence
          $ref: 'common/common.yaml#/components/schemas/RegionType'
        subdivision:
          description: State/Province of residence (2 letter code)
          $ref: 'common/common.yaml#/components/schemas/SubdivisionEnum'
        postal_code:
          description: Postal code. Required for Canadian and US addresses.
          $ref: 'common/common.yaml#/components/schemas/PostalCodeType'
        country:
          description: Country, must be CAN
          $ref: 'common/common.yaml#/components/schemas/CountryType'
    AliasProfile:
      type: object
      required:
        - alias
        - account_number
        - alias_ref_id
        - expiry_date
        - account_open_date
      properties:
        alias:
          $ref: '#/components/schemas/ContactEmailPhone'
        account_number:
          $ref: 'common/common.yaml#/components/schemas/AccountNumber'
        account_open_date: # TBC
          $ref: 'common/common.yaml#/components/schemas/DateType'
        alias_ref_id:
          $ref: 'common/common.yaml#/components/schemas/AliasReferenceId'
        expiry_date:
          description: Date and time when the alias will be considered expired.
          type: string
          format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
          example: '2020-06-13T17:19:26.951000Z'
        alias_status:
          $ref: 'common/common.yaml#/components/schemas/AliasStatus'
    Alias:
      description: Alias registered on the system
      type: object
      required:
        - type
        - handle
    Customer:
      type: object
      required:
        - name
        - type
        - language
        - email
      properties:
        id:
          $ref: 'common/common.yaml#/components/schemas/CustomerId'
        name:
          $ref: 'common/common.yaml#/components/schemas/CustomerName'
        type:
          $ref: 'common/common.yaml#/components/schemas/CustomerEnum'
        language:
          $ref: 'common/common.yaml#/components/schemas/LanguageEnum'
        date_of_birth:
          description: Customer's date of birth
          type: string
          format: date ##YYYY-MM-DD
          example: '2020-05-29'
        address:
          type: array
          maxItems: 2
          items:
            $ref: '#/components/schemas/Address'
        email:
          type: array
          minItems: 1
          maxItems: 2
          items:
            $ref: '#/components/schemas/Email'
        phone:
          type: array
          minItems: 1
          maxItems: 4
          items:
            $ref: '#/components/schemas/Phone'
    ContactEmailPhone:
      type: object
      oneOf:
        - type: object
          properties:
            email_address:
              $ref: 'common/common.yaml#/components/schemas/EmailAddress'
        - type: object
          properties:
            mobile_number:
              $ref: 'common/common.yaml#/components/schemas/PhoneNumber'
    Email:
      description: Customer's email address
      required:
        - type
        - address
      properties:
        id:
          description: PTC generated unique id
          $ref: '#/components/schemas/IdType'
        type:
          $ref: '#/components/schemas/PriorityType'
        address:
          $ref: 'common/common.yaml#/components/schemas/EmailAddress'
        enable_notification:
          type: boolean
          example: true
    Enrollment:
      description: A list of products a customer has enrolled
      required:
        - product_name
        - created_date
      properties:
        product_name:
          description: product name
          type: string
        created_date:
          description: The time the customer enrolled the product
          type: string
          format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
          example: '2020-05-29T17:19:26.951000Z'
    Phone:
      description: Customer's phone number
      required:
        - type
        - number
      properties:
        id:
          description: PTC generated unique id
          $ref: '#/components/schemas/IdType'
        type:
          $ref: 'common/common.yaml#/components/schemas/PhoneNumberEnum'
        number:
          $ref: 'common/common.yaml#/components/schemas/PhoneNumber'
        enable_notification:
          type: boolean
          example: true
    ############ Simple data type ############
    AddressLineType:
      description: Street address
      type: string
      minLength: 1
      maxLength: 80
      pattern: '^(?!\s)(?![\s\S]*\s$)[a-zA-Z0-9àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ ''\-\.,/#]+$'
    PriorityType:
      description: Provided information priority
      type: string
      enum: ['PRIMARY', 'SECONDARY']
      example: 'PRIMARY'
    IdType:
      description: 4 chars of alphanumeric Id field
      type: string
      pattern: '^[a-zA-Z0-9]{4,4}$'