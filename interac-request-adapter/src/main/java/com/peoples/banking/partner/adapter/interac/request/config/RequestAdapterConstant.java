package com.peoples.banking.partner.adapter.interac.request.config;

/**
 * Constants for Interac's <i>Request Management</i> service domain.
 */
public final class RequestAdapterConstant {

  /**
   * <i>Interac</i> root context path (incl. version)
   */
  public static final String REQUEST_ROOT_CONTEXT = "/request-api/v3.5.0";

  private static final String REQUEST_OPERATION = "/requests";
  public static final String REQUEST_ID = "id";
  public static final String REQUEST_WITH_ID_OPERATION = REQUEST_OPERATION + "/{" + REQUEST_ID + "}";

  public static final String SEND_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_OPERATION;
  public static final String UPDATE_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_WITH_ID_OPERATION;
  public static final String SEND_REQUEST_REMINDER = REQUEST_ROOT_CONTEXT + REQUEST_WITH_ID_OPERATION + "reminder";
  public static final String CANCEL_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_WITH_ID_OPERATION + "/cancel";
  public static final String DECLINE_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_WITH_ID_OPERATION + "/decline";

  // TODO confirm if this one actually exists ...
  public static final String GET_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_OPERATION + "/{" + REQUEST_ID + "}";
  public static final String RETRIEVE_INCOMING_REQUEST = REQUEST_ROOT_CONTEXT + REQUEST_OPERATION + "/{" + REQUEST_ID + "}/incoming";
  public static final String GET_REQUEST_DETAILS = REQUEST_ROOT_CONTEXT + REQUEST_OPERATION + "/{" + REQUEST_ID + "}" + "/detail ";

  public static final String GET_OUTGOING_REQUESTS = REQUEST_ROOT_CONTEXT + REQUEST_OPERATION;

  /**
   * Query parameters.
   */
  // TBD add query parameters

  // TODO rename static constants so a reorg on this class doesn't ruin the flow
  private RequestAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
