package com.peoples.banking.partner.adapter.interac.request.impl;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.adapter.interac.request.config.RequestAdapterConstant;
import com.peoples.banking.partner.domain.interac.request.model.*;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

/**
 * Adapter implementation for Interac's <i>Request Management</i> service domain.
 */
@Log4j2
@Service
public class RequestAdapterImpl extends InteracRestAdapter<ErrorModel> implements RequestAdapter {

  /**
   * <i>Interac</i> service domain.
   */
  private static final String SERVICE_DOMAIN = "RequestManagement";

  /**
   * <i>Interac</i> Send Request for Payment endpoint URL.
   */
  private String sendRequestEndpoint = null;

  /**
   * <i>Interac</i> Cancel Request for Payment endpoint URL.
   */
  private String cancelRequestEndpoint = null;

  /**
   * <i>Interac</i> Send Request for Payment endpoint URL.
   */
  private String getRequestPaymentEndpoint = null;

  /**
   * <i>Interac</i> Decline Request for Payment endpoint URL.
   */
  private String declineRequestEndpoint = null;

  /**
   * <i>Interac</i> getIncomingRequestForPayment endpoint URL.
   */
  private String getIncomingRequestForPaymentEndpoint = null;


  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public SendRequestForPaymentResponse requestPayment(String enrolmentId, SendRequestForPaymentRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, request={}", enrolmentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getSendRequestEndpoint();

    // build path N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this
        .execute(endpointUrl, HttpMethod.POST, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
            SendRequestForPaymentResponse.class, null, null);
  }

  /**
   * @return
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public RequestForPaymentResponse getRequestPayment(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || requestId == null || requestId.isBlank()) {
      log.warn("required parameters: enrolmentId={}, requestId={}", enrolmentId, requestId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getRequestPaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RequestAdapterConstant.REQUEST_ID, requestId);

    // build header parameters - N/A

    return this
        .execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
            RequestForPaymentResponse.class, pathParams, null);
  }


  @Override
  @PerfLogger
  public IncomingRequestForPaymentResponse retrieveIncomingRequestPayment(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (enrolmentId==null || enrolmentId.isBlank() || requestId == null || requestId.isBlank() ) {
      log.warn("required parameters: enrolmentId={}, requestId={}", enrolmentId, requestId);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getGetIncomingRequestForPaymentEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RequestAdapterConstant.REQUEST_ID, requestId);

    // build header parameters - N/A

    return this
        .execute(endpointUrl, HttpMethod.GET, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, null,
                 IncomingRequestForPaymentResponse.class, pathParams, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean sendRequestPaymentReminder(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException {
    throw new AdapterException(ErrorCode.NOT_IMPLEMENTED);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean updateRequestPayment(String enrolmentId, String requestId, UpdateRequestForPayment request, String indirectConnectorId)
      throws AdapterException {
    throw new AdapterException(ErrorCode.NOT_IMPLEMENTED);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public Boolean cancelRequestPayment(String enrolmentId, String requestId, CancelRequestForPaymentRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || requestId == null || requestId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, requestId={}, request={}", enrolmentId, requestId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getCancelRequestEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RequestAdapterConstant.REQUEST_ID, requestId);

    // build header parameters - N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
        CancelRequestForPaymentRequest.class, pathParams, null);

    return true;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public DeclineRequestForPaymentResponse declineRequestPayment(String enrolmentId, String requestId, DeclineRequestForPaymentRequest request, String indirectConnectorId)
          throws AdapterException, ResponseException, TBDException, TimeoutException {

    // sanity check
    if (enrolmentId == null || enrolmentId.isBlank() || requestId == null || requestId.isBlank() || request == null) {
      log.warn("required parameters: enrolmentId={}, requestId={}, request={}", enrolmentId, requestId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getDeclineRequestEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(RequestAdapterConstant.REQUEST_ID, requestId);

    // build header parameters - N/A

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    return this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.DEFAULT, enrolmentId, indirectConnectorId, request,
            DeclineRequestForPaymentResponse.class, pathParams, null);
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public OutgoingRequestResponse getOutgoingRequestPayments(String enrolmentId, String indirectConnectorId)
      throws AdapterException {
    throw new AdapterException(ErrorCode.NOT_IMPLEMENTED);
  }

  /**
   * @inheritDoc
   */
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams) {
    // no implementation needed
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return SERVICE_DOMAIN;
  }

  /**
   * Helper utility to construct <i>SendRequest</i> endpoint URL.
   *
   * @return SendRequest endpoint URL
   */
  private String getSendRequestEndpoint() {
    if (sendRequestEndpoint == null) {
      sendRequestEndpoint = this.getBaseUrl() + RequestAdapterConstant.SEND_REQUEST;
    }
    return sendRequestEndpoint;
  }

  /**
   * Helper utility to construct <i>CancelRequest</i> endpoint URL.
   *
   * @return CancelRequest endpoint URL
   */
  private String getCancelRequestEndpoint() {
    if (cancelRequestEndpoint == null) {
      cancelRequestEndpoint = this.getBaseUrl() + RequestAdapterConstant.CANCEL_REQUEST;
    }
    return cancelRequestEndpoint;
  }

  /**
   * Helper utility to construct <i>DeclineRequest</i> endpoint URL.
   *
   * @return DeclineRequest endpoint URL
   */
  private String getDeclineRequestEndpoint() {
    if (declineRequestEndpoint == null) {
      declineRequestEndpoint = this.getBaseUrl() + RequestAdapterConstant.DECLINE_REQUEST;
    }
    return declineRequestEndpoint;
  }

  /**
   * Helper utility to construct <i>GetRequest</i> endpoint URL.
   *
   * @return GetRequest endpoint URL
   */
  private String getRequestPaymentEndpoint() {
    if (getRequestPaymentEndpoint == null) {
      getRequestPaymentEndpoint = this.getBaseUrl() + RequestAdapterConstant.GET_REQUEST;
    }
    return getRequestPaymentEndpoint;
  }

  private String getGetIncomingRequestForPaymentEndpoint() {
    if (getIncomingRequestForPaymentEndpoint == null) {
      getIncomingRequestForPaymentEndpoint = this.getBaseUrl() + RequestAdapterConstant.RETRIEVE_INCOMING_REQUEST;
    }
    return getIncomingRequestForPaymentEndpoint;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }
}
