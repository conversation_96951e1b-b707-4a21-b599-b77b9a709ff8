package com.peoples.banking.partner.adapter.interac.request;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.IncomingRequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.OutgoingRequestResponse;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.UpdateRequestForPayment;

/**
 * Adapter for Interac's <i>Request Management</i> service domain.
 */
public interface RequestAdapter {

  /**
   * Sends a request for payment (ISO20022 payload) with the Interac e-Transfer platform.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  SendRequestForPaymentResponse requestPayment(
      String enrolmentId, SendRequestForPaymentRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieve the request details of an existing request for payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   unique request identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  RequestForPaymentResponse getRequestPayment(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Update the request details of an existing request for payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   unique request identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean sendRequestPaymentReminder(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Sends a reminder for a request for payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   unique request identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @param request     request body payload
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean updateRequestPayment(
      String enrolmentId, String requestId, UpdateRequestForPayment request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Cancels an existing request for for payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   send Money Request clearingSystemReference id
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return boolean
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  Boolean cancelRequestPayment(String enrolmentId, String requestId, CancelRequestForPaymentRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Declines an existing request for for payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   send Money Request clearingSystemReference id
   * @param request     request body payload
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return boolean
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  DeclineRequestForPaymentResponse declineRequestPayment(String enrolmentId, String requestId, DeclineRequestForPaymentRequest request, String indirectConnectorId)
          throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Retrieves list of outgoing request for payments (
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  // TODO confirm usage as API spec mentions we can provide transaction ID
  OutgoingRequestResponse getOutgoingRequestPayments(String enrolmentId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Get Incoming Request for Payment (ISO 20022 message)
   *
   * @param enrolmentId         unique customer identifier on Interac system
   * @param requestId           id of the request
   * @param indirectConnectorId indirect connector to pass if it's not null
   * @return
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  IncomingRequestForPaymentResponse retrieveIncomingRequestPayment(String enrolmentId, String requestId, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;
}
