package com.peoples.banking.api.deadletter.queue.listener.v1.service;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.deadletter.queue.listener.v1.config.DeadLetterQueueKafkaListenerProperty;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class DeadLetterQueueKafkaListenerService {

  @Autowired
  private AmazonClient amazonClient;

  @Autowired
  private DeadLetterQueueKafkaListenerProperty deadLetterQueueKafkaListenerProperty;

  @Transactional
  public void consumeMessage(String message) throws Exception {
    try {
      log.info("Processing Dead Letter message:{}", message);
      File dataFile = createLocalFile(message);
      String s3Url = amazonClient.uploadFile(dataFile.getPath(), "kafka-dlt");
      log.info("Processed Dead Letter s3Url:{}", s3Url);
    } catch (Exception e) {
      throw new KafkaListenerRetryableException("Error while processing dead letter topic", e);
    }
  }

  private File createLocalFile(String message) throws IOException {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd.HHmmssSSS");
    String dateStr = formatter.format(OffsetDateTime.now(ZoneOffset.UTC));
    File dataFile = new File(deadLetterQueueKafkaListenerProperty.getTempFolder(), dateStr + "." + UUID.randomUUID() + ".txt");
    log.info("Create local upload file:{}", dataFile.getAbsolutePath());
    FileWriter myWriter = new FileWriter(dataFile.getAbsolutePath());
    myWriter.write(message);
    myWriter.close();
    return dataFile;
  }
}
