package com.peoples.banking.api.deadletter.queue.listener.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
        "com.peoples.banking.api.deadletter.queue.listener",
        "com.peoples.banking.util"
})
public class DeadLetterKafkaListenerApplication {

  public static void main(String[] args) {
    SpringApplication.run(DeadLetterKafkaListenerApplication.class, args);
  }
}