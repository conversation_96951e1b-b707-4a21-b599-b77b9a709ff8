package com.peoples.banking.api.deadletter.queue.listener.v1.service;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.peoples.banking.api.deadletter.queue.listener.v1.config.DeadLetterQueueConstant;
import com.peoples.banking.api.deadletter.queue.listener.v1.config.DeadLetterQueueKafkaListenerProperty;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Getter
@Log4j2
public class AmazonClient {

  private AmazonS3 s3client;

  @Autowired
  private DeadLetterQueueKafkaListenerProperty deadLetterQueueKafkaListenerProperty;

  @PostConstruct
  private void initializeAmazon() {
    AWSCredentials credentials = new BasicAWSCredentials(deadLetterQueueKafkaListenerProperty.getAccessKey(),
        deadLetterQueueKafkaListenerProperty.getSecretKey());

    s3client = AmazonS3ClientBuilder
        .standard()
        .withCredentials(new AWSStaticCredentialsProvider(credentials))
        .withRegion(Regions.CA_CENTRAL_1)
        .build();
  }

  public String uploadFile(MultipartFile multipartFile) {
    String fileUrl = null;
    try {
      File file = convertMultiPartToFile(multipartFile);
      String fileName = generateFileName(multipartFile);
      fileUrl = deadLetterQueueKafkaListenerProperty.getEndpointUrl() + DeadLetterQueueConstant.FOLDER_SEPARATOR
          + deadLetterQueueKafkaListenerProperty.getBucketName() + DeadLetterQueueConstant.FOLDER_SEPARATOR + fileName;
      uploadFileTos3bucket(fileName, file);
      file.delete();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return fileUrl;
  }

  public String uploadFile(String FilePath, String subFolder) {
    String fileUrl = null;
    try {
      File file = new File(FilePath);
      Path path = Paths.get(FilePath);
      // call getFileName() and get FileName path object
      Path fileName = path.getFileName();

      fileUrl = deadLetterQueueKafkaListenerProperty.getEndpointUrl() + DeadLetterQueueConstant.FOLDER_SEPARATOR
          + deadLetterQueueKafkaListenerProperty.getBucketName() + DeadLetterQueueConstant.FOLDER_SEPARATOR + subFolder
          + DeadLetterQueueConstant.FOLDER_SEPARATOR + fileName.toString();

      log.info("uploadS3File fileUrl={} ; FilePath={} ; subFolder={}", fileUrl, FilePath, subFolder);

      uploadFileTos3bucket(subFolder + DeadLetterQueueConstant.FOLDER_SEPARATOR + fileName, file);

    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return fileUrl;
  }

  private File convertMultiPartToFile(MultipartFile file) throws IOException {
    File convertFile = new File(file.getOriginalFilename());
    FileOutputStream fos = new FileOutputStream(convertFile);
    fos.write(file.getBytes());
    fos.close();
    return convertFile;
  }

  private String generateFileName(MultipartFile multiPart) {
    return new Date().getTime() + "-" + multiPart.getOriginalFilename().replace(" ", "_");
  }

  private void uploadFileTos3bucket(String fileName, File file) {
    s3client.putObject(new PutObjectRequest(deadLetterQueueKafkaListenerProperty.getBucketName(), fileName, file)
        .withCannedAcl(CannedAccessControlList.PublicRead));
  }

  public List<S3ObjectSummary> listObjectsFromS3Bucket() {
    ObjectListing objectListing = s3client.listObjects(deadLetterQueueKafkaListenerProperty.getBucketName());
    return new ArrayList<>(objectListing.getObjectSummaries());
  }

}
