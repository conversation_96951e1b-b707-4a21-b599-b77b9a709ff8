package com.peoples.banking.adapter.pb.anrrouter;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.anrrouter.model.ServiceAccountResponse;

public interface AnrRouterAdapter {

  /**
   * Retrieve the registration details of an existing Customer that is registered on the Interac e-Transfer platform.
   *
   * @param interactionId unique interaction identifier on Baas platform
   * @param accountNumber Account number pass to AnrRouting API
   * @param systemId      system Id pass to AnrRouting API
   * @return
   * @throws AdapterException
   * @throws ResponseException
   * @throws TimeoutException
   */
  ServiceAccountResponse retrieveServiceAccount(String interactionId, String accountNumber, String systemId)
      throws AdapterException, ResponseException, TimeoutException;

  /**
   * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
   *
   * @param response       response body from <i>Interac</i>
   * @param httpStatusCode HTTP status code from downstream system
   * @throws ResponseException response exception from Interac
   */
  void throwResponseException(String response, Integer httpStatusCode) throws ResponseException;
}
