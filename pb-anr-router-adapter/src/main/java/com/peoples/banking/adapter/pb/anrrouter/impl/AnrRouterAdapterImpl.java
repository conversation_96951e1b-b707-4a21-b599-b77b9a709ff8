package com.peoples.banking.adapter.pb.anrrouter.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.pb.anrrouter.AnrRouterAdapter;
import com.peoples.banking.adapter.pb.anrrouter.config.AnrRouterAdapterConstant;
import com.peoples.banking.adapter.pb.anrrouter.config.AnrRouterAdapterProperty;
import com.peoples.banking.domain.system.model.ErrorResponse;
import com.peoples.banking.partner.domain.anrrouter.model.ServiceAccountResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

@Log4j2
@Service
public class AnrRouterAdapterImpl implements AnrRouterAdapter {

  @Autowired
  protected ObjectMapper objectMapper;

  @Autowired
  private AnrRouterAdapterProperty anrRouteProperty;

  @Autowired
  @Qualifier("anrRouterManagementRestTemplate")
  private RestTemplate restTemplate;

  /**
   * <i>BaaS</i> retrieveServiceAccountResponse endpoint URL.
   */
  private String serviceAccountResponseEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ServiceAccountResponse retrieveServiceAccount(String interactionId, String accountNumber, String systemId)
      throws AdapterException, ResponseException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(interactionId, accountNumber, systemId)) {
      log.warn("required parameters:  interactionId={},accountNumber={}, systemId={}",
          interactionId, accountNumber, systemId);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getAccountNumberEndpoint();

    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(AnrRouterAdapterConstant.ACCOUNT_NUMBER, accountNumber);

    // populate HTTP headers
    log.debug("populating HTTP headers");

    HttpHeaders headers = new HttpHeaders();
    // default HTTP httpHeaders
    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    headers.setContentType(MediaType.APPLICATION_JSON);

    headers.add(AnrRouterAdapterConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(AnrRouterAdapterConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(AnrRouterAdapterConstant.HEADER_SYSTEM_ID, systemId);
    headers.add(AnrRouterAdapterConstant.HEADER_SYSTEM_TOKEN, anrRouteProperty.getSystemToken());

    // prepare the retrieveServiceAccount API call
    if(log.isDebugEnabled()) {
      log.info("HTTP headers {}", headers.entrySet().stream()
          .map(Object::toString).collect(Collectors.joining(",")));
    } else {
      log.info("HTTP headers {}", headers.entrySet().stream()
          .filter(e -> !HttpHeaders.AUTHORIZATION.equals(e.getKey()) &&
                  !AnrRouterAdapterConstant.HEADER_SYSTEM_ID.equals(e.getKey()) &&
                  !AnrRouterAdapterConstant.HEADER_SYSTEM_TOKEN.equals(e.getKey()))
          .map(Object::toString).collect(Collectors.joining(",")));
    }

    log.info("initiating request to {}", endpointUrl);

    ServiceAccountResponse accountRouteResponse = null;
    try {

      ResponseEntity<ServiceAccountResponse> response =
          restTemplate.exchange(endpointUrl, HttpMethod.GET, new HttpEntity<>(headers), ServiceAccountResponse.class, params);

      // shield expensive call with debug level check

      log.info("responseBody={}", objectMapper.writeValueAsString(response.getBody()));


      accountRouteResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /**
       * result with no side effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)

      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());

      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // for any READ only operation, there are no side effects, so throw Timeout regardless of reason but do log reason for failure

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return accountRouteResponse;
  }

  /**
   * Returns the <i>customer</i> domain endpoint URL.
   */
  protected String getBaseUrl() {
    return anrRouteProperty.getAnrRouterUrlBase();
  }


  /**
   * Helper utility to construct <i>retrieveServiceAccountResponse</i> endpoint URL.
   *
   * @return retrieveServiceAccountResponse endpoint URL
   */
  public String getAccountNumberEndpoint() {
    if (serviceAccountResponseEndpoint == null) {
      serviceAccountResponseEndpoint = this.getBaseUrl() + AnrRouterAdapterConstant.ACCOUNT_NUMBER_API_ROUTE;
    }
    return serviceAccountResponseEndpoint;
  }

  /**
   * @inheritDoc
   */
  // TODO clean this up, not elegant; make part of abstract class
  @Override
  public void throwResponseException(String response, Integer httpStatusCode) throws ResponseException {

    // sanity check
    if (response != null && !response.isBlank()) {
      ErrorResponse responseModel = null;
      try {
        responseModel = this.convertResponseToDomain(response, ErrorResponse.class);
      } catch (TBDException e) {
        log.warn("cannot convert Customer Management error response into something meaningful");
        throw new ResponseException(httpStatusCode, null, null);
      }

      throw new ResponseException(httpStatusCode, responseModel.getError().get(0).getCode(),
          responseModel.getError().get(0).getAdditionalInformation());
    } else {
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>       the generic object type that the response is converted to
   * @param payload   payload to be converted
   * @param classType the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String payload, Class<T> classType) throws TBDException {
    T response = null;

    // sanity check
    if (payload != null && !payload.isBlank()) {
      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }
}
