package com.peoples.banking.adapter.pb.anrrouter.config;

import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.web.client.RestTemplate;
import com.peoples.banking.adapter.base.config.RestTemplateConfig;

/**
 * Anr Router RestTemplate Configuration
 *
 */
@Configuration
public class AnrRouterRestTemplateConfig extends RestTemplateConfig{

  /**
   * HTTP client.
   */
  @Autowired
  @Qualifier("anrRouteManagementHttpClient")
  CloseableHttpClient httpClient;

  /**
   * Instantiate new <i>RestTemplate</i> instance.
   *
   * @return instantiated object.
   */
  @Bean("anrRouterManagementRestTemplate")
  public RestTemplate restTemplate() {
    return getRestTemplate();
  }

  /**
   * Instantiate new <i>ClientHttpRequestFactory</i> instance.
   *
   * @return instantiated object.
   */
  @Bean("anrRouteManagementClientHttpRequestFactory")
  public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() {
    return getClientHttpRequestFactory();
  }

  /**
   * Instantiate new <i>TaskScheduler</i>, for reaping stale HTTP connections from the pool.
   *
   * @return instantiated object.
   */
  @Bean("anrRouteManagementTaskScheduler")
  public TaskScheduler taskScheduler() {
    return getTaskScheduler();
  }

  /**
   * @inheritDoc
   */
  @Override
  public CloseableHttpClient getHttpClient() {
    return this.httpClient;
  }
}
