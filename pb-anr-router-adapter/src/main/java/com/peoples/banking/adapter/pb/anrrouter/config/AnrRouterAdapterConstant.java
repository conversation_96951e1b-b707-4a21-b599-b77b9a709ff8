package com.peoples.banking.adapter.pb.anrrouter.config;

/**
 * Constants for Baas <i>Anr Router Adapter</i> service domain.
 */
public final class AnrRouterAdapterConstant {

  private AnrRouterAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

  private static final String ROOT_SERVICE_URL = "/v1/router";
  private static final String PARTNER_OPERATION = "/partner";
  public static final String ACCOUNT_NUMBER = "accountNum";

  public static final String ACCOUNT_NUMBER_API_ROUTE =
      ROOT_SERVICE_URL + PARTNER_OPERATION + "/{" + ACCOUNT_NUMBER + "}";

  public static final String HEADER_INTERACTION_ID = "x-pg-interaction-id";
  public static final String HEADER_INTERACTION_TIMESTAMP = "x-pg-interaction-timestamp";
  public static final String HEADER_SYSTEM_ID = "x-pg-system-id";
  public static final String HEADER_SYSTEM_TOKEN = "x-pg-system-token";
}
