package com.peoples.banking.adapter.pb.anrrouter.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class AnrRouterAdapterProperty {

  /**
   * anr router api URL (protocol://host:port).
   */
  @Value("${sys.pb.anr.router.url.base}")
  private String anrRouterUrlBase;

  /**
   * Time to live (maximum age) of a request (in milliseconds).
   * <pre>defaults to 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pb.anr.router.time-to-live:30000}")
  private Integer timeToLive;

  /**
   * HTTP connection keepalive interval (in milliseconds).
   * <pre>default is 20 seconds = 20000 ms</pre>
   */
  @Value("${sys.pb.anr.router.connection.keep-alive:20000}")
  private Integer connectionKeepAlive;

  /**
   * Maximum number of HTTP connections.
   * <pre>default is 25</pre>
   */
  @Value("${sys.pb.anr.router.connection.max:25}")
  private Integer maxHttpConnections;

  /**
   * Delay before reaper removes idle HTTP connections (in milliseconds).
   * <pre>default is 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pb.anr.router.connection.wait-time:30000}")
  private Integer waitTimeIdleConnection;

  /**
   * Connection timeout for requesting a connection from connection manager (in milliseconds).
   * <pre>defaults to 5 seconds = 5000 ms.</pre>
   */
  @Value("${sys.pb.anr.router.connection-request-timeout:5000}")
  private Integer connectionReqTimeout;

  /**
   * Timeout waiting until a connection is established (in milliseconds).
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pb.anr.router.connection.timeout:15000}")
  private Integer connectTimeout;

  /**
   * Read timeout for requests to customer api.
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pb.anr.router.read.timeout:15000}")
  private Integer readTimeout;

  /**
   * System token for anr router
   * <pre>system token to call anr router</pre>
   */
  @Value("${sys.pb.anr.router.system.token}")
  private String systemToken;

}
