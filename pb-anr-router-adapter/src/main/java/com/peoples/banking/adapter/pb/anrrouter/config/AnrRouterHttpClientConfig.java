package com.peoples.banking.adapter.pb.anrrouter.config;

import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.peoples.banking.adapter.base.config.HttpClientConfig;
import com.peoples.banking.adapter.base.exception.AdapterException;

/**
 * Anr Router HttpClient Config- Supports both HTTP and HTTPS - Uses a connection pool to re-use connections and save overhead
 * of creating connections. - Has a custom connection keep-alive strategy (to apply a default
 * keep-alive if one isn't specified) - Starts an idle connection monitor to continuously clean up
 * stale connections.
 */
@Configuration
@EnableScheduling
public class AnrRouterHttpClientConfig extends HttpClientConfig {

  /**
   * Anr Router adapter properties.
   */
  @Autowired
  private AnrRouterAdapterProperty property;

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getMaxHttpConnections() {
    return property.getMaxHttpConnections();
  }

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectionKeepAlive() {
    return property.getConnectionKeepAlive();
  }

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectionReqTimeout() {
    return property.getConnectionReqTimeout();
  }

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectTimeout() {
    return property.getConnectTimeout();
  }

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getReadTimeout() {
    return property.getReadTimeout();
  }

  /**
   * @inheritDoc
   */
  @Override
  protected Integer getWaitTimeIdleConnection() {
    return property.getWaitTimeIdleConnection();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "anrRouteManagementPoolingConnectionManager")
  public PoolingHttpClientConnectionManager poolingConnectionManager() throws AdapterException {
    return getPoolingConnectionManager();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "anrRouteManagementConnectionKeepAliveStrategy")
  public ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
    return getConnectionKeepAliveStrategy();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "anrRouteManagementHttpClient")
  public CloseableHttpClient httpClient() throws AdapterException {
    return getHttpClient();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "anrRouteManagementIdleConnectionMonitor")
  public Runnable idleConnectionMonitor(@Qualifier("anrRouteManagementPoolingConnectionManager")PoolingHttpClientConnectionManager connectionManager) {
    return getIdleConnectionMonitor(connectionManager);
  }
  
}