package com.peoples.banking.partner.adapter.interac.fraud.config;

/**
 * Constants for Interac's <i>Fraud Adapter</i> service domain.
 */
public final class FraudAdapterConstant {
  /**
   * <i>Interac</i> root context path (incl. version)
   */
  public static final String FRAUD_ROOT_CONTEXT = "/fraud-api/v3.5.0";

  public static final String ID = "id";

  public static final String PAYMENTS = "/payments/{";
  public static final String REQUESTS = "/requests/{";

  public static final String UPDATE_PAYMENT_FRAUD_STATUS = FRAUD_ROOT_CONTEXT + PAYMENTS + ID + "}/fraud-status";

  public static final String UPDATE_REQUEST_FOR_PAYMENT_FRAUD_STATUS = FRAUD_ROOT_CONTEXT + REQUESTS + ID + "}/fraud-status";

  public static final String BLOCK_PAYMENT_FRAUD = FRAUD_ROOT_CONTEXT + PAYMENTS + ID + "}/block";

  public static final String UNBLOCK_PAYMENT_FRAUD = FRAUD_ROOT_CONTEXT + PAYMENTS + ID + "}/unblock";

  public static final String DOMESTIC_PRODUCT_CODE = "DOMESTIC";

  /**
   * <i>Interac</i> service domain.
   */
  public static final String SERVICE_DOMAIN = "Fraud";

  // TODO rename static constants so a reorg on this class doesn't ruin the flow

  private FraudAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
