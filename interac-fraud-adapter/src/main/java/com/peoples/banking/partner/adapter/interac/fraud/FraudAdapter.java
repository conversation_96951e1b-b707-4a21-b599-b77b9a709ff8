package com.peoples.banking.partner.adapter.interac.fraud;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.partner.domain.interac.fraud.model.FraudBlockRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.FraudUnblockRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdatePaymentFraudStatusRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdateRequestForPaymentFraudStatusRequest;

/**
 * Adapter for Interac's <i>Fraud Management</i> service domain.
 */
public interface FraudAdapter {

  /**
   * This service is used by a Participant FI to submit to Interac the FI updated fraud status of a payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param paymentId   unique request identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId     indirect connector id
   * @return successful result of operation
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  boolean updatePaymentFraudStatus(String enrolmentId, String paymentId, UpdatePaymentFraudStatusRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * This service is used by a Participant FI to submit to Interac the FI requests updated fraud status of a payment.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param requestId   unique request identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId     indirect connector id
   * @return successful result of operation
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  boolean updateRequestForPaymentFraudStatus(String enrolmentId, String requestId, UpdateRequestForPaymentFraudStatusRequest request,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * This is a service that a Participant FI can use to place a block
   * on a specific Payment in order to prevent it from completing until it is confirmed
   * legitimate.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param paymentId   unique request identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId     indirect connector id
   * @return successful result of operation
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  boolean blockPaymentFraud(String enrolmentId, String paymentId, FraudBlockRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * This service can be used by a Participant FI to unblock a payment
   * that was previously blocked.
   *
   * @param enrolmentId unique customer identifier on Interac system
   * @param paymentId   unique request identifier on Interac system
   * @param request     request body payload
   * @param indirectConnectorId     indirect connector id
   * @return successful result of operation
   * @throws AdapterException, ResponseException, TBDException, TimeoutException
   */
  boolean unblockPaymentFraud(String enrolmentId, String paymentId, FraudUnblockRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;
}
