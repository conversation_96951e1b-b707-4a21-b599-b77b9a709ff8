package com.peoples.banking.partner.adapter.interac.fraud.impl;

import static com.peoples.banking.partner.adapter.interac.fraud.config.FraudAdapterConstant.DOMESTIC_PRODUCT_CODE;
import static com.peoples.banking.partner.adapter.interac.fraud.config.FraudAdapterConstant.SERVICE_DOMAIN;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.fraud.FraudAdapter;
import com.peoples.banking.partner.adapter.interac.fraud.config.FraudAdapterConstant;
import com.peoples.banking.partner.domain.interac.fraud.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.fraud.model.FraudBlockRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.FraudUnblockRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdatePaymentFraudStatusRequest;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdateRequestForPaymentFraudStatusRequest;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

/**
 * Adapter implementation for Interac's <i>Fraud</i> service domain.
 */
@Log4j2
@Service
public class FraudAdapterImpl extends InteracRestAdapter<ErrorModel> implements FraudAdapter {

  /**
   * <i>Interac</i> UpdatePaymentFraudStatus endpoint URL.
   */
  private String updatePaymentFraudStatusEndpoint = null;

  /**
   * <i>Interac</i> BlockPaymentFraud endpoint URL.
   */
  private String blockPaymentFraudEndpoint = null;

  /**
   * <i>Interac</i> UnBlockPaymentFraud endpoint URL.
   */
  private String unblockPaymentFraudEndpoint = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public boolean updatePaymentFraudStatus(String enrolmentId, String paymentId, UpdatePaymentFraudStatusRequest request,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(enrolmentId, paymentId) || request == null) {
      log.warn("required parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, paymentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getUpdatePaymentFraudStatusEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(FraudAdapterConstant.ID, paymentId);
    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_PRODUCT_CODE, DOMESTIC_PRODUCT_CODE);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.PATCH, InteracRequestType.UPDATE_PAYMENT_FRAUD_STATUS, enrolmentId, indirectConnectorId, request,
        String.class, pathParams, headerParams);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public boolean updateRequestForPaymentFraudStatus(String enrolmentId, String requestId, UpdateRequestForPaymentFraudStatusRequest request,
      String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(enrolmentId, requestId) || request == null) {
      log.warn("required parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, requestId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getUpdateRequestForPaymentFraudStatusEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(FraudAdapterConstant.ID, requestId);
    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_PRODUCT_CODE, DOMESTIC_PRODUCT_CODE);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.PATCH, InteracRequestType.UPDATE_PAYMENT_FRAUD_STATUS, enrolmentId, indirectConnectorId, request,
        String.class, pathParams, headerParams);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public boolean blockPaymentFraud(String enrolmentId, String paymentId, FraudBlockRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(enrolmentId, paymentId) || request == null) {
      log.warn("required parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, paymentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getBlockPaymentFraudEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(FraudAdapterConstant.ID, paymentId);
    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_PRODUCT_CODE, DOMESTIC_PRODUCT_CODE);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.BLOCK_PAYMENT_FRAUD, enrolmentId, indirectConnectorId, request,
        String.class, pathParams, headerParams);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public boolean unblockPaymentFraud(String enrolmentId, String paymentId, FraudUnblockRequest request, String indirectConnectorId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (StringUtils.isAnyBlank(enrolmentId, paymentId) || request == null) {
      log.warn("required parameters: enrolmentId={}, paymentId={}, request={}", enrolmentId, paymentId, request);

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getUnBlockPaymentFraudEndpoint();

    // build path parameters
    Map<String, String> pathParams = new HashMap<>();
    pathParams.put(FraudAdapterConstant.ID, paymentId);
    // build header parameters
    Map<String, String> headerParams = new HashMap<>();
    headerParams.put(InteracRestAdapterConstant.HEADER_PRODUCT_CODE, DOMESTIC_PRODUCT_CODE);

    // all exception handling dealt with by InteracRestAdapter::execute(...)
    // discard response, none supplied
    this.execute(endpointUrl, HttpMethod.POST, InteracRequestType.UNBLOCK_PAYMENT_FRAUD, enrolmentId, indirectConnectorId, request,
        String.class, pathParams, headerParams);

    return Boolean.TRUE;
  }

  /**
   * @inheritDoc
   */
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams) {

    // x-et-product-code applies to UPDATE_PAYMENT_STATUS
    if (interacRequestType.equals(InteracRequestType.UPDATE_PAYMENT_FRAUD_STATUS)) {
      String productCode = (String) headerParams.get(InteracRestAdapterConstant.HEADER_PRODUCT_CODE);

      if (!StringUtils.isBlank(productCode)) {
        httpHeaders.set(InteracRestAdapterConstant.HEADER_PRODUCT_CODE, productCode);
      }

      log.info("{}={}", InteracRestAdapterConstant.HEADER_PRODUCT_CODE,
              httpHeaders.get(InteracRestAdapterConstant.HEADER_PRODUCT_CODE));
    }
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return SERVICE_DOMAIN;
  }

  /**
   * Helper utility to construct <i>UpdatePaymentFraudStatus/i> endpoint URL.
   *
   * @return UpdatePaymentFraudStatus endpoint URL
   */
  private String getUpdateRequestForPaymentFraudStatusEndpoint() {
    if (updatePaymentFraudStatusEndpoint == null) {
      updatePaymentFraudStatusEndpoint = this.getBaseUrl() + FraudAdapterConstant.UPDATE_REQUEST_FOR_PAYMENT_FRAUD_STATUS;
    }
    return updatePaymentFraudStatusEndpoint;
  }

  /**
   * Helper utility to construct <i>UpdatePaymentFraudStatus/i> endpoint URL.
   *
   * @return UpdatePaymentFraudStatus endpoint URL
   */
  private String getUpdatePaymentFraudStatusEndpoint() {
    if (updatePaymentFraudStatusEndpoint == null) {
      updatePaymentFraudStatusEndpoint = this.getBaseUrl() + FraudAdapterConstant.UPDATE_PAYMENT_FRAUD_STATUS;
    }
    return updatePaymentFraudStatusEndpoint;
  }

  /**
   * Helper utility to construct <i>BlockPaymentFraud/i> endpoint URL.
   *
   * @return BlockPaymentFraud endpoint URL
   */
  private String getBlockPaymentFraudEndpoint() {
    if (blockPaymentFraudEndpoint == null) {
      blockPaymentFraudEndpoint = this.getBaseUrl() + FraudAdapterConstant.BLOCK_PAYMENT_FRAUD;
    }
    return blockPaymentFraudEndpoint;
  }

  /**
   * Helper utility to construct <i>UnBlockPaymentFraud/i> endpoint URL.
   *
   * @return UnBlockPaymentFraud endpoint URL
   */
  private String getUnBlockPaymentFraudEndpoint() {
    if (unblockPaymentFraudEndpoint == null) {
      unblockPaymentFraudEndpoint = this.getBaseUrl() + FraudAdapterConstant.UNBLOCK_PAYMENT_FRAUD;
    }
    return unblockPaymentFraudEndpoint;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }

}
