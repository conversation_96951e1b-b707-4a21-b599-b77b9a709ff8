package com.peoples.banking.adapter.pb.account.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.base.util.IdGeneratorUtil;
import com.peoples.banking.adapter.base.util.ValidateServiceAccountProfile;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.account.config.AccountAdapterConstant;
import com.peoples.banking.domain.account.model.AccountEligibilityRequest;
import com.peoples.banking.domain.account.model.AccountEligibilityResponse;
import com.peoples.banking.domain.account.model.AccountTransactionRequest;
import com.peoples.banking.domain.account.model.AccountTransactionResponse;
import com.peoples.banking.domain.account.model.AccountTransactionReversalRequest;
import com.peoples.banking.domain.account.model.AccountTransactionReversalResponse;
import com.peoples.banking.domain.account.model.ErrorResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

@Service
@Log4j2
@Setter
public class AccountAdapterImpl implements AccountAdapter {

  @Autowired
  protected ObjectMapper objectMapper;

  @Autowired
  @Qualifier("accountManagementRestTemplate")
  private RestTemplate restTemplate;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public AccountEligibilityResponse accountEligibility(String instBranchAccountNumber, AccountEligibilityRequest request,
      ServiceAccountProfile profile)
      throws AdapterException, TimeoutException, TBDException, ResponseException {
    // sanity check
    if (request == null || instBranchAccountNumber == null || instBranchAccountNumber.isBlank() ||
        !ValidateServiceAccountProfile.isValidServiceAccountProfile(profile)) {
      log.warn("required parameters: request={}", request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    String payload = null;
    try {
      payload = objectMapper.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      // should NOT be triggered, as we validate this during initialization
      log.error("failed on serializing request payload", e);
      throw new AdapterException("failed on serializing AccountEligibilityRequest payload", ErrorCode.UNEXPECTED_EXCEPTION);
    }
    log.info("HTTP payload (raw)={}", payload);


    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(AccountAdapterConstant.ACCOUNT_NUMBER, instBranchAccountNumber);

    // populate HTTP headers
    log.debug("populating HTTP headers");

    // populate API key in header, and AUTH token if required
    HttpHeaders headers = populateHeaders(profile.getApiToken(), profile.getAuthType(), profile.getAuthToken());

    // prepare the account eligibility API call
    log.debug("prepare the account eligibility API call");
    AccountEligibilityResponse accountEligibilityResponse = null;
    try {
      HttpEntity<AccountEligibilityRequest> httpEntity = new HttpEntity<>(request, headers);

      log.info("initiating request to {}", profile.getEndpointUrl());
      ResponseEntity<AccountEligibilityResponse> response = restTemplate
          .exchange(profile.getEndpointUrl(), HttpMethod.POST, httpEntity, AccountEligibilityResponse.class, params);

      // shield expensive call with debug level check
      log.info("responseBody={}", objectMapper.writeValueAsString(response.getBody()));

      accountEligibilityResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /**
       * result with no side effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        log.debug("http status: {}", e.getRawStatusCode());
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // for any READ only operation, there are no side effects, so throw Timeout regardless of reason but do log reason for failure

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error(e.getMessage(), e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return accountEligibilityResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public AccountTransactionResponse accountTransaction(String instBranchAccountNumber, AccountTransactionRequest request,
      ServiceAccountProfile profile)
      throws AdapterException, TBDException, TimeoutException, ResponseException {
    // sanity check
    if (request == null || instBranchAccountNumber == null || instBranchAccountNumber.isBlank() ||
        !ValidateServiceAccountProfile.isValidServiceAccountProfile(profile)) {
      log.warn("required parameters: request={}", request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    String payload = null;
    try {
      payload = objectMapper.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      // should NOT be triggered, as we validate this during initialization
      log.error("failed on serializing request payload", e);
      throw new AdapterException("failed on serializing AccountTransactionRequest payload", ErrorCode.UNEXPECTED_EXCEPTION);
    }
    log.info("HTTP payload raw={}", payload);


    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(AccountAdapterConstant.ACCOUNT_NUMBER, instBranchAccountNumber);

    // populate HTTP headers
    log.debug("populating HTTP headers");

    // populate API key in header, and AUTH token if required
    HttpHeaders headers = populateHeaders(profile.getApiToken(), profile.getAuthType(), profile.getAuthToken());

    // prepare the account eligibility API call
    log.debug("prepare the account transaction API call");
    AccountTransactionResponse accountTransactionResponse = null;

    try {
      HttpEntity<AccountTransactionRequest> httpEntity = new HttpEntity<>(request, headers);

      log.info("initiating request to {}", profile.getEndpointUrl());
      ResponseEntity<AccountTransactionResponse> response = restTemplate
          .exchange(profile.getEndpointUrl(), HttpMethod.POST, httpEntity, AccountTransactionResponse.class, params);

      // shield expensive call with debug level check
      log.info("responseBody={}", objectMapper.writeValueAsString(response.getBody()));

      accountTransactionResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /**
       * result with no side effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        log.debug("http status: {}", e.getRawStatusCode());
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // for any READ only operation, there are no side effects, so throw Timeout regardless of reason but do log reason for failure

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error(e.getMessage(), e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return accountTransactionResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public AccountTransactionReversalResponse accountTransactionReversal(String instBranchAccountNumber, String transactionRefId,
      AccountTransactionReversalRequest request, ServiceAccountProfile profile)
      throws AdapterException, TBDException, TimeoutException, ResponseException {

    // sanity check
    if (request == null || instBranchAccountNumber == null || instBranchAccountNumber.isBlank() ||
        !ValidateServiceAccountProfile.isValidServiceAccountProfile(profile)) {
      log.warn("required parameters: request={}", request);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    String payload = null;
    try {
      payload = objectMapper.writeValueAsString(request);
    } catch (JsonProcessingException e) {
      // should NOT be triggered, as we validate this during initialization
      log.error("failed on serializing request payload", e);
      throw new AdapterException("failed on serializing AccountTransactionReversalRequest payload", ErrorCode.UNEXPECTED_EXCEPTION);
    }
    log.info("HTTP payload (raw)={}", payload);


    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(AccountAdapterConstant.ACCOUNT_NUMBER, instBranchAccountNumber);
    params.put(AccountAdapterConstant.TRANSACTION_ID, transactionRefId);

    // populate HTTP headers
    log.debug("populating HTTP headers");

    // populate API key in header, and AUTH token if required
    HttpHeaders headers = populateHeaders(profile.getApiToken(), profile.getAuthType(), profile.getAuthToken());

    // prepare the account eligibility API call
    log.debug("prepare the account transaction API call");
    AccountTransactionReversalResponse accountTransactionReversalResponse = null;

    try {
      HttpEntity<AccountTransactionReversalRequest> httpEntity = new HttpEntity<>(request, headers);

      log.info("initiating request to {}", profile.getEndpointUrl());
      ResponseEntity<AccountTransactionReversalResponse> response = restTemplate
          .exchange(profile.getEndpointUrl(), HttpMethod.POST, httpEntity, AccountTransactionReversalResponse.class, params);

      // shield expensive call with debug level check
      log.info("responseBody={}", objectMapper.writeValueAsString(response.getBody()));


      accountTransactionReversalResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /**
       * result with no side effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());

      if (e.getRawStatusCode() >= 500) {
        log.debug("http status: {}", e.getRawStatusCode());
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // for any READ only operation, there are no side effects, so throw Timeout regardless of reason but do log reason for failure

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error(e.getMessage(), e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return accountTransactionReversalResponse;
  }

  /**
   * @inheritDoc
   */
  // TODO clean this up, not elegant; make part of abstract class
  @Override
  public void throwResponseException(String response, Integer httpStatusCode) throws ResponseException {

    // sanity check
    if (response != null && !response.isBlank()) {
      ErrorResponse responseModel = null;
      try {
        responseModel = this.convertResponseToDomain(response, ErrorResponse.class);
      } catch (TBDException e) {
        log.warn("cannot convert Customer Management error response into something meaningful");
        throw new ResponseException(httpStatusCode, null, null);
      }

      throw new ResponseException(httpStatusCode, responseModel.getError().get(0).getCode(),
          responseModel.getError().get(0).getAdditionalInformation());
    } else {
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>       the generic object type that the response is converted to
   * @param payload   payload to be converted
   * @param classType the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String payload, Class<T> classType) throws TBDException {
    T response = null;

    // sanity check
    if (payload != null && !payload.isBlank()) {
      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }

  /**
   * Utility function to populate headers.
   *
   * @param apiToken  the API token
   * @param authType
   * @param authToken
   * @return
   */
  private HttpHeaders populateHeaders(String apiToken, ServiceAccountAuthType authType, String authToken) {
    HttpHeaders headers = new HttpHeaders();

    // default HTTP httpHeaders
    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    headers.setContentType(MediaType.APPLICATION_JSON);

    // interaction ID
    String interactionId = IdGeneratorUtil.generateRequestId();

    headers.add(AccountAdapterConstant.HEADER_INTERACTION_ID, interactionId);

    // API key
    headers.add(AccountAdapterConstant.HEADER_API_TOKEN, apiToken);

    // interaction timestamp
    headers.add(AccountAdapterConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());

    // Authentication
    if (ServiceAccountAuthType.BASIC_AUTH.equals(authType)) {
      // basic authentication -- encode only
      String encodedCredentials = new String(Base64.encodeBase64(authToken.getBytes()));
      headers.add(HttpHeaders.AUTHORIZATION, "Basic " + encodedCredentials);
    }

    if(log.isDebugEnabled()) {
      log.info("HTTP headers {}", headers.entrySet().stream()
          .map(Object::toString).collect(Collectors.joining(",")));
    } else {
      log.info("HTTP headers {}", headers.entrySet().stream()
          .filter(e -> !HttpHeaders.AUTHORIZATION.equals(e.getKey()) &&
                       !AccountAdapterConstant.HEADER_API_TOKEN.equals(e.getKey()))
          .map(Object::toString).collect(Collectors.joining(",")));
    }



    return headers;
  }
}
