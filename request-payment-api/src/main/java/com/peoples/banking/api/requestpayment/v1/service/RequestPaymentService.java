package com.peoples.banking.api.requestpayment.v1.service;

import com.google.common.base.Strings;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentProperty;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonJsonDto;
import com.peoples.banking.api.requestpayment.v1.mapper.CancelRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.DeclineRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.RetrieveIncomingRequestPaymentMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.RetrieveRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.validator.constraint.NotNullContactInformation;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.RetrieveIncomingRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.RetrieveIncomingRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.*;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.config.NetworkErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.service.APIService;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.validator.constraint.LimitNotExceeded;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.constraints.Digits;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

/**
 * The request payment service class for processing business logic validation, mapping and calling Interac service.
 */
@Log4j2
@Service
public class RequestPaymentService extends APIService {

  @Autowired
  private Validator validator;

  @Autowired
  private RequestPaymentProperty requestPaymentProperty;

  @Autowired
  private InteracAdapterProperty interacAdaptorProperty;

  @Autowired
  private RequestAdapter requestAdapter;

  @Autowired
  private CustomerAdapter customerAdapter;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @Autowired
  private CreateRequestPaymentRequestMapper createRequestPaymentRequestMapper;

  @Autowired
  private RetrieveRequestPaymentRequestMapper retrieveRequestPaymentRequestMapper;

  @Autowired
  private PaymentsEntityMapper paymentsEntityMapper;

  @Autowired
  private RetrieveIncomingRequestPaymentMapper retrieveIncomingRequestPaymentMapper;

  @Autowired
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;

  @Autowired
  private CancelRequestPaymentRequestMapper cancelRequestPaymentRequestMapper;

  @Autowired
  private DeclineRequestPaymentRequestMapper declineRequestPaymentRequestMapper;

  /**
   * Create Request Payment service
   *
   * @param createRequestPaymentRequest the request payment request
   * @param serviceAccountResponse      the service account response
   * @param interactionId               the interaction identifier
   * @return RequestPaymentResponse the request payment response
   * @throws Exception the exception
   */
  @PerfLogger
  public CreateRequestPaymentResponse createRequestPayment(CreateRequestPaymentRequest createRequestPaymentRequest,
      ServiceAccountResponse serviceAccountResponse, String interactionId) throws Exception {
    CreateRequestPaymentResponse response;
    PaymentDto paymentDto;
    Payments payments;

    String serviceAccountRefId = serviceAccountResponse.getRefId();
    // call customer API to validate customer and get enrollment id
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = retrieveCustomerProduct(serviceAccountRefId,
        interactionId, createRequestPaymentRequest.getCustomerId(), APICommonUtilConstant.ETRANSFER);

    try {
      String paymentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(
          paymentsRepository.getNextPaymentRefIdSequence(),
          RequestPaymentConstant.PAYMENT_REF_ID_LENGTH,
          RequestPaymentConstant.PAYMENT_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);

      // convert request to DTO
      paymentDto = createRequestPaymentRequestMapper
          .createRequestPaymentRequestToPaymentDto(createRequestPaymentRequest, interacAdaptorProperty.getFiid(),
              retrieveCustomerProductResponse.getEnrollmentRefId(), serviceAccountRefId, paymentRefId, NetworkChannelType.EMT,
              NetworkPaymentType.REQUEST_FOR_PAYMENT, requestPaymentProperty.getExpiryDays(), serviceAccountResponse.getConnectorType().getValue(),
              buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

      // validate customer request data
      Set<ConstraintViolation<PaymentDto>> constraintViolations = validator.validate(paymentDto);

      // validate DTO
      if (constraintViolations.size() > 0) {
        log.debug("{} error(s) is/are found while validating DTO", constraintViolations.size());
        // populate validation error detail
        List<ErrorEntity> errorList = convertViolationToValidationError(constraintViolations);
        throw new ValidationException(errorList);
      }

      log.debug("preparing payment with refId={}, customerId={}", paymentRefId, createRequestPaymentRequest.getCustomerId());

      // TODO REMOVE TEMP FIX
      OffsetDateTime tempFix = paymentDto.getCreatedDate().minus(Duration.ofSeconds(15l));
      paymentDto.setCreatedDate(tempFix);

      // map dto to Interac's SendRequestForPaymentRequest
      SendRequestForPaymentRequest sendRequestForPaymentRequest =
          createRequestPaymentRequestMapper.paymentDtoToSendRequestForPaymentRequest(paymentDto);

      // call interac adapter
      SendRequestForPaymentResponse sendRequestForPaymentResponse =
          invokeNetworkRequestPayment(retrieveCustomerProductResponse.getEnrollmentRefId(), sendRequestForPaymentRequest, paymentDto,
              serviceAccountResponse);

      // update PaymentDto with Interac's SendRequestForPaymentResponse
      paymentDto = createRequestPaymentRequestMapper.sendRequestForPaymentResponseToPaymentDto(paymentDto, sendRequestForPaymentResponse);

      // map paymentDto to Payments entity
      payments = paymentsEntityMapper.paymentDtoToPayments(paymentDto);

      // persist request payment
      persistRequestPayment(payments, null);

      log.debug("request payment persisted, status={}", payments.getStatus());

      // map paymentDto to RequestPaymentResponse
      response = createRequestPaymentRequestMapper.paymentDtoToCreateRequestPaymentResponse(paymentDto);

    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception is equivalent to PTC's ServiceException
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (ServiceException e) {
      throw e;
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    // return RequestPaymentResponse;
    return response;
  }


  /**
   * The cancel request payment request service
   *
   * @param cancelRequestPaymentRequest the cancel request payment request
   * @param requestId                   the PTC generated request reference id
   * @param serviceAccountResponse      the service account response
   * @return boolean the result of cancel request payment
   * @throws Exception the exception
   */
  @PerfLogger
  public boolean cancelRequestPayment(CancelRequestPaymentRequest cancelRequestPaymentRequest, String requestId, ServiceAccountResponse serviceAccountResponse,
      String interactionId)
      throws Exception {

    Boolean result;
    PaymentDto paymentDto;
    Payments payments;
    String serviceAccountRefId = serviceAccountResponse.getRefId();

    // call customer API to validate customer and get enrollment id
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = retrieveCustomerProduct(serviceAccountRefId,
        interactionId, cancelRequestPaymentRequest.getCustomerId(), APICommonUtilConstant.ETRANSFER);

    try {
      // use external id try find the exist payment in system
      Optional<Payments> paymentsOption = paymentsRepository
          .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestId,
              NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);

      if (paymentsOption.isPresent()) {
        payments = paymentsOption.get();

        // Check if the customerID match, if not throw exception
        if (!payments.getCustomerRefId().equals(cancelRequestPaymentRequest.getCustomerId())) {
          log.warn("The customer id does not match to the original request payment");
          throw new ValidationException(ErrorProperty.INVALID_CUSTOMER.name());
        }

        // map cancelRequest + Payments to paymentDto
        paymentDto =
            cancelRequestPaymentRequestMapper.paymentsAndCancelRequestPaymentRequestToPaymentDto(payments, cancelRequestPaymentRequest);

        // validate DTO
        // validate customer request data
        Set<ConstraintViolation<PaymentDto>> constraintViolations = validator.validate(paymentDto);
        if (constraintViolations.size() > 0) {
          log.debug("{} error(s) is/are found while validating DTO", constraintViolations.size());
          // populate validation error detail
          List<ErrorEntity> errorList = convertViolationToValidationError(constraintViolations);
          throw new ValidationException(errorList);
        }

        //checking if the current status is eligible to be transitioned to CANCELLED
        if (PaymentStatus.CANCELLED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {

          log.debug("Prepare to cancel request payment {}", requestId);
          // map dto to Interac's CancelRequestForPaymentRequest
          CancelRequestForPaymentRequest cancelRequestForPaymentRequest =
              cancelRequestPaymentRequestMapper.paymentDtoToCancelRequestPaymentRequest(paymentDto);

          // call interac adapter
          result = requestAdapter
              .cancelRequestPayment(retrieveCustomerProductResponse.getEnrollmentRefId(), paymentDto.getNetworkPaymentRefId(),
                  cancelRequestForPaymentRequest, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

          // if cancel success, we will update DB record
          if (result) {
            paymentDto.setStatus(PaymentStatus.CANCELLED);
            paymentDto.setUpdatedDate(DateUtil.getCurrentUTCDateTime());
            Payments newPayments = paymentsEntityMapper.paymentDtoToPayments(paymentDto);

            // persist success cancel request payment
            persistRequestPayment(newPayments, payments);

            log.debug("request payment persisted, status={}", newPayments.getStatus());
          }
        } else {
          // if payment status is not ACCEPTED, QUEUED, AVAILABLE, we return back an error code that says
          // invalid payment status
          throw new ValidationException(ErrorProperty.INVALID_PAYMENT_STATUS.name());
        }
      } else {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application ResourceNotFound exception
      throw e;
    } catch (JpaSystemException e) {// Database exception is equivalent to PTC's ServiceException
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout, Read timeout or adapter error causes the application to proceed the next
        // step, wrap it to ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

      } else {
        // Interac return error
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("PTC converts the Network Service Error Code to {}", errorCode);
          if (ErrorProperty.RESOURCE_NOT_FOUND.name().equals(errorCode)) {
            throw new ResourceNotFoundException(errorCode, e);
          } else {
            throw new ValidationException(errorCode, e);
          }
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return result;
  }

  /**
   * The Retrieve RequestPayment service to validate the request, retrieve RequestPayment data from DB call interac side to get status
   * update check if we need update DB . and return the RequestPayment profile in response.
   *
   * @param requestId           the external request payment id
   * @param serviceAccountResponse the service account response
   * @return RetrieveRequestPaymentResponse the RetrieveRequestPayment Response
   * @throws Exception the exception
   */
  @PerfLogger
  public RetrieveRequestPaymentResponse retrieveRequestPayment(String requestId, ServiceAccountResponse serviceAccountResponse) throws Exception {
    RetrieveRequestPaymentResponse retrieveRequestPaymentResponse;
    PaymentDto paymentDto;
    Payments payments;
    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Payments> paymentsOption = paymentsRepository
          .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestId,
              NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);

      if (paymentsOption.isPresent()) {
        payments = paymentsOption.get();

        // map Payments to paymentDto
        paymentDto = paymentsEntityMapper.paymentsToPaymentDto(payments);

        if (!serviceAccountRefId.equals(paymentDto.getServiceAccountRefId())) {
          log.warn("The Service Account Reference Id does not match to the original request payment");
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }

        log.debug("Prepare to retrieve request payment {}", requestId);
        //call the adapter get RequestForPaymentResponse
        RequestForPaymentResponse requestForPaymentResponse = requestAdapter
            .getRequestPayment(paymentDto.getNetworkEnrollmentId(), paymentDto.getNetworkPaymentRefId(),
                buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

        //first check if our status matched to interac status, if they match, we did not need update DB
        if (!isStatusMatched(requestForPaymentResponse.getRequestStatus(), paymentDto.getStatus())) {
          if (!PaymentStatus.AVAILABLE.equals(paymentDto.getStatus()) && !isInteracFinalStatus(
              requestForPaymentResponse.getRequestStatus())) {
            //if interac side is not final status. our side is final status we should log an warning but do not update in DB
            log.warn("RetrieveRequestPayment Interac status:{}, PTC Status:{}, no update", requestForPaymentResponse.getRequestStatus(),
                paymentDto.getStatus());
          } else {
            //synchronize the status with Interac
            boolean dirtyFlag;

            dirtyFlag = syncStatus(requestForPaymentResponse.getRequestStatus(), paymentDto);

            log.debug("To refresh records in our DB = {}", dirtyFlag);
            if (dirtyFlag) {
              //Update the payment update date
              paymentDto.setUpdatedDate(DateUtil.getCurrentUTCDateTime());
              //get new payment status
              Payments newPayments = paymentsEntityMapper.paymentDtoToPayments(paymentDto);
              // persist success cancel request payment
              persistRequestPayment(newPayments, payments);
            }
          }
        }
        final PaymentDto fulfillment = paymentsRepository.findNotFailedByParentId(payments.getId()).map(paymentsEntityMapper::paymentsToPaymentDto).orElse(null);
        //at the end we will map the paymentDto back to retrieveRequestPaymentResponse and return to client side
        retrieveRequestPaymentResponse = retrieveRequestPaymentRequestMapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto, fulfillment);

        log.debug("retrieved request for payment");
      } else {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { //application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout, Read timeout or adapter error causes the application to proceed the next
        // step, wrap it to ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
      } else {
        // Interac return error
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("PTC converts the Network Service Error Code to {}", errorCode);
          if (ErrorProperty.RESOURCE_NOT_FOUND.name()
              .equals(errorCode)) {
            //for any resource not found, we need a special log event for this
            throw new ResourceNotFoundException(errorCode, e);
          } else {
            throw new ValidationException(errorCode, e);
          }
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveRequestPaymentResponse;
  }

  /**
   * decline Payment
   * <p>
   *
   * @param declineRequestPaymentRequest  the DeclineRequestPaymentRequest of PTC
   * @param networkRequestRefId           the Interac generated request reference id
   * @param serviceAccountResponse the business partner's service account
   * @return boolean suggest if success or failed
   * @throws Exception the exception
   */
  @PerfLogger
  public boolean declineRequestPayment(DeclineRequestPaymentRequest declineRequestPaymentRequest, String networkRequestRefId, ServiceAccountResponse serviceAccountResponse,
                                       String interactionId) throws Exception {
    boolean result;
    PaymentDto paymentDto;
    String serviceAccountRefId = serviceAccountResponse.getRefId();

    // call customer API to validate customer and get enrollment id
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = retrieveCustomerProduct(serviceAccountRefId,
            interactionId, declineRequestPaymentRequest.getCustomerId(), APICommonUtilConstant.ETRANSFER);

    try {
        // map declineRequest + Payments to paymentDto
        paymentDto =
                declineRequestPaymentRequestMapper.declineRequestPaymentRequestToPaymentDto(declineRequestPaymentRequest,
                        networkRequestRefId, serviceAccountResponse, retrieveCustomerProductResponse, interacAdaptorProperty.getFiid(), NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT);

        // validate DTO
        // validate customer request data
        Set<ConstraintViolation<PaymentDto>> constraintViolations = validator.validate(paymentDto);
        if (constraintViolations.size() > 0) {
          log.debug("{} error(s) is/are found while validating DTO", constraintViolations.size());
          // populate validation error detail
          List<ErrorEntity> errorList = convertViolationToValidationError(constraintViolations);
          throw new ValidationException(errorList);
        }

        log.debug("Prepare to decline request payment {}", networkRequestRefId);
        // map dto to Interac's DeclineRequestForPaymentRequest
        DeclineRequestForPaymentRequest declineRequestForPaymentRequest =
                declineRequestPaymentRequestMapper.paymentDtoToDeclineRequestPaymentRequest(paymentDto);

        // call interac adapter
        result = !Objects.isNull(requestAdapter
                  .declineRequestPayment(paymentDto.getNetworkEnrollmentId(), paymentDto.getNetworkPaymentRefId(),
                          declineRequestForPaymentRequest, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId())));
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application ResourceNotFound exception
      throw e;
    } catch (JpaSystemException e) {// Database exception is equivalent to PTC's ServiceException
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout, Read timeout or adapter error causes the application to proceed the next
        // step, wrap it to ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

      } else {
        // Interac return error
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("PTC converts the Network Service Error Code to {}", errorCode);
          if (ErrorProperty.RESOURCE_NOT_FOUND.name().equals(errorCode)) {
            throw new ResourceNotFoundException(errorCode, e);
          } else {
            throw new ValidationException(errorCode, e);
          }
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return result;
  }

  @PerfLogger
  public RetrieveIncomingRequestPaymentResponse retrieveIncomingRequestPayment(RetrieveIncomingRequestPaymentRequest request,
                                                                               ServiceAccountResponse serviceAccountResponse, String interactionId) throws Exception{
    RetrieveIncomingRequestPaymentResponse retrieveIncomingRequestPaymentResponse;
    PaymentDto paymentDto;
    try {
      RetrieveCustomerProductResponse retrieveCustomerProductResponse = retrieveCustomerProduct(serviceAccountResponse.getRefId(),
                                                                                                interactionId,
                                                                                                request.getCustomerId(),
                                                                                                APICommonUtilConstant.ETRANSFER);
      if (retrieveCustomerProductResponse.getActive()==null || !retrieveCustomerProductResponse.getActive()) {
        throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
      }
        // map Payments to paymentDto
      paymentDto = retrieveIncomingRequestPaymentMapper
          .retrieveIncomingRequestPaymentToPaymentDto(request, interacAdaptorProperty.getFiid(),
                                                      retrieveCustomerProductResponse.getEnrollmentRefId(), serviceAccountResponse, NetworkChannelType.EMT,
                                                      NetworkPaymentType.REQUEST_FOR_PAYMENT, requestPaymentProperty.getExpiryDays());

      // validate customer request data
      Set<ConstraintViolation<PaymentDto>> constraintViolations = validator.validate(paymentDto);

      // validate DTO
      if (constraintViolations.size() > 0) {
        log.debug("{} error(s) is/are found while validating DTO", constraintViolations.size());
        // populate validation error detail
        List<ErrorEntity> errorList = convertViolationToValidationError(constraintViolations);
        throw new ValidationException(errorList);
      }

      IncomingRequestForPaymentResponse interacResponse = requestAdapter.retrieveIncomingRequestPayment(retrieveCustomerProductResponse.getEnrollmentRefId(),
                                                                                                        request.getNetworkRequestRefId(),
                                                                                                        buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

      paymentDto = retrieveIncomingRequestPaymentMapper.incomingRequestForPaymentResponseToPaymentDto(paymentDto, interacResponse);
      retrieveIncomingRequestPaymentResponse = retrieveIncomingRequestPaymentMapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto);
      log.debug("retrieved request for payment");
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { //application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout, Read timeout or adapter error causes the application to proceed the next
        // step, wrap it to ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
      } else {
        // Interac return error
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        String interacCode = ((ResponseException) e).getResponseCode();
        log.debug("Network Service return error {}", interacCode);

        String errorCode = NetworkErrorProperty.getByErrorCode(interacCode);
        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("PTC converts the Network Service Error Code to {}", errorCode);
          if (ErrorProperty.RESOURCE_NOT_FOUND.name()
                                              .equals(errorCode)) {
            //for any resource not found, we need a special log event for this
            throw new ResourceNotFoundException(errorCode, e);
          } else {
            throw new ValidationException(errorCode, e);
          }
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveIncomingRequestPaymentResponse;
  }

  /**
   * implementation of constructing the request payment's validation error
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    ErrorEntity error = new ErrorEntity();

    if (annotation instanceof LimitNotExceeded) {
      error.setErrorCode(ErrorProperty.LIMIT_EXCEEDED.name());
    } else if (annotation instanceof NotNullContactInformation) {
      error.setErrorCode(ErrorProperty.MISSING_FIELD.name());
    } else if (annotation instanceof Digits) {
      error.setErrorCode(ErrorProperty.INVALID_AMOUNT_DIGITS.name());
    } else {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());
    }

    return error;
  }

  /**
   * The method of check current payment status in PaymentDto and RequestForPaymentStatus return from interac see if they are matching
   *
   * @param interacPaymentStatus the current Interact payment status
   * @param dtoStatus            current PTC payment status
   * @return boolean true=match/false=not match
   */
  private boolean isStatusMatched(RequestForPaymentStatus interacPaymentStatus,
      PaymentStatus dtoStatus) {
    boolean matchStatus = false;

    switch (interacPaymentStatus) {
      //we treat Interac's INITIATED and AVAILABLE are equivalent to AVAILABLE in our system
      case INITIATED:
      case AVAILABLE:
        if (PaymentStatus.AVAILABLE.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case FULFILLED:
        if (PaymentStatus.DEPOSIT_INITIATED.equals(dtoStatus) || PaymentStatus.DEPOSIT_PENDING.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case CANCELLED:
        if (PaymentStatus.CANCELLED.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case DECLINED:
        if (PaymentStatus.DECLINED.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case EXPIRED:
        if (PaymentStatus.EXPIRED.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case DEPOSIT_COMPLETE:
        if (PaymentStatus.COMPLETE.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
      case DEPOSIT_FAILED:
        if (PaymentStatus.FAILED.equals(dtoStatus)) {
          matchStatus = true;
        }
        break;
    }

    log.debug("isStatusMatched()={}", matchStatus);
    return matchStatus;
  }

  private boolean syncStatus(RequestForPaymentStatus paymentStatus, PaymentDto paymentDto) {
    boolean isUpdated = false;
    switch (paymentStatus) {
      case FULFILLED:
        //FULFILLED is equivalent to DEPOSIT_INITIATED. The request for payment can only be moved to DEPOSIT_INITIATED during the
        //RetrieveForPayment, therefore check the current status is eligible to be transitioned to DEPOSIT_INITIATED
        if (PaymentStatus.DEPOSIT_INITIATED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          paymentDto.setStatus(PaymentStatus.DEPOSIT_INITIATED);
          isUpdated = true;
        }
        break;
      case DEPOSIT_COMPLETE:
        //DEPOSIT_COMPLETE is equivalent to COMPLETE and in the PTC rules, only DEPOSIT_PENDING can be transitioned to COMPLETE
        //However, when we synchronize the status with Interac during the RetrieveRequestForPayment, we have the exception of allowing
        //current status in either AVAILABLE or DEPOSIT_INITIATED can be moved to COMPLETE, therefore when checking the request payment's
        //eligible transition, we need to check DEPOSIT_INITIATED, DEPOSIT_PENDING or COMPLETE transitions
        if (PaymentStatus.DEPOSIT_INITIATED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())
            || PaymentStatus.DEPOSIT_PENDING.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())
            || PaymentStatus.COMPLETE.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          log.warn("Incomplete in PTC, update {} to COMPLETE for Request Payment={}", paymentDto.getStatus(),
              paymentDto.getExternalRefId());
          paymentDto.setStatus(PaymentStatus.COMPLETE);
          isUpdated = true;
        }
        break;
      case DEPOSIT_FAILED:
        //DEPOSIT_FAILED is equivalent to FAILED and in the PTC rules, only DEPOSIT_INITIATED or FAILED_PENDING can be transitioned to
        //DEPOSIT_FAILED. However, when we synchronize the status with Interac during the RetrieveRequestForPayment, we have the exception
        //of allowing current status in either AVAILABLE or DEPOSIT_INITIATED can be moved to DEPOSIT_FAILED, therefore when checking the
        //request payment's eligible transition, we need to check DEPOSIT_INITIATED or FAILED transitions.
        //Moving from DEPOSIT_PENDING to FAILED should not be happened as request payment is updated to DEPOSIT_PENDING before the account
        //check in Submit Deposit process; however, in case of the account check failed and exceeded the Interac retry and eventually
        //failed at Interac, we should also allow DEPOSIT_PENDING to be moved to FAILED
        if (PaymentStatus.DEPOSIT_INITIATED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())
            || PaymentStatus.DEPOSIT_PENDING.equals(paymentDto.getStatus())
            || PaymentStatus.FAILED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          log.warn("Missing Interac Deposit Reversal, update {} to FAILED for Request Payment={}", paymentDto.getStatus(),
              paymentDto.getExternalRefId());
          paymentDto.setStatus(PaymentStatus.FAILED);
          isUpdated = true;
        }
        break;
      case DECLINED:
        //check the current status is eligible to be transitioned to DECLINED
        if (PaymentStatus.DECLINED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          paymentDto.setStatus(PaymentStatus.DECLINED);
          isUpdated = true;
        }
        break;
      case CANCELLED:
        //check the current status is eligible to be transitioned to CANCELLED
        if (PaymentStatus.CANCELLED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          paymentDto.setStatus(PaymentStatus.CANCELLED);
          isUpdated = true;
        }
        break;
      case EXPIRED:
        //check the current status is eligible to be transitioned to EXPIRED
        if (PaymentStatus.EXPIRED.isValidRequestPaymentTransitionFrom(paymentDto.getStatus())) {
          paymentDto.setStatus(PaymentStatus.EXPIRED);
          isUpdated = true;
        }
        break;

    }

    return isUpdated;
  }

  /**
   * The method of check current payment status in PaymentDto if is INTERIM status * please refer this wiki page for more detail
   * https://psigate.atlassian.net/wiki/spaces/E/pages/1063846006/Lifecycle+of+a+payment
   *
   * @param paymentStatus the current paymentStatus
   * @return the current payment final status
   */
  private boolean isInterimStatus(PaymentStatus paymentStatus) {
    boolean interimStatus = false;

    if (paymentStatus.equals(PaymentStatus.ACCEPTED) || paymentStatus.equals(PaymentStatus.QUEUED)
        || paymentStatus.equals(PaymentStatus.AVAILABLE)) {
      interimStatus = true;
    }

    log.info("isInterimStatus()={}", interimStatus);
    return interimStatus;
  }


  /**
   * The method of check current payment status in PaymentDto if is DECLINED/COMPLETE/FAILED
   *
   * @param requestForPaymentStatus the current interac side RequestForPaymentStatus
   * @return the current payment final status
   */
  private boolean isInteracFinalStatus(RequestForPaymentStatus requestForPaymentStatus) {
    boolean finalStatus = false;

    if (requestForPaymentStatus.equals(RequestForPaymentStatus.DECLINED)
        || requestForPaymentStatus.equals(RequestForPaymentStatus.CANCELLED)
        || requestForPaymentStatus.equals(RequestForPaymentStatus.EXPIRED)
        || requestForPaymentStatus.equals(RequestForPaymentStatus.DEPOSIT_FAILED)
        || requestForPaymentStatus.equals(RequestForPaymentStatus.DEPOSIT_COMPLETE)) {
      finalStatus = true;
    }
    log.info("isInteracFinalStatus()={}", finalStatus);
    return finalStatus;
  }

  /**
   * The method of persisting the request payment in DB
   *
   * @param currentPayment the current payment
   * @param oldPayments    the old payment
   */
  private void persistRequestPayment(Payments currentPayment, Payments oldPayments) {
    // persist request payment
    currentPayment = paymentsRepository.save(currentPayment);
    log.debug("persisted request payment, externalRefId={}", currentPayment.getExternalRefId());

    // request payment is inserted successfully, it needs to be recorded in history table
    if (currentPayment.getId() != 0) {
      PaymentStatusHistory paymentHistory = paymentStatusHistoryEntityMapper.paymentsToPaymentStatusHistory(currentPayment, oldPayments);
      paymentStatusHistoryRepository.save(paymentHistory);
      log.debug("payment history for externalRefId={} is created with status={}", currentPayment.getExternalRefId(),
          paymentHistory.getStatus());
    }
  }

  /**
   * Retrieve Customer Product status
   *
   * @param serviceAccountRefId service account reference id
   * @param interactionId       interaction id
   * @param customerId          PTC customer id
   * @param product             product
   * @return RetrieveCustomerProductResponse the RetrieveCustomerProductResponse
   */
  private RetrieveCustomerProductResponse retrieveCustomerProduct(String serviceAccountRefId,
      String interactionId, String customerId, String product) {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse;
    try {
      retrieveCustomerProductResponse = customerAdapter.retrieveCustomerProduct(serviceAccountRefId,
          interactionId, customerId, product);

      log.debug("customerId={}, enrollmentRefId={}", customerId,
          retrieveCustomerProductResponse.getEnrollmentRefId());
    } catch (BaseException e) {// Exceptions when calling Customer Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        log.error("error when calling CustomerAPI::retrieveCustomerProduct");
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);
      } else if (e instanceof TBDException) { //read timeout
        log.error("read timeout when calling CustomerAPI::retrieveCustomerProduct");
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.TIME_OUT.name(), null);
      } else { //ResponseException
        if (ErrorProperty.RESOURCE_NOT_FOUND.name().equals(((ResponseException) e).getResponseCode())) {
          throw new ResourceNotFoundException(((ResponseException) e).getResponseCode(), e);
        } else {
          String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());
          // only business validation error code from retrieveCustomerProduct() should be return as ValidationException
          if (!Strings.isNullOrEmpty(errorCode)) {
            throw new ValidationException(((ResponseException) e).getResponseCode(), e);
          } else {
            //Schema and Header validation should be returned as ApplicationException as it should not be happened
            log.warn("CustomerAPI::retrieveCustomerProduct return error {}, additional_information {}",
                ((ResponseException) e).getResponseCode(), ((ResponseException) e).getResponseText());
            throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
          }
        }
      }
    }
    return retrieveCustomerProductResponse;
  }

  /**
   * To invoke Payment Network Send Request Payment service
   *
   * @param enrolmentId                  network customer enrollment id
   * @param sendRequestForPaymentRequest network SendRequestPayment
   * @param paymentDto                   the payment Dto
   * @return SendRequestForPaymentResponse the network's send request payment response
   * @throws Exception
   */
  private SendRequestForPaymentResponse invokeNetworkRequestPayment(String enrolmentId,
      SendRequestForPaymentRequest sendRequestForPaymentRequest, PaymentDto paymentDto, ServiceAccountResponse serviceAccountResponse)
      throws Exception {
    SendRequestForPaymentResponse sendRequestForPaymentResponse;
    Payments sendRequestPayment;
    try {
      sendRequestForPaymentResponse = requestAdapter.requestPayment(enrolmentId, sendRequestForPaymentRequest,
          buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
    } catch (BaseException e) { // Exceptions when calling Adapter
      // mark payment to Failed status.
      paymentDto.setStatus(PaymentStatus.FAILED);
      if (e instanceof TimeoutException || e instanceof AdapterException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);

      } else if (e instanceof ResponseException) {

        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());

        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        StatusReasonDto statusReasonDto = null;

        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {

          // construct the StatusReasonDto into PaymentDto
          statusReasonDto = populateStatusReasonDto(errorCode, null);

          paymentDto.setStatusReasonDto(statusReasonDto);

          log.debug("PTC converts the Network Service Error Code to {}", ((ResponseException) e).getResponseText());

          // map the existing data to Payments entity but without network reference id and network url. The
          // expiry date is the date initially set by the PTC system and has not sync up with external service yet.
          sendRequestPayment = paymentsEntityMapper.paymentDtoToPayments(paymentDto);

          // persist failed request payment
          persistRequestPayment(sendRequestPayment, null);

          log.warn("request payment persisted, status={}", sendRequestPayment.getStatus());

          throw new ValidationException(errorCode, e);
        } else {
          statusReasonDto = populateStatusReasonDto((((ResponseException) e).getResponseCode()), ((ResponseException) e).getResponseText());

          paymentDto.setStatusReasonDto(statusReasonDto);
          // Other Interac's errors; however, the system should validate request data properly prior sending
          // to Interac to prevent this happens.
          log.debug("PTC does  not convert the Network Service Error Code");

          sendRequestPayment = paymentsEntityMapper.paymentDtoToPayments(paymentDto);

          // persist failed request payment
          persistRequestPayment(sendRequestPayment, null);

          log.warn("payment persisted, status={}", sendRequestPayment.getStatus());

          throw e;
        }
      } else {
        // TBDException, read timeout; external service has received the request and they may
        // have processed it but delay in response. In this case, we want to continue the process on
        // our end.
        log.warn("Network Service Read Timeout, PTC continue to persist payment:{}", paymentDto.getExternalRefId());

        StatusReasonDto statusReasonDto = populateStatusReasonDto(ErrorProperty.TIME_OUT.name(),
            "Read Timeout on InteracAdapter::requestPayment");
        paymentDto.setStatusReasonDto(statusReasonDto);

        sendRequestPayment = paymentsEntityMapper.paymentDtoToPayments(paymentDto);

        // persist failed request payment
        persistRequestPayment(sendRequestPayment, null);

        log.warn("request payment persisted, status={}", sendRequestPayment.getStatus());

        //populate TIMEOUT ERROR
        throw new ServiceException(e.getMessage(), e, statusReasonDto.getStatusReasonJsonDto().getReasonCode(), null);
      }
    }
    return sendRequestForPaymentResponse;
  }

  /**
   * Populate StatusReasonDto
   *
   * @param reasonCode  reason code
   * @param description description
   * @return StatusReasonDto StatusReasonDto
   */
  private StatusReasonDto populateStatusReasonDto(String reasonCode, String description) {
    StatusReasonDto statusReasonDto = new StatusReasonDto();
    StatusReasonJsonDto statusReasonJsonDto = new StatusReasonJsonDto();

    statusReasonJsonDto.setReasonCode(reasonCode);
    statusReasonJsonDto.setDescription(description);

    statusReasonDto.setStatusReasonJsonDto(statusReasonJsonDto);

    return statusReasonDto;
  }

}
