package com.peoples.banking.api.requestpayment.v1.mapper;

import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.dto.FraudResultDto;
import com.peoples.banking.api.requestpayment.v1.dto.OptionDto;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.api.requestpayment.v1.dto.RemittanceDto;
import com.peoples.banking.api.requestpayment.v1.util.ExpiryDateUtil;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.Status;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccount;
import com.peoples.banking.partner.domain.interac.request.model.ActiveOrHistoricCurrencyCode;
import com.peoples.banking.partner.domain.interac.request.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.request.model.CreditTransferTransaction35;
import com.peoples.banking.partner.domain.interac.request.model.CreditorPaymentActivationRequestStatusReportV07;
import com.peoples.banking.partner.domain.interac.request.model.ExternalPaymentTransactionStatus1Code;
import com.peoples.banking.partner.domain.interac.request.model.FraudCheckResult;
import com.peoples.banking.partner.domain.interac.request.model.GenericOrganisationIdentification1;
import com.peoples.banking.partner.domain.interac.request.model.OriginalPaymentInstruction31;
import com.peoples.banking.partner.domain.interac.request.model.PaymentCondition1;
import com.peoples.banking.partner.domain.interac.request.model.PaymentInstruction31;
import com.peoples.banking.partner.domain.interac.request.model.PaymentMethod7Code;
import com.peoples.banking.partner.domain.interac.request.model.PaymentTransaction104;
import com.peoples.banking.partner.domain.interac.request.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.type.TransactionType;
import java.util.ArrayList;
import java.util.List;

import org.mapstruct.*;

/**
 * The mapper class for request payment request.
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class, ExpiryDateUtil.class, IdGeneratorUtil.class, PaymentMethod7Code.class,
    ActiveOrHistoricCurrencyCode.class, ChargeBearerType1Code.class})
public abstract class CreateRequestPaymentRequestMapper {

  protected static final String NOT_PROVIDED = "NOTPROVIDED";

  /**
   * To map CreateRequestPaymentRequest to PaymentTo
   *
   * @param request             the CreateRequestPaymentRequest
   * @param fiId                the People's Banking's financial institution Id
   * @param networkEnrollmentId the network enrollment id
   * @param serviceAccountRefId the business partner's service account reference id
   * @param paymentRefId        system generated unique id for partner to ref
   * @param networkRefId        the downstream service's reference id
   * @param networkPaymentType  the downstream service's payment type
   * @param defaultExpiredDays  the PTC's default expiry days
   * @return PaymentDto the PaymentDto
   */
  @Mapping(source = "paymentRefId", target = "externalRefId")
  @Mapping(source = "request.customerId", target = "customerExternalId")
  @Mapping(source = "networkEnrollmentId", target = "networkEnrollmentId")
  @Mapping(source = "serviceAccountRefId", target = "serviceAccountRefId")
  @Mapping(source = "request", target = "transactionType")
  @Mapping(source = "fiId", target = "fiId")
  @Mapping(source = "networkRefId", target = "networkRefId")
  @Mapping(source = "networkPaymentType", target = "networkPaymentType")
  @Mapping(source = "request.contact.name", target = "contactDto.contactJsonDto.name")
  @Mapping(source = "request.contact.emailAddress", target = "contactDto.contactJsonDto.email")
  @Mapping(source = "request.contact.mobileNumber", target = "contactDto.contactJsonDto.mobile")
  @Mapping(source = "request.language", target = "language")
  @Mapping(source = "request.accountName", target = "accountDto.name")
  @Mapping(source = "request.accountNumber", target = "accountDto.accountNumber")
  @Mapping(source = "request.options.amountModification", target = "optionDto.optionJsonDto.amountModification", defaultValue = "false")
  @Mapping(target = "optionDto.optionJsonDto.expiredAfterDays",
      expression = "java(createRequestPaymentRequestOptions.getExpireAfterDays() == null ? defaultExpiredDays: createRequestPaymentRequestOptions.getExpireAfterDays().intValue())")
  @Mapping(source = "request.options.enableNotification", target = "optionDto.optionJsonDto.enableNotification", defaultValue = "false")
  // TODO: remittanceDto's structureDto mapping, it's excluded now until the data fields are finalized
  @Mapping(target = "expiryDate",
      expression = "java(ExpiryDateUtil.getExpiryDate(request.getOptions() == null || request.getOptions().getExpireAfterDays() == null ? defaultExpiredDays: request.getOptions().getExpireAfterDays().intValue()))")
  // exclude networkUrl as it should be retrieved from Interac's response
  // exclude status as it should be retrieved from Interac's response
  // exclude networkPaymentRefId as it should be retrieved from Interac's response
  // exclude settlementDate as it's not applicable for request payment
  // exclude isSettled as it's not applicable for request payment
  // exclude cancelReasonDto as it's not applicable for request payment
  @Mapping(source = "request.memo", target = "remittanceDto.remittanceJsonDto.unstructured.memo")
  @Mapping(target = "createdDate", expression = "java(DateUtil.getCurrentUTCDateTime())")
  @Mapping(source = "connectorType", target = "connectorType")
  @Mapping(source = "indirectConnectorId", target = "indirectConnectorId")
  public abstract PaymentDto createRequestPaymentRequestToPaymentDto(CreateRequestPaymentRequest request, String fiId,
      String networkEnrollmentId, String serviceAccountRefId, String paymentRefId, NetworkChannelType networkRefId,
      NetworkPaymentType networkPaymentType, @Context int defaultExpiredDays, String connectorType, String indirectConnectorId);


  /**
   * To map PaymentDto to Interac's SendRequestForPaymentRequest
   *
   * @param paymentDto the paymentDto
   * @return SendRequestForPaymentRequest the Interac's SendRequestForPaymentRequest
   */
  @Mapping(target = "creditorPaymentActivationRequest.groupHeader.messageIdentification",
      expression = "java(IdGeneratorUtil.generateRequestId().replace(\"-\", \"\"))")
  @Mapping(target = "creditorPaymentActivationRequest.groupHeader.creationDatetime", expression = "java(DateUtil.getCurrentUTCDateTime())")
  @Mapping(target = "creditorPaymentActivationRequest.groupHeader.numberOfTransactions", expression = "java(\"1\")")
  @Mapping(target = "creditorPaymentActivationRequest.groupHeader.initiatingParty.name", expression = "java(NOT_PROVIDED)")
  @Mapping(source = "paymentDto", target = "creditorPaymentActivationRequest.paymentInformation")
  @Mapping(source = "optionDto", target = "suppressResponderNotifications", defaultExpression = "java(false)")
  @Mapping(source = "accountDto.name", target = "accountHolderName")
  @Mapping(source = "language", target = "language")
  public abstract SendRequestForPaymentRequest paymentDtoToSendRequestForPaymentRequest(PaymentDto paymentDto);


  /**
   * The custom implementation to map SendRequestForPaymentResponse into the existing paymentDto
   *
   * @param paymentDto                    the paymentDto
   * @param sendRequestForPaymentResponse the Interac's sendRequestForPaymentResponse
   * @return PaymentDto the existing paymentDto
   */
  public PaymentDto sendRequestForPaymentResponseToPaymentDto(PaymentDto paymentDto,
      SendRequestForPaymentResponse sendRequestForPaymentResponse) {

    if (sendRequestForPaymentResponse == null) {
      return paymentDto;
    }

    if (sendRequestForPaymentResponse.getCreditorPaymentActivationRequestStatusReport() != null) {
      CreditorPaymentActivationRequestStatusReportV07 creditorPaymentActivationRequestStatusReport =
          sendRequestForPaymentResponse.getCreditorPaymentActivationRequestStatusReport();
      List<OriginalPaymentInstruction31> originalPaymentInformationAndStatusList =
          creditorPaymentActivationRequestStatusReport.getOriginalPaymentInformationAndStatus();

      if (originalPaymentInformationAndStatusList != null) {
        OriginalPaymentInstruction31 originalPaymentInformationAndStatus = originalPaymentInformationAndStatusList.get(0);
        List<PaymentTransaction104> transactionInformationAndStatusList =
            originalPaymentInformationAndStatus.getTransactionInformationAndStatus();
        if (transactionInformationAndStatusList != null) {
          PaymentTransaction104 transactionInformationAndStatus = transactionInformationAndStatusList.get(0);
          paymentDto.setNetworkPaymentRefId(transactionInformationAndStatus.getClearingSystemReference());

          ExternalPaymentTransactionStatus1Code transactionStatus = transactionInformationAndStatus.getTransactionStatus();
          PaymentStatus status = externalPaymentTransactionStatus1CodeToPaymentStatus(transactionStatus);

          paymentDto.setStatus(status);
        }
      }

      if (sendRequestForPaymentResponse.getFraudCheckResult() != null) {
        FraudResultDto fraudResultDto = fraudCheckResultToFraudReasonDto(sendRequestForPaymentResponse.getFraudCheckResult());
        paymentDto.setFraudResultDto(fraudResultDto);
      }

      paymentDto.setNetworkUrl(sendRequestForPaymentResponse.getGatewayUrl());
      paymentDto.setNetworkCreatedDate(
          sendRequestForPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getCreationDatetime());
    }

    return paymentDto;
  }


  /**
   * To map PaymentDto to CreateRequestPaymentResponse
   *
   * @param paymentDto the paymentDto
   * @return CreateRequestPaymentResponse the CreateRequestPaymentResponse
   */
  @Mapping(source = "customerExternalId", target = "customerId")
  @Mapping(source = "contactDto.contactJsonDto.name", target = "contact.name")
  @Mapping(source = "contactDto.contactJsonDto.email", target = "contact.emailAddress")
  @Mapping(source = "contactDto.contactJsonDto.mobile", target = "contact.mobileNumber")
  @Mapping(source = "accountDto.name", target = "accountName")
  @Mapping(source = "accountDto.accountNumber", target = "accountNumber")
  @Mapping(source = "optionDto.optionJsonDto.amountModification", target = "options.amountModification")
  @Mapping(source = "optionDto.optionJsonDto.expiredAfterDays", target = "options.expireAfterDays")
  @Mapping(source = "optionDto.optionJsonDto.enableNotification", target = "options.enableNotification")
  // TODO: remittanceInformation mapping, it's excluded now until the data fields are finalized
  @Mapping(source = "externalRefId", target = "requestRefId")
  @Mapping(source = "networkUrl", target = "requestUrl")
  @Mapping(source = "remittanceDto.remittanceJsonDto.unstructured.memo", target = "memo")
  public abstract CreateRequestPaymentResponse paymentDtoToCreateRequestPaymentResponse(PaymentDto paymentDto);

  /**
   * The custom implementation to map the CreateRequestPaymentRequest to TransactionType. Due to it's request payment therefore the TransactionType is
   * default to REQUEST.
   *
   * @param request the CreateRequestPaymentRequest
   * @return TransactionType the REQUEST transaction type
   */
  protected TransactionType createRequestPaymentRequestToTransactionType(CreateRequestPaymentRequest request) {
    return TransactionType.REQUEST;
  }


  /**
   * The custom implementation of mapping PaymentDto to PaymentInstruction31 list
   *
   * @param paymentDto the paymentDto
   * @return List<PaymentInstruction31> the PaymentInstruction31 list
   */
  protected List<PaymentInstruction31> paymentDtoToPaymentInstruction31List(PaymentDto paymentDto) {
    if (paymentDto == null) {
      return null;
    }

    List<PaymentInstruction31> paymentInformationList = new ArrayList<>();
    paymentInformationList.add(paymentDtoToPaymentInstruction31(paymentDto));

    return paymentInformationList;
  }


  /**
   * To map PaymentDto to PaymentInstruction31
   *
   * @param paymentDto the paymentDto
   * @return PaymentInstruction31 the PaymentInstruction31
   */
  @Mapping(target = "paymentInformationIdentification", expression = "java(IdGeneratorUtil.generateRequestId().replace(\"-\", \"\"))")
  @Mapping(target = "paymentMethod", expression = "java(PaymentMethod7Code.TRF)")
  @Mapping(source = "createdDate", target = "requestedExecutionDate")
  @Mapping(source = "expiryDate", target = "expiryDate")
  @Mapping(source = "optionDto", target = "paymentCondition")
  @Mapping(source = "contactDto.contactJsonDto.name", target = "debtor.name")
  @Mapping(source = "contactDto.contactJsonDto.email", target = "debtor.contactDetails.emailAddress")
  @Mapping(target = "debtor.contactDetails.mobileNumber", expression = "java(contactJsonDto.getMobile() == null? null : String.valueOf(contactJsonDto.getMobile()).replaceFirst(\"(\\\\d{10})\", \"+1-$1\"))")
  @Mapping(target = "debtorAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification",
      expression = "java(NOT_PROVIDED)")
  @Mapping(source = "paymentDto", target = "creditTransferTransaction")
  protected abstract PaymentInstruction31 paymentDtoToPaymentInstruction31(PaymentDto paymentDto);


  /**
   * The custom implementation of mapping PaymentDto to CreditTransferTransaction35 list
   *
   * @param paymentDto the paymentDto
   * @return List<CreditTransferTransaction35> the CreditTransferTransaction35 list
   */
  protected List<CreditTransferTransaction35> paymentDtoToCreditTransferTransaction35List(PaymentDto paymentDto) {
    if (paymentDto == null) {
      return null;
    }

    List<CreditTransferTransaction35> creditTransferTransactionList = new ArrayList<>();
    creditTransferTransactionList.add(paymentDtoToCreditTransferTransaction35(paymentDto));
    return creditTransferTransactionList;
  }


  /**
   * To map paymentDto to CreditTransferTransaction35
   *
   * @param paymentDto the paymentDto
   * @return CreditTransferTransaction35 the CreditTransferTransaction35
   */
  @Mapping(source = "externalRefId", target = "paymentIdentification.instructionIdentification")
  @Mapping(source = "endToEndId", target = "paymentIdentification.endToEndIdentification", qualifiedByName = "truncateEndToEndId")
  @Mapping(source = "amount", target = "amount.instructedAmount.amount")
  @Mapping(target = "amount.instructedAmount.currency", expression = "java(ActiveOrHistoricCurrencyCode.CAD)")
  @Mapping(target = "chargeBearer", expression = "java(ChargeBearerType1Code.SLEV)")
  @Mapping(source = "paymentDto",
      target = "creditorAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification",
      qualifiedByName = "debtorAgentMemberIdentification")
  @Mapping(source = "paymentDto", target = "creditor.identification.organisationIdentification.other")
  @Mapping(source = "accountDto.accountNumber", target = "creditorAccount.identification.other.identification")
  @Mapping(source = "remittanceDto", target = "remittanceInformation")
  protected abstract CreditTransferTransaction35 paymentDtoToCreditTransferTransaction35(PaymentDto paymentDto);


  /**
   * The custom implementation of mapping PaymentDto to GenericOrganisationIdentification1 list
   *
   * @param paymentDto the paymentDto
   * @return List<GenericOrganisationIdentification1> the GenericOrganisationIdentification1 list
   */
  protected List<GenericOrganisationIdentification1> paymentDtoToGenericOrganisationIdentification1List(PaymentDto paymentDto) {
    if (paymentDto == null) {
      return null;
    }

    List<GenericOrganisationIdentification1> organizationIdList = new ArrayList<>();
    organizationIdList.add(paymentDtoToGenericOrganisationIdentification1(paymentDto));
    return organizationIdList;
  }


  /**
   * The custom implementation of mapping OptionDto to PaymentCondition1
   *
   * @param optionDto the PaymentDto
   * @return PaymentCondition1 the PaymentCondition1
   */
  protected PaymentCondition1 optionDtoToPaymentCondition1(OptionDto optionDto) {
    PaymentCondition1 paymentCondition = new PaymentCondition1();

    if (optionDto == null || optionDto.getOptionJsonDto() == null) {
      paymentCondition.setAmountModificationAllowed(false);
    } else {
      paymentCondition.setAmountModificationAllowed(optionDto.getOptionJsonDto().isAmountModification());
    }

    paymentCondition.setEarlyPaymentAllowed(false);
    paymentCondition.setGuaranteedPaymentRequested(false);
    return paymentCondition;
  }


  /**
   * To map customer enrollment id from PaymentDto to GenericOrganisationIdentification1
   *
   * @param paymentDto the paymentDto
   * @return GenericOrganisationIdentification1 the GenericOrganisationIdentification1
   */
  @Mapping(source = "networkEnrollmentId", target = "identification")
  protected abstract GenericOrganisationIdentification1 paymentDtoToGenericOrganisationIdentification1(PaymentDto paymentDto);


  /**
   * The custom implementation of mapping OptionDto to SuppressResponderNotifications
   *
   * @param optionDto the paymentDto
   * @return boolean the boolean value for SuppressResponderNotifications
   */
  protected Boolean optionDtoToSuppressResponderNotifications(OptionDto optionDto) {
    boolean result = true;

    if (optionDto == null || optionDto.getOptionJsonDto() == null) {
      result = false;
    } else {
      result = !optionDto.getOptionJsonDto().isEnableNotification();
    }

    return result;

  }

  /**
   * The custom implementation of mapping ExternalPaymentTransactionStatus1Code to PaymentStatus. ACTC and PDNG are equivalent to PTC's AVAILABLE
   * status; RJCT and others are equivalent to PTC's FAILED status
   *
   * @param transactionStatus the external payment transaction status
   * @return PaymentStatus the PTC payment status
   */
  protected PaymentStatus externalPaymentTransactionStatus1CodeToPaymentStatus(ExternalPaymentTransactionStatus1Code transactionStatus) {
    PaymentStatus status = null;

    switch (transactionStatus) {
      case ACTC:
      case PDNG:
        status = PaymentStatus.AVAILABLE;
        break;
      case RJCT:
        status = PaymentStatus.FAILED;
        break;
      default:
        status = PaymentStatus.FAILED;
    }

    return status;
  }

  /**
   * map PaymentStatus enum to Status enum
   *
   * @param paymentStatus the payment status enum
   * @return Status the status enum
   */
  @ValueMappings({@ValueMapping(source = "FAILED_PENDING", target = "FAILED"),
      @ValueMapping(source = "FAILED_PENDING_REVERSAL", target = "FAILED"),
      @ValueMapping(source = "DEPOSIT_INITIATED", target = "ACCEPTED"),
      @ValueMapping(source = "REVERSE", target = "ACCEPTED"),
      @ValueMapping(source = "COMPLETE", target = "DEPOSIT_COMPLETE"),
      @ValueMapping(source = "SECURITY_ANSWER_FAILURE", target = MappingConstants.NULL)})
  protected abstract Status paymentStatusToStatus(PaymentStatus paymentStatus);

  /**
   * map FraudCheckResult to FraudResultDto
   *
   * @param fraudCheckResult the Interac's frauch check result
   * @return FraudResultDto the fraudResultDto
   */
  @Mapping(source = "fraudCheckResult", target = "fraudResultJsonDto")
  protected abstract FraudResultDto fraudCheckResultToFraudReasonDto(FraudCheckResult fraudCheckResult);

  /**
   * To map Remittance memo from PaymentDto to RemittanceInformation16
   *
   * @param remittanceDto the RemittanceDto
   * @return RemittanceInformation16 the RemittanceInformation16
   */
  protected RemittanceInformation16 paymentDtoToRemittanceInformation16(RemittanceDto remittanceDto) {
    if (remittanceDto == null || remittanceDto.getRemittanceJsonDto() == null
        || remittanceDto.getRemittanceJsonDto().getUnstructured() == null
        || remittanceDto.getRemittanceJsonDto().getUnstructured().getMemo() == null) {
      return null;
    }
    RemittanceInformation16 remittanceInformation16 = new RemittanceInformation16();
    List<String> memoLst = splitEqually(remittanceDto.getRemittanceJsonDto().getUnstructured().getMemo(),
        RequestPaymentConstant.INTERAC_REMITTANCE_MEMO_LENGTH);
    if (memoLst != null) {
      for (String memo : memoLst) {
        remittanceInformation16.addUnstructuredItem(memo);
      }
    }

    return remittanceInformation16;
  }

  @Named("truncateEndToEndId")
  protected String truncateEndToEndId(String endToEndId) {
    if (endToEndId == null) {
      return NOT_PROVIDED;
    }

    if (endToEndId.length() > 35) {
      endToEndId = endToEndId.substring(0, 35);
    }

    return endToEndId;
  }

  /**
   * split the string Equally
   *
   * @param text the text need to be split
   * @param size the size of text to be split
   * @return List<String> has been split to equal length
   */
  private static List<String> splitEqually(String text, int size) {
    // Give the list the right capacity to start with. You could use an array
    // instead if you wanted.
    if (text == null || text.isBlank()) {
      return null;
    }
    List<String> ret = new ArrayList<>((text.length() + size - 1) / size);

    for (int start = 0; start < text.length(); start += size) {
      ret.add(text.substring(start, Math.min(text.length(), start + size)));
    }
    return ret;
  }

  @Named("debtorAgentMemberIdentification")
  protected String getDebtorAgentMemberIdentification(PaymentDto paymentDto) {
    if (ServiceAccount.ConnectorTypeEnum.INDIRECT.getValue().equals(paymentDto.getConnectorType()) && paymentDto.getIndirectConnectorId() != null) {
      return paymentDto.getIndirectConnectorId();
    } else {
      return paymentDto.getFiId();
    }
  }
}
