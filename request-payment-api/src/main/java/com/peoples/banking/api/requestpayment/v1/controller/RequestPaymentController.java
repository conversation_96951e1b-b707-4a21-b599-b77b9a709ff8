package com.peoples.banking.api.requestpayment.v1.controller;

import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentProperty;
import com.peoples.banking.api.requestpayment.v1.service.RequestPaymentService;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.RetrieveIncomingRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.RetrieveIncomingRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.controller.APIController;
import com.peoples.banking.util.api.common.type.FeatureType;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import javax.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * The Rest controller for Request Payment Management.
 */
@RestController
@Log4j2
public class RequestPaymentController extends APIController {

  @Autowired
  private RequestPaymentProperty properties;

  @Autowired
  private RequestPaymentService service;

  /**
   * Create Request Payment operation
   *
   * @param serviceAccountApiToken      the business parnter's service account reference id
   * @param interactionId               the unique interaction id
   * @param interactionTime             the interaction time in UTC
   * @param createRequestPaymentRequest the request payment request
   * @return RequestPaymentResponse the request payment response
   * @throws Exception the exception
   */
  @PerfLogger
  @PostMapping(value = RequestPaymentConstant.ROOT_SERVICE_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<CreateRequestPaymentResponse> createRequestPayment(
      @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @Valid @RequestBody CreateRequestPaymentRequest createRequestPaymentRequest) throws Exception {
    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true,
        FeatureType.CREATE_PAYMENT_REQUEST, false, true);

    // call service for business process
    CreateRequestPaymentResponse response = service
        .createRequestPayment(createRequestPaymentRequest, serviceAccountResponse, interactionId);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    // return response
    return new ResponseEntity<>(response, headers, HttpStatus.CREATED);
  }

  /**
   * Cancel Request Payment operation
   *
   * @param serviceAccountApiToken      the business partner's service account reference id
   * @param interactionId               the unique interaction id
   * @param interactionTime             the interaction time in UTC
   * @param requestId                   the PTC generated request Id
   * @param cancelRequestPaymentRequest the cancel request payment request
   * @return null
   * @throws Exception the exception
   */
  @PerfLogger
  @PostMapping(value = RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> cancelRequestPayment(
      @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable String requestId, @Valid @RequestBody CancelRequestPaymentRequest cancelRequestPaymentRequest) throws Exception {
    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true,
        FeatureType.CANCEL_PAYMENT_REQUEST, false, true);

    // call service for business process
    Boolean result = service.cancelRequestPayment(cancelRequestPaymentRequest, requestId, serviceAccountResponse, interactionId);
    log.debug("Result of calling cancelRequestPayment {}", result);
    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    // return response
    return new ResponseEntity<>(null, headers, HttpStatus.NO_CONTENT);
  }

  /**
   * get Request Payments operation.
   *
   * @param serviceAccountApiToken the business partner's service account
   * @param interactionId          the unique id per request
   * @param interactionTime        the time the request was initiated
   * @param requestId              the external request Payment ref id
   * @return RetrieveRequestPaymentResponse the request Payment Response
   * @throws Exception
   */
  @PerfLogger
  @GetMapping(value = RequestPaymentConstant.GET_REQUEST_PAYMENT_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveRequestPaymentResponse> retrieveRequestPayment(
      @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable String requestId) throws Exception {
    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true,
        FeatureType.RETRIEVE_PAYMENT_REQUEST, false, true);

    RetrieveRequestPaymentResponse retrieveRequestPaymentResponse = service
        .retrieveRequestPayment(requestId, serviceAccountResponse);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    return new ResponseEntity<>(retrieveRequestPaymentResponse, headers, HttpStatus.OK);
  }

  /**
   * decline payment operation
   *
   * @param serviceAccountApiToken the business parnter's service account reference id
   * @param interactionId          the unique interaction id
   * @param interactionTime        the interaction time in UTC
   * @param interacRequestRefId    the Interac generated request payment reference id
   * @param declineRequestPaymentRequest  the Decline Payment request
   * @return http code to indicate if cancel success or failed
   * @throws Exception the exception
   */
  @PerfLogger
  @PostMapping(value = RequestPaymentConstant.DECLINE_REQUEST_PAYMENT_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> declineRequestPayment(
          @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
          @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
          @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
          @PathVariable String networkRequestRefId,
          @Valid @RequestBody DeclineRequestPaymentRequest declineRequestPaymentRequest) throws Exception {

    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true,
            FeatureType.DECLINE_PAYMENT_REQUEST, false, true);

    // call service for business process
    boolean result = service
            .declineRequestPayment(declineRequestPaymentRequest, networkRequestRefId, serviceAccountResponse, interactionId);

    log.debug("Result of calling declinePayment {}", result);
    // set appropriate response code (in header)
    HttpHeaders httpHeaders = createResponseHttpHeaders(interactionId, false);

    // return response
    return new ResponseEntity<>(null, httpHeaders, HttpStatus.NO_CONTENT);
  }

  /**
   * get Request Payments operation.
   *
   * @param serviceAccountApiToken                  the business partner's service account reference id
   * @param interactionId                           the unique interaction id
   * @param interactionTime                         the interaction time in UTC
   * @param request                                 the cancel request payment request
   * @return RetrieveIncomingRequestPaymentResponse the request Payment Response
   * @throws Exception
   */
  @PerfLogger
  @PostMapping(value = RequestPaymentConstant.RETRIEVE_INCOMING_REQUEST_PAYMENT_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveIncomingRequestPaymentResponse> retrieveIncomingRequestPayment(
      @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @Valid @RequestBody RetrieveIncomingRequestPaymentRequest request) throws Exception {
    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true,
                                                                 FeatureType.RETRIEVE_INCOMING_PAYMENT_REQUEST, false, true);

    RetrieveIncomingRequestPaymentResponse response = service
        .retrieveIncomingRequestPayment(request, serviceAccountResponse, interactionId);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    return new ResponseEntity<>(response, headers, HttpStatus.OK);
  }

  /**
   * return Request Payment API's time to live value
   */
  @Override
  protected int getApiTimeToLiveValue() {
    return properties.getTimeToLive();
  }
}
