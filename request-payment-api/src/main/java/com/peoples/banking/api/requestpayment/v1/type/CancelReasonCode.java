package com.peoples.banking.api.requestpayment.v1.type;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * The cancel reason code
 *
 */
public enum CancelReasonCode {
  CUSTOMER_INITIATED("CUSTOMER_INITIATED"),

  AGENT_INITIATED("AGENT_INITIATED"),

  SYSTEM_INITIATED("SYSTEM_INITIATED"),

  OTHER("OTHER");

  private String text;

  private static Map<String, CancelReasonCode> cancelTypeCodeMap =
      new HashMap<String, CancelReasonCode>();

  static {
    for (CancelReasonCode cancelTypeCode : CancelReasonCode.values()) {
      cancelTypeCodeMap.put(cancelTypeCode.text, cancelTypeCode);
    }
  }


  CancelReasonCode(String string) {
    this.text = string;
  }

  public String getCancelTypeCode() {
    return text;
  }

  public static CancelReasonCode fromString(String value) {
    CancelReasonCode cancelTypeCode = cancelTypeCodeMap.get(value);

    if (cancelTypeCode != null) {
      return cancelTypeCode;
    } else {
      return OTHER;
    }
  }

}
