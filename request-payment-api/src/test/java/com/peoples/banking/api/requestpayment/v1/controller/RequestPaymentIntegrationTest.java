package com.peoples.banking.api.requestpayment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import java.math.BigDecimal;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RequestPaymentIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private CreateRequestPaymentRequest request;

  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    request = RequestPaymentTestUtil.createRequestPaymentRequest();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test RequestPayment happy path
   */
  @Test
  public void createRequestPayment_success() throws Exception {

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);
  }

  /**
   * The test RequestPayment missing customerID trigger Error
   */
  @Test
  public void createRequestPayment_missing_customerID_failed() throws Exception {

    request.setCustomerId(null);

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * test null expired after days
   *
   * @throws Exception the exception
   */
  @Test
  public void createRequestPayment_nullExpiredAfterDays_success() throws Exception {

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    request.getOptions().setExpireAfterDays(null);

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<CreateRequestPaymentResponse> response = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, CreateRequestPaymentResponse.class);

    assert (response.getStatusCode() == HttpStatus.CREATED);
    assertEquals(45, response.getBody().getOptions().getExpireAfterDays());
  }

  @Test
  public void createRequestPayment_missing_contactEmailAndMobile_failed() throws Exception {

    request.getContact().setEmailAddress(null);
    request.getContact().setMobileNumber(null);

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
    assertEquals(RequestPaymentTestUtil.JSON_PATH_CONTACT_CONTACT_INFO,
        errResponseEntity.getBody().getError().get(0).getAdditionalInformation());
  }

  @Test
  public void createRequestPayment_invalidAmount_failed() throws Exception {

    request.setAmount(new BigDecimal("123.123"));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_AMOUNT_DIGITS, errResponseEntity.getBody().getError().get(0).getCode());

    assertEquals(PaymentUtil.JSON_PATH_AMOUNT,
        errResponseEntity.getBody().getError().get(0).getAdditionalInformation());

  }

  @Test
  public void createRequestPayment_invalidAccountName_failed() throws Exception {

    request.setAccountName("errdcodearesourceaexistsRESOURCEaEXISTSerracodearesource.existsRESOURCEaEXISTSsaaaaaaaaas");

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_FIELD_LENGTH, errResponseEntity.getBody().getError().get(0).getCode());

    assertEquals(PaymentUtil.JSON_PATH_ACCOUNT_NAME,
        errResponseEntity.getBody().getError().get(0).getAdditionalInformation());

  }

  @Test
  public void createRequestPayment_expireLimitExceeded_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("390");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.EXPIRY_LIMIT_EXCEEDED, errResponseEntity.getBody().getError().get(0).getCode());

  }

  /**
   * The test RequestPayment with memo happy path
   */
  @Test
  public void createRequestPayment_with_memo_success() throws Exception {

    String testMemo = "In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically";
    request.setMemo(testMemo);

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<CreateRequestPaymentResponse> response = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, CreateRequestPaymentResponse.class);

    assert (response.getStatusCode() == HttpStatus.CREATED);
    assertEquals(testMemo, response.getBody().getMemo());
  }

  @Test
  public void createRequestPayment_withLongMemo_failed() throws Exception {

    String testMemo = "In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically";
    testMemo += "Extra long memo over 420";
    request.setMemo(testMemo);

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_FIELD_LENGTH, errResponseEntity.getBody().getError().get(0).getCode());

    assertEquals(PaymentUtil.JSON_PATH_MEMO,
        errResponseEntity.getBody().getError().get(0).getAdditionalInformation());
  }

  @Test
  public void createRequestPayment_allWhitSpaceMemo_failed() throws Exception {

    String testMemo = "                                 ";
    request.setMemo(testMemo);

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_INPUT, errResponseEntity.getBody().getError().get(0).getCode());

    assertEquals(PaymentUtil.JSON_PATH_MEMO,
        errResponseEntity.getBody().getError().get(0).getAdditionalInformation());
  }

  @Test
  public void createRequestPayment_OAS_customerDisabled_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("410");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.CUSTOMER_DISABLED_AT_NETWORK, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_OAS_customerBlocked_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("501");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.CUSTOMER_BLOCKED, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_contactName_allWhiteSpace_failed() throws Exception {
    //Set name to be all whitespace to trigger error
    request.getContact().setName("            ");

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_INPUT, errResponseEntity.getBody().getError().get(0).getCode());

    assertEquals(PaymentUtil.JSON_PATH_CONTACT_NAME, errResponseEntity.getBody().getError().get(0).getAdditionalInformation());
  }

  @Test
  public void createRequestPayment_contact_blocked_request_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("532");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.CONTACT_BLOCKED_REQUEST, errResponseEntity.getBody().getError().get(0).getCode());

  }

  @Test
  public void createRequestPayment_generalResponseException_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("999");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.NETWORK_UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_illegalArgumentException_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doThrow(IllegalArgumentException.class).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from their
   * aspect all the values they pass to us are valid
   */
  @Test
  public void createRequestPayment_customerAdapter_ThrowUnExpectedException_failed() throws Exception {

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    ResponseException responseException = new ResponseException();
    responseException.setHttpStatusCode(500);

    doThrow(responseException).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from their
   * aspect all the values they pass to us are valid
   */
  @Test
  public void createRequestPayment_customerAdapter_ThrowResourceNotFoundException_failed() throws Exception {

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    ResourceNotFoundException resourceNotFoundException = new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());

    doThrow(resourceNotFoundException).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from their
   * aspect all the values they pass to us are valid
   */
  @Test
  public void createRequestPayment_serviceAccountAdapter_ThrowUnExpectedException_failed() throws Exception {

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    TBDException tbdException = new TBDException();
    doThrow(tbdException).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_tbdException_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    TBDException tbdException = new TBDException();
    doThrow(tbdException).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.SERVICE_UNAVAILABLE);

    assertEquals(PaymentUtil.TIME_OUT, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_serviceAccountDisabled_failed() throws Exception {
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doThrow(IllegalArgumentException.class).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.SERVICE_DISABLED, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createRequestPayment_serviceAccountSuspended_failed() throws Exception {
    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity  = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

  }

}
