package com.peoples.banking.api.requestpayment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.Status;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentStatus;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RetrieveRequestPaymentIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private RetrieveRequestPaymentResponse response;

  private ServiceAccountResponse serviceAccountResponse;

  private Payments requestPayment;

  private List<PaymentStatusHistory> depositPaymentStatusHistoryList;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    response = RequestPaymentTestUtil.createRetrieveRequestPaymentResponse();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    if (depositPaymentStatusHistoryList != null) {
      paymentStatusHistoryRepository.deleteAllInBatch(depositPaymentStatusHistoryList);
      depositPaymentStatusHistoryList = null;
    }

    if (requestPayment != null) {
      paymentsRepository.delete(requestPayment);
      requestPayment = null;
    }
    template = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;

  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test getRequestPayment with Memo happy path
   */
  @Test
  public void getRequestPayment_withMemo_success() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment = RequestPaymentTestUtil.setMemoForPayments(requestPayment,
        "{\"unstructured\": {\"memo\": \"In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically\"}}");

    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.AVAILABLE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.AVAILABLE, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.AVAILABLE.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @Test
  public void getRequestPayment_withMemo_serviceAccountSuspended_success() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();

    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.AVAILABLE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.AVAILABLE, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.AVAILABLE.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @Test
  public void getRequestPayment_missingReferenceIdNotFound_failed() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("511");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, errResponseEntity.getStatusCode());
    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void getRequestPayment_generalResponseException_failed() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("999");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, errResponseEntity.getStatusCode());
    assertEquals(PaymentUtil.NETWORK_UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void getRequestPayment_illegalArgumentException_failed() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    doThrow(IllegalArgumentException.class).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, errResponseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void getRequestPayment_invalidServiceAccount_failed() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    ResponseException responseException = new ResponseException(HttpStatus.BAD_REQUEST.value(), "INVALID_SERVICE_ACCOUNT", null);
    doThrow(responseException).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    //Set this one to be a not match service account ID
    headers.add("x-pg-service-account", "XYZ987");

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, errResponseEntity.getStatusCode());
    assertEquals(PaymentUtil.INVALID_SERVICE_ACCOUNT, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "QUEUED",
      "ACCEPTED",
      "AVAILABLE",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  void getRequestPayment_interacInitiatedStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.INITIATED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "QUEUED",
      "ACCEPTED",
      "AVAILABLE",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_interacAvailableStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.AVAILABLE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }


  @ParameterizedTest
  @ValueSource(strings = {
      "AVAILABLE"
  })
  public void getRequestPayment_availableStatus_interacFulFilledStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.FULFILLED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.DEPOSIT_INITIATED, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.DEPOSIT_INITIATED.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertFalse(depositPaymentStatusHistoryList.isEmpty());
    assertEquals(1, depositPaymentStatusHistoryList.size());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_noneInterimStatus_interacFulFilledStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.FULFILLED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "QUEUED",
      "ACCEPTED",
      "AVAILABLE"
  })
  public void getRequestPayment_interimStatus_interacDeclinedStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DECLINED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.DECLINED, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.DECLINED.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertFalse(depositPaymentStatusHistoryList.isEmpty());
    assertEquals(1, depositPaymentStatusHistoryList.size());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_noneInterimStatus_interacDeclinedStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DECLINED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "QUEUED",
      "ACCEPTED",
      "AVAILABLE",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING"
  })
  public void getRequestPayment_interimStatus_interacExpiredStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.EXPIRED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.EXPIRED, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.EXPIRED.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertFalse(depositPaymentStatusHistoryList.isEmpty());
    assertEquals(1, depositPaymentStatusHistoryList.size());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_noneInterimStatus_interacExpiredStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.EXPIRED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "AVAILABLE",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING"
  })
  public void getRequestPayment_availableAndDepositStatus_interacDepositFailedStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_FAILED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.FAILED, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.FAILED.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertFalse(depositPaymentStatusHistoryList.isEmpty());
    assertEquals(1, depositPaymentStatusHistoryList.size());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_noneInterimStatusAndDeposit_interacDepositFailedStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_FAILED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "AVAILABLE",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING"
  })
  public void getRequestPayment_availableAndDepositStatus_interacDepositCompleteStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_COMPLETE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    assertEquals(Status.DEPOSIT_COMPLETE, responseEntity.getBody().getStatus());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(PaymentStatus.COMPLETE.getValue(), requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertFalse(depositPaymentStatusHistoryList.isEmpty());
    assertEquals(1, depositPaymentStatusHistoryList.size());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "COMPLETE",
      "CANCELLED",
      "DECLINED",
      "EXPIRED",
      "FAILED"
  })
  public void getRequestPayment_noneInterimStatusAndDeposit_interacDepositCompleteStatus_success(String ptcStatus) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    requestPayment.setStatus(ptcStatus);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_COMPLETE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    ResponseEntity<RetrieveRequestPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, RetrieveRequestPaymentResponse.class);

    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(response.getCustomerId(), responseEntity.getBody().getCustomerId());
    Status status = null;
    if (PaymentStatus.COMPLETE.getValue().equals(ptcStatus)) {
      status = Status.DEPOSIT_COMPLETE;
    } else {
      status = Status.fromValue(ptcStatus);
    }
    assertEquals(status.getValue(), responseEntity.getBody().getStatus().getValue());

    //retrieve current request payment status
    Optional<Payments> retrievedPaymentOption = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(requestPayment.getExternalRefId(), NetworkPaymentType.REQUEST_FOR_PAYMENT.name(), PaymentCdType.REQUEST);
    assertTrue(retrievedPaymentOption.isPresent());
    requestPayment = retrievedPaymentOption.get();
    assertEquals(ptcStatus, requestPayment.getStatus());

    // retrieve status history
    depositPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);
    assertTrue(depositPaymentStatusHistoryList.isEmpty());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "REGULAR_PAYMENT",
      "FULFILL_REQUEST_FOR_PAYMENT",
      "ACCOUNT_ALIAS_PAYMENT",
      "REALTIME_ACCOUNT_ALIAS_PAYMENT",
      "ACCOUNT_DEPOSIT_PAYMENT",
      "REALTIME_ACCOUNT_DEPOSIT_PAYMENT"
  })
  public void getRequestPayment_noValid_NetworkPaymentType_failed(String netWorkPaymentType) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //give all kinds wrong networkPaymentType
    requestPayment.setNetworkPaymentType(netWorkPaymentType);
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_COMPLETE);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void getRequestPayment_serviceAccountDisabled_success() throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID
    requestPayment.setCustomerRefId(response.getCustomerId());
    //create DB record
    requestPayment = paymentsRepository.save(requestPayment);

    RequestForPaymentResponse requestForPaymentResponse = RequestPaymentTestUtil.getRequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.FULFILLED);
    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.ROOT_SERVICE_URL + "/" + requestPayment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity getRequestPayment = new HttpEntity<>(headers);

    errResponseEntity = template
        .exchange(finalUrl, HttpMethod.GET, getRequestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, errResponseEntity.getStatusCode());
    assertEquals(PaymentUtil.SERVICE_DISABLED, errResponseEntity.getBody().getError().get(0).getCode());
  }
}



