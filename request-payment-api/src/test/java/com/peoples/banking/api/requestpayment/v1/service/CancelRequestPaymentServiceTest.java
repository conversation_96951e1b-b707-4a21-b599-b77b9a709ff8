package com.peoples.banking.api.requestpayment.v1.service;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import javax.validation.Validator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.mapper.CancelRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.CancelRequestPaymentRequestMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapperImpl;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {CreateRequestPaymentRequestMapperImpl.class, CancelRequestPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    PaymentStatusHistoryEntityMapperImpl.class, LocalValidatorFactoryBean.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
public class CancelRequestPaymentServiceTest {

  @InjectMocks
  private RequestPaymentService requestPaymentService;

  @Mock
  private RequestAdapter requestAdapter;

  @Mock
  private CustomerAdapter customerAdapter;

  @Mock
  private PaymentsRepository paymentsRepository;

  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;

  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;

  @SpyBean
  private Validator validator;

  @SpyBean
  private JsonConverter jsonConverter;

  @SpyBean
  private DateConverter dateConverter;

  @SpyBean
  private CancelRequestPaymentRequestMapper cancelRequestPaymentRequestMapper;

  private CancelRequestPaymentRequest cancelRequestPaymentRequest;
  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() {
    cancelRequestPaymentRequest = RequestPaymentTestUtil.createCancelRequestPaymentRequest();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, "ABC123",false);

    ReflectionTestUtils.setField(requestPaymentService, "cancelRequestPaymentRequestMapper", cancelRequestPaymentRequestMapper);
    ReflectionTestUtils.setField(requestPaymentService, "validator", validator);
    ReflectionTestUtils.setField(requestPaymentService, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(requestPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(paymentsEntityMapper, "jsonConverter", jsonConverter);
    ReflectionTestUtils.setField(paymentsEntityMapper, "dateConverter", dateConverter);
  }

  @Test
  void cancelRequestPayment_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse=RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class),isA(String.class),isA(String.class),isA(String.class));

    Payments payments=RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(cancelRequestPaymentRequest.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository).findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(
        PaymentCdType.class));
    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class),isA(String.class),isA(CancelRequestForPaymentRequest.class),
        isA(String.class));

    assertTrue(requestPaymentService.cancelRequestPayment(cancelRequestPaymentRequest, "CA1MRYeMPUSU", serviceAccountResponse, IdGeneratorUtil.generateId()));
  }

}
