package com.peoples.banking.api.requestpayment.v1.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapperImpl;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import javax.validation.Validator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentProperty;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {CreateRequestPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    PaymentStatusHistoryEntityMapperImpl.class, LocalValidatorFactoryBean.class})
@TestPropertySource(locations = {"classpath:application.properties"})
@SpringJUnitConfig()
public class RequestPaymentServiceTest {

  @InjectMocks
  private RequestPaymentService requestPaymentService;
  @Mock
  private RequestAdapter requestAdapter;
  @Mock
  private CustomerAdapter customerAdapter;
  @Mock
  private InteracAdapterProperty interacAdaptorProperty;
  @Mock
  private RequestPaymentProperty requestPaymentProperty;
  @Mock
  private PaymentsRepository paymentsRepository;
  @Mock
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
  @SpyBean
  private CreateRequestPaymentRequestMapper createRequestPaymentRequestMapper;
  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;
  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;
  @SpyBean
  private Validator validator;
  @SpyBean
  private JsonConverter jsonConverter;
  @SpyBean
  private DateConverter dateConverter;
  private CreateRequestPaymentRequest request;
  private SendRequestForPaymentResponse sendRequestResponse;
  private RetrieveCustomerProductResponse customerProductResponse;
  private Payments payments;
  private PaymentStatusHistory paymentStatusHistory;
  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() {
    request = RequestPaymentTestUtil.createRequestPaymentRequest();
    customerProductResponse = RequestPaymentTestUtil.getCustomerProductResponse();
    sendRequestResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    payments = new Payments();
    payments.setId(1);
    paymentStatusHistory = new PaymentStatusHistory();
    paymentStatusHistory.setId(1);
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);


    ReflectionTestUtils.setField(requestPaymentService, "createRequestPaymentRequestMapper", createRequestPaymentRequestMapper);
    ReflectionTestUtils.setField(requestPaymentService, "validator", validator);
    ReflectionTestUtils.setField(requestPaymentService, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(requestPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(paymentsEntityMapper, "jsonConverter", jsonConverter);
    ReflectionTestUtils.setField(paymentsEntityMapper, "dateConverter", dateConverter);
  }

  @Test
  public void sendRequestPayment_success()
      throws AdapterException, ResponseException, TimeoutException, TBDException {

    doReturn("CA000621").when(interacAdaptorProperty).getFiid();
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));
    doReturn(sendRequestResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    CreateRequestPaymentResponse response = assertDoesNotThrow(() -> {
      return requestPaymentService.createRequestPayment(request, serviceAccountResponse, IdGeneratorUtil.generateId());
    });

    assertNotNull(response);

    assertEquals(request.getOptions().getEnableNotification(),response.getOptions().getEnableNotification());
  }

  @Test
  public void sendRequestPayment_nullEmailAndMobile_fail() throws Exception {

    doReturn("CA000621").when(interacAdaptorProperty).getFiid();
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    request.getContact().setEmailAddress(null);
    request.getContact().setMobileNumber(null);
    ValidationException exception = assertThrows(ValidationException.class, () -> {
      requestPaymentService.createRequestPayment(request, serviceAccountResponse, IdGeneratorUtil.generateId());
    });

    assertNotNull(exception);
    assertEquals(1, exception.getErrorList().size());

    ErrorEntity error = exception.getErrorList().get(0);
    assertEquals(ErrorProperty.MISSING_FIELD.name(), error.getErrorCode());
    assertEquals(RequestPaymentTestUtil.JSON_PATH_CONTACT_CONTACT_INFO, error.getAdditionalInformation());
  }
}
