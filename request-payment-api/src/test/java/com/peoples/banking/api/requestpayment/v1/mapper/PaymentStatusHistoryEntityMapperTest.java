package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {PaymentStatusHistoryEntityMapperImpl.class, JsonConverter.class, DateConverter.class})
public class PaymentStatusHistoryEntityMapperTest {

  @Autowired
  private PaymentStatusHistoryEntityMapper mapper;

  private Payments payments;
  private Payments oldPayments;

  @BeforeEach
  public void setup() {
    payments = RequestPaymentTestUtil.createPayments();
    oldPayments = RequestPaymentTestUtil.createPayments();
  }

  @AfterEach
  public void tearDown() {
    payments = null;
    oldPayments = null;
  }

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_success() {
    PaymentStatusHistory paymentStatusHistory = mapper.paymentsToPaymentStatusHistory(payments, oldPayments);

    assertEquals(payments.getId(), paymentStatusHistory.getPayments().getId());
    assertEquals(payments.getStatus(), paymentStatusHistory.getStatus());
    assertEquals(payments.getStatusReasonJson(), paymentStatusHistory.getStatusReasonJson());
    assertEquals(payments.getEndToEndId(), paymentStatusHistory.getEndToEndId());
    assertEquals(oldPayments.getStatus(), paymentStatusHistory.getPreviousStatus());
  }
}
