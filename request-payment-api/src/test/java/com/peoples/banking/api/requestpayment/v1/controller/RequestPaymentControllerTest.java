package com.peoples.banking.api.requestpayment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.service.RequestPaymentService;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.ContactNameEmailPhone;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.math.BigDecimal;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.bind.MethodArgumentNotValidException;

@SpringBootTest
@AutoConfigureMockMvc
public class RequestPaymentControllerTest {

  @Autowired
  private MockMvc mvc;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void init() throws Exception {
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    serviceAccountResponse = null;
  }

  /**
   * The test requestPayment controller level happy path
   */
  @Test
  public void requestPayment_success() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @Test
  public void requestPayment_serviceAccountSuspended_success() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @Test
  public void requestPayment_nullContactEmailAndMobile_fail() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.getContact().setEmailAddress(null);
    request.getContact().setMobileNumber(null);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    assertTrue(result.getResolvedException() instanceof ValidationException);
  }

  @Test
  public void requestPayment_InvalidMobileNumber_fail() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.getContact().setMobileNumber("************");

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    assertTrue(result.getResolvedException() instanceof MethodArgumentNotValidException);
  }


  @ParameterizedTest
  @ValueSource(strings = {
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "test/<EMAIL>",
      "<EMAIL>",
      "mailhost!<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
  })
  public void requestPayment_email_RFC_success(String email) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    ContactNameEmailPhone contactNameEmailPhone = request.getContact();
    contactNameEmailPhone.setEmailAddress(email);
    request.setContact(contactNameEmailPhone);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "test",
      "Je m’appelle Jessica",
      "La présentation",
      "C'est ma vie",
      "Les fêtes en France",
      "Lettre à ma meilleure amie",
      "La célébration de Pâques",
      "Les vacances d'été",
      "Noël en France",
      "àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ''’"
  })
  public void requestPayment_memo_success(String memo) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.setMemo(memo);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "A1",
      "A",
      "ABC",
      "ABC 123",
      "ABC- Hello",
      "ABC-   Hello"
  })
  public void requestPayment_name_success(String name) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.getContact().setName(name);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      " ABC",
      "   ABC",
      "ABC ",
      "ABC   ",
      "    ABC    "
  })
  public void requestPayment_name_fail(String name) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.getContact().setName(name);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
    assertTrue(result.getResolvedException() instanceof MethodArgumentNotValidException);

  }

  @ParameterizedTest
  @ValueSource(strings = {
      "Abc.example.com",
      "A@b@<EMAIL>",
      "a\"b(c)d,e:f;g<h>i[j\\k]<EMAIL>",
      "just\"not\"<EMAIL>",
      "this is\"not\\<EMAIL>",
      "this\\ still\\\"not\\\\<EMAIL>",
      "<EMAIL>",
      "i_like_underscore@but_its_not_allowed_in_this_part.example.com",
      "QA[icon]CHOCOLATE[icon]@test.com",
      "\" \"@example.org",
      "admin@mailserver1"
  })
  public void requestPayment_email_RFC_failed(String email) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    ContactNameEmailPhone contactNameEmailPhone = request.getContact();
    contactNameEmailPhone.setEmailAddress(email);
    request.setContact(contactNameEmailPhone);

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    assertTrue(result.getResolvedException() instanceof MethodArgumentNotValidException);
  }

  @Test
  public void requestPayment_serviceAccountDisabled_fail() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    //make the service account disabled
    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    assertTrue(result.getResolvedException() instanceof ValidationException);
  }


  @ParameterizedTest
  @ValueSource(strings = {
      "michael jackson",
      "michael m. jackson",
      "michal-jackson",
      "michal- jackson",
      "michal- jackson-",
      "michal- jackson---",
      "--michal- jackson---",
      "--michal - jackson---",
      "--michal m.- jackson---",
  })
  public void requestPayment_validAccountName_success(String accountName) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    request.setAccountName(accountName);
    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
            .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      " michael jackson",
      "michael jackson ",
      " michael jackson ",
      "mişael jackson",
      "michael jacksoné",
      "émichael jacksoné",
      "émichael jackson",
      "ümichael jackson",
      "michael jacksonü",
      "ümichael jacksonü",
      "michaél jackson",
  })
  public void requestPayment_invalidAccountName_fail(String accountName) throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    request.setAccountName(accountName);
    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
            .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void createRequestPayment_missingHeaders_fail() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
            .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpectAll(status().isBadRequest(),
            content().string(RequestPaymentTestUtil.getJsonErrorResponse(ErrorProperty.MISSING_HEADER.name(), APICommonUtilConstant.HEADER_SERVICE_ACCOUNT)));
  }

  @Test
  public void createRequestPayment_invalidAmount_fail() throws Exception {
    // First create a test Payment Request
    CreateRequestPaymentRequest request = RequestPaymentTestUtil.createRequestPaymentRequest();
    request.setAmount(BigDecimal.valueOf(-100));

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    SendRequestForPaymentResponse sendRequestForPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();
    doReturn(sendRequestForPaymentResponse).when(requestAdapter).requestPayment(isA(String.class), isA(SendRequestForPaymentRequest.class),
        isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.ROOT_SERVICE_URL)
            .content(JsonUtil.toString(request)).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
            .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString()))
        .andExpectAll(status().isBadRequest(),
            content().string(RequestPaymentTestUtil.getJsonErrorResponse(ErrorProperty.INVALID_AMOUNT.name(), "amount")));
  }
}
