package com.peoples.banking.api.requestpayment.v1.controller;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class DeclinePaymentRequestControllerMockMvcTest {

  @Autowired
  private MockMvc mvc;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ServiceAccountResponse serviceAccountResponse;

  private DeclineRequestForPaymentResponse declineRequestForPaymentResponse;

  @BeforeEach
  public void init() throws Exception {
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));

    declineRequestForPaymentResponse = RequestPaymentTestUtil.createDeclineRequestForPaymentResponse();
  }

  @AfterEach
  public void tearDown() {
    serviceAccountResponse = null;
  }

  /**
   * The test use decline payment data  to call decline request to mock test
   */
  @Test
  public void declinePayment_success() throws Exception {
    //First create a test declinePayment Request
    DeclineRequestPaymentRequest request = RequestPaymentTestUtil.createDeclineRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));
    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    doReturn(declineRequestForPaymentResponse).when(requestAdapter).declineRequestPayment(isA(String.class), isA(String.class),
        isA(DeclineRequestForPaymentRequest.class), isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.DECLINE_REQUEST_PAYMENT_URL.replace("{networkRequestRefId}", payments.getNetworkPaymentRefId()))
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }
}
