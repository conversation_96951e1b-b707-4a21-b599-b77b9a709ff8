package com.peoples.banking.api.requestpayment.v1.validator;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import javax.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.peoples.banking.api.requestpayment.v1.dto.ContactJsonDto;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
public class NotNullContactInformationValidatorTest {

  private NotNullContactInformationValidator validator;

  @Mock
  ConstraintValidatorContext constraintValidatorContext;

  private ContactJsonDto contactJsonDto;

  @BeforeEach
  public void init() {
    validator = new NotNullContactInformationValidator();
    contactJsonDto = new ContactJsonDto();
    contactJsonDto.setName("contactName");
    contactJsonDto.setEmail("<EMAIL>");
    contactJsonDto.setMobile("**********");
  }

  @AfterEach
  public void tearDown() {
    validator = null;
    contactJsonDto = null;
  }

  @Test
  public void isValid_success() {
    boolean result = validator.isValid(contactJsonDto, constraintValidatorContext);

    assertTrue(result);
  }

  @Test
  public void isValid_nullEmail_success() {
    contactJsonDto.setEmail(null);
    boolean result = validator.isValid(contactJsonDto, constraintValidatorContext);

    assertTrue(result);
  }
  
  @Test
  public void isValid_nullMobile_success() {
    contactJsonDto.setMobile(null);
    boolean result = validator.isValid(contactJsonDto, constraintValidatorContext);

    assertTrue(result);
  }
  
  @Test
  public void isValid_bothNull_fail() {
    contactJsonDto.setEmail(null);
    contactJsonDto.setMobile(null);
    
    boolean result = validator.isValid(contactJsonDto, constraintValidatorContext);

    assertFalse(result);
  }

}
