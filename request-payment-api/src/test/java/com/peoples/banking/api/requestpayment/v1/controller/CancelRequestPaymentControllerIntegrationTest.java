package com.peoples.banking.api.requestpayment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CancelRequestPaymentControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private CancelRequestPaymentRequest request;

  private ServiceAccountResponse serviceAccountResponse;

  private Payments requestPayment;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    request = RequestPaymentTestUtil.createCancelRequestPaymentRequest();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    if (requestPayment != null) {
      paymentsRepository.delete(requestPayment);
      requestPayment = null;
    }
    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "REGULAR_PAYMENT",
      "FULFILL_REQUEST_FOR_PAYMENT",
      "ACCOUNT_ALIAS_PAYMENT",
      "REALTIME_ACCOUNT_ALIAS_PAYMENT",
      "ACCOUNT_DEPOSIT_PAYMENT",
      "REALTIME_ACCOUNT_DEPOSIT_PAYMENT"
  })
  public void cancelRequestPayment_notValid_NetworkPaymentType_failed(String netWorkPaymentType) throws Exception {
    requestPayment = RequestPaymentTestUtil.createPayments();
    //give all kinds wrong networkPaymentType
    requestPayment.setNetworkPaymentType(netWorkPaymentType);
    //Sync the customer ID
    requestPayment.setCustomerRefId(request.getCustomerId());
    //create DB record
    paymentsRepository.save(requestPayment);

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", requestPayment.getExternalRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());

  }

}
