package com.peoples.banking.api.requestpayment.v1.mapper;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.*;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;


@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {DeclineRequestPaymentRequestMapperImpl.class, JsonConverter.class, DateConverter.class})
public class DeclineRequestPaymentRequestMapperTest {

  @Autowired
  private DeclineRequestPaymentRequestMapper mapper;

  private DeclineRequestPaymentRequest request;

  private Payments requestPayment;

  private RetrieveCustomerProductResponse retrieveCustomerProductResponse;

  private ServiceAccountResponse serviceAccountResponse;
  private String fiId;

  @BeforeEach
  public void setup() {
    request = RequestPaymentTestUtil.createDeclineRequestPaymentRequest();
    requestPayment = RequestPaymentTestUtil.createPayments();
    retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    serviceAccountResponse = RequestPaymentTestUtil
            .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    fiId = "CA000621";
  }

  @AfterEach
  public void tearDown(){
    request = null;
    requestPayment = null;
  }

  @Test
  public void declineRequestPaymentRequestToPaymentDto_success() throws Exception {
    DeclineRequestPaymentRequest declineRequestPaymentRequest = RequestPaymentTestUtil.createDeclineRequestPaymentRequest();

    PaymentDto paymentDto = mapper.declineRequestPaymentRequestToPaymentDto(declineRequestPaymentRequest, requestPayment.getNetworkPaymentRefId(),
            serviceAccountResponse, retrieveCustomerProductResponse, fiId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT);

    assertEquals(paymentDto.getCustomerExternalId(), declineRequestPaymentRequest.getCustomerId());
    assertEquals(paymentDto.getStatusReasonDto().getStatusReasonJsonDto().getReasonCode(), declineRequestPaymentRequest.getReasonCode().getValue());
    assertEquals(paymentDto.getStatusReasonDto().getStatusReasonJsonDto().getDescription(), declineRequestPaymentRequest.getReasonDescription());
    assertEquals(paymentDto.getNetworkPaymentRefId(), requestPayment.getNetworkPaymentRefId());
    assertEquals(paymentDto.getIndirectConnectorId(), serviceAccountResponse.getIndirectConnectorId());
    assertEquals(paymentDto.getNetworkEnrollmentId(), retrieveCustomerProductResponse.getEnrollmentRefId());
    assertEquals(paymentDto.getConnectorType(), serviceAccountResponse.getConnectorType().toString());
    assertEquals(paymentDto.getFiId(), fiId);
    assertEquals(paymentDto.getNetworkRefId(), NetworkChannelType.EMT);
    assertEquals(paymentDto.getNetworkPaymentType(), NetworkPaymentType.REQUEST_FOR_PAYMENT);
  }

  @Test
  public void paymentDtoToDeclineRequestForPaymentRequest_success() {
    PaymentDto paymentDto = new PaymentDto();

    StatusReasonDto statusReasonDto = new StatusReasonDto();
    StatusReasonJsonDto statusReasonJsonDto = new StatusReasonJsonDto();
    statusReasonJsonDto.setDescription("Decline the request");
    statusReasonJsonDto.setReasonCode("CUSTOMER_INITIATED");
    statusReasonDto.setStatusReasonJsonDto(statusReasonJsonDto);
    paymentDto.setStatusReasonDto(statusReasonDto);

    DeclineRequestForPaymentRequest request = mapper.paymentDtoToDeclineRequestPaymentRequest(paymentDto);

    assertEquals(statusReasonJsonDto.getDescription(), request.getDeclineReason());
  }
}
