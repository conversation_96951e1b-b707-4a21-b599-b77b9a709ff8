package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.ContactJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.FraudResultJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.OptionJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonJsonDto;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.TransactionType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {CancelRequestPaymentRequestMapperImpl.class, JsonConverter.class, DateConverter.class})
public class CancelRequestPaymentRequestMapperTest {

  @Autowired
  private CancelRequestPaymentRequestMapper mapper;

  private CancelRequestPaymentRequest request;

  private Payments requestPayment;

  @BeforeEach
  public void setup() {
    request = RequestPaymentTestUtil.createCancelRequestPaymentRequest();
    requestPayment = RequestPaymentTestUtil.createPayments();
  }

  @AfterEach
  public void tearDown(){
    request = null;
    requestPayment = null;
  }

  @Test
  public void cancelRequestPaymentRequestToPaymentDto_success() throws Exception {
    PaymentDto paymentDto = mapper.paymentsAndCancelRequestPaymentRequestToPaymentDto(requestPayment, request);

    assertEquals(requestPayment.getExternalRefId(), paymentDto.getExternalRefId());
    assertEquals(requestPayment.getCustomerRefId(), paymentDto.getCustomerExternalId());
    assertEquals(requestPayment.getNetworkEnrollmentId(), paymentDto.getNetworkEnrollmentId());
    assertEquals(requestPayment.getServiceAccountRefId(), paymentDto.getServiceAccountRefId());
    assertEquals(request.getEndToEndId(), paymentDto.getEndToEndId());
    assertEquals(TransactionType.REQUEST, paymentDto.getTransactionType());
    assertNull(paymentDto.getParent());
    assertEquals(requestPayment.getId(), paymentDto.getId());
    assertEquals(NetworkChannelType.EMT, paymentDto.getNetworkRefId());
    assertEquals(requestPayment.getNetworkPaymentRefId(), paymentDto.getNetworkPaymentRefId());
    assertEquals(NetworkPaymentType.REQUEST_FOR_PAYMENT, paymentDto.getNetworkPaymentType());

    ContactJsonDto contactJsonDto = JsonUtil.toObject(requestPayment.getContactJson(), ContactJsonDto.class);
    assertEquals(requestPayment.getContactJsonVersion(), paymentDto.getContactDto().getVersion());
    assertEquals(contactJsonDto.getName(), paymentDto.getContactDto().getContactJsonDto().getName());
    assertEquals(contactJsonDto.getEmail(), paymentDto.getContactDto().getContactJsonDto().getEmail());
    assertEquals(contactJsonDto.getMobile(), paymentDto.getContactDto().getContactJsonDto().getMobile());
    assertEquals(requestPayment.getAmount(), paymentDto.getAmount());
    assertEquals(requestPayment.getAccountName(), paymentDto.getAccountDto().getName());
    assertEquals(requestPayment.getAccountNumber(), paymentDto.getAccountDto().getAccountNumber());

    OptionJsonDto optionJsonDto = JsonUtil.toObject(requestPayment.getOptionJson(), OptionJsonDto.class);
    assertEquals(requestPayment.getOptionJsonVersion(), paymentDto.getOptionDto().getVersion());
    assertEquals(optionJsonDto.isAmountModification(), paymentDto.getOptionDto().getOptionJsonDto().isAmountModification());
    assertEquals(optionJsonDto.getExpiredAfterDays(), paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays());
    assertEquals(optionJsonDto.isEnableNotification(), paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification());

    assertEquals(requestPayment.getExpiryDate(), DateUtil.toLocalDateTime(paymentDto.getExpiryDate()));
    assertEquals(requestPayment.getNetworkPaymentUrl(), paymentDto.getNetworkUrl());
    assertEquals(requestPayment.getStatus(), paymentDto.getStatus().toString());

    StatusReasonJsonDto statusReasonJsonDto = JsonUtil.toObject(requestPayment.getStatusReasonJson(), StatusReasonJsonDto.class);
    assertEquals(requestPayment.getStatusReasonJsonVersion(), paymentDto.getStatusReasonDto().getVersion());
    assertEquals(request.getReasonCode().getValue(), paymentDto.getStatusReasonDto().getStatusReasonJsonDto().getReasonCode());
    assertEquals(request.getReasonDescription(), paymentDto.getStatusReasonDto().getStatusReasonJsonDto().getDescription());

    FraudResultJsonDto fraudResultJsonDto = JsonUtil.toObject(requestPayment.getNetworkFraudResultJson(), FraudResultJsonDto.class);
    assertEquals(requestPayment.getNetworkFraudResultJsonVersion(), paymentDto.getFraudResultDto().getVersion());
    assertEquals(fraudResultJsonDto.getAction(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getAction());
    assertEquals(fraudResultJsonDto.getReason(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getReason());
    assertEquals(fraudResultJsonDto.getScore(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getScore());

    assertEquals(requestPayment.getSettlementDate(), DateUtil.toLocalDateTime(paymentDto.getSettlementDate()));
    assertEquals(requestPayment.isNetworkSettled(), paymentDto.isNetworkSettled());
    assertEquals(requestPayment.isSaSettled(), paymentDto.isSaSettled());
    assertEquals(requestPayment.getCreatedOn(), DateUtil.toLocalDateTime(paymentDto.getCreatedDate()));
    assertEquals(requestPayment.getUpdatedOn(), DateUtil.toLocalDateTime(paymentDto.getUpdatedDate()));
  }

  @Test
  public void paymentDtoToCancelRequestForPaymentRequest_success() {
    PaymentDto paymentDto = new PaymentDto();

    StatusReasonDto statusReasonDto = new StatusReasonDto();
    StatusReasonJsonDto statusReasonJsonDto = new StatusReasonJsonDto();
    statusReasonJsonDto.setDescription("Cancel the request");
    statusReasonJsonDto.setReasonCode("CUSTOMER_INITIATED");
    statusReasonDto.setStatusReasonJsonDto(statusReasonJsonDto);
    paymentDto.setStatusReasonDto(statusReasonDto);

    CancelRequestForPaymentRequest request = mapper.paymentDtoToCancelRequestPaymentRequest(paymentDto);

    assertEquals(statusReasonJsonDto.getDescription(), request.getCancelReason());
  }
}
