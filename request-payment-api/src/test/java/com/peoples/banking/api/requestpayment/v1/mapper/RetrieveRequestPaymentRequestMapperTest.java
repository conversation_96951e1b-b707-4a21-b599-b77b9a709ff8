package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.domain.requestpayment.model.ContactAccountInfo;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequestOptions;
import com.peoples.banking.domain.requestpayment.model.Fulfillment;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.Status;
import com.peoples.banking.util.api.common.AccountNumberUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {RetrieveRequestPaymentRequestMapperImpl.class, JsonConverter.class, DateConverter.class})
public class RetrieveRequestPaymentRequestMapperTest {

  @Autowired
  private RetrieveRequestPaymentRequestMapper mapper;

  private PaymentDto paymentDto;
  private PaymentDto fulfillment;

  @BeforeEach
  public void setup() {
    paymentDto = RequestPaymentTestUtil.createPaymentDto();
    fulfillment = RequestPaymentTestUtil.createPaymentDto();
  }

  @AfterEach
  public void tearDown(){
    paymentDto = null;
    fulfillment = null;
  }

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_success(){
    RetrieveRequestPaymentResponse retrieveRequestPaymentResponse = mapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto, fulfillment);

    assertEquals(paymentDto.getCustomerExternalId(), retrieveRequestPaymentResponse.getCustomerId());
    assertEquals(paymentDto.getEndToEndId(), retrieveRequestPaymentResponse.getEndToEndId());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), retrieveRequestPaymentResponse.getContact().getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), retrieveRequestPaymentResponse.getContact().getEmailAddress());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getMobile(), retrieveRequestPaymentResponse.getContact().getMobileNumber());
    CreateRequestPaymentRequestOptions options = retrieveRequestPaymentResponse.getOptions();
    assertNotNull(options);
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays(), options.getExpireAfterDays());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification(), options.getEnableNotification());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isAmountModification(), options.getAmountModification());
    assertEquals(paymentDto.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo(), retrieveRequestPaymentResponse.getMemo());
    assertEquals(paymentDto.getExternalRefId(), retrieveRequestPaymentResponse.getRequestRefId());
    assertEquals(paymentDto.getNetworkUrl(), retrieveRequestPaymentResponse.getRequestUrl());
    assertEquals(Status.ACCEPTED, retrieveRequestPaymentResponse.getStatus());
    assertEquals(paymentDto.getExpiryDate(), retrieveRequestPaymentResponse.getExpiryDate());
    assertEquals(paymentDto.getCreatedDate(), retrieveRequestPaymentResponse.getCreatedDate());
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), retrieveRequestPaymentResponse.getAccountNumber());
    Fulfillment paymentFulfillment = retrieveRequestPaymentResponse.getFulfillment();
    assertNotNull(paymentFulfillment);
    assertNotNull(paymentFulfillment.getExternalFiInfo());
    ContactAccountInfo creditor = paymentFulfillment.getExternalFiInfo().getCreditor();
    assertNotNull(creditor);
    assertEquals(AccountNumberUtil.maskAccountNumber(fulfillment.getExternalFiAccountInfoDto().getExternalFiAccountInfoJsonDto().getCreditor().getAccountNumber()), creditor.getAccountNumber());
  }

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_nullOption_success(){
    paymentDto.setOptionDto(null);

    RetrieveRequestPaymentResponse retrieveRequestPaymentResponse = mapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto, fulfillment);

    assertEquals(paymentDto.getCustomerExternalId(), retrieveRequestPaymentResponse.getCustomerId());
    assertEquals(paymentDto.getEndToEndId(), retrieveRequestPaymentResponse.getEndToEndId());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), retrieveRequestPaymentResponse.getContact().getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), retrieveRequestPaymentResponse.getContact().getEmailAddress());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getMobile(), retrieveRequestPaymentResponse.getContact().getMobileNumber());
    assertNull(retrieveRequestPaymentResponse.getOptions());
    assertEquals(paymentDto.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo(), retrieveRequestPaymentResponse.getMemo());
    assertEquals(paymentDto.getExternalRefId(), retrieveRequestPaymentResponse.getRequestRefId());
    assertEquals(paymentDto.getNetworkUrl(), retrieveRequestPaymentResponse.getRequestUrl());
    assertEquals(Status.ACCEPTED, retrieveRequestPaymentResponse.getStatus());
    assertEquals(paymentDto.getExpiryDate(), retrieveRequestPaymentResponse.getExpiryDate());
    assertEquals(paymentDto.getCreatedDate(), retrieveRequestPaymentResponse.getCreatedDate());
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), retrieveRequestPaymentResponse.getAccountNumber());
    Fulfillment paymentFulfillment = retrieveRequestPaymentResponse.getFulfillment();
    assertNotNull(paymentFulfillment);
    assertNotNull(paymentFulfillment.getExternalFiInfo());
    ContactAccountInfo creditor = paymentFulfillment.getExternalFiInfo().getCreditor();
    assertNotNull(creditor);
    assertEquals(AccountNumberUtil.maskAccountNumber(fulfillment.getExternalFiAccountInfoDto().getExternalFiAccountInfoJsonDto().getCreditor().getAccountNumber()), creditor.getAccountNumber());
  }

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_nullRequestUrl_success(){
    paymentDto.setNetworkUrl(null);

    RetrieveRequestPaymentResponse retrieveRequestPaymentResponse = mapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto, fulfillment);

    assertEquals(paymentDto.getCustomerExternalId(), retrieveRequestPaymentResponse.getCustomerId());
    assertEquals(paymentDto.getEndToEndId(), retrieveRequestPaymentResponse.getEndToEndId());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), retrieveRequestPaymentResponse.getContact().getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), retrieveRequestPaymentResponse.getContact().getEmailAddress());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getMobile(), retrieveRequestPaymentResponse.getContact().getMobileNumber());
    CreateRequestPaymentRequestOptions options = retrieveRequestPaymentResponse.getOptions();
    assertNotNull(options);
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays(), options.getExpireAfterDays());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification(), options.getEnableNotification());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isAmountModification(), options.getAmountModification());
    assertEquals(paymentDto.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo(), retrieveRequestPaymentResponse.getMemo());
    assertEquals(paymentDto.getExternalRefId(), retrieveRequestPaymentResponse.getRequestRefId());
    assertNull(retrieveRequestPaymentResponse.getRequestUrl());
    assertEquals(Status.ACCEPTED, retrieveRequestPaymentResponse.getStatus());
    assertEquals(paymentDto.getExpiryDate(), retrieveRequestPaymentResponse.getExpiryDate());
    assertEquals(paymentDto.getCreatedDate(), retrieveRequestPaymentResponse.getCreatedDate());
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), retrieveRequestPaymentResponse.getAccountNumber());
    Fulfillment paymentFulfillment = retrieveRequestPaymentResponse.getFulfillment();
    assertNotNull(paymentFulfillment);
    assertNotNull(paymentFulfillment.getExternalFiInfo());
    ContactAccountInfo creditor = paymentFulfillment.getExternalFiInfo().getCreditor();
    assertNotNull(creditor);
    assertEquals(AccountNumberUtil.maskAccountNumber(fulfillment.getExternalFiAccountInfoDto().getExternalFiAccountInfoJsonDto().getCreditor().getAccountNumber()), creditor.getAccountNumber());
  }

}
