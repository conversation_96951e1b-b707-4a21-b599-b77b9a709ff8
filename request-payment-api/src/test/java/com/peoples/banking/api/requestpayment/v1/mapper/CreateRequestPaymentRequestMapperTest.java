package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccount;
import com.peoples.banking.util.api.common.type.TransactionType;
import java.time.OffsetDateTime;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.partner.domain.interac.request.model.ActiveOrHistoricCurrencyCode;
import com.peoples.banking.partner.domain.interac.request.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.request.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.request.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.request.model.CreditTransferTransaction35;
import com.peoples.banking.partner.domain.interac.request.model.CreditorPaymentActivationRequestV07;
import com.peoples.banking.partner.domain.interac.request.model.GroupHeader78;
import com.peoples.banking.partner.domain.interac.request.model.Language;
import com.peoples.banking.partner.domain.interac.request.model.PartyIdentification135;
import com.peoples.banking.partner.domain.interac.request.model.PaymentCondition1;
import com.peoples.banking.partner.domain.interac.request.model.PaymentInstruction31;
import com.peoples.banking.partner.domain.interac.request.model.PaymentMethod7Code;
import com.peoples.banking.partner.domain.interac.request.model.PaymentTransaction104;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {CreateRequestPaymentRequestMapperImpl.class})
public class CreateRequestPaymentRequestMapperTest {

  private static final String NOT_PROVIDED = "NOTPROVIDED";
  private static final String INDIRECT_CONNECTOR_ID = "INDIRECT_CONNECTOR_ID";
  private static final String INDIRECT_CONNECTOR_TYPE = ServiceAccount.ConnectorTypeEnum.INDIRECT.getValue();

  @Autowired
  private CreateRequestPaymentRequestMapper mapper;


  private CreateRequestPaymentRequest request;
  private String serviceAccountRefId;
  private int defaultExpiredDays;
  private String networkEnrollmentId;
  private String fiId;
  private String paymentRefId;


  @BeforeEach
  public void setup() {
    request = RequestPaymentTestUtil.createRequestPaymentRequest();
    serviceAccountRefId = "ABC123";
    defaultExpiredDays = 45;
    networkEnrollmentId = "enrollId1";
    fiId = "CA000621";
    paymentRefId = "eiwi39Eo23";
  }

  @Test
  public void requestPaymentRequestToPaymentDto_success() {
    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    assertEquals(request.getCustomerId(), paymentDto.getCustomerExternalId());
    assertEquals(networkEnrollmentId, paymentDto.getNetworkEnrollmentId());
    assertEquals(serviceAccountRefId, paymentDto.getServiceAccountRefId());
    assertEquals(request.getEndToEndId(), paymentDto.getEndToEndId());
    assertEquals(TransactionType.REQUEST, paymentDto.getTransactionType());
    assertEquals(NetworkChannelType.EMT, paymentDto.getNetworkRefId());
    assertEquals(NetworkPaymentType.REQUEST_FOR_PAYMENT, paymentDto.getNetworkPaymentType());
    assertEquals(request.getContact().getName(), paymentDto.getContactDto().getContactJsonDto().getName());
    assertEquals(request.getContact().getEmailAddress(), paymentDto.getContactDto().getContactJsonDto().getEmail());
    assertEquals(request.getContact().getMobileNumber(), paymentDto.getContactDto().getContactJsonDto().getMobile());
    assertEquals(request.getAmount(), paymentDto.getAmount());
    assertEquals(request.getAccountName(), paymentDto.getAccountDto().getName());
    assertEquals(request.getAccountNumber(), paymentDto.getAccountDto().getAccountNumber());
    assertEquals(request.getOptions().getAmountModification(), paymentDto.getOptionDto().getOptionJsonDto().isAmountModification());
    assertEquals(request.getOptions().getEnableNotification(), paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification());
    assertEquals(request.getOptions().getExpireAfterDays(), paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays());

    OffsetDateTime expiryDate = DateUtil.getCurrentUTCDateTime();
    expiryDate = expiryDate.plusDays(request.getOptions().getExpireAfterDays());
    assertEquals(expiryDate.getYear(), paymentDto.getExpiryDate().getYear());
    assertEquals(expiryDate.getMonth(), paymentDto.getExpiryDate().getMonth());
    assertEquals(expiryDate.getDayOfMonth(), paymentDto.getExpiryDate().getDayOfMonth());
    assertEquals(expiryDate.getHour(), paymentDto.getExpiryDate().getHour());

    OffsetDateTime currentDateTime = DateUtil.getCurrentUTCDateTime();
    assertEquals(currentDateTime.getYear(), paymentDto.getCreatedDate().getYear());
    assertEquals(currentDateTime.getMonth(), paymentDto.getCreatedDate().getMonth());
    assertEquals(currentDateTime.getDayOfMonth(), paymentDto.getCreatedDate().getDayOfMonth());
  }

  @Test
  public void requestPaymentRequestToPaymentDto_nullExpiredAfterDays_success() {
    request.getOptions().setExpireAfterDays(null);

    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    assertEquals(request.getCustomerId(), paymentDto.getCustomerExternalId());
    assertEquals(networkEnrollmentId, paymentDto.getNetworkEnrollmentId());
    assertEquals(serviceAccountRefId, paymentDto.getServiceAccountRefId());
    assertEquals(request.getEndToEndId(), paymentDto.getEndToEndId());
    assertEquals(TransactionType.REQUEST, paymentDto.getTransactionType());
    assertEquals(NetworkChannelType.EMT, paymentDto.getNetworkRefId());
    assertEquals(NetworkPaymentType.REQUEST_FOR_PAYMENT, paymentDto.getNetworkPaymentType());
    assertEquals(request.getContact().getName(), paymentDto.getContactDto().getContactJsonDto().getName());
    assertEquals(request.getContact().getEmailAddress(), paymentDto.getContactDto().getContactJsonDto().getEmail());
    assertEquals(request.getContact().getMobileNumber(), paymentDto.getContactDto().getContactJsonDto().getMobile());
    assertEquals(request.getAmount(), paymentDto.getAmount());
    assertEquals(request.getAccountName(), paymentDto.getAccountDto().getName());
    assertEquals(request.getAccountNumber(), paymentDto.getAccountDto().getAccountNumber());
    assertEquals(request.getOptions().getAmountModification(), paymentDto.getOptionDto().getOptionJsonDto().isAmountModification());
    assertEquals(request.getOptions().getEnableNotification(), paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification());
    assertEquals(45, paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays());

    OffsetDateTime expiryDate = DateUtil.getCurrentUTCDateTime();
    expiryDate = expiryDate.plusDays(45);
    assertEquals(expiryDate.getYear(), paymentDto.getExpiryDate().getYear());
    assertEquals(expiryDate.getMonth(), paymentDto.getExpiryDate().getMonth());
    assertEquals(expiryDate.getDayOfMonth(), paymentDto.getExpiryDate().getDayOfMonth());
    assertEquals(expiryDate.getHour(), paymentDto.getExpiryDate().getHour());

    OffsetDateTime currentDateTime = DateUtil.getCurrentUTCDateTime();
    assertEquals(currentDateTime.getYear(), paymentDto.getCreatedDate().getYear());
    assertEquals(currentDateTime.getMonth(), paymentDto.getCreatedDate().getMonth());
    assertEquals(currentDateTime.getDayOfMonth(), paymentDto.getCreatedDate().getDayOfMonth());
  }

  @Test
  public void paymentDtoToSendRequestForPaymentRequest_success() {
    request.setMemo("");

    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    paymentDto.setEndToEndId(RandomStringUtils.randomAlphanumeric(36));

    SendRequestForPaymentRequest sendRequestPaymentRequest = mapper.paymentDtoToSendRequestForPaymentRequest(paymentDto);

    OffsetDateTime now = DateUtil.getCurrentUTCDateTime();

    assertEquals(paymentDto.getAccountDto().getName(), sendRequestPaymentRequest.getAccountHolderName());
    assertEquals(Language.EN, sendRequestPaymentRequest.getLanguage());

    CreditorPaymentActivationRequestV07 creditorPaymentActivationRequest = sendRequestPaymentRequest.getCreditorPaymentActivationRequest();

    GroupHeader78 groupHeader = creditorPaymentActivationRequest.getGroupHeader();
    assertNotNull(groupHeader.getMessageIdentification());
    assertEquals("1", groupHeader.getNumberOfTransactions());
    assertEquals(now.getYear(), groupHeader.getCreationDatetime().getYear());
    assertEquals(now.getMonth(), groupHeader.getCreationDatetime().getMonth());
    assertEquals(now.getDayOfMonth(), groupHeader.getCreationDatetime().getDayOfMonth());
    assertEquals(now.getHour(), groupHeader.getCreationDatetime().getHour());
    assertEquals(now.getMinute(), groupHeader.getCreationDatetime().getMinute());
    assertEquals(NOT_PROVIDED, groupHeader.getInitiatingParty().getName());

    List<PaymentInstruction31> paymentInformationList = creditorPaymentActivationRequest.getPaymentInformation();
    assertEquals(1, paymentInformationList.size());
    PaymentInstruction31 paymentInformation = paymentInformationList.get(0);
    assertNotNull(paymentInformation.getPaymentInformationIdentification());
    assertEquals(PaymentMethod7Code.TRF, paymentInformation.getPaymentMethod());
    assertEquals(now.getYear(), paymentInformation.getRequestedExecutionDate().getYear());
    assertEquals(now.getMonth(), paymentInformation.getRequestedExecutionDate().getMonth());
    assertEquals(now.getDayOfMonth(), paymentInformation.getRequestedExecutionDate().getDayOfMonth());
    assertEquals(now.getHour(), paymentInformation.getRequestedExecutionDate().getHour());
    assertEquals(now.getMinute(), paymentInformation.getRequestedExecutionDate().getMinute());
    assertEquals(paymentDto.getExpiryDate(), paymentInformation.getExpiryDate());

    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification(),
        !sendRequestPaymentRequest.getSuppressResponderNotifications());

    PaymentCondition1 paymentCondition = paymentInformation.getPaymentCondition();
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isAmountModification(), paymentCondition.getAmountModificationAllowed());
    assertEquals(false, paymentCondition.getEarlyPaymentAllowed());
    assertEquals(false, paymentCondition.getGuaranteedPaymentRequested());

    PartyIdentification135 debtor = paymentInformation.getDebtor();
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), debtor.getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), debtor.getContactDetails().getEmailAddress());
    assertEquals("+1-" + paymentDto.getContactDto().getContactJsonDto().getMobile(), debtor.getContactDetails().getMobileNumber());

    BranchAndFinancialInstitutionIdentification6 debtorAgent = paymentInformation.getDebtorAgent();
    assertEquals(NOT_PROVIDED,
        debtorAgent.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification());

    List<CreditTransferTransaction35> creditTransferTransactionList = paymentInformation.getCreditTransferTransaction();
    assertEquals(1, creditTransferTransactionList.size());
    CreditTransferTransaction35 creditTransferTransaction = creditTransferTransactionList.get(0);
    assertEquals(paymentDto.getExternalRefId(), creditTransferTransaction.getPaymentIdentification().getInstructionIdentification());

    //PTC side endtoEndId is 36, interac endtoEndId max is 35
    assertEquals(paymentDto.getEndToEndId().replaceAll("-", "").substring(0, 35),
        sendRequestPaymentRequest.getCreditorPaymentActivationRequest().getPaymentInformation().get(0).getCreditTransferTransaction().get(0)
            .getPaymentIdentification().getEndToEndIdentification());

    assertEquals(paymentDto.getAmount(), creditTransferTransaction.getAmount().getInstructedAmount().getAmount());
    assertEquals(ActiveOrHistoricCurrencyCode.CAD, creditTransferTransaction.getAmount().getInstructedAmount().getCurrency());

    assertEquals(ChargeBearerType1Code.SLEV, creditTransferTransaction.getChargeBearer());

    BranchAndFinancialInstitutionIdentification6 creditorAgent = creditTransferTransaction.getCreditorAgent();
    assertEquals(paymentDto.getIndirectConnectorId(),
        creditorAgent.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification());

    PartyIdentification135 creditor = creditTransferTransaction.getCreditor();
    assertEquals(paymentDto.getNetworkEnrollmentId(),
        creditor.getIdentification().getOrganisationIdentification().getOther().get(0).getIdentification());

    CashAccount38 creditorAccount = creditTransferTransaction.getCreditorAccount();
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), creditorAccount.getIdentification().getOther().getIdentification());


  }

  @Test
  public void paymentDtoToSendRequestForPaymentRequest_nullMobile_success() {
    request.setMemo("");
    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    paymentDto.getContactDto().getContactJsonDto().setMobile(null);
    paymentDto.setEndToEndId(RandomStringUtils.randomAlphanumeric(36));

    SendRequestForPaymentRequest sendRequestPaymentRequest = mapper.paymentDtoToSendRequestForPaymentRequest(paymentDto);
    OffsetDateTime now = DateUtil.getCurrentUTCDateTime();

    assertEquals(paymentDto.getAccountDto().getName(), sendRequestPaymentRequest.getAccountHolderName());
    assertEquals(Language.EN, sendRequestPaymentRequest.getLanguage());

    CreditorPaymentActivationRequestV07 creditorPaymentActivationRequest = sendRequestPaymentRequest.getCreditorPaymentActivationRequest();

    GroupHeader78 groupHeader = creditorPaymentActivationRequest.getGroupHeader();
    assertNotNull(groupHeader.getMessageIdentification());
    assertEquals("1", groupHeader.getNumberOfTransactions());
    assertEquals(now.getYear(), groupHeader.getCreationDatetime().getYear());
    assertEquals(now.getMonth(), groupHeader.getCreationDatetime().getMonth());
    assertEquals(now.getDayOfMonth(), groupHeader.getCreationDatetime().getDayOfMonth());
    assertEquals(now.getHour(), groupHeader.getCreationDatetime().getHour());
    assertEquals(now.getMinute(), groupHeader.getCreationDatetime().getMinute());
    assertEquals(NOT_PROVIDED, groupHeader.getInitiatingParty().getName());

    List<PaymentInstruction31> paymentInformationList = creditorPaymentActivationRequest.getPaymentInformation();
    assertEquals(1, paymentInformationList.size());
    PaymentInstruction31 paymentInformation = paymentInformationList.get(0);
    assertNotNull(paymentInformation.getPaymentInformationIdentification());
    assertEquals(PaymentMethod7Code.TRF, paymentInformation.getPaymentMethod());
    assertEquals(now.getYear(), paymentInformation.getRequestedExecutionDate().getYear());
    assertEquals(now.getMonth(), paymentInformation.getRequestedExecutionDate().getMonth());
    assertEquals(now.getDayOfMonth(), paymentInformation.getRequestedExecutionDate().getDayOfMonth());
    assertEquals(now.getHour(), paymentInformation.getRequestedExecutionDate().getHour());
    assertEquals(now.getMinute(), paymentInformation.getRequestedExecutionDate().getMinute());
    assertEquals(paymentDto.getExpiryDate(), paymentInformation.getExpiryDate());

    PaymentCondition1 paymentCondition = paymentInformation.getPaymentCondition();
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isAmountModification(), paymentCondition.getAmountModificationAllowed());
    assertEquals(false, paymentCondition.getEarlyPaymentAllowed());
    assertEquals(false, paymentCondition.getGuaranteedPaymentRequested());

    PartyIdentification135 debtor = paymentInformation.getDebtor();
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), debtor.getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), debtor.getContactDetails().getEmailAddress());
    assertNull(debtor.getContactDetails().getMobileNumber());

    BranchAndFinancialInstitutionIdentification6 debtorAgent = paymentInformation.getDebtorAgent();
    assertEquals(NOT_PROVIDED,
        debtorAgent.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification());

    List<CreditTransferTransaction35> creditTransferTransactionList = paymentInformation.getCreditTransferTransaction();
    assertEquals(1, creditTransferTransactionList.size());
    CreditTransferTransaction35 creditTransferTransaction = creditTransferTransactionList.get(0);
    assertEquals(paymentDto.getExternalRefId(), creditTransferTransaction.getPaymentIdentification().getInstructionIdentification());
    assertEquals(paymentDto.getEndToEndId().replaceAll("-", "").substring(0,35),
        creditTransferTransaction.getPaymentIdentification().getEndToEndIdentification());
    assertEquals(paymentDto.getAmount(), creditTransferTransaction.getAmount().getInstructedAmount().getAmount());
    assertEquals(ActiveOrHistoricCurrencyCode.CAD, creditTransferTransaction.getAmount().getInstructedAmount().getCurrency());

    assertEquals(ChargeBearerType1Code.SLEV, creditTransferTransaction.getChargeBearer());

    BranchAndFinancialInstitutionIdentification6 creditorAgent = creditTransferTransaction.getCreditorAgent();
    assertEquals(paymentDto.getIndirectConnectorId(),
        creditorAgent.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification());

    PartyIdentification135 creditor = creditTransferTransaction.getCreditor();
    assertEquals(paymentDto.getNetworkEnrollmentId(),
        creditor.getIdentification().getOrganisationIdentification().getOther().get(0).getIdentification());

    CashAccount38 creditorAccount = creditTransferTransaction.getCreditorAccount();
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), creditorAccount.getIdentification().getOther().getIdentification());
  }

  @Test
  public void sendRequestForPaymentResponseToPaymentDto_success() {
    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    SendRequestForPaymentResponse response = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    paymentDto = mapper.sendRequestForPaymentResponseToPaymentDto(paymentDto, response);

    PaymentTransaction104 paymentTransaction = response.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().get(0);

    assertEquals(paymentTransaction.getClearingSystemReference(), paymentDto.getNetworkPaymentRefId());
    assertEquals(PaymentStatus.AVAILABLE, paymentDto.getStatus());

    assertEquals(response.getFraudCheckResult().getAction().toString(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getAction());
    assertEquals(response.getFraudCheckResult().getReason(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getReason());
    assertEquals(response.getFraudCheckResult().getScore(), paymentDto.getFraudResultDto().getFraudResultJsonDto().getScore());

    assertEquals(response.getGatewayUrl(), paymentDto.getNetworkUrl());
  }

  @Test
  public void paymentDtoToRequestPaymentResponse_success() {
    PaymentDto paymentDto = mapper.createRequestPaymentRequestToPaymentDto(request, fiId, networkEnrollmentId, serviceAccountRefId,
        paymentRefId, NetworkChannelType.EMT, NetworkPaymentType.REQUEST_FOR_PAYMENT, defaultExpiredDays, INDIRECT_CONNECTOR_TYPE, INDIRECT_CONNECTOR_ID);

    SendRequestForPaymentResponse sendRequestPaymentResponse = RequestPaymentTestUtil.createSendRequestForPaymentResponse();

    paymentDto = mapper.sendRequestForPaymentResponseToPaymentDto(paymentDto, sendRequestPaymentResponse);

    CreateRequestPaymentResponse response = mapper.paymentDtoToCreateRequestPaymentResponse(paymentDto);

    assertEquals(paymentDto.getCustomerExternalId(), response.getCustomerId());
    assertEquals(paymentDto.getEndToEndId(), response.getEndToEndId());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), response.getContact().getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(paymentDto.getAmount(), response.getAmount());
    assertEquals(paymentDto.getAccountDto().getName(), response.getAccountName());
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), response.getAccountNumber());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().getExpiredAfterDays(), response.getOptions().getExpireAfterDays());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isAmountModification(), response.getOptions().getAmountModification());
    assertEquals(paymentDto.getOptionDto().getOptionJsonDto().isEnableNotification(), response.getOptions().getEnableNotification());
    assertEquals(paymentDto.getExternalRefId(), response.getRequestRefId());
    assertEquals(paymentDto.getNetworkUrl(), response.getRequestUrl());
    assertEquals(paymentDto.getStatus().toString(), response.getStatus().toString());
    assertEquals(paymentDto.getExpiryDate(), response.getExpiryDate());
    assertEquals(paymentDto.getCreatedDate(), response.getCreatedDate());

  }

}
