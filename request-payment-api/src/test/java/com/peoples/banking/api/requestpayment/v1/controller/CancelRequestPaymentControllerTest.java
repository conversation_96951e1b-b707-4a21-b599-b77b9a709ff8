package com.peoples.banking.api.requestpayment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.api.requestpayment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CancelRequestPaymentControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private CancelRequestPaymentRequest request;

  private ServiceAccountResponse serviceAccountResponse;

  private Payments requestPayment;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    request = RequestPaymentTestUtil.createCancelRequestPaymentRequest();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    if (requestPayment != null) {
      paymentsRepository.delete(requestPayment);
      requestPayment = null;
    }
    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test cancelRequestPayment happy path
   */
  @Test
  public void cancelRequestPayment_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  /**
   * The test cancelRequestPayment missing customer ID to trigger error
   */
  @Test
  public void cancelRequestPayment_missingCustomerID_failed() throws Exception {
    //Empty customerID to trigger error
    request.setCustomerId(null);
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment missing reason code to trigger error
   */
  @Test
  public void cancelRequestPayment_missingReasonCode_failed() throws Exception {
    //Empty customerID to trigger error
    request.setReasonCode(null);
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment payment not exist to trigger error
   */
  @Test
  public void cancelRequestPayment_paymentNotExist_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

  }

  /**
   * The test cancelRequestPayment missing reason code to trigger error
   */
  @Test
  public void cancelRequestPayment_customerId_notMatch_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_CUSTOMER, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment payment status is not available to trigger error
   */
  @Test
  public void cancelRequestPayment_paymentStatus_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    payments.setStatus("FAILED");
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_PAYMENT_STATUS, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment interac throw exception customer did not exist to trigger error
   */
  @Test
  public void cancelRequestPayment_interac_customerNotExist_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("301");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment interac throw exception payment reference num did not exist to trigger error
   */
  @Test
  public void cancelRequestPayment_interac_paymentNotExist_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("511");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment interac throw exception Customer is not the Sender of this request for payment return did not exist to
   * trigger error
   */
  @Test
  public void cancelRequestPayment_interac_customerIsNotTheSender_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("541");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelRequestPayment interac throw exception when payment status is not valid
   */
  @Test
  public void cancelRequestPayment_interac_invalidStatus_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("533");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_STATUS, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void cancelRequestPayment_generalResponseException_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("999");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.NETWORK_UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void cancelRequestPayment_illegalArgumentException_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doThrow(IllegalArgumentException.class).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }


  @Test
  public void cancelRequestPayment_reason_description_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request.setReasonDescription("àâäèéêëîïôœùûüÿÀÂÄÈÉÊËÎÏÔŒÙÛÜŸ");

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  /**
   * The test cancelRequestPayment with max 12 customerId happy path
   */
  @Test
  public void cancelRequestPayment_12CustomerId_success() throws Exception {
    request.setCustomerId("cid123456789");
    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelRequestPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from
   * their aspect all the values they pass to us are valid
   */
  @Test
  public void cancelRequestPayment_customerAdapter_ThrowUnExpectedException_failed() throws Exception {

    ResponseException responseException = new ResponseException();
    responseException.setHttpStatusCode(500);

    doThrow(responseException).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR,errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from
   * their aspect all the values they pass to us are valid
   */
  @Test
  public void cancelRequestPayment_customerAdapter_ThrowResourceNotFoundException_failed() throws Exception {

    ResourceNotFoundException resourceNotFoundException=new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());

    doThrow(resourceNotFoundException).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND,errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * when calling BaaS adapters and adapter returns an error, we should return UNEXPECTED_ERROR to our caller to avoid the confusion as from
   * their aspect all the values they pass to us are valid
   */
  @Test
  public void cancelRequestPayment_serviceAccountAdapter_ThrowUnExpectedException_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    TBDException tbdException=new TBDException();
    doThrow(tbdException).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR,errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }


  @Test
  public void cancelRequestPayment_serviceAccountDisable_failed() throws Exception {

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();

    //ResponseException
    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    //Set service account disabled
    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST,errResponseEntity.getStatusCode());

    assertEquals(PaymentUtil.SERVICE_DISABLED, errResponseEntity.getBody().getError().get(0).getCode());
  }

}
