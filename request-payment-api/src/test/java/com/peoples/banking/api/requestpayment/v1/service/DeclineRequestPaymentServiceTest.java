package com.peoples.banking.api.requestpayment.v1.service;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.mapper.*;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.DeclineRequestForPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import javax.validation.Validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {DeclineRequestPaymentRequestMapperImpl.class, DeclineRequestPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    PaymentStatusHistoryEntityMapperImpl.class, LocalValidatorFactoryBean.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
public class DeclineRequestPaymentServiceTest {

  @InjectMocks
  private RequestPaymentService requestPaymentService;

  @Mock
  private RequestAdapter requestAdapter;

  @Mock
  private CustomerAdapter customerAdapter;

  @Mock
  private PaymentsRepository paymentsRepository;

  @Mock
  private InteracAdapterProperty interacAdapterProperty;

  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;

  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;

  @SpyBean
  private Validator validator;

  @SpyBean
  private JsonConverter jsonConverter;

  @SpyBean
  private DateConverter dateConverter;

  @SpyBean
  private DeclineRequestPaymentRequestMapper declineRequestPaymentRequestMapper;

  private DeclineRequestPaymentRequest declineRequestPaymentRequest;
  private DeclineRequestForPaymentResponse declineRequestForPaymentResponse;
  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() {
    declineRequestPaymentRequest = RequestPaymentTestUtil.createDeclineRequestPaymentRequest();
    declineRequestForPaymentResponse = RequestPaymentTestUtil.createDeclineRequestForPaymentResponse();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, "ABC123",false);

    ReflectionTestUtils.setField(requestPaymentService, "declineRequestPaymentRequestMapper", declineRequestPaymentRequestMapper);
    ReflectionTestUtils.setField(requestPaymentService, "validator", validator);
    ReflectionTestUtils.setField(requestPaymentService, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(requestPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(paymentsEntityMapper, "jsonConverter", jsonConverter);
    ReflectionTestUtils.setField(paymentsEntityMapper, "dateConverter", dateConverter);
  }

  @Test
  void declineRequestPayment_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse=RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class),isA(String.class),isA(String.class),isA(String.class));

    Payments payments=RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and declinePayment Request
    payments.setCustomerRefId(declineRequestPaymentRequest.getCustomerId());

    doReturn(declineRequestForPaymentResponse).when(requestAdapter).declineRequestPayment(isA(String.class),isA(String.class),isA(DeclineRequestForPaymentRequest.class),
        isA(String.class));

    assertTrue(requestPaymentService.declineRequestPayment(declineRequestPaymentRequest, payments.getNetworkPaymentRefId(), serviceAccountResponse, IdGeneratorUtil.generateId()));
  }
}
