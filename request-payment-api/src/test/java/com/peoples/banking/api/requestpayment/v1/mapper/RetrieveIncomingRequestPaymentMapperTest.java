package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.api.requestpayment.v1.dto.StructuredDto;
import com.peoples.banking.domain.requestpayment.model.RemittanceAmount2;
import com.peoples.banking.domain.requestpayment.model.RemittanceLocation7;
import com.peoples.banking.domain.requestpayment.model.RetrieveIncomingRequestPaymentResponse;
import com.peoples.banking.domain.requestpayment.model.StructuredRemittanceInformation16;
import com.peoples.banking.partner.domain.interac.request.model.ActiveOrHistoricCurrencyAndAmount;
import com.peoples.banking.partner.domain.interac.request.model.AmountType4Choice;
import com.peoples.banking.partner.domain.interac.request.model.Contact4;
import com.peoples.banking.partner.domain.interac.request.model.CreditTransferTransaction35;
import com.peoples.banking.partner.domain.interac.request.model.CreditorPaymentActivationRequestV07;
import com.peoples.banking.partner.domain.interac.request.model.IncomingRequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.PartyIdentification135;
import com.peoples.banking.partner.domain.interac.request.model.PaymentInstruction31;
import com.peoples.banking.partner.domain.interac.request.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentStatus;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {RetrieveIncomingRequestPaymentMapperImpl.class, JsonConverter.class, DateConverter.class})
public class RetrieveIncomingRequestPaymentMapperTest {

  public static final String CREDITOR_NAME = "creditor";
  public static final String CREDITOR_EMAIL = "<EMAIL>";
  public static final String CREDITOR_MOBILE = "********";
  public static final String MEMO = "memo";
  public static final String ID = "ID";

  @Autowired
  private RetrieveIncomingRequestPaymentMapper mapper;

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_success(){
    PaymentDto paymentDto = RequestPaymentTestUtil.createPaymentDto();
    paymentDto.setStatus(PaymentStatus.AVAILABLE);
    RetrieveIncomingRequestPaymentResponse response = mapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto);

    assertEquals(paymentDto.getCustomerExternalId(), response.getCustomerId());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getName(), response.getContact().getName());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(paymentDto.getContactDto().getContactJsonDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(paymentDto.getAmount(), response.getAmount());
    assertEquals(paymentDto.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo(), response.getMemo());
    assertEquals(paymentDto.getStatus().getValue(), response.getStatus().getValue());
    assertEquals(paymentDto.getExpiryDate(), response.getExpiryDate());
    assertEquals(paymentDto.getCreatedDate(), response.getCreatedDate());
    assertNull(response.getRemittanceInfo());
  }

  @Test
  public void paymentDtoToRetrieveRequestPaymentResponse_successRemittance(){
    PaymentDto paymentDto = RequestPaymentTestUtil.createPaymentDto();
    paymentDto.setStatus(PaymentStatus.AVAILABLE);
    StructuredDto structured = new StructuredDto();
    StructuredRemittanceInformation16 info = new StructuredRemittanceInformation16();
    info.setReferredDocumentAmount(new RemittanceAmount2().duePayableAmount(new com.peoples.banking.domain.requestpayment.model.ActiveOrHistoricCurrencyAndAmount().amount(BigDecimal.TEN)));
    structured.setRemittanceInformation(Collections.singletonList(info));
    RemittanceLocation7 location7 = new RemittanceLocation7();
    location7.setRemittanceIdentification(ID);
    structured.setRemittanceLocation(Collections.singletonList(location7));
    paymentDto.getRemittanceDto().getRemittanceJsonDto().setStructured(structured);
    RetrieveIncomingRequestPaymentResponse response = mapper.paymentDtoToRetrieveRequestPaymentResponse(paymentDto);

    assertEquals(BigDecimal.TEN, response.getRemittanceInfo().getInformation().get(0).getReferredDocumentAmount().getDuePayableAmount().getAmount());
    assertEquals(ID, response.getRemittanceInfo().getLocation().get(0).getRemittanceIdentification());
  }

  @Test
  public void incomingRequestForPaymentResponseToPaymentDto_success(){
    PaymentDto paymentDto = new PaymentDto();
    IncomingRequestForPaymentResponse response = createIncomingRequestForPaymentResponse();
    PaymentDto payment = mapper.incomingRequestForPaymentResponseToPaymentDto(paymentDto, response);

    assertEquals(OffsetDateTime.MAX, payment.getExpiryDate());
    assertEquals(OffsetDateTime.MIN, payment.getCreatedDate());
    assertEquals(CREDITOR_NAME, payment.getContactDto().getContactJsonDto().getName());
    assertEquals(CREDITOR_EMAIL, payment.getContactDto().getContactJsonDto().getEmail());
    assertEquals(CREDITOR_MOBILE, payment.getContactDto().getContactJsonDto().getMobile());
    assertEquals(BigDecimal.TEN, payment.getAmount());
    assertEquals(MEMO, payment.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo());
    assertEquals(MEMO, payment.getRemittanceDto().getRemittanceJsonDto().getUnstructured().getMemo());
  }

  private IncomingRequestForPaymentResponse createIncomingRequestForPaymentResponse() {
    IncomingRequestForPaymentResponse res = new IncomingRequestForPaymentResponse();
    res.setRequestForPaymentStatus(RequestForPaymentStatus.AVAILABLE);
    CreditorPaymentActivationRequestV07 request = new CreditorPaymentActivationRequestV07();
    res.setCreditorPaymentActivationRequest(request);
    PaymentInstruction31 instruction31 = new PaymentInstruction31();
    request.setPaymentInformation(Collections.singletonList(instruction31));
    instruction31.setExpiryDate(OffsetDateTime.MAX);
    instruction31.setRequestedExecutionDate(OffsetDateTime.MIN);
    CreditTransferTransaction35 transaction35 = new CreditTransferTransaction35();
    instruction31.setCreditTransferTransaction(Collections.singletonList(transaction35));
    PartyIdentification135 creditor = new PartyIdentification135();
    transaction35.setCreditor(creditor);
    creditor.setName(CREDITOR_NAME);
    Contact4 contact4 = new Contact4();
    creditor.setContactDetails(contact4);
    contact4.setEmailAddress(CREDITOR_EMAIL);
    contact4.setMobileNumber(CREDITOR_MOBILE);
    AmountType4Choice amount = new AmountType4Choice();
    ActiveOrHistoricCurrencyAndAmount am = new ActiveOrHistoricCurrencyAndAmount();
    am.setAmount(BigDecimal.TEN);
    amount.setInstructedAmount(am);
    transaction35.setAmount(amount);
    com.peoples.banking.partner.domain.interac.request.model.RemittanceLocation7 location = new com.peoples.banking.partner.domain.interac.request.model.RemittanceLocation7();
    location.setRemittanceIdentification(ID);
    transaction35.setRelatedRemittanceInformation(Collections.singletonList(location));
    RemittanceInformation16 ri = new RemittanceInformation16();
    ri.setUnstructured(Collections.singletonList(MEMO));
    transaction35.setRemittanceInformation(ri);
    return res;
  }


}
