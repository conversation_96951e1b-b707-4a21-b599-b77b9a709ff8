package com.peoples.banking.api.requestpayment.v1;

import com.peoples.banking.adapter.base.type.ServiceAccountEndpointType;
import com.peoples.banking.api.requestpayment.v1.dto.AccountDto;
import com.peoples.banking.api.requestpayment.v1.dto.ContactDto;
import com.peoples.banking.api.requestpayment.v1.dto.ContactJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.ExternalFiAccountInfoDto;
import com.peoples.banking.api.requestpayment.v1.dto.ExternalFiAccountInfoJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.FiAccountDto;
import com.peoples.banking.api.requestpayment.v1.dto.FraudResultDto;
import com.peoples.banking.api.requestpayment.v1.dto.FraudResultJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.OptionDto;
import com.peoples.banking.api.requestpayment.v1.dto.OptionJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.api.requestpayment.v1.dto.RemittanceDto;
import com.peoples.banking.api.requestpayment.v1.dto.RemittanceJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonDto;
import com.peoples.banking.api.requestpayment.v1.dto.StatusReasonJsonDto;
import com.peoples.banking.api.requestpayment.v1.dto.UnstructuredDto;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.ReasonCode;
import com.peoples.banking.domain.requestpayment.model.ContactNameEmailPhone;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.DeclineRequestPaymentRequest;
import com.peoples.banking.domain.requestpayment.model.CreateRequestPaymentRequestOptions;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ApiEndpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.ConnectorTypeEnum;
import com.peoples.banking.partner.domain.interac.request.model.*;
import com.peoples.banking.partner.domain.interac.request.model.FraudCheckResult.ActionEnum;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.type.TransactionType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.RandomStringUtils;

@UtilityClass
public class RequestPaymentTestUtil {

  public static final String JSON_PATH_CONTACT_CONTACT_INFO = "contact.email_address/contact.mobile_number";
  public static final String SERVICE_ACCOUNT_API_TOKEN = "AxYQ1GGl0";
  public static final String SERVICE_ACCOUNT_REF_ID = "ABC123";
  public static final String ACCOUNT_NUMBER = "621-00001-************";

  public static CreateRequestPaymentRequest createRequestPaymentRequest() {
    CreateRequestPaymentRequest request = new CreateRequestPaymentRequest();

    request.setCustomerId("cust123");
    request.setEndToEndId(UUID.randomUUID().toString());

    ContactNameEmailPhone contact = new ContactNameEmailPhone();
    contact.setName("Jasonwang");
    contact.setEmailAddress("<EMAIL>");
    contact.setMobileNumber("**********");
    request.setContact(contact);

    request.setAmount(BigDecimal.TEN);
    request.setAccountNumber("621-00525-*********");
    request.setAccountName("Customer Account Name");

    CreateRequestPaymentRequestOptions options = new CreateRequestPaymentRequestOptions();
    options.setAmountModification(true);
    options.setEnableNotification(true);
    options.setExpireAfterDays(30);
    request.setOptions(options);

    // TODO: add remittance data when it's finalized

    return request;
  }

  public static RetrieveRequestPaymentResponse createRetrieveRequestPaymentResponse() {
    RetrieveRequestPaymentResponse getRequestPaymentRequest = new RetrieveRequestPaymentResponse();
    getRequestPaymentRequest.setCustomerId("cid055");
    return getRequestPaymentRequest;
  }

  public static RequestForPaymentResponse createRequestForPaymentResponse() {
    RequestForPaymentResponse requestForPaymentResponse = new RequestForPaymentResponse();
    return requestForPaymentResponse;
  }

  public static SendRequestForPaymentResponse createSendRequestForPaymentResponse() {
    SendRequestForPaymentResponse sendRequestForPaymentResponse =
        new SendRequestForPaymentResponse();

    CreditorPaymentActivationRequestStatusReportV07 report =
        new CreditorPaymentActivationRequestStatusReportV07();

    GroupHeader87 groupHeader87 = new GroupHeader87();
    groupHeader87.setCreationDatetime(OffsetDateTime.now());
    report.setGroupHeader(groupHeader87);

    OriginalGroupInformation30 originalGroupInfo = new OriginalGroupInformation30();
    originalGroupInfo.setOriginalCreationDatetime(DateUtil.getCurrentUTCDateTime());
    report.setOriginalGroupInformationAndStatus(originalGroupInfo);

    OriginalPaymentInstruction31 paymentInstruction = new OriginalPaymentInstruction31();

    List<PaymentTransaction104> transactionInformationAndStatusList = new ArrayList<>();
    PaymentTransaction104 paymentTransaction = new PaymentTransaction104();

    paymentTransaction.setTransactionStatus(ExternalPaymentTransactionStatus1Code.ACTC);
    paymentTransaction.setClearingSystemReference("CA1MR6HHz4ZM");

    OriginalTransactionReference29 originalTransaction = new OriginalTransactionReference29();
    OffsetDateTime expiryDate = DateUtil.getCurrentUTCDateTime();
    originalTransaction.setExpiryDate(expiryDate.plusDays(30));

    BranchAndFinancialInstitutionIdentification6 creditorAgent =
        new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 creditorFI = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 systemId = new ClearingSystemMemberIdentification2();
    systemId.setMemberIdentification("CA000621");
    creditorFI.setClearingSystemMemberIdentification(systemId);

    creditorAgent.setFinancialInstitutionIdentification(creditorFI);
    originalTransaction.setCreditorAgent(creditorAgent);

    PartyIdentification135 creditor = new PartyIdentification135();
    Party38Choice creditorId = new Party38Choice();
    OrganisationIdentification29 organisationId = new OrganisationIdentification29();

    List<GenericOrganisationIdentification1> otherList = new ArrayList<>();
    GenericOrganisationIdentification1 other = new GenericOrganisationIdentification1();
    other.setIdentification("C4QRsavhR");

    otherList.add(other);
    organisationId.setOther(otherList);
    creditorId.setOrganisationIdentification(organisationId);
    creditor.setIdentification(creditorId);
    originalTransaction.setCreditor(creditor);

    paymentTransaction.setOriginalTransactionReference(originalTransaction);

    transactionInformationAndStatusList.add(paymentTransaction);
    paymentInstruction.setTransactionInformationAndStatus(transactionInformationAndStatusList);

    List<OriginalPaymentInstruction31> originalPaymentInformationAndStatusList = new ArrayList<>();
    originalPaymentInformationAndStatusList.add(paymentInstruction);

    report.setOriginalPaymentInformationAndStatus(originalPaymentInformationAndStatusList);
    sendRequestForPaymentResponse.setCreditorPaymentActivationRequestStatusReport(report);

    FraudCheckResult fraudCheckResult = new FraudCheckResult();
    fraudCheckResult.setAction(ActionEnum.ALLOW);
    sendRequestForPaymentResponse.setFraudCheckResult(fraudCheckResult);

    sendRequestForPaymentResponse
        .setGatewayUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6HHz4ZM");
    return sendRequestForPaymentResponse;
  }

  public static CancelRequestPaymentRequest createCancelRequestPaymentRequest() {
    CancelRequestPaymentRequest cancelRequestPaymentRequest = new CancelRequestPaymentRequest();
    cancelRequestPaymentRequest.setCustomerId("test111");
    cancelRequestPaymentRequest.setEndToEndId(UUID.randomUUID().toString());
    cancelRequestPaymentRequest.setReasonCode(ReasonCode.CUSTOMER_INITIATED);
    cancelRequestPaymentRequest.setReasonDescription("unit testing");
    return cancelRequestPaymentRequest;
  }

  public static DeclineRequestPaymentRequest createDeclineRequestPaymentRequest() {
    DeclineRequestPaymentRequest declineRequestPaymentRequest = new DeclineRequestPaymentRequest();
    declineRequestPaymentRequest.setCustomerId("CUST1235");
    declineRequestPaymentRequest.setReasonCode(ReasonCode.CUSTOMER_INITIATED);
    declineRequestPaymentRequest.setReasonDescription("unit testing");
    return declineRequestPaymentRequest;
  }

  public static DeclineRequestForPaymentResponse createDeclineRequestForPaymentResponse() {
    return new DeclineRequestForPaymentResponse();
  }

  public static RetrieveCustomerProductResponse createRetrieveCustomerProductResponse() {
    RetrieveCustomerProductResponse getCustomerProductResponse = new RetrieveCustomerProductResponse();
    getCustomerProductResponse.setEnrollmentRefId("Ktul6UIHB");
    getCustomerProductResponse.setActive(true);
    getCustomerProductResponse.setCreatedDate(OffsetDateTime.now());
    return getCustomerProductResponse;
  }

  public static PaymentDto createPaymentDto() {
    PaymentDto paymentDto = new PaymentDto();
    paymentDto.setId(1);
    paymentDto.setExternalRefId("ulfiyo05lfZiceLH");
    paymentDto.setCustomerExternalId("NF1vmcn5XHfe");
    paymentDto.setNetworkEnrollmentId("CY1VMCOWLCUP");
    paymentDto.setServiceAccountRefId(SERVICE_ACCOUNT_REF_ID);
    paymentDto.setEndToEndId(UUID.randomUUID().toString());
    paymentDto.setTransactionType(TransactionType.REQUEST);
    paymentDto.setFiId("CA000621");
    paymentDto.setNetworkRefId(NetworkChannelType.EMT);
    paymentDto.setNetworkPaymentRefId("CA1MR857vCfP");
    paymentDto.setNetworkPaymentType(NetworkPaymentType.REQUEST_FOR_PAYMENT);

    ContactDto contactDto = new ContactDto();
    ContactJsonDto contactJsonDto = new ContactJsonDto();
    contactJsonDto.setName("Bob");
    contactJsonDto.setEmail("<EMAIL>");
    contactJsonDto.setMobile("**********");
    contactDto.setContactJsonDto(contactJsonDto);
    paymentDto.setContactDto(contactDto);

    paymentDto.setAmount(new BigDecimal(20.50));

    AccountDto accountDto = new AccountDto();
    accountDto.setName("Bob Antman");
    accountDto.setAccountNumber(ACCOUNT_NUMBER);
    paymentDto.setAccountDto(accountDto);

    OptionDto optionDto = new OptionDto();
    OptionJsonDto optionJsonDto = new OptionJsonDto();
    optionJsonDto.setAmountModification(false);
    optionJsonDto.setEnableNotification(true);
    optionJsonDto.setExpiredAfterDays(30);
    optionDto.setOptionJsonDto(optionJsonDto);
    paymentDto.setOptionDto(optionDto);

    RemittanceDto remittanceDto = new RemittanceDto();
    RemittanceJsonDto remittanceJsonDto = new RemittanceJsonDto();
    UnstructuredDto unstructuredDto = new UnstructuredDto();
    unstructuredDto.setMemo("This is memo");
    remittanceJsonDto.setUnstructured(unstructuredDto);
    remittanceDto.setRemittanceJsonDto(remittanceJsonDto);
    paymentDto.setRemittanceDto(remittanceDto);

    OffsetDateTime expiryDate = DateUtil.getCurrentUTCDateTime();
    expiryDate = expiryDate.plusDays(30);
    paymentDto.setExpiryDate(expiryDate);

    paymentDto.setNetworkUrl("http://lcoalhost:8080/someURL");
    paymentDto.setStatus(PaymentStatus.ACCEPTED);

    StatusReasonDto statusReasonDto = new StatusReasonDto();
    StatusReasonJsonDto statusReasonJsonDto = new StatusReasonJsonDto();
    statusReasonJsonDto.setDescription("Mock Test");
    statusReasonJsonDto.setReasonCode("CUSTOMER_INITIATED");
    statusReasonDto.setStatusReasonJsonDto(statusReasonJsonDto);
    paymentDto.setStatusReasonDto(statusReasonDto);

    FraudResultDto fraudResultDto = new FraudResultDto();
    FraudResultJsonDto fraudResultJsonDto = new FraudResultJsonDto();
    fraudResultJsonDto.setAction("ALLOW");
    fraudResultDto.setFraudResultJsonDto(fraudResultJsonDto);
    paymentDto.setFraudResultDto(fraudResultDto);

    paymentDto.setCreatedDate(DateUtil.getCurrentUTCDateTime().minusDays(2));
    paymentDto.setUpdatedDate(DateUtil.getCurrentUTCDateTime());

    ExternalFiAccountInfoDto externalFiAccountInfoDto = new ExternalFiAccountInfoDto();
    ExternalFiAccountInfoJsonDto externalFiAccountInfoJsonDto = new ExternalFiAccountInfoJsonDto();
    FiAccountDto creditor = new FiAccountDto();
    creditor.setAccountName("John");
    creditor.setAccountNumber(ACCOUNT_NUMBER);
    externalFiAccountInfoJsonDto.setCreditor(creditor);
    externalFiAccountInfoDto.setExternalFiAccountInfoJsonDto(externalFiAccountInfoJsonDto);
    paymentDto.setExternalFiAccountInfoDto(externalFiAccountInfoDto);

    return paymentDto;
  }

  public static Payments setMemoForPayments(Payments payments, String memo) {
    short version = 1;
    payments.setRemittanceJson(memo);
    payments.setRemittanceJsonVersion(version);
    return payments;
  }

  public static Payments createPayments() {
    Payments payments = new Payments();
    payments.setParentId(0);
    payments.setServiceAccountRefId(SERVICE_ACCOUNT_REF_ID);
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("CUST1235");
    payments.setExternalRefId(RandomStringUtils.randomAlphanumeric(17));
    payments.setTypeCd(PaymentCdType.REQUEST);

    short version = 1;
    payments.setContactJson("{\"name\":\"Jasonwang\",\"email\":\"<EMAIL>\",\"mobile\":\"**********\"}");
    payments.setContactJsonVersion(version);

    payments.setAmount(new BigDecimal("100.25"));
    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-00525-*********");

    payments.setOptionJson("{\"expiredAfterDays\":30,\"amountModification\":false,\"enableNotification\":false}");
    payments.setRemittanceJsonVersion(version);

    payments.setNetworkRefId("INTERAC_EMT");
    payments.setNetworkPaymentRefId("CA1MR6jDK712");
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6jDK7tA");
    payments.setNetworkPaymentType("REQUEST_FOR_PAYMENT");
    payments.setNetworkEnrollmentId("2LO7MqgRE");
    payments.setStatus("AVAILABLE");

    payments.setStatusReasonJson("{\"reasonCode\":\"CUSTOMER_INITIATED\",\"description\":\"tesing\"}");
    payments.setStatusReasonJsonVersion(version);

    payments.setNetworkFraudResultJson("{\"action\":\"ALLOW\"}");
    payments.setNetworkFraudResultJsonVersion(version);

    payments.setExpiryDate(LocalDateTime.now());
    payments.setSettlementDate(LocalDateTime.now());
    payments.setNetworkSettled(false);
    payments.setSaSettled(false);

    payments.setCreatedOn(LocalDateTime.now());
    payments.setUpdatedOn(LocalDateTime.now());

    return payments;
  }

  public static RetrieveCustomerProductResponse getCustomerProductResponse() {
    RetrieveCustomerProductResponse customerProductResponse = new RetrieveCustomerProductResponse();
    customerProductResponse.setActive(true);
    customerProductResponse.setEnrollmentRefId("W3je2De9");
    customerProductResponse.setCreatedDate(OffsetDateTime.now());

    return customerProductResponse;
  }

  public static RequestForPaymentResponse getRequestForPaymentResponse() {
    RequestForPaymentResponse requestForPaymentResponse = new RequestForPaymentResponse();
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.DEPOSIT_COMPLETE);

    Contact contact = new Contact();
    contact.setId("1");
    ContactName contactName = new ContactName();
    LegalName legalName = new LegalName();
    BusinessName businessName = new BusinessName();
    businessName.setCompanyName("Test Company");

    legalName.setBusinessName(businessName);
    contactName.setLegalName(legalName);
    contact.setName(contactName);

    requestForPaymentResponse.setContact(contact);

    return requestForPaymentResponse;
  }

  public static ServiceAccountResponse createServiceAccountResponse(String serviceAccountApiToken, String serviceAccountRefId, boolean directFlag) {
    ServiceAccountResponse serviceAccountResponse = new ServiceAccountResponse();
    serviceAccountResponse.setRefId(serviceAccountRefId);
    serviceAccountResponse.setInboundApiToken(serviceAccountApiToken);
    serviceAccountResponse.setOutboundApiToken("80a2b00743214abb8e7d10710814e005");
    serviceAccountResponse.setName("Test ServiceAccount");
    serviceAccountResponse.setLimitGroupId("PRODVAL");

    Map<String, ApiEndpoint> apiEndpointMap = new HashMap<>();
    ApiEndpoint elgty = new ApiEndpoint();
    elgty.setUrl("http://localhost:8080/account/{account_num}/eligibility");
    apiEndpointMap.put(ServiceAccountEndpointType.ELIGIBILITY.toString(), elgty);

    ApiEndpoint trns = new ApiEndpoint();
    trns.setUrl("http://localhost:8080/account/{account_num}/transaction");
    apiEndpointMap.put(ServiceAccountEndpointType.TRANSACTION.toString(), trns);

    ApiEndpoint rvs = new ApiEndpoint();
    rvs.setUrl("http://localhost:8080/account/{account_num}/{transaction_id}/reversal");
    apiEndpointMap.put(ServiceAccountEndpointType.REVERSAL.toString(), rvs);

    serviceAccountResponse.setApiEndpoints(apiEndpointMap);

    serviceAccountResponse.setCreatedDate(DateUtil.getCurrentUTCDateTime());
    serviceAccountResponse.setUpdatedDate(DateUtil.getCurrentUTCDateTime());

    if (directFlag) {
      serviceAccountResponse.setConnectorType(ConnectorTypeEnum.DIRECT);
    } else {
      serviceAccountResponse.setConnectorType(ConnectorTypeEnum.INDIRECT);
      serviceAccountResponse.setIndirectConnectorId("test12121");
    }

    return serviceAccountResponse;
  }

  public static String getJsonErrorResponse(String error, String additionalInformation) {
    return "{\"error\":[{\"code\":\"" + error + "\",\"additional_information\":\"" + additionalInformation + "\"}]}";
  }
}
