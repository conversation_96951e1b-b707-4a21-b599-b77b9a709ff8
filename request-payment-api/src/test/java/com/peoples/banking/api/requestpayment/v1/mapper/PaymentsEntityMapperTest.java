package com.peoples.banking.api.requestpayment.v1.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.dto.PaymentDto;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {PaymentsEntityMapperImpl.class, JsonConverter.class, DateConverter.class})
public class PaymentsEntityMapperTest {

  @Autowired
  private PaymentsEntityMapper mapper;

  private PaymentDto paymentDto;

  private Payments payments;

  @BeforeEach
  public void setup() {

    paymentDto = RequestPaymentTestUtil.createPaymentDto();
    payments = RequestPaymentTestUtil.createPayments();
  }

  @AfterEach
  public void tearDown() {
    paymentDto = null;
    payments = null;
  }

  @Test
  public void paymentDtoToPayments_success() throws Exception {
    Payments payments = mapper.paymentDtoToPayments(paymentDto);

    assertEquals(paymentDto.getExternalRefId(), payments.getExternalRefId());
    assertEquals(paymentDto.getCustomerExternalId(), payments.getCustomerRefId());
    assertEquals(paymentDto.getNetworkEnrollmentId(), payments.getNetworkEnrollmentId());
    assertEquals(paymentDto.getServiceAccountRefId(), payments.getServiceAccountRefId());
    assertEquals(paymentDto.getEndToEndId(), payments.getEndToEndId());
    assertEquals(paymentDto.getTransactionType().toString(), payments.getTypeCd().toString());
    assertEquals(0, payments.getParentId());
    assertEquals(paymentDto.getNetworkRefId().getNetworkRefId(), payments.getNetworkRefId());
    assertEquals(paymentDto.getNetworkPaymentRefId(), payments.getNetworkPaymentRefId());
    assertEquals(paymentDto.getNetworkPaymentType().name(), payments.getNetworkPaymentType());
    assertEquals(paymentDto.getContactDto().getVersion(), payments.getContactJsonVersion());
    assertEquals(JsonUtil.toString(paymentDto.getContactDto().getContactJsonDto()), payments.getContactJson());
    assertEquals(paymentDto.getAmount(), payments.getAmount());
    assertEquals(paymentDto.getAccountDto().getName(), payments.getAccountName());
    assertEquals(paymentDto.getAccountDto().getAccountNumber(), payments.getAccountNumber());
    assertEquals(paymentDto.getOptionDto().getVersion(), payments.getOptionJsonVersion());
    assertEquals(JsonUtil.toString(paymentDto.getOptionDto().getOptionJsonDto()), payments.getOptionJson());
    assertEquals(paymentDto.getRemittanceDto().getVersion(), payments.getRemittanceJsonVersion());
    assertEquals(JsonUtil.toString(paymentDto.getRemittanceDto().getRemittanceJsonDto()), payments.getRemittanceJson());
    assertEquals(paymentDto.getExpiryDate(), DateUtil.toOffsetDateTime(payments.getExpiryDate()));
    assertEquals(paymentDto.getNetworkUrl(), payments.getNetworkPaymentUrl());
    assertEquals(paymentDto.getNetworkCreatedDate(), payments.getNetworkCreatedDate());
    assertEquals(paymentDto.getStatus().toString(), payments.getStatus());
    assertEquals(paymentDto.getStatusReasonDto().getVersion(), payments.getStatusReasonJsonVersion());
    assertEquals(JsonUtil.toString(paymentDto.getStatusReasonDto().getStatusReasonJsonDto()), payments.getStatusReasonJson());
    assertEquals(paymentDto.getFraudResultDto().getVersion(), payments.getNetworkFraudResultJsonVersion());
    assertEquals(JsonUtil.toString(paymentDto.getFraudResultDto().getFraudResultJsonDto()), payments.getNetworkFraudResultJson());
    assertEquals(paymentDto.getSettlementDate(), payments.getSettlementDate());
    assertEquals(paymentDto.isNetworkSettled(), payments.isNetworkSettled());
    assertEquals(paymentDto.isSaSettled(), payments.isSaSettled());
    assertEquals(paymentDto.getCreatedDate(), DateUtil.toOffsetDateTime(payments.getCreatedOn()));
    assertEquals(paymentDto.getUpdatedDate(), DateUtil.toOffsetDateTime(payments.getUpdatedOn()));
  }

  @Test
  public void paymentsToPaymentDto_success() throws Exception {

    PaymentDto paymentDto = mapper.paymentsToPaymentDto(payments);

    assertEquals(payments.getExternalRefId(), paymentDto.getExternalRefId());
    assertEquals(payments.getCustomerRefId(), paymentDto.getCustomerExternalId());
    assertEquals(payments.getNetworkEnrollmentId(), paymentDto.getNetworkEnrollmentId());
    assertEquals(payments.getServiceAccountRefId(), paymentDto.getServiceAccountRefId());
    assertEquals(payments.getEndToEndId(), paymentDto.getEndToEndId());
    assertEquals(payments.getTypeCd().toString(), paymentDto.getTransactionType().toString());
    assertEquals(payments.getParentId(), 0);
    assertEquals(payments.getNetworkRefId(), paymentDto.getNetworkRefId().getNetworkRefId());
    assertEquals(payments.getNetworkPaymentRefId(), paymentDto.getNetworkPaymentRefId());
    assertEquals(payments.getNetworkPaymentType(), paymentDto.getNetworkPaymentType().name());
    assertEquals(payments.getContactJsonVersion(), paymentDto.getContactDto().getVersion());
    assertEquals(payments.getContactJson(), JsonUtil.toString(paymentDto.getContactDto().getContactJsonDto()));
    assertEquals(payments.getAmount(), paymentDto.getAmount());
    assertEquals(payments.getAccountName(), paymentDto.getAccountDto().getName());
    assertEquals(payments.getAccountNumber(), paymentDto.getAccountDto().getAccountNumber());
    assertEquals(payments.getOptionJsonVersion(), paymentDto.getOptionDto().getVersion());
    assertEquals(payments.getOptionJson(), JsonUtil.toString(paymentDto.getOptionDto().getOptionJsonDto()));
    assertEquals(payments.getRemittanceJsonVersion(), paymentDto.getRemittanceDto().getVersion());
    assertEquals(payments.getRemittanceJson(), JsonUtil.toString(paymentDto.getRemittanceDto().getRemittanceJsonDto()));
    assertEquals(DateUtil.toOffsetDateTime(payments.getExpiryDate()), paymentDto.getExpiryDate());
    assertEquals(payments.getNetworkPaymentUrl(), paymentDto.getNetworkUrl());
    assertEquals(payments.getNetworkCreatedDate(), paymentDto.getNetworkCreatedDate());
    assertEquals(payments.getStatus(), paymentDto.getStatus().toString());
    assertEquals(payments.getStatusReasonJsonVersion(), paymentDto.getStatusReasonDto().getVersion());
    assertEquals(payments.getStatusReasonJson(), JsonUtil.toString(paymentDto.getStatusReasonDto().getStatusReasonJsonDto()));
    assertEquals(payments.getNetworkFraudResultJsonVersion(), paymentDto.getFraudResultDto().getVersion());
    assertEquals(payments.getNetworkFraudResultJson(), JsonUtil.toString(paymentDto.getFraudResultDto().getFraudResultJsonDto()));
    assertEquals(payments.getSettlementDate(), DateUtil.toLocalDateTime(paymentDto.getSettlementDate()));
    assertEquals(payments.isNetworkSettled(), paymentDto.isNetworkSettled());
    assertEquals(payments.isSaSettled(), paymentDto.isSaSettled());
    assertEquals(DateUtil.toOffsetDateTime(payments.getCreatedOn()), paymentDto.getCreatedDate());
    assertEquals(DateUtil.toOffsetDateTime(payments.getUpdatedOn()), paymentDto.getUpdatedDate());
  }

}
