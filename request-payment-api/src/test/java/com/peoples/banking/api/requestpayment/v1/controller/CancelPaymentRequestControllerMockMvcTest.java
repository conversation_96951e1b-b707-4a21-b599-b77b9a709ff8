package com.peoples.banking.api.requestpayment.v1.controller;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentConstant;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.requestpayment.model.CancelRequestPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
public class CancelPaymentRequestControllerMockMvcTest {

  @Autowired
  private MockMvc mvc;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private RequestAdapter requestAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void init() throws Exception {
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    serviceAccountResponse = null;
  }

  /**
   * The test use cancel payment data  to call cancel request to mock test
   */
  @Test
  public void cancelPayment_success() throws Exception {
    //First create a test cancelPayment Request
    CancelRequestPaymentRequest request = RequestPaymentTestUtil.createCancelRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));
    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847"))
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @Test
  public void cancelPayment_serviceAccountSuspended_success() throws Exception {
    //First create a test cancelPayment Request
    CancelRequestPaymentRequest request = RequestPaymentTestUtil.createCancelRequestPaymentRequest();

    RetrieveCustomerProductResponse getCustomerProductResponse = RequestPaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = RequestPaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));
    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    doReturn(true).when(requestAdapter).cancelRequestPayment(isA(String.class), isA(String.class),
        isA(CancelRequestForPaymentRequest.class), isA(String.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.post(RequestPaymentConstant.CANCEL_REQUEST_PAYMENT_URL.replace("{requestId}", "ABC1231593101413847"))
        .header(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }


}
