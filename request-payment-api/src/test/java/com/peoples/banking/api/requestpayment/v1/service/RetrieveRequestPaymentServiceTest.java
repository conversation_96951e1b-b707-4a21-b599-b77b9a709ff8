package com.peoples.banking.api.requestpayment.v1.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.api.requestpayment.v1.RequestPaymentTestUtil;
import com.peoples.banking.api.requestpayment.v1.config.RequestPaymentProperty;
import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.CreateRequestPaymentRequestMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.api.requestpayment.v1.mapper.RetrieveRequestPaymentRequestMapper;
import com.peoples.banking.api.requestpayment.v1.mapper.RetrieveRequestPaymentRequestMapperImpl;
import com.peoples.banking.domain.requestpayment.model.RetrieveRequestPaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.request.RequestAdapter;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentStatus;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import javax.validation.Validator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {CreateRequestPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    RetrieveRequestPaymentRequestMapperImpl.class,
    PaymentStatusHistoryEntityMapperImpl.class, LocalValidatorFactoryBean.class})
@TestPropertySource(locations = {"classpath:application.properties"})
@SpringJUnitConfig()
public class RetrieveRequestPaymentServiceTest {

  @InjectMocks
  private RequestPaymentService requestPaymentService;
  @Mock
  private RequestAdapter requestAdapter;
  @Mock
  private PaymentsRepository paymentsRepository;
  @SpyBean
  private CreateRequestPaymentRequestMapper createRequestPaymentRequestMapper;
  @SpyBean
  RetrieveRequestPaymentRequestMapper retrieveRequestPaymentRequestMapper;
  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;
  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;
  @SpyBean
  private Validator validator;
  @SpyBean
  private JsonConverter jsonConverter;
  @SpyBean
  private DateConverter dateConverter;
  private RequestForPaymentResponse requestForPaymentResponse;
  private String serviceAccountRefId;
  private Payments payments;
  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setUp() {
    serviceAccountRefId = "ABC123";
    payments = RequestPaymentTestUtil.createPayments();
    payments.setId(1);
    requestForPaymentResponse = RequestPaymentTestUtil.createRequestForPaymentResponse();
    serviceAccountResponse = RequestPaymentTestUtil
        .createServiceAccountResponse(RequestPaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, RequestPaymentTestUtil.SERVICE_ACCOUNT_REF_ID,false);

    ReflectionTestUtils.setField(requestPaymentService, "createRequestPaymentRequestMapper", createRequestPaymentRequestMapper);
    ReflectionTestUtils.setField(requestPaymentService, "validator", validator);
    ReflectionTestUtils.setField(requestPaymentService, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(requestPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(paymentsEntityMapper, "jsonConverter", jsonConverter);
    ReflectionTestUtils.setField(paymentsEntityMapper, "dateConverter", dateConverter);
  }

  @Test
  public void retrieveRequestPayment_success()
      throws AdapterException, ResponseException, TimeoutException, TBDException {

    //Sync the status to make it pass through
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.fromValue(payments.getStatus()));

    doReturn(requestForPaymentResponse).when(requestAdapter).getRequestPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    RetrieveRequestPaymentResponse response = assertDoesNotThrow(() -> {
      return requestPaymentService.retrieveRequestPayment("LCfiyo05lfZMWKWC", serviceAccountResponse);
    });

    assertNotNull(response);
  }

  @Test
  public void sendRequestPayment_invalidServiceAccount_fail() throws Exception {

    //Sync the status to make it pass through
    requestForPaymentResponse.setRequestStatus(RequestForPaymentStatus.fromValue(payments.getStatus()));

    payments.setServiceAccountRefId("XYZ123");
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd((isA(String.class)), isA(String.class), isA(PaymentCdType.class));

    serviceAccountResponse.setRefId(serviceAccountRefId + "1");
    //Send the service account did not match request
    ResourceNotFoundException resourceNotFoundException = assertThrows(ResourceNotFoundException.class, () -> {
      requestPaymentService.retrieveRequestPayment("LCfiyo05lfZMWKWC", serviceAccountResponse);
    });

    assertNotNull(resourceNotFoundException);

    ErrorEntity error = resourceNotFoundException.getError();
    assertEquals(ErrorProperty.RESOURCE_NOT_FOUND.name(), error.getErrorCode());
  }
}
