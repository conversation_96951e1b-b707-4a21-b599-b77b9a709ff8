package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.api.serviceaccount.v1.dto.JsonDto;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventCategory;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventReasonType;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventType;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * The mapper class to map objects between ServiceAccounts and ServiceAccountEvents
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class}, uses = {JsonConverter.class, DateConverter.class})
public abstract class ServiceAccountEventEntityMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(expression = "java(DateUtil.getLocalUtcTime())", target = "createdOn")
  @Mapping(source = "oldObjectDto", target = "originalEventJson", qualifiedByName = {"JsonConverter", "toJsonString"})
  @Mapping(source = "newObjectDto", target = "updatedEventJson", qualifiedByName = {"JsonConverter", "toJsonString"})
  @Mapping(source = "serviceAccounts", target = "serviceAccounts")
  @Mapping(source = "oldObjectDto.version", target = "originalEventJsonVersion")
  @Mapping(source = "newObjectDto.version", target = "updatedEventJsonVersion")
  public abstract ServiceAccountEvents serviceAccountsToServiceAccountEvents(ServiceAccounts serviceAccounts,
      JsonDto oldObjectDto,
      JsonDto newObjectDto, String description, ServiceAccountEventReasonType reasonType,
      ServiceAccountEventCategory category,
      ServiceAccountEventType event, String updatedBy, String scope) throws JsonProcessingException;

}
