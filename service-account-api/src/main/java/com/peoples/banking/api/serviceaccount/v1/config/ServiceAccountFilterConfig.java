package com.peoples.banking.api.serviceaccount.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.InternalLoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Service Account API filter configuration
 */
@Configuration
public class ServiceAccountFilterConfig {

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<InternalLoggingFilter> serviceAccountAPILoggingFilter() {
    InternalLoggingFilter serviceAccountAPILoggingFilter = new InternalLoggingFilter();
    serviceAccountAPILoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    serviceAccountAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    serviceAccountAPILoggingFilter.setSystemName(ServiceAccountAPIConstant.SERVICE_ACCOUNT_API_SYSTEM_NAME);
    FilterRegistrationBean<InternalLoggingFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(serviceAccountAPILoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);

    return registrationBean;
  }


  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
