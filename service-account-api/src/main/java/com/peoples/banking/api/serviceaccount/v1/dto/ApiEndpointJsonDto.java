package com.peoples.banking.api.serviceaccount.v1.dto;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ApiEndpointJsonDto implements java.io.Serializable {

  private int id;
  private String typeCd;
  private String url;
  private LocalDateTime createdOn;
  private LocalDateTime updatedOn;

}
