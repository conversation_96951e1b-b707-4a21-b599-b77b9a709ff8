package com.peoples.banking.api.serviceaccount.v1.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountCertificateRequest;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificateByRefResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountCertificate;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountCertificates;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;

@Mapper(componentModel = "spring", imports = DateUtil.class, uses = {DateConverter.class})
public abstract class ServiceAccountCertificateMapper {

  @Mapping(source = "createdOn", target = "createdDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "updatedOn", target = "updatedDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "expiredOn", target = "expiredDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "serviceAccounts.refId", target = "saRef")
  public abstract ServiceAccountCertificate serviceAccountCertificatesToServiceAccountCertificate(ServiceAccountCertificates certificates);

  @Mapping(target = "createdOn", expression = "java(DateUtil.getLocalUtcTime())")
  @Mapping(source = "expiredDate", target = "expiredOn",
          qualifiedByName = {"DateConverter", "toLocalDateTime"})
  public abstract ServiceAccountCertificates createServiceAccountCertificateRequestToServiceAccountCertificates(CreateServiceAccountCertificateRequest request);

  public abstract RetrieveServiceAccountCertificateByRefResponse serviceAccountCertificatesToRetrieveServiceAccountCertificateByRefResponse(ServiceAccountCertificates certificates);
}
