package com.peoples.banking.api.serviceaccount.v1.util;

import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountStatus;
import com.peoples.banking.domain.serviceaccount.model.*;

import java.util.HashSet;

import java.util.List;

import org.springframework.util.CollectionUtils;

import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountAuthenticationMethod;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.PriorityEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.TypeEnum;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.math.BigDecimal;
import java.util.Set;

public class PayloadValidator {

  public static void validateCreateServiceAccountRequest(CreateServiceAccountRequest request) {
    if (CreateServiceAccountRequest.OutboundApiAuthEnum.BASIC_AUTH.equals(request.getOutboundApiAuth())) {
      if (org.apache.commons.lang3.StringUtils.isBlank(request.getOutboundApiAuthToken())) {
        throw new ValidationException(ErrorProperty.MISSING_FIELD.name(), "outbound_api_auth_token is mandatory when outbound_api_auth=BASIC_AUTH");
      }
    }
    if (request.getAuthentication() != null && !CollectionUtils.isEmpty(request.getAuthentication().getAuthMethods())
        && request.getAuthentication().getAuthMethods().stream()
        .anyMatch(m -> ServiceAccountAuthenticationMethod.MethodEnum.BASIC_AUTH.equals(m.getMethod()))
        && CollectionUtils.isEmpty(request.getAuthentication().getBasicAuthCredentials())) {
      throw new ValidationException(ErrorProperty.MISSING_FIELD.name(),
          "authentication.basic_auth_credentials is mandatory when authentication.authMethods contains BASIC_AUTH");
    }
    if (CreateServiceAccountRequest.ConnectorTypeEnum.INDIRECT.equals(request.getConnectorType())) {
      if (org.apache.commons.lang3.StringUtils.isBlank(request.getIndirectConnectorId())) {
        throw new ValidationException(ErrorProperty.MISSING_FIELD.name(), "indirect_connector_id is mandatory when connector_type=INDIRECT");
      }
    }

    boolean hasFraudPrimary = false;
    boolean hasTechnicalPrimary = false;
    boolean hasOperationalPrimary = false;
    for (ServiceAccountContact serviceAccountContact : request.getContacts()) {
      //find the PRIMARY
      if (PriorityEnum.PRIMARY.equals(serviceAccountContact.getPriority())) {
        if (TypeEnum.FRAUD.equals(serviceAccountContact.getType())) {
          hasFraudPrimary = true;
        }
        if (TypeEnum.OPERATIONAL.equals(serviceAccountContact.getType())) {
          hasOperationalPrimary = true;
        }
        if (TypeEnum.TECHNICAL.equals(serviceAccountContact.getType())) {
          hasTechnicalPrimary = true;
        }
      }
    }
    if (!hasFraudPrimary || !hasTechnicalPrimary || !hasOperationalPrimary) {
      throw new ValidationException(ErrorProperty.MISSING_FIELD.name(), "all the primary contacts are mandatory");
    }

    final List<ServiceAccountConfiguration> configurations = request.getConfigurations();
    validateConfigurations(configurations);

    ServiceAccountLimit limits = request.getLimits();
    if (hasIncorrectScale(limits.getReserveAmount()) || hasIncorrectScale(limits.getNotificationThresholdAmount())
        || hasIncorrectScale(limits.getSuspendThresholdAmount())) {
      throw new ValidationException(ErrorProperty.INVALID_AMOUNT.name(), "all limits should have 2 decimal maximum");
    }

    if( !(ServiceAccountStatus.ENABLED.name().equals(request.getStatus())) &&
        !(ServiceAccountStatus.DISABLED.name().equals(request.getStatus())) &&
        !(ServiceAccountStatus.SUSPENDED.name().equals(request.getStatus())) )
    {
      throw new ValidationException(ErrorProperty.INVALID_INPUT.name(), "not valid status");
    }
  }

  private static void validateConfigurations(List<ServiceAccountConfiguration> configurations) {
    if (!CollectionUtils.isEmpty(configurations)) {
      Set<String> keySet = new HashSet<>();
      for (ServiceAccountConfiguration configuration : configurations) {
        if (!keySet.add(configuration.getKey())) {
          throw new ValidationException(ErrorProperty.INVALID_INPUT.name(), "duplicate key: " + configuration.getKey());
        }
      }
    }
  }

  public static void validateUpdateServiceAccountConfigurationsRequest(UpdateServiceAccountConfigurationsRequest request) {
   validateConfigurations(request.getConfigurations());
  }

  private static boolean hasIncorrectScale(BigDecimal limits) {
    return limits.scale() > 2;
  }
}
