package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountFeatureDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountFeatureJsonDto;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.util.api.common.DateUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * The mapper class to map objects between ServiceAccounts and ServiceAccountPermissions
 */
@Mapper(componentModel = "spring", imports = DateUtil.class)
public abstract class ServiceAccountFeaturesMapper {

  @Mapping(expression = "java(DateUtil.getLocalUtcTime())", target = "updatedOn")
  @Mapping(source = "serviceAccountFeatures.createdOn", target = "createdOn")
  @Mapping(source = "serviceAccounts", target = "serviceAccounts")
  @Mapping(source = "feature", target = "feature")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "serviceAccountFeatures.id", target = "id")
  public abstract ServiceAccountFeatures serviceAccountsToServiceAccountFeatures(
      ServiceAccounts serviceAccounts, String feature, boolean active,
      ServiceAccountFeatures serviceAccountFeatures);

  @Mapping(target = "id", ignore = true)
  @Mapping(expression = "java( DateUtil.getLocalUtcTime())", target = "createdOn")
  @Mapping(source = "serviceAccounts", target = "serviceAccounts")
  @Mapping(source = "active", target = "active")
  public abstract ServiceAccountFeatures serviceAccountsToServiceAccountFeatures(
      ServiceAccounts serviceAccounts, String feature, boolean active);


  @Mapping(source = "serviceAccountFeatures", target = "serviceAccountFeatures", qualifiedByName = "toServiceAccountFeatureDto")
  public abstract ServiceAccountFeatureDto serviceAccountsToServiceAccountFeatureDto(
      Map<String, ServiceAccountFeatures> serviceAccountFeatures);

  @Named("toServiceAccountFeatureDto")
  public List<ServiceAccountFeatureJsonDto> toServiceAccountFeatureDto(
      Map<String, ServiceAccountFeatures> serviceAccountFeatures) {
    if (serviceAccountFeatures == null) {
      return null;
    }
    ServiceAccountFeatureDto result = new ServiceAccountFeatureDto();
    result.setServiceAccountFeatures(new ArrayList<>(serviceAccountFeatures.size()));
    for (ServiceAccountFeatures features : serviceAccountFeatures.values()) {

      ServiceAccountFeatureJsonDto serviceAccountFeatureJsonDto = new ServiceAccountFeatureJsonDto();

      serviceAccountFeatureJsonDto.setFeature(features.getFeature());
      serviceAccountFeatureJsonDto.setActive(features.isActive());
      serviceAccountFeatureJsonDto.setCreatedOn(features.getCreatedOn());
      serviceAccountFeatureJsonDto.setUpdatedOn(features.getUpdatedOn());
      serviceAccountFeatureJsonDto.setId(features.getId());

      result.getServiceAccountFeatures().add(serviceAccountFeatureJsonDto);
    }
    return result.getServiceAccountFeatures();

  }

}
