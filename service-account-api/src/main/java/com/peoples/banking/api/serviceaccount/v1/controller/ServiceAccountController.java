package com.peoples.banking.api.serviceaccount.v1.controller;

import static com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant.FEATURE_NAME;
import static com.peoples.banking.api.serviceaccount.v1.util.PayloadValidator.validateUpdateServiceAccountConfigurationsRequest;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountProperty;
import com.peoples.banking.api.serviceaccount.v1.service.ServiceAccountService;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountStatus;
import com.peoples.banking.api.serviceaccount.v1.util.JwtUtil;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountCertificateRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactResponse;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountFeatureRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitResponse;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificateByRefResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificatesResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountContactListResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountEventsResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountLimitResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountProfileResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountCertificate;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.domain.serviceaccount.model.TransactionEligibilityResponse;
import com.peoples.banking.domain.serviceaccount.model.UpdateFeatureStatusRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountConfigurationsRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountContactRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountFeaturesRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountLimitRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountProfileRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountStatusRequest;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.controller.InternalAPIController;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ServiceAccountController extends InternalAPIController {

  @Autowired
  private ServiceAccountService serviceAccountService;

  @Autowired
  private ServiceAccountProperty serviceAccountProperty;

  @Autowired
  private Environment env;

  //region public api
  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_BY_REF_ID, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountProfileResponse> retrieveServiceAccountProfileByRefId(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    RetrieveServiceAccountProfileResponse response = serviceAccountService.retrieveServiceAccountProfileByRefId(refId);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.OK);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_CREATE, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<CreateServiceAccountResponse> createServiceAccount(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @Valid @RequestBody CreateServiceAccountRequest request) {
    checkJwtTokenAndVerifyGroup(authorization, ServiceAccountAPIConstant.ETRANSFER_ADMINISTRATOR);

    initialCheck(interactionTime, interactionId);

    serviceAccountService.validateCreateServiceAccountRequest(request);

    CreateServiceAccountResponse response = serviceAccountService.createServiceAccount(request);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.CREATED);
  }

  @PerfLogger
  @PutMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_BY_REF_ID, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateServiceAccount(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.updateServiceAccount(refId, updateServiceAccountProfileRequest, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @PatchMapping(value = ServiceAccountAPIConstant.ENABLE_SERVICE_ACCOUNT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity enableServiceAccount(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.updateServiceAccountStatus(refId, ServiceAccountStatus.ENABLED, updateServiceAccountStatusRequest, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @PatchMapping(value = ServiceAccountAPIConstant.DISABLE_SERVICE_ACCOUNT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity disableServiceAccount(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService
        .updateServiceAccountStatus(refId, ServiceAccountStatus.DISABLED, updateServiceAccountStatusRequest, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @PatchMapping(value = ServiceAccountAPIConstant.SUSPEND_SERVICE_ACCOUNT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity suspendServiceAccount(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService
        .updateServiceAccountStatus(refId, ServiceAccountStatus.SUSPENDED, updateServiceAccountStatusRequest, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNTS_LIST, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<List<RetrieveServiceAccountsResponse>> retrieveServiceAccountsList(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime
  ) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    List<RetrieveServiceAccountsResponse> response = serviceAccountService.retrieveServiceAccountsList();

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.OK);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_EVENTS, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountEventsResponse> retrieveServiceAccountsEvents(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @RequestParam(name = ServiceAccountAPIConstant.PATH_CATEGORY, required = false) String category,
      @RequestParam(name = APICommonUtilConstant.PATH_FROM_DATE, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime startDate,
      @RequestParam(name = APICommonUtilConstant.PATH_TO_DATE, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime endDate,
      @RequestParam(name = APICommonUtilConstant.OFFSET, required = false, defaultValue = "0") Integer offset,
      @RequestParam(name = APICommonUtilConstant.MAX_ITEMS, required = false, defaultValue = "25") Integer maxResponseItems) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    RetrieveServiceAccountEventsResponse response = serviceAccountService
        .retrieveServiceAccountsEvents(refId, startDate, endDate, offset, maxResponseItems, category);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.OK);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_FEATURE_BY_REF_ID, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<ServiceAccountResponse> createServiceAccountFeature(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody CreateServiceAccountFeatureRequest createServiceAccountFeatureRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.createServiceAccountFeature(refId, createServiceAccountFeatureRequest, authorization);

    return new ResponseEntity<>(createResponseHttpHeaders(interactionId, false), HttpStatus.CREATED);
  }

  @PerfLogger
  @PatchMapping(value = ServiceAccountAPIConstant.ENABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> enableServiceAccountFeature(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @PathVariable(value = FEATURE_NAME) String featureName,
      @Valid @RequestBody UpdateFeatureStatusRequest updateFeatureStatusRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.enableServiceAccountFeature(refId, featureName, authorization, updateFeatureStatusRequest);

    return new ResponseEntity<>(createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @PatchMapping(value = ServiceAccountAPIConstant.DISABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<ServiceAccountResponse> disableServiceAccountFeature(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @PathVariable(value = FEATURE_NAME) String featureName,
      @Valid @RequestBody UpdateFeatureStatusRequest updateFeatureStatusRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    boolean success = serviceAccountService.disableServiceAccountFeature(refId, featureName, authorization, updateFeatureStatusRequest);

    return new ResponseEntity<>(createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }


  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_LIMIT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountLimitResponse> retrieveServiceAccountsLimits(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    RetrieveServiceAccountLimitResponse response = serviceAccountService.retrieveServiceAccountsLimits(refId, authorization);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.OK);
  }

  @PerfLogger
  @PutMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_LIMIT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateServiceAccountsLimits(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountLimitRequest updateServiceAccountLimitRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.updateServiceAccountLimits(refId, updateServiceAccountLimitRequest, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_LIMIT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<CreateServiceAccountLimitResponse> createServiceAccountsLimits(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody CreateServiceAccountLimitRequest createServiceAccountLimitRequest) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    CreateServiceAccountLimitResponse response = serviceAccountService
        .createServiceAccountLimits(refId, createServiceAccountLimitRequest, authorization);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.CREATED);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_CONTACT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<CreateServiceAccountContactResponse> createServiceAccountsContact(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody CreateServiceAccountContactRequest request) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    CreateServiceAccountContactResponse response = serviceAccountService.createServiceAccountContact(refId, request, authorization);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.CREATED);
  }

  @PerfLogger
  @PutMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_CONTACT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateServiceAccountsContact(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountContactRequest request) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    serviceAccountService.updateServiceAccountContact(refId, request, authorization);

    return new ResponseEntity<>(null, createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.SERVICE_ACCOUNT_CONTACT, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountContactListResponse> retrieveServiceAccountsContacts(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);

    RetrieveServiceAccountContactListResponse response = serviceAccountService.retrieveServiceAccountsContacts(refId, authorization);

    return new ResponseEntity<>(response, createResponseHttpHeaders(interactionId, false), HttpStatus.OK);
  }

  @PerfLogger
  @PutMapping(value = ServiceAccountAPIConstant.UPDATE_SERVICE_ACCOUNT_FEATURES_BY_REF_ID, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateServiceAccountFeatures(
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountFeaturesRequest updateServiceAccountFeaturesRequest) {
    checkJwtToken(authorization);
    boolean result = serviceAccountService.updateServiceAccountFeatures(refId, authorization, updateServiceAccountFeaturesRequest);
    return new ResponseEntity<>(createResponseHttpHeaders(interactionId, false), HttpStatus.NO_CONTENT);
  }
  //endregion

  //region internal api
  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_VALIDATE_FEATURE, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<Boolean> validateServiceAccountFeature(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId, @PathVariable(value = FEATURE_NAME) String featureName) {

    boolean response = serviceAccountService.validateServiceAccountFeature(refId, featureName);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_BY_API_TOKEN, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<ServiceAccountResponse> retrieveServiceAccountByApiToken(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.API_TOKEN) String apiToken) {
    ServiceAccountResponse response = serviceAccountService.retrieveServiceAccountByApiToken(apiToken);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CONFIG, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountConfigurationsResponse> retrieveServiceAccountConfigByRefId(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {

    RetrieveServiceAccountConfigurationsResponse response = serviceAccountService.retrieveServiceAccountConfigurations(refId);

    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    if (response != null) {
      return new ResponseEntity<>(response, headers, HttpStatus.OK);
    } else {
      return new ResponseEntity<>(null, headers, HttpStatus.NO_CONTENT);
    }
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_BY_REF_ID, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<ServiceAccountResponse> retrieveServiceAccountByRefId(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {
    ServiceAccountResponse response = serviceAccountService.retrieveServiceAccountByRefId(refId);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_TRANSACTION_ELIGIBILITY, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<TransactionEligibilityResponse> validateServiceAccountTransactionEligibility(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody ServiceAccountTransaction serviceAccountTransaction) {
    TransactionEligibilityResponse result = serviceAccountService
        .validateServiceAccountTransactionEligibility(refId, serviceAccountTransaction);
    return new ResponseEntity<>(result, null, HttpStatus.OK);
  }

  @PerfLogger
  @PostMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CERTIFICATE, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<ServiceAccountCertificate> createServiceAccountCertificate(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody CreateServiceAccountCertificateRequest request) {
    ServiceAccountCertificate response = serviceAccountService.createServiceAccountCertificate(refId, request);

    return new ResponseEntity<>(response, null, HttpStatus.CREATED);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CERTIFICATE, produces = {ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountCertificatesResponse> getAllServiceAccountCertificates(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId) {
    RetrieveServiceAccountCertificatesResponse response = serviceAccountService.getAllServiceAccountCertificates(refId);

    return new ResponseEntity<>(response, null, HttpStatus.OK);
  }

  @PerfLogger
  @GetMapping(value = ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CERTIFICATE_BY_KEY, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<RetrieveServiceAccountCertificateByRefResponse> getCertificateByKey(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = false) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @PathVariable(value = ServiceAccountAPIConstant.API_TOKEN) String apiToken,
      @PathVariable(value = ServiceAccountAPIConstant.CERTIFICATE_KEY) String certificateKey) {
    RetrieveServiceAccountCertificateByRefResponse response = serviceAccountService.getCertificateByKey(apiToken, certificateKey);

    return new ResponseEntity<>(response, null, HttpStatus.OK);
  }

  @PerfLogger
  @PutMapping(value = ServiceAccountAPIConstant.UPDATE_SERVICE_ACCOUNT_CONFIGURATION, produces = {
      ServiceAccountAPIConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateServiceAccountConfigurations(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = false) String interactionTime,
      @RequestHeader(name = ServiceAccountAPIConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @PathVariable(value = ServiceAccountAPIConstant.REF_ID) String refId,
      @Valid @RequestBody UpdateServiceAccountConfigurationsRequest request) {
    checkJwtToken(authorization);
    initialCheck(interactionTime, interactionId);
    validateUpdateServiceAccountConfigurationsRequest(request);
    serviceAccountService.updateServiceAccountConfiguration(refId, request, authorization);
    return ResponseEntity.noContent().build();
  }
  //endregion

  @Override
  protected int getApiTimeToLiveValue() {
    return serviceAccountProperty.getTimeToLive();
  }

  //TODO this is a temporary fix, the long term solution should call Cognito for validating the token in API Gateway
  private void checkJwtToken(String authorization) {
    String disableJwt = env.getProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
    if (!Boolean.parseBoolean(disableJwt)) {
      JwtUtil.isTokenValid(authorization);
    }
  }

  private void checkJwtTokenAndVerifyGroup(String authorization, String checkUserGroup) {
    String disableJwt = env.getProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
    if (!Boolean.parseBoolean(disableJwt)) {
      JwtUtil.isTokenValidAndVerifyGroup(authorization, checkUserGroup);
    }
  }
}
