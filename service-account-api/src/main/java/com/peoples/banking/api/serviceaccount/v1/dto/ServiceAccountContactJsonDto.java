package com.peoples.banking.api.serviceaccount.v1.dto;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class ServiceAccountContactJsonDto {

  private int id;
  private String type;
  private String priority;
  private String firstName;
  private String lastName;
  private String phone;
  private String email;
  private LocalDateTime updatedOn;
  private LocalDateTime createdOn;
}
