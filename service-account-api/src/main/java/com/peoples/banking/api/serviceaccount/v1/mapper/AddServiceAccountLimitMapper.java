package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountLimit;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountLimits;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import org.mapstruct.Mapper;

/**
 * The mapper class to map objects between Service Accounts entity and Service Account Response domain
 */
@Mapper(componentModel = "spring", uses = {DateConverter.class})
public abstract class AddServiceAccountLimitMapper {

  /**
   * Map ServiceLimits entity to ServiceAccountLimit Response
   *
   * @param serviceAccountLimit the ServiceAccount Limit
   * @param reasonDescription   the reason
   * @return UpdateServiceAccountLimitResponse the update service account limit response
   */
  public abstract CreateServiceAccountLimitResponse ServiceAccountLimitAndReasonToAddServiceAccountLimitResponse(
      ServiceAccountLimit serviceAccountLimit, String reasonDescription);


}
