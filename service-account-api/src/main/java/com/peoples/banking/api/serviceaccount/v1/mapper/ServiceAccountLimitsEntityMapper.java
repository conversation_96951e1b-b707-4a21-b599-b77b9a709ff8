package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountLimitDto;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountLimit;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountLimits;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.util.CollectionUtils;

/**
 * The mapper class to map objects between ServiceAccounts and ServiceAccountLimits
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class}, uses = {DateConverter.class})
@Named("ServiceAccountLimitsEntityMapper")
public abstract class ServiceAccountLimitsEntityMapper {

  @Named("mapToServiceAccountLimitDto")
  public abstract ServiceAccountLimitDto serviceAccountLimitsToServiceAccountLimitDto(ServiceAccountLimits serviceAccountLimits);

  /**
   * Map ServiceLimits request to ServiceAccountLimit entity
   * @param serviceAccountLimit the service account limit entity
   * @return ServiceAccountLimitResponse the service account limit response domain
   */
  @Mapping(target = "createdOn",  expression = "java(DateUtil.getLocalUtcTime())")
  public abstract ServiceAccountLimits serviceAccountLimitToServiceAccountLimits(ServiceAccountLimit serviceAccountLimit);

  /**
   * Map ServiceLimits entity to ServiceAccountLimit Response
   *
   * @param serviceAccountLimits the service account limit entity
   * @return ServiceAccountLimitResponse the service account limit response domain
   */
  public abstract ServiceAccountLimit serviceAccountLimitsToServiceAccountLimit(ServiceAccountLimits serviceAccountLimits);

  @Named("serviceAccountLimitsListToServiceAccountLimit")
  public ServiceAccountLimit serviceAccountLimitsListToServiceAccountLimit(List<ServiceAccountLimits> serviceAccountLimits){
    return CollectionUtils.isEmpty(serviceAccountLimits) ? null : serviceAccountLimitsToServiceAccountLimit(serviceAccountLimits.get(0));
  }

  /**
   * Map ServiceLimits response to updated ServiceAccountLimit entity
   *
   * @param serviceAccountLimit the service account limit entity
   * @return ServiceAccountLimitResponse the service account limit response domain
   */
  @Named("mapToServiceAccountLimits")
  public ServiceAccountLimits serviceAccountLimitToUpdatedServiceAccountLimits(ServiceAccountLimit serviceAccountLimit,
      ServiceAccountLimits existServiceAccountLimits) {
    if (serviceAccountLimit == null || existServiceAccountLimits == null) {
      return null;
    }

    existServiceAccountLimits.setReserveAmount(serviceAccountLimit.getReserveAmount());
    existServiceAccountLimits.setNotificationThresholdAmount(serviceAccountLimit.getNotificationThresholdAmount());
    existServiceAccountLimits.setSuspendThresholdAmount(serviceAccountLimit.getSuspendThresholdAmount());
    existServiceAccountLimits.setUpdatedOn(DateUtil.getLocalUtcTime());

    return existServiceAccountLimits;
  }
}
