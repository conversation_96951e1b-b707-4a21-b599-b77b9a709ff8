package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature;
import com.peoples.banking.domain.serviceaccount.model.Endpoint;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountProfileResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.persistence.serviceaccount.entity.ApiEndpoints;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * The mapper class to map objects between Service Accounts entity and Service Account Response domain
 */
@Named("RetrieveServiceAccountMapper")
@Mapper(componentModel = "spring", imports = ServiceAccountAPIConstant.class ,uses = {DateConverter.class, ServiceAccountLimitsEntityMapper.class, ServiceAccountContactEntityMapper.class})
public abstract class RetrieveServiceAccountMapper {

  /**
   * Map ServiceAccounts entity to ServiceAccountResponse
   * @param serviceAccount the service account entity
   * @return ServiceAccountResponse the service account response domain
   */
  @Mapping(source="settlementAccountNum", target="settlementAccountNumber")
  @Mapping(source="accountNumRange", target="accountNumberRange")
  @Mapping(source = "apiEndpointsMap", target = "apiEndpoints")
  @Mapping(source = "createdOn", target = "createdDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "updatedOn", target = "updatedDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "statusReason", target = "statusReason")
  @Mapping(source = "contacts", target = "contacts", qualifiedByName = {"ServiceAccountContactEntityMapper", "serviceAccountContactListToServiceAccountContactDomainList"})
  @Mapping(source = "limits", target = "limits", qualifiedByName = {"ServiceAccountLimitsEntityMapper", "serviceAccountLimitsListToServiceAccountLimit"})
  @Mapping(source = "crmId", target = "crmRefId")
  @Mapping(source = "configs", target = "configurations", qualifiedByName = "configMapper")
  public abstract ServiceAccountResponse serviceAccountsToServiceAccountResponse(ServiceAccounts serviceAccount);

  /**
   * Map ServiceAccounts entity to ServiceAccountProfile
   * @param serviceAccount the service account entity
   * @return ServiceAccountProfile the service account response domain
   */
  @Mapping(source="settlementAccountNum", target="settlementAccountNumber")
  @Mapping(source="accountNumRange", target="accountNumberRange")
  @Mapping(source = "apiEndpointsMap", target = "endpoints", qualifiedByName = "apiEndpointMapper")
  @Mapping(source = "inboundApiToken", target = "authentication.inboundApiKey")
  @Mapping(source = "outboundApiToken", target = "authentication.outboundApiKey")
  @Mapping(source = "outboundApiAuth", target = "authentication.outboundApiAuth")
  @Mapping(source = "outboundApiAuthToken", target = "authentication.outboundApiAuthToken")
  @Mapping(source = "createdOn", target = "createdDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "updatedOn", target = "updatedDate", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  @Mapping(source = "features", target = "features", qualifiedByName = "featuresMapper")
  @Mapping(source = "limits", target = "limits", qualifiedByName = {"ServiceAccountLimitsEntityMapper", "serviceAccountLimitsListToServiceAccountLimit"})
  @Mapping(source = "configs", target = "configurations", qualifiedByName = "configMapper")
  public abstract RetrieveServiceAccountProfileResponse serviceAccountsToServiceAccountProfile(ServiceAccounts serviceAccount);

  /**
   * Map ServiceAccounts entity to ServiceAccount
   * @param serviceAccounts the of service account entity
   * @return ServiceAccount the service account response domain
   */
  public abstract RetrieveServiceAccountsResponse serviceAccountsToServiceAccount(ServiceAccounts serviceAccounts);

  /**
   * Map features field of ServiceAccounts entity to list of Feature
   * @param featuresMap service account features
   * @return list of Feature domain
   */
  @Named("featuresMapper")
  public List<ApiFeature> toFeature(Map<String, ServiceAccountFeatures> featuresMap) {
    if (featuresMap == null || featuresMap.isEmpty()) {
      return null;
    }
    return featuresMap.values().stream().map(f -> {
      ApiFeature res = new ApiFeature();
      res.setFeatureName(f.getFeature());
      res.setStatus(f.isActive() ? ApiFeature.StatusEnum.ENABLED : ApiFeature.StatusEnum.DISABLED);
      return res;
    }).collect(Collectors.toList());
  }


  /**
   * Map apiEndpointsMap field of ServiceAccounts entity to list  of Endpoint
   * @param apiEndpointsMap the service account entity
   * @return list of Endpoint domain
   */
  @Named("apiEndpointMapper")
  public List<Endpoint> toApiEndpoint(Map<String, ApiEndpoints> apiEndpointsMap) {
    if (apiEndpointsMap == null) {
      return null;
    }
    return apiEndpointsMap.values().stream().map(e -> {
      Endpoint endpoint = new Endpoint();
      endpoint.setType(Endpoint.TypeEnum.valueOf(e.getTypeCd()));
      endpoint.setUrl(e.getUrl());
      return endpoint;
    }).collect(Collectors.toList());
  }

  /**
   * Map configurationsMap field of ServiceAccounts entity to list  of ServiceAccountConfigurations
   * @param configurationsMap the service account entity
   * @return list of ServiceAccountConfigurations domain
   */
  @Named("configMapper")
  public List<com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration> toServiceAccountConfigurations(Map<String, ServiceAccountConfigurations> configurationsMap) {
    if (configurationsMap == null || configurationsMap.isEmpty()) {
      return null;
    }
    return configurationsMap.values().stream().map(e -> {
      com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration configurations =
          new com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration();
      configurations.setKey(e.getKey());
      configurations.setValue(e.getValue());
      return configurations;
    }).collect(Collectors.toList());
  }

}
