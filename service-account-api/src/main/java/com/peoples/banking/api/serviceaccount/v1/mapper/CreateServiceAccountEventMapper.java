package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.api.serviceaccount.v1.dto.ApiEndpointJsonDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ApiEndpointsDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountDto;
import com.peoples.banking.persistence.serviceaccount.entity.ApiEndpoints;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Map;
import java.util.Map.Entry;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * The mapper class to map objects between ServiceAccounts and ServiceAccountDto
 */
@Mapper(componentModel = "spring", imports = LocalDateTime.class)
public abstract class CreateServiceAccountEventMapper {

  @Mapping(source = "apiEndpointsMap", target = "serviceAccountJsonDto.apiEndpointsDto", qualifiedByName = "toApiEndpointsMapDto")
  @Mapping(source = "id", target = "serviceAccountJsonDto.id")
  @Mapping(source = "inboundApiToken", target = "serviceAccountJsonDto.inboundApiToken")
  @Mapping(source = "outboundApiToken", target = "serviceAccountJsonDto.outboundApiToken")
  @Mapping(source = "outboundApiAuth", target = "serviceAccountJsonDto.outboundApiAuth")
  @Mapping(source = "outboundApiAuthToken", target = "serviceAccountJsonDto.outboundApiAuthToken")
  @Mapping(source = "name", target = "serviceAccountJsonDto.name")
  @Mapping(source = "settlementAccountNum", target = "serviceAccountJsonDto.settlementAccountNum")
  @Mapping(source = "accountNumRange", target = "serviceAccountJsonDto.accountNumRange")
  @Mapping(source = "limitGroupId", target = "serviceAccountJsonDto.limitGroupId")
  @Mapping(source = "createdOn", target = "serviceAccountJsonDto.createdOn")
  @Mapping(source = "updatedOn", target = "serviceAccountJsonDto.updatedOn")
  @Mapping(source = "status", target = "serviceAccountJsonDto.status")
  public abstract ServiceAccountDto serviceAccountsToServiceAccountDto(ServiceAccounts serviceAccounts);


  /**
   * Map apiEndpointsMap field of ServiceAccounts entity to map of ApiEndpointsDto
   *
   * @param apiEndpoints the service account entity
   * @return map of ApiEndpointsDto domain
   */
  @Named("toApiEndpointsMapDto")
  public ApiEndpointsDto toApiEndpointsDto(Map<String, ApiEndpoints> apiEndpoints) {
    if (apiEndpoints == null) {
      return null;
    }
    ApiEndpointsDto apiEndpointsDto = new ApiEndpointsDto();
    apiEndpointsDto.setApiEndpoints(new ArrayList<>(apiEndpoints.size()));
    for (Entry<String, ApiEndpoints> apiEndpoint : apiEndpoints.entrySet()) {
      ApiEndpoints apiEndpointValue = apiEndpoint.getValue();
      ApiEndpointJsonDto apiEndpointsJsonDto = new ApiEndpointJsonDto();

      apiEndpointsJsonDto.setTypeCd(apiEndpointValue.getTypeCd());
      apiEndpointsJsonDto.setId(apiEndpointValue.getId());
      apiEndpointsJsonDto.setUrl(apiEndpointValue.getUrl());
      apiEndpointsJsonDto.setUpdatedOn(apiEndpointValue.getUpdatedOn());
      apiEndpointsJsonDto.setCreatedOn(apiEndpointValue.getCreatedOn());

      apiEndpointsDto.getApiEndpoints().add(apiEndpointsJsonDto);
    }
    return apiEndpointsDto;
  }
}
