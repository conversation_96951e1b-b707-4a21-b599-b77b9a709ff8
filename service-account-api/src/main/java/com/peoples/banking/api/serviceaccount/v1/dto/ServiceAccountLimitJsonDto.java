package com.peoples.banking.api.serviceaccount.v1.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ServiceAccountLimitJsonDto {

  private int id;
  private BigDecimal reserveAmount;
  private BigDecimal notificationThresholdAmount;
  private BigDecimal suspendThresholdAmount;
  private LocalDateTime createdOn;
  private LocalDateTime updatedOn;

}
