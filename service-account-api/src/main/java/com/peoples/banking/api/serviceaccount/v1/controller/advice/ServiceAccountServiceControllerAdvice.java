package com.peoples.banking.api.serviceaccount.v1.controller.advice;

import com.peoples.banking.api.serviceaccount.v1.converter.ErrorResponseConverter;
import com.peoples.banking.domain.serviceaccount.model.Error;
import com.peoples.banking.domain.serviceaccount.model.ErrorResponse;
import com.peoples.banking.util.api.common.HttpHeadersUtil;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.controller.advice.APIServiceControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import java.util.Collections;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.ControllerAdvice;

@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
@Log4j2
public class ServiceAccountServiceControllerAdvice extends APIServiceControllerAdvice{

  @Autowired
  private ErrorResponseConverter errorConverter;


  /**
   * {@inheritDoc}
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(String errorCode,
      String additionalInformation) {

    Error error = errorConverter.buildError(errorCode, additionalInformation);
    ErrorResponse errorResponse = errorConverter.buildErrorResponse(Collections.singletonList(error));

    return new ErrorResponseEntity<>(errorResponse);
  }


  /**
   * {@inheritDoc}
   */
  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return HttpHeadersUtil.buildResponseHttpHeaders(request);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  protected String convertToErrorCode(String responseCode) {
      return ErrorProperty.UNEXPECTED_ERROR.name();
  }
}
