package com.peoples.banking.api.serviceaccount.v1.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.UnauthorizedException;
import java.util.Date;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

@Getter
@Log4j2
public class JwtUtil {

  private JwtUtil() {
  }

  /***
   *  admin UI will pass id_token to us
   *  so we will use email here for tracking purpose
   *  we will switch back use  "email": "<EMAIL>"
   ***/
  public static String findUsernameFromToken(String authorization) {
    checkTokenEmpty(authorization);
    DecodedJWT decodedJWT = JWT.decode(authorization.replace(ServiceAccountAPIConstant.BEARER_TOKEN_HEADER, ""));
    return decodedJWT.getClaim(ServiceAccountAPIConstant.COGNITO_EMAIL_FIELD).asString();
  }

  /*** with https://jwt.io/ decode the sample Cognito access_token
   *  "cognito:groups": [
   *     "us-east-2_oH2B5QIUo_PeoplesTrustADFS",
   *     "ETRANSFER_ADMINISTRATOR"
   *   ],
   ***/
  public static String[] findUserGroupsFromToken(String authorization) {
    checkTokenEmpty(authorization);
    DecodedJWT decodedJWT = JWT.decode(authorization.replace(ServiceAccountAPIConstant.BEARER_TOKEN_HEADER, ""));
    String[] groups = null;
    //we will need remove the Bearer token header to make it working
    groups = decodedJWT.getClaim("cognito:groups").asArray(String.class);
    return groups;
  }

  public static void isTokenExpire(String authorization) {
    checkTokenEmpty(authorization);
    DecodedJWT decodedJWT = JWT.decode(authorization.replace(ServiceAccountAPIConstant.BEARER_TOKEN_HEADER, ""));
    if (decodedJWT.getExpiresAt().before(new Date())) {
      log.warn("Expire Token");
      throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
    }
  }

  public static void checkTokenEmpty(String authorization) {
    //make sure the token is not empty also not expired
    if (StringUtils.isEmpty(authorization)) {
      log.warn("Missing Token");
      throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
    }
  }

  public static void isTokenValid(String authorization) {
    try {
      //first check if token expired
      isTokenExpire(authorization);

      //Check user name not empty inside token
      if (StringUtils.isEmpty(findUsernameFromToken(authorization))) {
        log.warn("Invalid User Name");
        throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
      }

      String[] userGroups = findUserGroupsFromToken(authorization);
      boolean userGroupMatch = false;
      if (userGroups != null && userGroups.length > 0) {
        //iterate the String array
        for (int i = 0; i < userGroups.length; i++) {
          //check if string array contains the string
          if (ServiceAccountAPIConstant.ETRANSFER_ADMINISTRATOR.equals(userGroups[i])) {
            //string found
            userGroupMatch = true;
            break;
          }
          //check if string array contains the string
          if (ServiceAccountAPIConstant.ETRANSFER_RISK.equals(userGroups[i])) {
            //string found
            userGroupMatch = true;
            break;
          }
          //check if string array contains the string
          if (ServiceAccountAPIConstant.ETRANSFER_VIEW.equals(userGroups[i])) {
            //string found
            userGroupMatch = true;
            break;
          }
        }
      }
      if (!userGroupMatch) {
        log.warn("Invalid User Group");
        throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
      }
    } catch (UnauthorizedException unauthorizedException) {
      throw unauthorizedException;
    } catch (Exception ex) {
      log.warn("Invalid Token");
      throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
    }
  }

  public static void isTokenValidAndVerifyGroup(String authorization,String checkUserGroup) {
    try {
      //first check if token expired
      isTokenExpire(authorization);

      //Check user name not empty inside token
      if (StringUtils.isEmpty(findUsernameFromToken(authorization))) {
        log.warn("Invalid User Name");
        throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
      }

      String[] userGroups = findUserGroupsFromToken(authorization);
      boolean userGroupMatch = false;
      if (userGroups != null && userGroups.length > 0) {
        //iterate the String array
        for (int i = 0; i < userGroups.length; i++) {
          //check if string array contains the string
          if (checkUserGroup.equals(userGroups[i])) {
            //string found
            userGroupMatch = true;
            break;
          }
        }
      }
      if (!userGroupMatch) {
        log.warn("Invalid User Group");
        throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
      }
    } catch (UnauthorizedException unauthorizedException) {
      throw unauthorizedException;
    } catch (Exception ex) {
      log.warn("Invalid Token");
      throw new UnauthorizedException(ErrorProperty.UNAUTHORIZED.name());
    }
  }
}
