package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.util.CollectionUtils;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountStatus;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.Endpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountAuthentication;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountAuthenticationMethod;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ApiEndpoints;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;

@Mapper(componentModel = "spring", imports = {DateUtil.class, ServiceAccountAPIConstant.class, ServiceAccountStatus.class}, uses = {DateConverter.class, RetrieveServiceAccountMapper.class, ServiceAccountFeaturesMapper.class, ServiceAccountLimitsEntityMapper.class, ServiceAccountContactEntityMapper.class})
public abstract class CreateServiceAccountMapper {

  @Mapping(source = "refId", target = "refId")
  @Mapping(source = "name", target = "name")
  @Mapping(source = "outboundApiAuth", target = "outboundApiAuth")
  @Mapping(source="settlementAccountNumber", target="settlementAccountNum")
  @Mapping(source="accountNumberRange", target="accountNumRange")
  @Mapping(source="limitGroupId", target="limitGroupId")
  @Mapping(source = "crmRefId", target = "crmId")
  @Mapping(target = "createdOn",  expression = "java(DateUtil.getLocalUtcTime())")
  @Mapping(source = "contacts", target = "contacts", qualifiedByName = {"ServiceAccountContactEntityMapper", "serviceAccountContactDomainListToServiceAccountContactList"})
  @Mapping(source = "endpoints", target = "apiEndpointsMap", qualifiedByName = "endpointsMapper")
  @Mapping(source = "configurations", target = "configs", qualifiedByName = "configurationsMapper")
  @Mapping(target = "features",ignore = true)
  @Mapping(target = "limits",ignore = true)
  public abstract ServiceAccounts сreateServiceAccountRequestToServiceAccounts(CreateServiceAccountRequest request);

  @Mapping(source="serviceAccounts.settlementAccountNum", target="settlementAccountNumber")
  @Mapping(source="serviceAccounts.accountNumRange", target="accountNumberRange")
  @Mapping(source = "serviceAccounts.apiEndpointsMap", target = "endpoints", qualifiedByName = {"RetrieveServiceAccountMapper", "apiEndpointMapper"})
  @Mapping(source = "serviceAccounts.inboundApiToken", target = "authentication.inboundApiKey")
  @Mapping(source = "serviceAccounts.outboundApiToken", target = "authentication.outboundApiKey")
  @Mapping(source = "serviceAccounts.outboundApiAuth", target = "authentication.outboundApiAuth")
  @Mapping(source = "serviceAccounts.outboundApiAuthToken", target = "authentication.outboundApiAuthToken")
  @Mapping(source = "crmRefId", target = "crmRefId")
  @Mapping(source = "basicAuthCredentials", target = "basicAuthCredentials")
  @Mapping(source = "serviceAccounts.features", target = "features", qualifiedByName = {"RetrieveServiceAccountMapper", "featuresMapper"})
  @Mapping(source = "serviceAccounts.limits", target = "limits", qualifiedByName = {"ServiceAccountLimitsEntityMapper", "serviceAccountLimitsListToServiceAccountLimit"})
  @Mapping(source = "serviceAccounts.configs", target = "configurations", qualifiedByName = {"RetrieveServiceAccountMapper", "configMapper"})
  public abstract CreateServiceAccountResponse serviceAccountsToCreateServiceAccountResponse(ServiceAccounts serviceAccounts,
                                                                                             String crmRefId,
                                                                                             List<ServiceAccountAuthenticationMethod> authMethods,
                                                                                             List basicAuthCredentials);

  @Named("responseFeaturesMapper")
  public List<ApiFeature> toFeature(Map<String, ServiceAccountFeatures> featuresMap) {
    if (featuresMap == null || featuresMap.isEmpty()) {
      return null;
    }
    return featuresMap.values().stream().map(f -> {
      ApiFeature res = new ApiFeature();
      res.setFeatureName(f.getFeature());
      res.setStatus(f.isActive() ? ApiFeature.StatusEnum.ENABLED : ApiFeature.StatusEnum.DISABLED);
      return res;
    }).collect(Collectors.toList());
  }

  /**
   * Map list  of Endpoint to apiEndpointsMap field of ServiceAccounts entity
   * @param endpointList list of Endpoint domain
   * @return the service account entity
   */
  @Named("endpointsMapper")
  public Map<String, ApiEndpoints> toApiEndpoint(List<Endpoint> endpointList) {
    if (CollectionUtils.isEmpty(endpointList)) {
      return null;
    }
    Map<String, ApiEndpoints> res = new HashMap<>();
    endpointList.forEach(e->{
      final ApiEndpoints endpoints = new ApiEndpoints();
      endpoints.setCreatedOn(DateUtil.getLocalUtcTime());
      endpoints.setTypeCd(e.getType().getValue());
      endpoints.setUrl(e.getUrl());
      res.put(e.getType().getValue(), endpoints);
    });
    return res;
  }

  @Named("configurationsMapper")
  public Map<String, ServiceAccountConfigurations> toServiceAccountConfigurations(List<com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration> endpointList) {
    if (CollectionUtils.isEmpty(endpointList)) {
      return null;
    }
    Map<String, ServiceAccountConfigurations> res = new HashMap<>();
    endpointList.forEach(e -> {
      final ServiceAccountConfigurations configuration = new ServiceAccountConfigurations();
      configuration.setCreatedOn(DateUtil.getLocalUtcTime());
      configuration.setKey(e.getKey());
      configuration.setValue(e.getValue());
      res.put(e.getKey(), configuration);
    });
    return res;
  }

  @Mapping(source = "authMethods", target = "authMethod", qualifiedByName = "authMethodMapper")
  @Mapping(source = "basicAuthCredentials", target = "credential")
  public abstract CreateServiceAccountCredentialRequest createServiceAccountRequestToCreateServiceAccountCredentialRequest(ServiceAccountAuthentication request);

  @Named("authMethodMapper")
  protected List<String> authMethodMapper(List<ServiceAccountAuthenticationMethod> methods) {
      if (methods == null) {
          return null;
      }
      return methods.stream().map(m->m.getMethod().toString()).collect(Collectors.toList());
  }
}
