package com.peoples.banking.api.serviceaccount.v1.service;

import static com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant.USER_SYSTEM;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.peoples.banking.adapter.pb.serviceaccountcredential.ServiceAccountCredentialAdapter;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountProperty;
import com.peoples.banking.api.serviceaccount.v1.dto.JsonDto;
import com.peoples.banking.api.serviceaccount.v1.dto.LimitBreachKafkaMessageType;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountContactDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountFeatureDto;
import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountLimitDto;
import com.peoples.banking.api.serviceaccount.v1.dto.LimitBreachKafkaMessage;
import com.peoples.banking.api.serviceaccount.v1.mapper.AddServiceAccountLimitMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.CreateServiceAccountEventMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.CreateServiceAccountMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.RetrieveServiceAccountEventMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.RetrieveServiceAccountMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountCertificateMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountConfigEntityMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountContactEntityMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountEventEntityMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountFeaturesMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ServiceAccountLimitsEntityMapper;
import com.peoples.banking.api.serviceaccount.v1.mapper.ValidateServiceAccountTransactionEligibilityMapper;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventCategory;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventReasonType;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountEventType;
import com.peoples.banking.api.serviceaccount.v1.type.ServiceAccountStatus;
import com.peoples.banking.api.serviceaccount.v1.util.JwtUtil;
import com.peoples.banking.api.serviceaccount.v1.util.PayloadValidator;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature.StatusEnum;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountCertificateRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactResponse;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountFeatureRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitResponse;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificateByRefResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificatesResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountContactListResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountEventsResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountLimitResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountProfileResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountCertificate;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountLimit;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.domain.serviceaccount.model.TransactionEligibilityResponse;
import com.peoples.banking.domain.serviceaccount.model.TransactionEligibilityResult;
import com.peoples.banking.domain.serviceaccount.model.UpdateFeatureStatusRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountConfigurationsRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountContactRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountFeaturesRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountLimitRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountProfileRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountStatusRequest;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.persistence.serviceaccount.entity.Features;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountCertificates;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountLimits;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountTransactions;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountsContact;
import com.peoples.banking.persistence.serviceaccount.repository.FeaturesRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountCertificatesRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountConfigurationsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountFeaturesRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountTransactionRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Log4j2
@Service
public class ServiceAccountService {

  private static final String EVENT_TYPE_UPDATE = "service-account:update";
  private static final String EVENT_TYPE_LIMIT = "service-account:limit";
  private static final String EVENT_TYPE_LIMIT_UPDATE = "service-account:limit:update";
  private static final String EVENT_TYPE_FEATURE_ENABLE = "service-account:feature:enable";
  private static final String EVENT_TYPE_FEATURE_DISABLE = "service-account:feature:disable";
  private static final String EVENT_TYPE_STATUS_ENABLE = "service-account:status:enable";
  private static final String EVENT_TYPE_STATUS_DISABLE = "service-account:status:disable";
  private static final String EVENT_TYPE_STATUS_SUSPEND = "service-account:status:suspend";
  private static final String EVENT_TYPE_ACCOUNT_CONFIGURATION_UPDATED = "service-account:configuration:updated";
  public static final String SERVICE_ACCOUNT_FEATURE_STATUS_UPDATE = "service-account:feature:status:update";
  private static final BigDecimal DEFAULT_TOKEN_LENGTH = BigDecimal.valueOf(25);

  @Autowired
  private ServiceAccountCacheService serviceAccountCacheService;

  @Autowired
  private RetrieveServiceAccountMapper retrieveServiceAccountMapper;

  @Autowired
  private ValidateServiceAccountTransactionEligibilityMapper validateServiceAccountTransactionEligibilityMapper;

  @Autowired
  private AddServiceAccountLimitMapper addServiceAccountLimitMapper;

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  @Autowired
  private ServiceAccountTransactionRepository serviceAccountTransactionRepository;

  @Autowired
  private ServiceAccountFeaturesRepository serviceAccountFeaturesRepository;

  @Autowired
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  @Autowired
  private CreateServiceAccountMapper createServiceAccountMapper;

  @Autowired
  private ServiceAccountEventEntityMapper serviceAccountEventEntityMapper;

  @Autowired
  private RetrieveServiceAccountEventMapper retrieveServiceAccountEventMapper;

  @Autowired
  private CreateServiceAccountEventMapper createServiceAccountEventMapper;

  @Autowired
  private ServiceAccountFeaturesMapper serviceAccountFeaturesMapper;

  @Autowired
  private ServiceAccountCertificateMapper serviceAccountCertificateMapper;

  @Autowired
  private ServiceAccountCertificatesRepository serviceAccountCertificatesRepository;

  @Autowired
  private ServiceAccountLimitsEntityMapper serviceAccountLimitsEntityMapper;

  @Autowired
  private FeaturesRepository featuresRepository;

  @Autowired
  private ServiceAccountContactEntityMapper serviceAccountContactEntityMapper;

  @Autowired
  private ServiceAccountConfigEntityMapper serviceAccountConfigEntityMapper;

  @Autowired
  private KafkaTemplate kafkaTemplate;

  @Autowired
  private ServiceAccountProperty serviceAccountProperty;

  @Autowired
  private ServiceAccountCredentialAdapter serviceAccountCredentialAdapter;

  @Autowired
  private ServiceAccountConfigurationsRepository serviceAccountConfigurationsRepository;

  private final ZoneId easternTimeZone = ZoneId.of("America/Toronto", ZoneId.SHORT_IDS);
  private final ZoneId utc = ZoneId.of("UTC");

  /**
   * Retrieve Service Account information by reference id
   *
   * @param refId the reference id
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  public RetrieveServiceAccountProfileResponse retrieveServiceAccountProfileByRefId(String refId) {
    return processServiceAccountWithChecks(refId, sa -> retrieveServiceAccountMapper.serviceAccountsToServiceAccountProfile(sa));
  }

  @PerfLogger
  public List<RetrieveServiceAccountsResponse> retrieveServiceAccountsList() {
    try {
      return serviceAccountsRepository.findAll().stream().map(retrieveServiceAccountMapper::serviceAccountsToServiceAccount)
          .collect(Collectors.toList());
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }

  @PerfLogger
  public void updateServiceAccountStatus(String refId, ServiceAccountStatus status,
      UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest, String authorization) {
    processServiceAccountWithChecks(refId, sa -> {

      String eventScope = ServiceAccountStatus.ENABLED == status ? EVENT_TYPE_STATUS_ENABLE : EVENT_TYPE_STATUS_DISABLE;
      ServiceAccountDto oldServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);
      sa.setStatus(status.toString());
      ServiceAccountDto newServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, eventScope, oldServiceAccountDto,
          newServiceAccountDto, updateServiceAccountStatusRequest.getReasonDescription(),
          ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED,
          ServiceAccountEventReasonType.valueOf(updateServiceAccountStatusRequest.getReasonCode().getValue()));
      sa.setStatusUpdatedBy(event.getUpdatedBy());
      sa.setStatusReason(updateServiceAccountStatusRequest.getReasonDescription());
      serviceAccountsRepository.save(sa);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return null;
    });
    if (status == ServiceAccountStatus.ENABLED) {
      sendKafkaMessage(createLimitBreachKafkaMessage(LimitBreachKafkaMessageType.RESET, refId));
    }
  }

  @PerfLogger
  public RetrieveServiceAccountEventsResponse retrieveServiceAccountsEvents(String refId,
      LocalDateTime startDate, LocalDateTime endDate, Integer offset, Integer maxResponseItems, String category) {
    return processServiceAccountWithChecks(refId, sa -> {
      RetrieveServiceAccountEventsResponse res = new RetrieveServiceAccountEventsResponse();
      res.setRefId(refId);
      res.setName(sa.getName());
      LocalDateTime dateFrom = startDate;
      LocalDateTime dateTo = endDate;
      //first we will adjust the fromDate and endDate if PTC user did not provide us
      if (dateFrom == null) {
        dateFrom = LocalDate.now().minusMonths(1).atStartOfDay(easternTimeZone).withZoneSameInstant(utc).toLocalDateTime();
      }

      if (dateTo == null) {
        dateTo = DateUtil.getLocalUtcTime();
      }

      //Then we will check PTC DB to find the payments for both inBound and outBound
      List<ServiceAccountEvents> serviceAccountEvents;
      if (Strings.isNullOrEmpty(category) || "ALL".equals(category)) {
        serviceAccountEvents = serviceAccountEventsRepository.findServiceAccountEvents(dateFrom, dateTo, offset, maxResponseItems,
            sa.getId());
      } else {
        serviceAccountEvents = serviceAccountEventsRepository.findServiceAccountEventsByCategory(dateFrom, dateTo, offset, maxResponseItems,
            sa.getId(), category);
      }

      res.setEvents(serviceAccountEvents.stream().map(retrieveServiceAccountEventMapper::serviceAccountEventsToServiceAccountEvent)
          .collect(Collectors.toList()));
      return res;
    });
  }


  @PerfLogger
  public boolean updateServiceAccount(String refId,
      UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest, String authorization) {
    return processServiceAccountWithChecks(refId, sa -> {
      ServiceAccountDto oldServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);

      sa.setName(updateServiceAccountProfileRequest.getName());
      sa.setAccountNumRange(updateServiceAccountProfileRequest.getAccountNumberRange());
      sa.setLimitGroupId(updateServiceAccountProfileRequest.getLimitGroupId());
      sa.setSettlementAccountNum(updateServiceAccountProfileRequest.getSettlementAccountNumber());
      sa.setUpdatedOn(DateUtil.getLocalUtcTime());
      serviceAccountsRepository.save(sa);

      ServiceAccountDto newServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, EVENT_TYPE_UPDATE, oldServiceAccountDto,
          newServiceAccountDto, "user update service account",
          ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED,
          ServiceAccountEventReasonType.AGENT_INITIATED);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return true;
    });
  }

  /**
   * Internal API service for verifying service account transaction limits
   *
   * @param refId
   * @param serviceAccountTransaction
   * @return TransactionEligibilityResult TransactionEligibilityResult
   */
  @PerfLogger
  public TransactionEligibilityResponse validateServiceAccountTransactionEligibility(String refId,
      ServiceAccountTransaction serviceAccountTransaction) {

    TransactionEligibilityResult transactionEligibilityResult = TransactionEligibilityResult.ALLOW;
    TransactionEligibilityResponse response = new TransactionEligibilityResponse();
    try {
      ServiceAccountLimits serviceAccountLimits;

      //1. verify service account exist
      Optional<ServiceAccounts> serviceAccountOption = serviceAccountsRepository.internalFindByRefIdWithContacts(refId);
      if (serviceAccountOption.isPresent()) {
        ServiceAccounts serviceAccounts = serviceAccountOption.get();

        //2. get service account's limit - we treating it as one limit, and it should exists!
        serviceAccountLimits = serviceAccounts.getLimits().get(0);

        //3. transfer domain to entity
        ServiceAccountTransactions serviceAccountTransactions = validateServiceAccountTransactionEligibilityMapper
            .serviceAccountTransactionToServiceAccountTransactions(refId, serviceAccountTransaction);

        //4.save it to redis
        serviceAccountTransactionRepository.save(serviceAccountTransactions);
        log.info("{} - {} for amount={} saved", serviceAccountTransactions.getServiceAccountRefId(),
            serviceAccountTransactions.getExternalPaymentRefId(), serviceAccountTransactions.getAmount());

        //5. retrieve all the transaction for the service account
        Map<String, ServiceAccountTransactions> serviceAccountTransactionsMap = serviceAccountTransactionRepository
            .getServiceAccountTransactions(serviceAccountTransactions.getServiceAccountRefId());

        //6. after the save and the total transaction count is 1, that means it's the first transaction of the day for the service account
        // and we need to set the expiry date for that Service Account because we use the service account ref id as the key
        //TODO: urgent fix for increasing size from 1 to 50; need to implement a solution that we ensure every key has expiry date
        if (serviceAccountTransactionsMap.size() < 50) {
          log.info("{} size={}, setting expiry date", serviceAccountTransactions.getServiceAccountRefId(),
              serviceAccountTransactionsMap.size());
          serviceAccountTransactionRepository.expireAt(serviceAccountTransactions, LocalDate.now().plusDays(1));
        }

        //7. sum all the transactions for the service account
        BigDecimal sum = serviceAccountTransactionsMap.values().stream().map(ServiceAccountTransactions::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("{} - current volume = {}; warning limit = {}; suspend limit = {}",
                serviceAccounts.getRefId(), sum, serviceAccountLimits.getNotificationThresholdAmount(), serviceAccountLimits.getSuspendThresholdAmount());
        //8. verify if the daily transaction breached the suspend threshold
        if (sum.compareTo(serviceAccountLimits.getSuspendThresholdAmount()) > 0) {

          log.warn("{} : Payment {} breached {} suspend threshold amount", APICommonUtilConstant.INVESTIGATE,
              serviceAccountTransactions.getExternalPaymentRefId(), serviceAccountTransactions.getServiceAccountRefId());
          //remove the transaction that breached the suspend threshold amount.
          serviceAccountTransactionRepository.delete(serviceAccountTransactions);
          ServiceAccountDto oldServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(serviceAccounts);

          if (ServiceAccountStatus.ENABLED.name().equals(serviceAccounts.getStatus())) {
            changeServiceAccountStatusAndRefreshCache(serviceAccounts, ServiceAccountStatus.SUSPENDED.name(), "SUSPEND limit breached");

            //if the current status is ENABLED and we change the status then we log that info and insert the event.
            // otherwise we do not need to insert the event.
            ServiceAccountDto newServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(serviceAccounts);
            ServiceAccountEvents blockedEvent = buildServiceAccountEvent(null, serviceAccounts, EVENT_TYPE_STATUS_SUSPEND,
                oldServiceAccountDto,
                newServiceAccountDto, "System blocked because of transaction limit",
                ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED, ServiceAccountEventReasonType.SYSTEM_INITIATED);
            serviceAccountEventsRepository.save(blockedEvent);
          }

          log.warn("{} is {}", serviceAccountTransactions.getServiceAccountRefId(), serviceAccounts.getStatus());

          transactionEligibilityResult = TransactionEligibilityResult.BLOCK;
          response.setStatusReason(serviceAccounts.getStatusReason());
          sendKafkaMessage(createLimitBreachKafkaMessage(LimitBreachKafkaMessageType.SUSPENSION, serviceAccounts.getRefId()));
        } else if (ServiceAccountStatus.SUSPENDED.name().equals(serviceAccounts.getStatus())
            && sum.compareTo(serviceAccountLimits.getSuspendThresholdAmount()) <= 0) {

          final String statusReason = "SA enabled, daily balance refreshed";
          ServiceAccountDto oldServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(serviceAccounts);
          changeServiceAccountStatusAndRefreshCache(serviceAccounts, ServiceAccountStatus.ENABLED.name(), statusReason);
          ServiceAccountDto newServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(serviceAccounts);

          ServiceAccountEvents event = buildServiceAccountEvent(null, serviceAccounts, EVENT_TYPE_STATUS_ENABLE, oldServiceAccountDto,
                  newServiceAccountDto, statusReason,
                  ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED,
                  ServiceAccountEventReasonType.SYSTEM_INITIATED);
          serviceAccountEventsRepository.save(event);
          //add a log entry for easy trouble shooting
          log.warn("{} is {} -- SA enabled and accumulated daily balance refreshed", serviceAccountTransactions.getServiceAccountRefId(),
              serviceAccounts.getStatus());

        } else if (sum.compareTo(serviceAccountLimits.getNotificationThresholdAmount()) > 0) {
          sendKafkaMessage(createLimitBreachKafkaMessage(LimitBreachKafkaMessageType.NOTIFICATION, serviceAccounts.getRefId(), sum));
          transactionEligibilityResult = TransactionEligibilityResult.WARNING;
          log.warn("Payment {} breached {} notification threshold amount", serviceAccountTransactions.getExternalPaymentRefId(),
              serviceAccountTransactions.getServiceAccountRefId());
        }
      } else {
        log.warn("ref id={} not found", refId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    response.setEligibilityResult(transactionEligibilityResult);
    return response;
  }

  private void sendKafkaMessage(final LimitBreachKafkaMessage message) {
    kafkaTemplate.send(serviceAccountProperty.getKafkaTopic(), message)
        .addCallback(new ListenableFutureCallback<LimitBreachKafkaMessage>() {
          @Override
          public void onSuccess(LimitBreachKafkaMessage message) {
            log.info("Sent message to topic {} for SA {} type {}", serviceAccountProperty.getKafkaTopic(),
                message.getServiceAccountRefId(), message.getType());
          }

          @Override
          public void onFailure(Throwable ex) {
            log.error("Error while sending kafka message for SA: " + message.getServiceAccountRefId(), ex);
          }
        });
  }

  private LimitBreachKafkaMessage createLimitBreachKafkaMessage(final LimitBreachKafkaMessageType type, final String refId) {
    return createLimitBreachKafkaMessage(type, refId, null);
  }

  private LimitBreachKafkaMessage createLimitBreachKafkaMessage(final LimitBreachKafkaMessageType type, final String refId, BigDecimal amount) {
    LimitBreachKafkaMessage res = new LimitBreachKafkaMessage();
    res.setServiceAccountRefId(refId);
    res.setType(type);
    res.setAmount(amount);
    return res;
  }

  /**
   * refresh service account caches
   */
  private void evictServiceAccountCaches() {
    serviceAccountCacheService.evictServiceAccountByApiTokenCacheValues();
    serviceAccountCacheService.evictServiceAccountByRefIdCacheValues();
    serviceAccountCacheService.evictServiceAccountInternalByRefIdAllCacheValues();
  }

  /**
   * Change service account feature status for service account by reference id
   *
   * @param refId the reference id
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  public boolean updateServiceAccountFeature(String refId, String featureName,
      String authorization, boolean enableFeature, UpdateFeatureStatusRequest updateFeatureStatusRequest) {

    return processServiceAccountWithChecksLoadFeatures(refId, serviceAccounts -> {
      ServiceAccountFeatures serviceAccountFeatures = serviceAccounts.getFeatures()
          .get(featureName);
      String eventScope =
          enableFeature ? EVENT_TYPE_FEATURE_ENABLE : EVENT_TYPE_FEATURE_DISABLE;
      updateServiceAccountFeature(refId, authorization, serviceAccounts, featureName, enableFeature, serviceAccountFeatures,
          updateFeatureStatusRequest.getReasonDescription(), eventScope);

      //refresh cache
      evictServiceAccountCaches();

      return true;

    });
  }

  /**
   * Change service account features status for service account by reference id
   *
   * @param refId the reference id
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  @Transactional
  public boolean updateServiceAccountFeatures(String refId,
      String authorization, UpdateServiceAccountFeaturesRequest updateServiceAccountFeaturesRequest) {

    return processServiceAccountWithChecksLoadFeatures(refId, serviceAccounts -> {

      ServiceAccountFeatureDto oldServiceAccountFeatureDto = serviceAccountFeaturesMapper
          .serviceAccountsToServiceAccountFeatureDto(serviceAccounts.getFeatures());

      List<ServiceAccountFeatures> serviceAccountFeaturesList = new ArrayList<>();
      //TODO - merge this code with update one feature method
      for (ApiFeature apiFeature : updateServiceAccountFeaturesRequest.getFeatures()) {
        String featureName = apiFeature.getFeatureName();
        boolean enableFeature = apiFeature.getStatus() == StatusEnum.ENABLED;
        ServiceAccountFeatures serviceAccountFeatures = serviceAccounts.getFeatures()
            .get(featureName);

        if (serviceAccountFeatures == null) {
          log.info("Feature to update is not available {} for refid={}", featureName, refId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name(), featureName);
        }

        serviceAccountFeatures = serviceAccountFeaturesMapper
            .serviceAccountsToServiceAccountFeatures(serviceAccounts, featureName, enableFeature, serviceAccountFeatures);
        serviceAccountFeaturesList.add(serviceAccountFeatures);

      }
      ServiceAccountFeatureDto newServiceAccountFeatureDto = serviceAccountFeaturesMapper
          .serviceAccountsToServiceAccountFeatureDto(serviceAccounts.getFeatures());

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, serviceAccounts, SERVICE_ACCOUNT_FEATURE_STATUS_UPDATE,
          oldServiceAccountFeatureDto,
          newServiceAccountFeatureDto, updateServiceAccountFeaturesRequest.getReasonDescription(),
          ServiceAccountEventCategory.FEATURE, ServiceAccountEventType.UPDATED, ServiceAccountEventReasonType.AGENT_INITIATED);

      serviceAccountFeaturesRepository.saveAll(serviceAccountFeaturesList);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return true;
    });
  }

  private void updateServiceAccountFeature(String refId, String authorization, ServiceAccounts serviceAccounts, String featureName,
      boolean enableFeature, ServiceAccountFeatures serviceAccountFeatures, String reasonDescription, String eventScope) {
    if (serviceAccountFeatures == null) {
      log.info("Feature to update is not available {} for refid={}", featureName, refId);
      throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name(), featureName);
    }

    if (serviceAccountFeatures.isActive() == enableFeature) {
      log.info("Feature already exists and has already requested status {} for refid={}", serviceAccountFeatures, refId);
      throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name(), featureName);
    }

    serviceAccountFeatures = serviceAccountFeaturesMapper
        .serviceAccountsToServiceAccountFeatures(serviceAccounts, featureName, enableFeature, serviceAccountFeatures);

    ServiceAccountFeatureDto oldServiceAccountFeatureDto = serviceAccountFeaturesMapper
        .serviceAccountsToServiceAccountFeatureDto(serviceAccounts.getFeatures());

    serviceAccounts.getFeatures().put(featureName, serviceAccountFeatures);

    ServiceAccountFeatureDto newServiceAccountFeatureDto = serviceAccountFeaturesMapper
        .serviceAccountsToServiceAccountFeatureDto(serviceAccounts.getFeatures());

    ServiceAccountEvents event = buildServiceAccountEvent(authorization, serviceAccounts, eventScope, oldServiceAccountFeatureDto,
        newServiceAccountFeatureDto, reasonDescription,
        ServiceAccountEventCategory.FEATURE, ServiceAccountEventType.UPDATED, ServiceAccountEventReasonType.AGENT_INITIATED);

    serviceAccountsRepository.save(serviceAccounts);
    serviceAccountEventsRepository.save(event);
  }


  @PerfLogger
  public boolean createServiceAccountFeature(String refId, CreateServiceAccountFeatureRequest createServiceAccountFeatureRequest,
      String authorization) {
    return processServiceAccountWithChecksLoadFeatures(refId, serviceAccounts -> {

      ServiceAccountFeatures serviceAccountFeatures = null;
      Map<String, ServiceAccountFeatures> serviceAccountFeaturesMap = null;
      if (serviceAccounts.getFeatures() != null) {
        serviceAccountFeaturesMap = serviceAccounts.getFeatures();
        serviceAccountFeatures = serviceAccountFeaturesMap.get(createServiceAccountFeatureRequest.getFeatureName());
      } else {
        serviceAccountFeaturesMap = new HashMap<>();
      }

      if (serviceAccountFeatures != null) {
        log.info("Feature already exists and has already requested status {} for refid={}", serviceAccountFeatures, refId);
        throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
      }

      List<Features> featuresList = featuresRepository.findAll();
      boolean isValidFeatureName = false;
      for (Features feature : featuresList) {
        if (feature.getFeature().equals(createServiceAccountFeatureRequest.getFeatureName())) {
          if (feature.isRestricted()) {
            isValidFeatureName = true;
            break;
          } else {
            log.warn("Feature {} is not restricted for refid={}", createServiceAccountFeatureRequest.getFeatureName(), refId);
            throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
          }
        }
      }
      if (!isValidFeatureName) {
        throw new ValidationException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }

      serviceAccountFeatures = serviceAccountFeaturesMapper
          .serviceAccountsToServiceAccountFeatures(serviceAccounts, createServiceAccountFeatureRequest.getFeatureName(), true);

      serviceAccountFeaturesMap.put(createServiceAccountFeatureRequest.getFeatureName(), serviceAccountFeatures);
      serviceAccounts.setFeatures(serviceAccountFeaturesMap);

      ServiceAccountFeatureDto newServiceAccountFeatureDto = serviceAccountFeaturesMapper
          .serviceAccountsToServiceAccountFeatureDto(serviceAccounts.getFeatures());

      String eventScope = "service-account:feature:add";

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, serviceAccounts, eventScope, null,
          newServiceAccountFeatureDto, createServiceAccountFeatureRequest.getReasonDescription(),
          ServiceAccountEventCategory.FEATURE, ServiceAccountEventType.ADDED, ServiceAccountEventReasonType.AGENT_INITIATED);

      //TODO: default save() also update associated entities, we'll need to customize the save for insert a new record only
      serviceAccountsRepository.save(serviceAccounts);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return true;
    });
  }

  @PerfLogger
  public boolean enableServiceAccountFeature(String refId, String featureName, String authorization,
      UpdateFeatureStatusRequest updateFeatureStatusRequest) {
    return updateServiceAccountFeature(refId, featureName, authorization, true, updateFeatureStatusRequest);
  }

  @PerfLogger
  public boolean disableServiceAccountFeature(String refId, String featureName, String authorization,
      UpdateFeatureStatusRequest updateFeatureStatusRequest) {
    return updateServiceAccountFeature(refId, featureName, authorization, false, updateFeatureStatusRequest);
  }

  /**
   * Retrieve Service Account limit by reference id
   *
   * @param refId         the service account reference id
   * @param authorization
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  public RetrieveServiceAccountLimitResponse retrieveServiceAccountsLimits(String refId, String authorization) {
    return processServiceAccountWithChecksLoadLimits(refId, sa -> {
      if (sa.getLimits().isEmpty()) {
        log.warn("ref id={} service account limit not found", refId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
      ServiceAccountLimit serviceAccountLimit = this.serviceAccountLimitsEntityMapper
          .serviceAccountLimitsToServiceAccountLimit(sa.getLimits().get(0));

      RetrieveServiceAccountLimitResponse retrieveServiceAccountLimitResponse = new RetrieveServiceAccountLimitResponse();
      retrieveServiceAccountLimitResponse.serviceAccountLimit(serviceAccountLimit);
      return retrieveServiceAccountLimitResponse;
    });
  }

  /**
   * update service account limit for service account by reference id
   *
   * @param refId         the reference id
   * @param authorization
   * @return ServiceAccountLimit the service account limit response
   */
  @PerfLogger
  public boolean updateServiceAccountLimits(String refId,
      UpdateServiceAccountLimitRequest updateServiceAccountLimitRequest,
      String authorization) {

    return processServiceAccountWithChecksLoadLimits(refId, sa -> {
      if (sa.getLimits().isEmpty()) {
        log.warn("ref id={} service account limit not found", refId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
      ServiceAccountLimitDto oldServiceAccountLimitDto = serviceAccountLimitsEntityMapper
          .serviceAccountLimitsToServiceAccountLimitDto(sa.getLimits().get(0));
      //update the limits
      serviceAccountLimitsEntityMapper
          .serviceAccountLimitToUpdatedServiceAccountLimits(updateServiceAccountLimitRequest.getServiceAccountLimit(),
              sa.getLimits().get(0));
      serviceAccountsRepository.save(sa);

      String eventScope = EVENT_TYPE_LIMIT_UPDATE;
      ServiceAccountLimitDto newServiceAccountLimitDto = serviceAccountLimitsEntityMapper
          .serviceAccountLimitsToServiceAccountLimitDto(sa.getLimits().get(0));

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, eventScope, oldServiceAccountLimitDto,
          newServiceAccountLimitDto, updateServiceAccountLimitRequest.getReasonDescription(),
          ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED, ServiceAccountEventReasonType.AGENT_INITIATED);
      serviceAccountsRepository.save(sa);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return true;
    });
  }

  /**
   * create service account limit for service account by reference id
   *
   * @param refId         the reference id
   * @param authorization
   * @return ServiceAccountLimit the service account limit response
   */
  @PerfLogger
  public CreateServiceAccountLimitResponse createServiceAccountLimits(String refId,
      CreateServiceAccountLimitRequest createServiceAccountLimitRequest,
      String authorization) {

    return processServiceAccountWithChecksLoadLimits(refId, sa -> {

      if (sa.getLimits() != null && !sa.getLimits().isEmpty()) {
        log.warn("Limit already exists {} for refId={}", createServiceAccountLimitRequest.getServiceAccountLimit(), refId);
        throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
      }

      ServiceAccountLimits sal = serviceAccountLimitsEntityMapper
          .serviceAccountLimitToServiceAccountLimits(createServiceAccountLimitRequest.getServiceAccountLimit());
      sal.setServiceAccounts(sa);

      //TODO: limits and service accounts are oneToOne relationship but lazy fetch doesn't apply for oneToOne relationship
      // therefore we sacrifice the oneToOne constraint and will need to fix it on entity level in the future
      //this line is to enforce only one limit entity is allowed
      sa.setLimits(new ArrayList<>(Collections.singletonList(sal)));

      sa = serviceAccountsRepository.save(sa);

      ServiceAccountLimitDto newServiceAccountLimitDto = serviceAccountLimitsEntityMapper
          .serviceAccountLimitsToServiceAccountLimitDto(sa.getLimits().get(0));
      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, EVENT_TYPE_LIMIT, null,
          newServiceAccountLimitDto, createServiceAccountLimitRequest.getReasonDescription(), ServiceAccountEventCategory.LIMIT,
          ServiceAccountEventType.ADDED, ServiceAccountEventReasonType.AGENT_INITIATED);

      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      ServiceAccountLimit serviceAccountLimit = serviceAccountLimitsEntityMapper
          .serviceAccountLimitsToServiceAccountLimit(sa.getLimits().get(0));

      CreateServiceAccountLimitResponse createServiceAccountLimitResponse = addServiceAccountLimitMapper
          .ServiceAccountLimitAndReasonToAddServiceAccountLimitResponse(serviceAccountLimit,
              createServiceAccountLimitRequest.getReasonDescription());

      return createServiceAccountLimitResponse;
    });
  }

  /**
   * create service account contact for service account by reference id
   *
   * @param request       CreateServiceAccountContactRequest
   * @param refId         the reference id
   * @param authorization auth token from cognito and aws gateway
   * @return serviceAccountContact the service account response
   */
  @PerfLogger
  public CreateServiceAccountContactResponse createServiceAccountContact(String refId, CreateServiceAccountContactRequest request,
      String authorization) {
    return processServiceAccountWithChecksLoadContacts(refId, sa -> {
      ServiceAccountsContact entity = serviceAccountContactEntityMapper
          .serviceAccountContactDomainToServiceAccountContactEntity(request);
      if (contactAlreadyExists(sa, entity)) {
        throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
      }
      entity.setServiceAccounts(sa);
      sa.getContacts().add(entity);
      serviceAccountsRepository.save(sa);
      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, "service-account:contact:add", null,
          new ServiceAccountContactDto(serviceAccountContactEntityMapper.serviceAccountContactEntityToServiceAccountContactJsonDto(entity)),
          null,
          ServiceAccountEventCategory.CONTACT, ServiceAccountEventType.ADDED,
          ServiceAccountEventReasonType.AGENT_INITIATED);
      serviceAccountEventsRepository.save(event);

      //refresh cache
      evictServiceAccountCaches();

      return serviceAccountContactEntityMapper
          .serviceAccountContactEntityToServiceAccountContactResponse(entity);
    });
  }


  /**
   * update service account contact for service account by reference id
   *
   * @param refId         the reference id
   * @param request
   * @param authorization
   * @return serviceAccountContact the service account response
   */
  @PerfLogger
  public boolean updateServiceAccountContact(String refId, UpdateServiceAccountContactRequest request,
      String authorization) {
    return processServiceAccountWithChecksLoadContacts(refId, sa -> {
      if (sa.getContacts().size() == 0) {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
      //for service account contact priority+type is unique
      ServiceAccountsContact entity = sa.getContacts().stream()
          .filter(c -> (request.getType().name().equals(c.getType()))
              && request.getPriority().name().equals(c.getPriority()))
          .findFirst()
          .orElseThrow(() -> new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name()));
      if (entity != null) {
        ServiceAccountContactDto beforeChanges = new ServiceAccountContactDto(
            serviceAccountContactEntityMapper.serviceAccountContactEntityToServiceAccountContactJsonDto(entity));
        serviceAccountContactEntityMapper.serviceAccountContactToUpdatedServiceAccountContacts(request, entity);
        ServiceAccountContactDto afterChanges = new ServiceAccountContactDto(
            serviceAccountContactEntityMapper.serviceAccountContactEntityToServiceAccountContactJsonDto(entity));
        serviceAccountsRepository.save(sa);
        ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, "service-account:contact:update", beforeChanges,
            afterChanges, null, ServiceAccountEventCategory.CONTACT, ServiceAccountEventType.UPDATED,
            ServiceAccountEventReasonType.AGENT_INITIATED);
        serviceAccountEventsRepository.save(event);

        //refresh cache
        evictServiceAccountCaches();

        return true;
      } else {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    });
  }

  @PerfLogger
  public RetrieveServiceAccountContactListResponse retrieveServiceAccountsContacts(String refId, String authorization) {
    RetrieveServiceAccountContactListResponse response = new RetrieveServiceAccountContactListResponse();
    response.setContacts(processServiceAccountWithChecksLoadContacts(refId, sa -> sa.getContacts().stream()
        .map(serviceAccountContactEntityMapper::serviceAccountContactEntityToServiceAccountContactDomain).collect(Collectors.toList())));
    return response;
  }

  @PerfLogger
  public RetrieveServiceAccountConfigurationsResponse retrieveServiceAccountConfigurations(String refId) {

    return retrieveCachedServiceAccountBy(refId, null, sa -> {

      if (sa.getConfigs().isEmpty()) {
        log.debug("refId = {} service account config not found", refId);
        return null;
      }

      List<ServiceAccountConfiguration> configs = serviceAccountConfigEntityMapper.
          serviceAccountConfigurationsEntityToServiceAccountConfigurationDomainLst(sa.getConfigs());

      RetrieveServiceAccountConfigurationsResponse response = new RetrieveServiceAccountConfigurationsResponse();
      response.setServiceAccountConfigurations(configs);

      return response;
    });
  }

  @PerfLogger
  public boolean validateServiceAccountFeature(String refId, String featureName) {

    return processServiceAccountWithChecksLoadFeaturesCache(refId, serviceAccounts -> {

      if (featureName == null) {
        return false;
      }

      ServiceAccountFeatures apiFeature =
          serviceAccounts.getFeatures() == null ? null : serviceAccounts.getFeatures().get(featureName);
      if (apiFeature != null) {
        return apiFeature.isActive();
      }
      log.debug("Haven't founded featureName {} at account {}", featureName, refId);
      List<Features> featuresList = featuresRepository.findAll();
      for (Features features : featuresList) {
        if (features.getFeature().equals(featureName)) {
          if (!features.isRestricted()) {
            return true;
          }
          break;
        }
      }
      log.debug("Feature {} at account {} is restricted", featureName, refId);
      return false;

    });
  }

  /**
   * Retrieve Service Account information by Inbound API Token
   *
   * @param inboundApiToken inbound API token
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  public ServiceAccountResponse retrieveServiceAccountByApiToken(String inboundApiToken) {
    ServiceAccountResponse response = getServiceAccountResponse(inboundApiToken, null);

    return response;
  }

  /**
   * Retrieve Service Account information by reference id
   *
   * @param refId the reference id
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  public com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse retrieveServiceAccountByRefId(String refId) {
    ServiceAccountResponse response = getServiceAccountResponse(null, refId);

    return response;
  }

  @SneakyThrows
  private ServiceAccountEvents buildServiceAccountEvent(String authorization, ServiceAccounts serviceAccounts, String eventScope,
      JsonDto oldObject, JsonDto newObject, String reasonDescription,
      ServiceAccountEventCategory profile,
      ServiceAccountEventType updated,
      ServiceAccountEventReasonType serviceAccountEventReasonType) {

    String updateBy;
    //Controller layer validates the authorization shouldn't be empty and user name shouldn't be empty
    //so extract userName from cognito token, we will use it
    if (!StringUtils.isEmpty(authorization)) {
      updateBy = JwtUtil.findUsernameFromToken(authorization);
    } else {
      //internal api pass null authorization token and the default updatedBy user is SYSTEM
      updateBy = USER_SYSTEM;
    }

    return serviceAccountEventEntityMapper
        .serviceAccountsToServiceAccountEvents(serviceAccounts, oldObject, newObject,
            reasonDescription, serviceAccountEventReasonType,
            profile, updated, updateBy, eventScope);
  }

  private <T> T processServiceAccountWithChecksLoadEvents(String refId, Function<ServiceAccounts, T> consumer) {
    return processServiceAccountWithChecks(refId, consumer, true, false, false, false);
  }

  private <T> T processServiceAccountWithChecksLoadContacts(String refId, Function<ServiceAccounts, T> consumer) {
    return processServiceAccountWithChecks(refId, consumer, false, true, false, false);
  }

  private <T> T processServiceAccountWithChecksLoadFeatures(String refId, Function<ServiceAccounts, T> consumer) {
    return processServiceAccountWithChecks(refId, consumer, false, false, true, false);
  }

  private <T> T processServiceAccountWithChecksLoadLimits(String refId, Function<ServiceAccounts, T> consumer) {
    return processServiceAccountWithChecks(refId, consumer, false, false, false, true);
  }

  private <T> T retrieveCachedServiceAccountBy(String serviceAccountRefId, String inboundApiToken, Function<ServiceAccounts, T> consumer) {

    try {

      Optional<ServiceAccounts> serviceAccountOption = Optional.empty();

      if (serviceAccountRefId != null) {
        serviceAccountOption = serviceAccountsRepository.internalFindByRefId(serviceAccountRefId);
      } else if (inboundApiToken != null) {
        serviceAccountOption = serviceAccountsRepository.internalFindByRefId(inboundApiToken);
      }

      if (serviceAccountOption.isPresent()) {
        return consumer.apply(serviceAccountOption.get());
      } else {
        log.warn("ref id={} not found", serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }

    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }

  private <T> T processServiceAccountWithChecks(String refId, Function<ServiceAccounts, T> consumer) {
    return processServiceAccountWithChecks(refId, consumer, false, false, true, false);
  }

  private <T> T processServiceAccountWithChecks(String refId, Function<ServiceAccounts, T> consumer, boolean initEvents,
      boolean initContacts, boolean initFeatures, boolean initLimits) {
    try {
      Optional<ServiceAccounts> serviceAccountOption = serviceAccountsRepository.findByRefId(refId);
      if (serviceAccountOption.isPresent()) {
        if (initEvents) {
          serviceAccountOption.get().getEvents();
        }
        if (initContacts) {
          serviceAccountOption.get().getContacts();
        }
        if (initFeatures) {
          serviceAccountOption.get().getFeatures();
        }
        if (initLimits) {
          serviceAccountOption.get().getLimits();
        }
        return consumer.apply(serviceAccountOption.get());
      } else {
        log.warn("ref id={} not found", refId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }

  private <T> T processServiceAccountWithChecksLoadFeaturesCache(String refId, Function<ServiceAccounts, T> consumer) {
    try {
      Optional<ServiceAccounts> serviceAccountOption = serviceAccountsRepository.internalFindByRefIdWithContacts(refId);
      if (serviceAccountOption.isPresent()) {
        return consumer.apply(serviceAccountOption.get());
      } else {
        log.warn("ref id={} not found", refId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }

  private boolean contactAlreadyExists(ServiceAccounts sa, ServiceAccountsContact entity) {
    return sa.getContacts().stream()
        .anyMatch(c -> c.getPriority().equals(entity.getPriority()) && c.getType().equals(entity.getType()));
  }

  /**
   * Retrieve Service Account information by Inbound API Token
   *
   * @param inboundApiToken inbound API token
   * @param refId           service account ref id
   * @return ServiceAccountResponse the service account response
   */
  @PerfLogger
  private ServiceAccountResponse getServiceAccountResponse(String inboundApiToken, String refId) {
    ServiceAccountResponse response = null;
    try {
      Optional<ServiceAccounts> serviceAccountOption = null;

      if (!Strings.isNullOrEmpty(inboundApiToken)) {
        serviceAccountOption = serviceAccountsRepository.internalFindByInboundApiTokenWithContacts(inboundApiToken);
      } else if (!Strings.isNullOrEmpty(refId)) {
        serviceAccountOption = serviceAccountsRepository.internalFindByRefIdWithContacts(refId);
      }

      if (serviceAccountOption.isPresent()) {
        response = retrieveServiceAccountMapper.serviceAccountsToServiceAccountResponse(serviceAccountOption.get());
      } else {
        String serviceAccount = inboundApiToken != null ? inboundApiToken : refId;
        log.warn("service account={} not found", serviceAccount);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return response;
  }

  @PerfLogger
  public void validateCreateServiceAccountRequest(CreateServiceAccountRequest request) {
    PayloadValidator.validateCreateServiceAccountRequest(request);
  }

  @PerfLogger
  public CreateServiceAccountResponse createServiceAccount(CreateServiceAccountRequest request) {
    try {
      final ServiceAccounts serviceAccounts = createServiceAccountMapper.сreateServiceAccountRequestToServiceAccounts(request);
      serviceAccountsRepository.findByRefId(serviceAccounts.getRefId()).ifPresent(s -> {
        throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name(), request.getRefId());
      });
      serviceAccounts.setFeatures(mapFeatures(request, serviceAccounts));
      serviceAccounts.setLimits(mapLimits(request, serviceAccounts));
      if (!CollectionUtils.isEmpty(serviceAccounts.getContacts())) {
        serviceAccounts.getContacts().forEach(c -> c.setServiceAccounts(serviceAccounts));
      }
      if (!CollectionUtils.isEmpty(serviceAccounts.getApiEndpointsMap())) {
        serviceAccounts.getApiEndpointsMap().values().forEach(e -> e.setServiceAccounts(serviceAccounts));
      }
      if (!CollectionUtils.isEmpty(serviceAccounts.getConfigs())) {
        serviceAccounts.getConfigs().values().forEach(e -> e.setServiceAccounts(serviceAccounts));
      }
      serviceAccounts.setInboundApiToken(
          RandomStringUtils.randomAlphanumeric(ObjectUtils.defaultIfNull(request.getInboundApiTokenLength(), DEFAULT_TOKEN_LENGTH).intValue()));
      serviceAccounts.setOutboundApiToken(
          RandomStringUtils.randomAlphanumeric(ObjectUtils.defaultIfNull(request.getOutboundApiTokenLength(), DEFAULT_TOKEN_LENGTH).intValue()));
      final CreateServiceAccountCredentialRequest credentialRequest = createServiceAccountMapper
          .createServiceAccountRequestToCreateServiceAccountCredentialRequest(
              request.getAuthentication());
      credentialRequest.setToken(serviceAccounts.getInboundApiToken());
      serviceAccountCredentialAdapter.createServiceAccountCredential(credentialRequest);
      serviceAccountsRepository.save(serviceAccounts);
      return createServiceAccountMapper.serviceAccountsToCreateServiceAccountResponse(serviceAccounts, request.getCrmRefId(),
          request.getAuthentication().getAuthMethods(),
          request.getAuthentication().getBasicAuthCredentials());
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (ValidationException e) {
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }

  private List<ServiceAccountLimits> mapLimits(CreateServiceAccountRequest request, ServiceAccounts serviceAccounts) {
    return Optional.ofNullable(request.getLimits()).map(l -> {
      final ServiceAccountLimits limits = serviceAccountLimitsEntityMapper.serviceAccountLimitToServiceAccountLimits(l);
      limits.setServiceAccounts(serviceAccounts);
      return Collections.singletonList(limits);
    }).orElse(null);
  }

  private Map<String, ServiceAccountFeatures> mapFeatures(CreateServiceAccountRequest request, ServiceAccounts serviceAccounts) {
    if (CollectionUtils.isEmpty(request.getFeatures())) {
      return null;
    }
    return request.getFeatures().stream().map(f -> serviceAccountFeaturesMapper
        .serviceAccountsToServiceAccountFeatures(serviceAccounts, f.getFeatureName(), f.getStatus() == StatusEnum.ENABLED))
        .collect(Collectors.toMap(ServiceAccountFeatures::getFeature, Function.identity()));
  }

  @PerfLogger
  public ServiceAccountCertificate createServiceAccountCertificate(String refId, CreateServiceAccountCertificateRequest request) {
    if (serviceAccountCertificatesRepository.findByKeyId(request.getKeyId()).isPresent()) {
      throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name(), request.getKeyId());
    }
    return processServiceAccountWithChecks(refId, sa -> {
      final ServiceAccountCertificates certificates = serviceAccountCertificateMapper
          .createServiceAccountCertificateRequestToServiceAccountCertificates(request);
      certificates.setServiceAccounts(sa);
      return serviceAccountCertificateMapper.serviceAccountCertificatesToServiceAccountCertificate(
          serviceAccountCertificatesRepository.save(certificates));
    });
  }

  @PerfLogger
  public RetrieveServiceAccountCertificatesResponse getAllServiceAccountCertificates(String refId) {
    return processServiceAccountWithChecks(refId, sa -> {
      final List<ServiceAccountCertificate> list = serviceAccountCertificatesRepository.findAllByServiceAccounts(sa).stream()
          .map(serviceAccountCertificateMapper::serviceAccountCertificatesToServiceAccountCertificate).collect(Collectors.toList());
      RetrieveServiceAccountCertificatesResponse res = new RetrieveServiceAccountCertificatesResponse();
      res.setCertificates(list);
      return res;
    });
  }

  @PerfLogger
  public RetrieveServiceAccountCertificateByRefResponse getCertificateByKey(String apiToken, String keyId) {
    return serviceAccountCertificatesRepository.findByApiTokenAndKeyId(apiToken, keyId)
        .map(serviceAccountCertificateMapper::serviceAccountCertificatesToRetrieveServiceAccountCertificateByRefResponse)
        .orElseThrow(() -> new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name()));
  }

  private void changeServiceAccountStatusAndRefreshCache(ServiceAccounts serviceAccounts, String newStatus, String reasonCode) {
    //change service account status
    serviceAccounts.setStatus(newStatus);
    serviceAccounts.setStatusUpdatedBy(USER_SYSTEM);
    serviceAccounts.setStatusReason(reasonCode);
    serviceAccountsRepository.save(serviceAccounts);

    //refresh cache
    evictServiceAccountCaches();
  }

  @Transactional
  public void updateServiceAccountConfiguration(String refId, UpdateServiceAccountConfigurationsRequest request, String authorization) {

    retrieveCachedServiceAccountBy(refId, null, sa -> {

      Map<String, ServiceAccountConfigurations> configs = serviceAccountConfigEntityMapper.
          serviceAccountConfigurationsDomainToServiceAccountConfigurationEntityList(request.getConfigurations(), sa.getConfigs() == null ?
              Maps.newHashMap() : sa.getConfigs());
      ServiceAccountDto oldServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);

      sa.setConfigs(configs);
      ServiceAccountDto newServiceAccountDto = createServiceAccountEventMapper.serviceAccountsToServiceAccountDto(sa);

      ServiceAccountEvents event = buildServiceAccountEvent(authorization, sa, EVENT_TYPE_ACCOUNT_CONFIGURATION_UPDATED, oldServiceAccountDto,
          newServiceAccountDto, request.getReasonDescription(),
          ServiceAccountEventCategory.PROFILE, ServiceAccountEventType.UPDATED,
          ServiceAccountEventReasonType.CUSTOMER_INITIATED);
      sa.setStatusUpdatedBy(event.getUpdatedBy());
      sa.setStatusReason(request.getReasonDescription());
      if (!CollectionUtils.isEmpty(sa.getConfigs())) {
        sa.getConfigs().values().forEach(e -> e.setServiceAccounts(sa));
      }
      serviceAccountConfigurationsRepository.saveAll(sa.getConfigs().values());
      serviceAccountEventsRepository.save(event);
      evictServiceAccountCaches();
      return null;
    });

  }

}
