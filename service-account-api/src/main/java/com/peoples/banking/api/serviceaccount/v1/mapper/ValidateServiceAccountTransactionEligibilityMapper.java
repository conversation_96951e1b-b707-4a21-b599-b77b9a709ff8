package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountTransactions;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {DateConverter.class}, imports = {DateUtil.class})
public abstract class ValidateServiceAccountTransactionEligibilityMapper {

  @Mapping(source = "refId", target = "serviceAccountRefId")
  @Mapping(source = "serviceAccountTransaction.paymentRefId", target = "externalPaymentRefId")
  @Mapping(source = "serviceAccountTransaction.amount", target = "amount")
  @Mapping(source = "serviceAccountTransaction.transactionDate", target = "transactionDate", qualifiedByName = {"DateConverter", "toLocalDateTime"})
  @Mapping(expression = "java(DateUtil.getLocalUtcTime())", target = "createdOn")
  public abstract ServiceAccountTransactions serviceAccountTransactionToServiceAccountTransactions(String refId, ServiceAccountTransaction serviceAccountTransaction);
}
