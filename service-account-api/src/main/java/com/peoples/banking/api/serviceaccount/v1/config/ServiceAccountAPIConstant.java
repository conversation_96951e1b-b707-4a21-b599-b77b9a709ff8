package com.peoples.banking.api.serviceaccount.v1.config;

public class ServiceAccountAPIConstant {

  public static final String AUTHORIZATION_HEADER = "Authorization";
  public static final String ROOT_SERVICE_URL = "/v1/service-account";
  private static final String ROOT_INTERNAL_SERVICE_URL = "/v1/internal/service-account";
  private static final String CONFIG = "/config";
  public static final String REF_ID = "refId";
  public static final String CERTIFICATE_KEY = "certificateKeyId";
  public static final String API_TOKEN = "apiToken";
  private static final String TOKEN = "/token";
  private static final String EVENTS = "/events";
  private static final String LIMIT = "/limit";
  private static final String CONTACT = "/contact";
  private static final String ENABLE = "/enable";
  private static final String DISABLE = "/disable";
  private static final String SUSPEND = "/suspend";
  public static final String FEATURE_NAME = "featureName";
  public static final String FEATURE = "/feature";
  private static final String TRANSACTION_ELIGIBILITY = "/transaction-eligibility";
  private static final String CERTIFICATE = "/certificate";
  private static final String CONFIGURATION = "/configuration";

  public static final String PATH_CATEGORY = "category";

  public static final String SERVICE_ACCOUNT_CREATE = ROOT_SERVICE_URL;
  public static final String SERVICE_ACCOUNT_BY_REF_ID = ROOT_SERVICE_URL + "/{" + REF_ID + "}";
  public static final String SERVICE_ACCOUNT_EVENTS = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + EVENTS;
  public static final String SERVICE_ACCOUNT_LIMIT = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + LIMIT;
  public static final String SERVICE_ACCOUNT_CONTACT = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + CONTACT;
  public static final String ENABLE_SERVICE_ACCOUNT = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + ENABLE;
  public static final String DISABLE_SERVICE_ACCOUNT = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + DISABLE;
  public static final String SUSPEND_SERVICE_ACCOUNT = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + SUSPEND;
  public static final String UPDATE_SERVICE_ACCOUNT_CONFIGURATION = ROOT_SERVICE_URL + "/{" + REF_ID + "}" + CONFIGURATION;

  public static final String SERVICE_ACCOUNTS_LIST = ROOT_SERVICE_URL;

  public static final String SERVICE_ACCOUNT_FEATURE_BY_REF_ID = SERVICE_ACCOUNT_BY_REF_ID + FEATURE;
  public static final String ENABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID =
      SERVICE_ACCOUNT_FEATURE_BY_REF_ID + "/{" + FEATURE_NAME + "}" + ENABLE;
  public static final String DISABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID =
      SERVICE_ACCOUNT_FEATURE_BY_REF_ID + "/{" + FEATURE_NAME + "}" + DISABLE;
  public static final String UPDATE_SERVICE_ACCOUNT_FEATURES_BY_REF_ID =
      SERVICE_ACCOUNT_FEATURE_BY_REF_ID + "/status";

  public static final String INTERNAL_SERVICE_ACCOUNT_CONFIG = ROOT_INTERNAL_SERVICE_URL + "/{" + REF_ID + "}" + CONFIG;

  //internal services
  public static final String INTERNAL_SERVICE_ACCOUNT_VALIDATE_FEATURE =
      ROOT_INTERNAL_SERVICE_URL + "/{" + REF_ID + "}" + FEATURE + "/{" + FEATURE_NAME + "}";
  public static final String INTERNAL_SERVICE_ACCOUNT_BY_REF_ID = ROOT_INTERNAL_SERVICE_URL + "/{" + REF_ID + "}";
  public static final String INTERNAL_SERVICE_ACCOUNT_BY_API_TOKEN = ROOT_INTERNAL_SERVICE_URL + TOKEN + "/{" + API_TOKEN + "}";
  public static final String INTERNAL_SERVICE_ACCOUNT_TRANSACTION_ELIGIBILITY =
      ROOT_INTERNAL_SERVICE_URL + "/{" + REF_ID + "}" + TRANSACTION_ELIGIBILITY;
  public static final String INTERNAL_SERVICE_ACCOUNT_CERTIFICATE = ROOT_INTERNAL_SERVICE_URL + "/{" + REF_ID + "}" + CERTIFICATE;
  public static final String INTERNAL_SERVICE_ACCOUNT_CERTIFICATE_BY_KEY =
      ROOT_INTERNAL_SERVICE_URL + "/{" + API_TOKEN + "}" + CERTIFICATE + "/{" + CERTIFICATE_KEY + "}";

  //JSON content type
  public static final String DEFAULT_CONTENT_TYPE = "application/json";

  public static final String SERVICE_ACCOUNT_API_SYSTEM_NAME = "ServiceAccountAPI";

  public static final String BEARER_TOKEN_HEADER = "Bearer ";

  public static final String COGNITO_EMAIL_FIELD = "email";

  public static final String COGNITO_USERNAME_FIELD = "username";

  public static final String USER_SYSTEM = "SYSTEM";

  public static final String ETRANSFER_ADMINISTRATOR = "ETRANSFER_ADMINISTRATOR";
  public static final String ETRANSFER_RISK = "ETRANSFER_RISK";
  public static final String ETRANSFER_VIEW = "ETRANSFER_VIEW";

  /**
   * Environment variable to disable cognito JWT token validation.
   */
  public static final String ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION = "DISABLE_COGNITO_JWT_VERIFICATION";
}
