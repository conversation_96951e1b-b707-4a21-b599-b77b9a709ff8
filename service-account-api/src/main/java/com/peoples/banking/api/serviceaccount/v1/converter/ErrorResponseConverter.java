package com.peoples.banking.api.serviceaccount.v1.converter;

import com.peoples.banking.domain.serviceaccount.model.ErrorResponse;
import com.peoples.banking.domain.serviceaccount.model.Error;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * A helper class to convert the  error details into ErrorResponse
 */
@Component
public class ErrorResponseConverter {
  
  public Error buildError(String errorCode, String additionalInformation) {
    Error error = new Error();

    error.setCode(errorCode);
    error.setAdditionalInformation(additionalInformation);

    return error;
  }
  
  public ErrorResponse buildErrorResponse(List<Error> errorList) {
    ErrorResponse errorResponse = new ErrorResponse();
    errorResponse.setError(errorList);

    return errorResponse;
  }

}
