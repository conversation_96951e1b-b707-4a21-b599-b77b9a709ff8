package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.api.serviceaccount.v1.dto.ServiceAccountContactJsonDto;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactResponse;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountsContact;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * The mapper class to map objects between ServiceAccountsContact and ServiceAccountContact
 */
@Mapper(componentModel = "spring", imports = {DateUtil.class}, uses = {DateConverter.class})
@Named("ServiceAccountContactEntityMapper")
public abstract class ServiceAccountContactEntityMapper {

  public abstract CreateServiceAccountContactResponse serviceAccountContactEntityToServiceAccountContactResponse(
      ServiceAccountsContact serviceAccount);

  public abstract ServiceAccountContact serviceAccountContactEntityToServiceAccountContactDomain(ServiceAccountsContact serviceAccount);

  @Mapping(target = "createdOn", expression = "java(DateUtil.getLocalUtcTime())")
  public abstract ServiceAccountsContact serviceAccountContactDomainToServiceAccountContactEntity(ServiceAccountContact serviceAccount);

  public abstract ServiceAccountContactJsonDto serviceAccountContactEntityToServiceAccountContactJsonDto(
      ServiceAccountsContact serviceAccount);

  @Named("serviceAccountContactListToServiceAccountContactDomainList")
  public List<ServiceAccountContact> serviceAccountLimitsListToServiceAccountLimit(List<ServiceAccountsContact> accounts){
    return CollectionUtils.isEmpty(accounts) ? null : accounts.stream().map(this::serviceAccountContactEntityToServiceAccountContactDomain).collect(Collectors.toList());
  }

  @Named("serviceAccountContactDomainListToServiceAccountContactList")
  public List<ServiceAccountsContact> serviceAccountContactDomainListToServiceAccountContactList(List<ServiceAccountContact> accounts){
    return CollectionUtils.isEmpty(accounts) ? null : accounts.stream().map(this::serviceAccountContactDomainToServiceAccountContactEntity).collect(Collectors.toList());
  }

  public ServiceAccountsContact serviceAccountContactToUpdatedServiceAccountContacts(ServiceAccountContact dto,
      ServiceAccountsContact entity) {
    if (dto == null || entity == null) {
      return null;
    }
    entity.setEmail(dto.getEmail());
    entity.setPhone(dto.getPhone());
    entity.setFirstName(dto.getFirstName());
    entity.setLastName(dto.getLastName());
    entity.setUpdatedOn(DateUtil.getLocalUtcTime());
    return entity;
  }
}
