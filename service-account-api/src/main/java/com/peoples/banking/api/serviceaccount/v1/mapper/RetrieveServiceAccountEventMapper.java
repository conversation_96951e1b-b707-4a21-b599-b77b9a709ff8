package com.peoples.banking.api.serviceaccount.v1.mapper;

import com.peoples.banking.domain.serviceaccount.model.ServiceAccountEvent;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * The mapper class to map objects between Service Accounts entity and Service Account Response domain
 */
@Mapper(componentModel = "spring", uses = {DateConverter.class})
public abstract class RetrieveServiceAccountEventMapper {

  /**
   * Map ServiceAccountEvents entity to ServiceAccountEvent
   *
   * @param serviceAccountEvents the service account event entity
   * @return ServiceAccountEvent the service account event response domain
   */
  @Mapping(source = "event", target = "event")
  @Mapping(source = "createdOn", target = "timestamp", qualifiedByName = {"DateConverter", "toOffsetDateTime"})
  public abstract ServiceAccountEvent serviceAccountEventsToServiceAccountEvent(ServiceAccountEvents serviceAccountEvents);
}
