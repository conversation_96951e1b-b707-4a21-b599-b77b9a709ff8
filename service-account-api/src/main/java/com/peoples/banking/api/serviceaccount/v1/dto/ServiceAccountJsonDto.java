package com.peoples.banking.api.serviceaccount.v1.dto;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ServiceAccountJsonDto {

  private int id;
  private String refId;
  private String inboundApiToken;
  private String outboundApiToken;
  private String outboundApiAuth;
  private String outboundApiAuthToken;
  private String name;
  private String settlementAccountNum;
  private String accountNumRange;
  private String limitGroupId;
  private LocalDateTime createdOn;
  private LocalDateTime updatedOn;
  private ApiEndpointsDto apiEndpointsDto;
  private String status;

}
