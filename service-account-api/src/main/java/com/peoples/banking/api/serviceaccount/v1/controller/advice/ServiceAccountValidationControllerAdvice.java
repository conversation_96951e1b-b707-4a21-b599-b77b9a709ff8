package com.peoples.banking.api.serviceaccount.v1.controller.advice;

import com.peoples.banking.api.serviceaccount.v1.converter.ErrorResponseConverter;
import com.peoples.banking.domain.serviceaccount.model.Error;
import com.peoples.banking.domain.serviceaccount.model.ErrorResponse;
import com.peoples.banking.util.api.common.HttpHeadersUtil;
import com.peoples.banking.util.api.common.controller.advice.APIValidationControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import java.util.Collections;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.ControllerAdvice;

@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ServiceAccountValidationControllerAdvice extends APIValidationControllerAdvice {

  @Autowired
  private ErrorResponseConverter errorConverter;

  /**
   * construct request payment error response from validation error;
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(ErrorEntity validationError) {
    ErrorResponse errorResponse = convertValidationToErrorResponse(validationError);

    ErrorResponseEntity<ErrorResponse> errorResponseEntity =
        new ErrorResponseEntity<>(errorResponse);
    return errorResponseEntity;
  }

  /**
   * construct request payment error response from multiple validation errors;
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(
      List<ErrorEntity> validationErrorList) {
    //no applicable for service Account api
    return null;
  }

  /**
   * to convert validation error to error response
   *
   * @param validationError
   * @return ErrorResponse the error response
   */
  private ErrorResponse convertValidationToErrorResponse(ErrorEntity validationError) {
    Error error = convertValidationErrorToError(validationError);

    ErrorResponse errorResponse = errorConverter.buildErrorResponse(Collections.singletonList(error));

    return errorResponse;

  }


  /**
   * convert validation error to error
   *
   * @param validationError the validation error
   * @return Error the error detail
   */
  private Error convertValidationErrorToError(ErrorEntity validationError) {
    Error error = errorConverter.buildError(validationError.getErrorCode(),
        validationError.getAdditionalInformation());

    return error;

  }

  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return HttpHeadersUtil.buildResponseHttpHeaders(request);
  }

}
