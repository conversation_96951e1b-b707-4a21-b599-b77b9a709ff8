package com.peoples.banking.api.serviceaccount.v1.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.api.serviceaccount.v1.dto.LimitBreachKafkaMessage;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableKafka
@Log4j2
public class KafkaConfig {
  private static final String SSL = "SSL";

  @Autowired
  private ServiceAccountProperty serviceAccountProperty;
  @Autowired
  private ObjectMapper objectMapper;

  @Bean
  public ProducerFactory<String, LimitBreachKafkaMessage> producerFactory() {
    Map<String, Object> configProps = createGenericConfigProps();

    //do not pass headers in order to be able to deserialize the message successfully into different class at the consumer side
    configProps.put(JsonSerializer.ADD_TYPE_INFO_HEADERS, false);
    return new DefaultKafkaProducerFactory<>(configProps, new StringSerializer(), new JsonSerializer<>(objectMapper));
  }

  @Bean
  public KafkaAdmin kafkaAdminClient() {
    return new KafkaAdmin(createGenericConfigProps());
  }

  @Bean
  public KafkaTemplate<String, LimitBreachKafkaMessage> kafkaTemplate() {
    return new KafkaTemplate<>(producerFactory());
  }

  @Bean
  public NewTopic limitBreachTopic() {
    //TODO debug line, we will remove it later
    log.info("create kafka topic:{}", serviceAccountProperty.getKafkaTopic());
    return TopicBuilder.name(serviceAccountProperty.getKafkaTopic()).build();
  }

  private Map<String, Object> createGenericConfigProps() {
    Map<String, Object> configProps = new HashMap<>();
    configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, serviceAccountProperty.getKafkaBootstrapServers());
    if (serviceAccountProperty.isSslEnabled()) {
      configProps.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, SSL);
      configProps.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, serviceAccountProperty.getSslTruststoreLocation());
      configProps.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, serviceAccountProperty.getSslTruststorePassword());
      configProps.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, serviceAccountProperty.getSslKeyPassword());
      configProps.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, serviceAccountProperty.getSslKeystoreLocation());
      configProps.put(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG, serviceAccountProperty.getSslKeystorePassword());
    }
    return configProps;
  }
}
