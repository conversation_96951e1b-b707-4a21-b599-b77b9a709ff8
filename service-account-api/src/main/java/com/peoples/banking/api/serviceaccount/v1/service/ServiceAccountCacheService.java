package com.peoples.banking.api.serviceaccount.v1.service;

import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class ServiceAccountCacheService {

  @Scheduled(cron = "${service.account.cache.refresh.schedule}")
  @CacheEvict(value = "ServiceAccountByInboundAPIToken", allEntries = true, cacheManager = "serviceAccountCacheManager")
  public void evictServiceAccountByApiTokenCacheValues() {
    log.info("ServiceAccountCacheService::ServiceAccountByInboundAPIToken cache refreshed\n");
  }

  @Scheduled(cron = "${service.account.cache.refresh.schedule}")
  @CacheEvict(value = "ServiceAccountByRefId", allEntries = true, cacheManager = "serviceAccountCacheManager")
  public void evictServiceAccountByRefIdCacheValues() {
    log.info("ServiceAccountCacheService::ServiceAccountByRefId cache refreshed\n");
  }

  @Scheduled(cron = "${service.account.feature.catch.refresh.schedule}")
  @CacheEvict(value = "FeaturesAll", allEntries = true, cacheManager = "serviceAccountCacheManager")
  public void evictFeaturesAllCacheValues() {
    log.info("ServiceAccountCacheService::FeaturesAll cache refreshed\n");
  }

  @Scheduled(cron = "${service.account.feature.catch.refresh.schedule}")
  @CacheEvict(value = "ServiceAccountInternalByRefId", allEntries = true, cacheManager = "serviceAccountCacheManager")
  public void evictServiceAccountInternalByRefIdAllCacheValues() {
    log.info("ServiceAccountCacheService::ServiceAccountInternalByRefId cache refreshed\n");
  }

}