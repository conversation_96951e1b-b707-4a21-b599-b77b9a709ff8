package com.peoples.banking.api.serviceaccount.v1.mapper;


import com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public abstract class ServiceAccountConfigEntityMapper {

  @Mapping(source = "key", target = "key")
  @Mapping(source = "value", target = "value")
  public abstract ServiceAccountConfiguration serviceAccountConfigurationsEntityToServiceAccountConfigurationDomain(
      ServiceAccountConfigurations serviceAccount);

  public abstract ServiceAccountConfigurations serviceAccountConfigurationDomainToServiceAccountConfigurationsEntity(
      ServiceAccountConfiguration serviceAccount);

  public List<ServiceAccountConfiguration> serviceAccountConfigurationsEntityToServiceAccountConfigurationDomainLst(
      Map<String, ServiceAccountConfigurations> serviceAccountConfigurationsMap) {
    List<ServiceAccountConfiguration> serviceAccountConfigurations = new ArrayList<>();

    if (serviceAccountConfigurationsMap != null) {
      for (Map.Entry<String, ServiceAccountConfigurations> entry : serviceAccountConfigurationsMap.entrySet()) {
        ServiceAccountConfiguration accountConfiguration = this.serviceAccountConfigurationsEntityToServiceAccountConfigurationDomain(
            entry.getValue());
        serviceAccountConfigurations.add(accountConfiguration);
      }
    }
    return serviceAccountConfigurations;
  }

  public Map<String, ServiceAccountConfigurations> serviceAccountConfigurationsDomainToServiceAccountConfigurationEntityList(
      List<ServiceAccountConfiguration> serviceAccountConfigurations, Map<String, ServiceAccountConfigurations> serviceAccountConfigurationsMap) {
    Map<String, ServiceAccountConfigurations> result = new HashMap<>();

    if (serviceAccountConfigurations != null) {
      for (ServiceAccountConfiguration configuration : serviceAccountConfigurations) {
        ServiceAccountConfigurations accountConfiguration;
        if (serviceAccountConfigurationsMap.containsKey(configuration.getKey())) {
          accountConfiguration = serviceAccountConfigurationsMap.get(configuration.getKey());
          accountConfiguration.setValue(configuration.getValue());
        } else {
          accountConfiguration = serviceAccountConfigurationDomainToServiceAccountConfigurationsEntity(configuration);
        }
        accountConfiguration.setCreatedOn(LocalDateTime.now());
        accountConfiguration.setUpdatedOn(LocalDateTime.now());

        result.put(accountConfiguration.getKey(), accountConfiguration);
      }
    }
    return result;
  }
}
