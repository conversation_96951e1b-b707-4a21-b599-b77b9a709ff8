package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountLimitResponse;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.UUID;
import javax.transaction.Transactional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class CreateServiceAccountLimitControllerTest extends AbstractControllerTest {

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  @Autowired
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }


  @AfterEach
  @Transactional
  public void tearDown() {
    if (serviceAccounts != null) {
      serviceAccountEventsRepository.deleteByServiceAccounts(serviceAccounts);
      serviceAccountsRepository.delete(serviceAccounts);
    }
  }

  /**
   * The test use is to create service account limit
   */
  @Test
  @Transactional
  public void createLimit_success() throws Exception {
    // create a service account without limit
    String refId = RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountLimitRequest createServiceAccountLimitRequest = new CreateServiceAccountLimitRequest();
    createServiceAccountLimitRequest.setServiceAccountLimit(ServiceAccountTestUtil.createServiceAccountLimitRequest());
    createServiceAccountLimitRequest.setReasonDescription("unit test");

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders
        .post(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(serviceAccounts.getRefId()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(createServiceAccountLimitRequest))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated()).andReturn();

    CreateServiceAccountLimitResponse createServiceAccountLimitResponse = JsonUtil
        .toObject(mvcResult.getResponse().getContentAsString(), CreateServiceAccountLimitResponse.class);

    assertNotNull(createServiceAccountLimitResponse.getServiceAccountLimit().getNotificationThresholdAmount());
    assertNotNull(createServiceAccountLimitResponse.getServiceAccountLimit().getReserveAmount());
    assertNotNull(createServiceAccountLimitResponse.getServiceAccountLimit().getSuspendThresholdAmount());
    assertNotNull(createServiceAccountLimitResponse.getReasonDescription());
  }

  /**
   * The test create service account limit throw exception resource not found due to missing account
   */
  @Test
  public void createLimit_refIdNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders.post(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createServiceAccountLimitRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

  /**
   * The test retrieve service account limit throw exception resource not found due to missing account
   */
  @Test
  public void createLimit_limitExists() throws Exception {
    // create a service account with limit
    String refId= RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken= RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders
        .post(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(refId))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createServiceAccountLimitRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
  }

}
