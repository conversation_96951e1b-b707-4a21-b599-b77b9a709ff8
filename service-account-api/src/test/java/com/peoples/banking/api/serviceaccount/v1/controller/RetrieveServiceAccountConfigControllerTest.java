package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.UUID;
import javax.transaction.Transactional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class RetrieveServiceAccountConfigControllerTest {

  @Autowired
  private MockMvc mvc;

  @MockBean
  private KafkaAdmin kafkaAdmin;
  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  private ServiceAccounts serviceAccounts;

  @AfterEach
  @Transactional
  public void tearDown() {
    if (serviceAccounts != null) {
      serviceAccountsRepository.delete(serviceAccounts);
    }
  }

  /**
   * The test use is to retrieve service account config
   */
  @Test
  public void retrieveConfig_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithConfigs(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders
        .get(ServiceAccountTestUtil.buildServiceAccountConfigApiUrlByRefId(serviceAccounts.getRefId()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk()).andReturn();

  }

  /**
   * The test retrieve service account config throw account resource not found
   */
  @Test
  public void retrieveConfig_byRefIdByKey_NotFound() throws Exception {
    mvc.perform(MockMvcRequestBuilders.get(ServiceAccountTestUtil.buildServiceAccountConfigApiUrlByRefId(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

  /**
   * The test retrieve service account with no config get 204
   */
  @Test
  public void retrieveConfig_byRefId_noConfigValue_getNotContent() throws Exception {
    //first we create a test service account in DB with new config values
    String refId = RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithNoConfigs(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders
        .get(ServiceAccountTestUtil.buildServiceAccountConfigApiUrlByRefId(refId))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent()).andReturn();
  }

  /**
   * The test retrieve service account config throw config resource not found
   */
  @Test
  public void retrieveConfig_configNotFound() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithConfigs(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders
        .get(ServiceAccountTestUtil.buildServiceAccountConfigApiUrlByRefId(serviceAccounts.getRefId() + "111"))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

}

