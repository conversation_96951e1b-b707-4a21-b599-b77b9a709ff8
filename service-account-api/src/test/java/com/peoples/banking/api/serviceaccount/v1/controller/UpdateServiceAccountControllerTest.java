package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountConfigurationsRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountProfileRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class UpdateServiceAccountControllerTest extends AbstractControllerTest {

  public static final String REFERENCE_ID = "100011";
  public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

  @MockBean
  private ServiceAccountsRepository serviceAccountsRepository;

  @MockBean
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  /**
   * The test use is to update service account contact
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "Hello World",
      "Hello World Jr"
  })
  public void updateServiceAccount_goodName_success(String serviceAccountName) throws Exception {

    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = ServiceAccountTestUtil
        .createUpdateServiceAccountProfileRequest(REFERENCE_ID);

    updateServiceAccountProfileRequest.setName(serviceAccountName);

    ServiceAccountEvents serviceAccountEvents = new ServiceAccountEvents();

    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));
    doReturn(serviceAccountEvents).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountApiUrl(REFERENCE_ID))
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(updateServiceAccountProfileRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent()).andReturn();
  }

  /**
   * The test use is to update service account contact with different limit group id
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "LONGLIMITGROUPIDLENGTH0026",
      "1234"
  })
  public void updateServiceAccount_goodLevelId_success(String limitGroupId) throws Exception {

    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = ServiceAccountTestUtil
        .createUpdateServiceAccountProfileRequest(REFERENCE_ID);

    updateServiceAccountProfileRequest.setLimitGroupId(limitGroupId);
    ServiceAccountEvents serviceAccountEvents = new ServiceAccountEvents();

    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));
    doReturn(serviceAccountEvents).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountApiUrl(REFERENCE_ID))
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(updateServiceAccountProfileRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent()).andReturn();
  }

  /**
   * The test use is to update service account contact
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "Hello World ",
      "Hello@ World Jr"
  })
  public void updateServiceAccount_badName_failed(String serviceAccountName) throws Exception {

    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = ServiceAccountTestUtil
        .createUpdateServiceAccountProfileRequest(REFERENCE_ID);

    updateServiceAccountProfileRequest.setName(serviceAccountName);

    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountApiUrl(REFERENCE_ID))
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(updateServiceAccountProfileRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }


  /**
   * The test use is to update service account configuration
   */
  @Test
  public void updateServiceAccountConfiguration_duplicatekey_failed() throws Exception {
    UpdateServiceAccountConfigurationsRequest request = new UpdateServiceAccountConfigurationsRequest();
    request.configurations(List.of(ServiceAccountTestUtil.createServiceAccountConfiguration(),
            ServiceAccountTestUtil.createServiceAccountConfiguration()));

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountConfigurationApiUrl(REFERENCE_ID))
                    .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
                    .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
                    .content(JsonUtil.toString(request))
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest())
            .andReturn();
  }

  /**
   * The test use is to update service account configuration
   */
  @Test
  public void updateServiceAccountConfiguration_emptyInteractionId_failed() throws Exception {

    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = ServiceAccountTestUtil
        .createUpdateServiceAccountProfileRequest(REFERENCE_ID);

    UpdateServiceAccountConfigurationsRequest request = new UpdateServiceAccountConfigurationsRequest();
    request.configurations(List.of(ServiceAccountTestUtil.createServiceAccountConfiguration()));

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountConfigurationApiUrl(REFERENCE_ID))
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .content(JsonUtil.toString(updateServiceAccountProfileRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

}
