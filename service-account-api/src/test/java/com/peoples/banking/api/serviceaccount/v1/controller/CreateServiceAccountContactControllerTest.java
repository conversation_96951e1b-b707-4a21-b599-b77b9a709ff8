package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountContactResponse;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class CreateServiceAccountContactControllerTest extends AbstractControllerTest {

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  @Autowired
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  /**
   * The test use is to create service account contact
   */
  @Test
  public void createContact_success() throws Exception {
    String refId= RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken= RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        refId, inboundApiToken);

    serviceAccountsRepository.save(serviceAccounts);

    // create a service account with contact
    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders
        .post(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(refId))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createAddServiceAccountContactRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated()).andReturn();

    CreateServiceAccountContactResponse addServiceAccountLimitResponse = JsonUtil
        .toObject(mvcResult.getResponse().getContentAsString(), CreateServiceAccountContactResponse.class);
  }

  /**
   * The test create service account contact throw exception resource not found due to missing account
   */
  @Test
  public void createContact_refIdNotFound() throws Exception {
    //give a random fake RefID to trigger error
    mvc.perform(MockMvcRequestBuilders.post(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createAddServiceAccountContactRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }
}
