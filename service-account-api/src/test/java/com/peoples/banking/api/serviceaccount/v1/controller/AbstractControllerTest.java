package com.peoples.banking.api.serviceaccount.v1.controller;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class AbstractControllerTest {

  @MockBean
  protected KafkaAdmin kafkaAdmin;

  @Autowired
  protected MockMvc mvc;

  static {
    System.setProperty("SPRING_CONFIG_LOCATION", "classpath:/");
  }
}
