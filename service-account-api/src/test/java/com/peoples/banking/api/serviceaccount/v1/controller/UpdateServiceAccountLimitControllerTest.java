package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class UpdateServiceAccountLimitControllerTest extends AbstractControllerTest {

  public static final String REFERENCE_ID = "100011";
  public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

  @MockBean
  private ServiceAccountsRepository serviceAccountsRepository;

  @MockBean
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  /**
   * The test use is to update service account limit
   */
  @Test
  public void updateLimit_success() throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    ServiceAccountEvents event = new ServiceAccountEvents();
    doReturn(event).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    mvc.perform(MockMvcRequestBuilders
        .put(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_WITH_LIMIT))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createUpdateServiceAccountLimitRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent()).andReturn();
  }

  /**
   * The test update service account limit throw exception resource not found due to missing account
   */
  @Test
  public void updateLimit_refIdNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createServiceAccountLimitRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

  /**
   * The test retrieve service account limit throw exception resource not found due to missing account
   */
  @Test
  public void updateLimit_limitNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders
        .put(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil.createServiceAccountLimitRequest()))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

}
