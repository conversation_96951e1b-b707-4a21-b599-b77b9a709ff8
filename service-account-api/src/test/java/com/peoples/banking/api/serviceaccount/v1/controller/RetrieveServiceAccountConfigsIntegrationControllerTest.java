package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountProfileRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaAdmin;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RetrieveServiceAccountConfigsIntegrationControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  public static final String REFERENCE_ID = "100011";

  private UpdateServiceAccountProfileRequest request;

  ServiceAccounts serviceAccounts;

  @MockBean
  private KafkaAdmin kafkaAdmin;

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  @Autowired
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    request = ServiceAccountTestUtil.createUpdateServiceAccountProfileRequest(REFERENCE_ID);
  }

  @AfterEach
  public void tearDown() {
    if (serviceAccounts != null) {
      Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(serviceAccounts.getRefId());
      del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));
      serviceAccounts = null;
    }
    template = null;
    base = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  @Test
  public void retrieveServiceAccountConfigs_success() throws Exception {
    //create and save a service DB inside DB
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithConfigs(
        refId, inboundApiToken);
    serviceAccountsRepository.save(serviceAccounts);

    String finalUrl = base + ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CONFIG;

    HttpHeaders headers = getHttpHeader();

    ResponseEntity responseEntity = template.exchange(finalUrl.replace("{refId}", refId),
        HttpMethod.GET, new HttpEntity<>(headers),
        RetrieveServiceAccountConfigurationsResponse.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());

    //clean db for new create service account
    Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(request.getRefId());
    del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));
  }
}

