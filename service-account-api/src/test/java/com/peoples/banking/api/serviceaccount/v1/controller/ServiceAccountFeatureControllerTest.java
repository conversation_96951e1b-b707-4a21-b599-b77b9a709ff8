package com.peoples.banking.api.serviceaccount.v1.controller;

import static com.peoples.banking.util.api.common.type.FeatureType.DECLINE_PAYMENT;
import static com.peoples.banking.util.api.common.type.FeatureType.INITIATE_SEND_PAYMENT;
import static com.peoples.banking.util.api.common.type.FeatureType.SUBMIT_SEND_PAYMENT;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature;
import com.peoples.banking.domain.serviceaccount.model.ApiFeature.StatusEnum;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountFeatureRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateFeatureStatusRequest;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountFeaturesRequest;
import com.peoples.banking.persistence.serviceaccount.entity.Features;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.FeaturesRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountFeaturesRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.type.FeatureType;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.transaction.Transactional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class ServiceAccountFeatureControllerTest extends AbstractControllerTest {

  public static final String REFERENCE_ID = "100011";
  public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

  @MockBean
  private ServiceAccountsRepository serviceAccountsRepository;

  @MockBean
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  @MockBean
  private ServiceAccountFeaturesRepository serviceAccountFeaturesRepository;

  @MockBean
  private FeaturesRepository featuresRepository;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void init() {
    serviceAccounts = ServiceAccounts.builder().id(77777).refId(REFERENCE_ID)
        .inboundApiToken(INBOUND_TOKEN_TEST)
        .outboundApiToken("outbound_token_test").outboundApiAuth(ServiceAccountAuthType.BASIC_AUTH.name()).name("SOME_NAME")
        .settlementAccountNum("sett_account_num").limitGroupId("11").accountNumRange("account_num")
        .createdOn(LocalDateTime.now()).updatedOn(LocalDateTime.now()).status("ENABLED")
        .build();

    serviceAccountsRepository.save(serviceAccounts);
  }

  @AfterEach
  @Transactional
  public void tearDown() {
    if (serviceAccounts != null) {
      serviceAccountEventsRepository.deleteByServiceAccounts(serviceAccounts);
      Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(serviceAccounts.getRefId());
      del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));
    }
  }


  /**
   * The test use is to grant restricted feature to customer WILL fail on second run because we don't have full revoke now
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "AUTHENTICATE_PAYMENT",
      "INITIATE_RECEIVE_PAYMENT"
  })
  @Order(1)
  public void grant_RestrictedFeature_Success(String saFeature) throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, saFeature);
    serviceAccountFeatures.setActive(false);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    //Mock the feature list
    List<Features> features = new ArrayList<>();
    Features features1 = new Features();
    features1.setFeature(saFeature);
    features1.setRestricted(true);
    features.add(features1);
    doReturn(features).when(featuresRepository).findAll();

    CreateServiceAccountFeatureRequest featureRequest = new CreateServiceAccountFeatureRequest();
    featureRequest.setFeatureName(saFeature);

    featureRequest.setReasonDescription("new feature for amazing service account");
    mvc.perform(MockMvcRequestBuilders.post(buildAddFeatureApiUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(featureRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "A1",
      "ABC",
      "ABC 123",
      "ABC- Hello",
      "ABC-   Hello"
  })
  @Order(1)
  public void grant_RestrictedFeature_reasons_Success(String reasonDescription) throws Exception {

    String saFeature = "AUTHENTICATE_PAYMENT";
    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, saFeature);
    serviceAccountFeatures.setActive(false);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    //Mock the feature list
    List<Features> features = new ArrayList<>();
    Features features1 = new Features();
    features1.setFeature(saFeature);
    features1.setRestricted(true);
    features.add(features1);
    doReturn(features).when(featuresRepository).findAll();

    CreateServiceAccountFeatureRequest featureRequest = new CreateServiceAccountFeatureRequest();
    featureRequest.setFeatureName(saFeature);

    featureRequest.setReasonDescription(reasonDescription);
    mvc.perform(MockMvcRequestBuilders.post(buildAddFeatureApiUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(featureRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  /**
   * The test use is to grant restricted feature to customer WILL fail on second run because we don't have full revoke now
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "CREATE_PAYMENT_REQUEST",
      "CANCEL_PAYMENT_REQUEST",
      "RETRIEVE_PAYMENT_REQUEST",
      "REVERSE_SEND_PAYMENT",
      "CANCEL_PAYMENT",
      "RETRIEVE_OUTGOING_PAYMENT"
  })
  @Order(1)
  public void grant_UnRestrictedFeature_Failed(String saFeature) throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, saFeature);
    serviceAccountFeatures.setActive(false);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    CreateServiceAccountFeatureRequest featureRequest = new CreateServiceAccountFeatureRequest();
    featureRequest.setFeatureName(saFeature);
    featureRequest.setReasonDescription("new feature for amazing service account");
    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.post(buildAddFeatureApiUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(featureRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();


  }

  /**
   * The test use is to grant feature to customer - should fail with bad request - 400 error
   */
  @Test
  @Order(2)
  public void grantFeatureDuplicated() throws Exception {
    CreateServiceAccountFeatureRequest featureRequest = new CreateServiceAccountFeatureRequest();
    featureRequest.setFeatureName(INITIATE_SEND_PAYMENT.name());
    featureRequest.setReasonDescription("new duplicated for amazing service account");
    mvc.perform(MockMvcRequestBuilders.post(buildApiUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(featureRequest))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  private String buildApiUrl() {
    return ServiceAccountAPIConstant.SERVICE_ACCOUNT_FEATURE_BY_REF_ID.replace("{refId}", REFERENCE_ID);
  }

  private String buildAddFeatureApiUrl() {
    return ServiceAccountAPIConstant.SERVICE_ACCOUNT_FEATURE_BY_REF_ID.replace("{refId}", REFERENCE_ID);
  }

  private String buildFeatureEnableUrl(String featureName) {
    return ServiceAccountAPIConstant.ENABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID.replace("{refId}", REFERENCE_ID)
        .replace("{featureName}", featureName);
  }

  private String buildFeatureDisableUrl(String featureName) {
    return ServiceAccountAPIConstant.DISABLE_SERVICE_ACCOUNT_FEATURE_BY_REF_ID.replace("{refId}", REFERENCE_ID)
        .replace("{featureName}", featureName);
  }

  private String buildFeaturesUpdateUrl() {
    return ServiceAccountAPIConstant.UPDATE_SERVICE_ACCOUNT_FEATURES_BY_REF_ID.replace("{refId}", REFERENCE_ID);
  }

  /**
   * The test use is to revoke feature from service account
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "RETRIEVE_PAYMENT_OPTIONS",
      "INITIATE_SEND_PAYMENT",
      "SUBMIT_SEND_PAYMENT"
  })
  @Order(4)
  public void disableFeatureSuccess(String saFeature) throws Exception {

    UpdateFeatureStatusRequest updateFeatureStatusRequest = new UpdateFeatureStatusRequest();
    updateFeatureStatusRequest.setReasonDescription("Disabling Service Account Feature Test");

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, saFeature);
    serviceAccountFeatures.setActive(true);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    hashMap.put(saFeature, serviceAccountFeatures);
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    mvc.perform(MockMvcRequestBuilders.patch(buildFeatureDisableUrl(saFeature))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(updateFeatureStatusRequest))
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  /**
   * The test use is to revoke feature from service account
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "RETRIEVE_PAYMENT_OPTIONS",
      "INITIATE_SEND_PAYMENT",
      "SUBMIT_SEND_PAYMENT"
  })
  @Order(5)
  public void enableFeatureSuccess(String saFeature) throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, saFeature);
    serviceAccountFeatures.setActive(false);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    hashMap.put(saFeature, serviceAccountFeatures);
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    UpdateFeatureStatusRequest updateFeatureStatusRequest = new UpdateFeatureStatusRequest();
    updateFeatureStatusRequest.setReasonDescription("Enable good guys");
    mvc.perform(MockMvcRequestBuilders.patch(buildFeatureEnableUrl(saFeature))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(updateFeatureStatusRequest))
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

  }

  /**
   * The test use is to revoke already disabled feature from service account
   */
  @Test
  @Order(6)
  public void disableFeatureFail() throws Exception {
    UpdateFeatureStatusRequest updateFeatureStatusRequest = new UpdateFeatureStatusRequest();
    updateFeatureStatusRequest.setReasonDescription("Disabling bad guys will fail now");
    mvc.perform(MockMvcRequestBuilders.patch(buildFeatureDisableUrl(INITIATE_SEND_PAYMENT.name()))
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(updateFeatureStatusRequest))
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  /**
   * The test use is to grant restricted feature to customer WILL fail on second run because we don't have full revoke now
   */
  @Test
  @Order(7)
  public void updateServiceAccountFeatures_Success() throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, INITIATE_SEND_PAYMENT.name());
    ServiceAccountFeatures serviceAccountFeatures2 = ServiceAccountTestUtil.createServiceAccountFeatures(sa, SUBMIT_SEND_PAYMENT.name());
    serviceAccountFeatures.setActive(false);
    serviceAccountFeatures2.setActive(true);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    hashMap.put(INITIATE_SEND_PAYMENT.name(), serviceAccountFeatures);
    hashMap.put(SUBMIT_SEND_PAYMENT.name(), serviceAccountFeatures2);
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    UpdateServiceAccountFeaturesRequest featureRequest = new UpdateServiceAccountFeaturesRequest();
    List<ApiFeature> features = new ArrayList<>();
    ApiFeature apiFeature = new ApiFeature();
    apiFeature.setFeatureName(FeatureType.INITIATE_SEND_PAYMENT.name());
    apiFeature.setStatus(StatusEnum.ENABLED);
    ApiFeature apiFeature2 = new ApiFeature();
    apiFeature2.setFeatureName(SUBMIT_SEND_PAYMENT.name());
    apiFeature2.setStatus(StatusEnum.DISABLED);

    features.add(apiFeature);
    features.add(apiFeature2);
    featureRequest.setFeatures(features);

    featureRequest.setReasonDescription("new feature for amazing service account");
    mvc.perform(MockMvcRequestBuilders.put(buildFeaturesUpdateUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(featureRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }


  /**
   * The test use is to update service account features with existing status, which should give failed request
   */
  @Test
  @Order(8)
  public void updateServiceAccountFeatures_Fail() throws Exception {

    ServiceAccounts sa = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(REFERENCE_ID, INBOUND_TOKEN_TEST);
    ServiceAccountFeatures serviceAccountFeatures = ServiceAccountTestUtil.createServiceAccountFeatures(sa, INITIATE_SEND_PAYMENT.name());
    ServiceAccountFeatures serviceAccountFeatures2 = ServiceAccountTestUtil.createServiceAccountFeatures(sa, SUBMIT_SEND_PAYMENT.name());
    serviceAccountFeatures.setActive(true);
    serviceAccountFeatures2.setActive(true);
    Map<String, ServiceAccountFeatures> hashMap = new HashMap<>();
    hashMap.put(INITIATE_SEND_PAYMENT.name(), serviceAccountFeatures);
    hashMap.put(SUBMIT_SEND_PAYMENT.name(), serviceAccountFeatures2);
    sa.setFeatures(hashMap);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    UpdateServiceAccountFeaturesRequest featureRequest = new UpdateServiceAccountFeaturesRequest();
    List<ApiFeature> features = new ArrayList<>();
    ApiFeature apiFeature = new ApiFeature();
    apiFeature.setFeatureName(FeatureType.INITIATE_SEND_PAYMENT.name());
    apiFeature.setStatus(StatusEnum.ENABLED);
    ApiFeature apiFeature2 = new ApiFeature();
    apiFeature2.setFeatureName(DECLINE_PAYMENT.name());
    apiFeature2.setStatus(StatusEnum.DISABLED);

    features.add(apiFeature);
    features.add(apiFeature2);
    featureRequest.setFeatures(features);

    featureRequest.setReasonDescription("new feature for amazing service account");
    mvc.perform(MockMvcRequestBuilders.put(buildFeaturesUpdateUrl())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(featureRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());
  }

}
