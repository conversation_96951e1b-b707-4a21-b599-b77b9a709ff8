package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountProfileRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import javax.transaction.Transactional;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UpdateServiceAccountControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  public static final String REFERENCE_ID = "100011";
  public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

  private UpdateServiceAccountProfileRequest request;

  ServiceAccounts serviceAccounts;

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  @Autowired
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
    request = ServiceAccountTestUtil.createUpdateServiceAccountProfileRequest(REFERENCE_ID);
  }

  @AfterEach
  public void tearDown() {
    if (serviceAccounts != null) {
      //TODO the delete did not working cause issue
//      serviceAccountEventsRepository.deleteByServiceAccounts(serviceAccounts);
//      Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(serviceAccounts.getRefId());
//      del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));
      serviceAccounts = null;
    }
    template = null;
    base = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test use is to update service account and verify with DB
   */
  @Test
  public void updateServiceAccount_updateData_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    //Minus 10 days
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = ServiceAccountTestUtil
        .createUpdateServiceAccountProfileRequest(refId);

    updateServiceAccountProfileRequest.setName(RandomStringUtils.randomAlphabetic(10));

    String finalUrl = base + ServiceAccountAPIConstant.SERVICE_ACCOUNT_BY_REF_ID.replace("{refId}", refId);

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestPayment, Void.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());

    //Verify Service accounts with DB
    Optional<ServiceAccounts> serviceAccountsOptional = serviceAccountsRepository.findByRefId(refId);
    assertTrue(serviceAccountsOptional.isPresent());
    ServiceAccounts saDB = serviceAccountsOptional.get();
    assertNotNull(saDB);
    //verify the update date is being updated and DayOfYear is same
    assertEquals(LocalDateTime.now().getDayOfYear(), saDB.getUpdatedOn().getDayOfYear());

  }

}
