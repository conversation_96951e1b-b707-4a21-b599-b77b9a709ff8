package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

import com.peoples.banking.adapter.pb.serviceaccountcredential.ServiceAccountCredentialAdapter;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.Endpoint;
import com.peoples.banking.domain.serviceaccount.model.ErrorResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.PriorityEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.TypeEnum;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaAdmin;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CreateServiceAccountControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  public static final String REFERENCE_ID = "100011";

  ServiceAccounts serviceAccounts;

  @MockBean
  private KafkaAdmin kafkaAdmin;

  @MockBean
  private ServiceAccountCredentialAdapter serviceAccountCredentialAdapter;
  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;


  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1");
  }

  @AfterEach
  public void tearDown() {
    if (serviceAccounts != null) {
      Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(serviceAccounts.getRefId());
      del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));
      serviceAccounts = null;
    }
    template = null;
    base = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test use is to create service account and verify with DB
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "ENABLED",
      "DISABLED",
      "SUSPENDED"
  })
  public void createServiceAccount_withStatus_success(String status) throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    //Minus 10 days
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setStatus(status);
    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Verify Service accounts with DB
    Optional<ServiceAccounts> serviceAccountsOptional = serviceAccountsRepository.findByRefId(refId);
    assertTrue(serviceAccountsOptional.isPresent());
    ServiceAccounts saDB = serviceAccountsOptional.get();
    assertNotNull(saDB);
    assertNotNull(saDB.getConfigs());

    //clean db for new create service account
    Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(request.getRefId());
    del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));

  }

  @ParameterizedTest
  @ValueSource(strings = {
      "ENABLED1",
      "DISABLED1",
      "SUSPENDED1"
  })
  public void createServiceAccount_badStatus_failed(String status) throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setStatus(status);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.INVALID_INPUT, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_outboundApiAuthIsMissing_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    //Minus 10 days
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //remove the OutboundApiAuthToken to trigger the error
    request.setOutboundApiAuthToken("");

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_contactIsMissing_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //remove the Contacts to trigger the error
    request.setContacts(null);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_contact_missingFraud_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //remove the Contacts to trigger the error
    List<ServiceAccountContact> contactList = request.getContacts();
    ServiceAccountContact serviceAccountContactToBeRemoved = null;
    for (ServiceAccountContact serviceAccountContact : contactList) {
      //find the PRIMARY + FRAUD
      if (PriorityEnum.PRIMARY.equals(serviceAccountContact.getPriority())) {
        if (TypeEnum.FRAUD.equals(serviceAccountContact.getType())) {
          serviceAccountContactToBeRemoved = serviceAccountContact;
        }
      }
    }
    contactList.remove(serviceAccountContactToBeRemoved);
    request.setContacts(contactList);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_contact_missingTechnical_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //remove the Contacts to trigger the error
    List<ServiceAccountContact> contactList = request.getContacts();
    ServiceAccountContact serviceAccountContactToBeRemoved = null;
    for (ServiceAccountContact serviceAccountContact : contactList) {
      //find the PRIMARY + Technical
      if (PriorityEnum.PRIMARY.equals(serviceAccountContact.getPriority())) {
        if (TypeEnum.TECHNICAL.equals(serviceAccountContact.getType())) {
          serviceAccountContactToBeRemoved = serviceAccountContact;
        }
      }
    }
    contactList.remove(serviceAccountContactToBeRemoved);
    request.setContacts(contactList);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_contact_missingOperational_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //remove the Contacts to trigger the error
    List<ServiceAccountContact> contactList = request.getContacts();
    ServiceAccountContact serviceAccountContactToBeRemoved = null;
    for (ServiceAccountContact serviceAccountContact : contactList) {
      //find the PRIMARY + Operational
      if (PriorityEnum.PRIMARY.equals(serviceAccountContact.getPriority())) {
        if (TypeEnum.OPERATIONAL.equals(serviceAccountContact.getType())) {
          serviceAccountContactToBeRemoved = serviceAccountContact;
        }
      }
    }
    contactList.remove(serviceAccountContactToBeRemoved);
    request.setContacts(contactList);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_zeroEndpoint_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setEndpoints(null);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.FIELD_MISSING, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_moreThan_3_Endpoint_failed() throws Exception {
    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    //make the endpoint more than 3
    for (int i = 0; i < 5; i++) {
      Endpoint oneEndpoint = new Endpoint();
      oneEndpoint.setType(Endpoint.TypeEnum.TRANSACTION);
      oneEndpoint.setUrl("http://aol.com");
      request.getEndpoints().add(oneEndpoint);
    }

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.INVALID_LIST_SIZE, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_accountNumberRangeWithD_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    //Minus 10 days
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setAccountNumberRange("621-00525-,{},-,\\*,?.[],d{12-24}");

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Verify Service accounts with DB
    Optional<ServiceAccounts> serviceAccountsOptional = serviceAccountsRepository.findByRefId(refId);
    assertTrue(serviceAccountsOptional.isPresent());
    ServiceAccounts saDB = serviceAccountsOptional.get();
    assertNotNull(saDB);

    //clean db for new create service account
    Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(request.getRefId());
    del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));

  }


  @ParameterizedTest
  @ValueSource(strings = {
      "621-00525-\\dabaca{12-24}",
      "621-16001-\\d{12-24}@@ABCaz"
  })
  public void createServiceAccount_accountNumberRangeWithOthersChars_failed(String accountNumRange) throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    //Minus 10 days
    serviceAccounts.setUpdatedOn(LocalDateTime.now().minusDays(10));
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setAccountNumberRange(accountNumRange);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.POST, requestPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(ServiceAccountTestUtil.INVALID_INPUT, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void createServiceAccount_inbound_api_token_length_notProvided_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setInboundApiTokenLength(null);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Verify Service accounts with DB
    Optional<ServiceAccounts> serviceAccountsOptional = serviceAccountsRepository.findByRefId(refId);
    assertTrue(serviceAccountsOptional.isPresent());
    ServiceAccounts saDB = serviceAccountsOptional.get();
    assertNotNull(saDB);

    //clean db for new create service account
    Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(request.getRefId());
    del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));

  }

  @Test
  public void createServiceAccount_outbound_api_token_length_notProvided_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(10);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccountsRepository.save(serviceAccounts);

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);
    request.setOutboundApiTokenLength(null);

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    String finalUrl = base + ServiceAccountAPIConstant.ROOT_SERVICE_URL;

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestPayment = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestPayment, Void.class);

    assertNotNull(responseEntity);
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Verify Service accounts with DB
    Optional<ServiceAccounts> serviceAccountsOptional = serviceAccountsRepository.findByRefId(refId);
    assertTrue(serviceAccountsOptional.isPresent());
    ServiceAccounts saDB = serviceAccountsOptional.get();
    assertNotNull(saDB);

    //clean db for new create service account
    Optional<ServiceAccounts> del = serviceAccountsRepository.findByRefId(request.getRefId());
    del.ifPresent(accounts -> serviceAccountsRepository.delete(accounts));

  }

}
