package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.adapter.pb.serviceaccountcredential.ServiceAccountCredentialAdapter;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ReasonCode;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountStatusRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.transaction.Transactional;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class ServiceAccountControllerTest extends AbstractControllerTest {

  @MockBean
  private ServiceAccountsRepository serviceAccountsRepository;

  @MockBean
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  @MockBean
  private ServiceAccountCredentialAdapter serviceAccountCredentialAdapter;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  private String buildServiceAccountStatusChangeApiUrl(String apiUrl) {
    return apiUrl.replace("{refId}", ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT);
  }

  private String buildInternalGetServiceAccontByRefIdUrl() {
    return ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_BY_REF_ID
        .replace("{refId}", ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT);
  }

  private String buildInternalGetServiceAccontByApiTokenUrl() {
    return ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_BY_API_TOKEN
        .replace("{apiToken}", ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
  }


  /**
   * The test use is to disable service account
   */
  @Test
  @Order(1)
  @Transactional
  public void disableServiceAccount() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(Optional.ofNullable(serviceAccounts)).when(serviceAccountsRepository).findByRefId(isA(String.class));
    doReturn(serviceAccounts).when(serviceAccountsRepository).save(serviceAccounts);

    UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest = new UpdateServiceAccountStatusRequest();
    updateServiceAccountStatusRequest.setReasonCode(ReasonCode.CUSTOMER_INITIATED);
    updateServiceAccountStatusRequest.setReasonDescription("some reason description with some . and - #");
    mvc.perform(MockMvcRequestBuilders.patch(buildServiceAccountStatusChangeApiUrl(ServiceAccountAPIConstant.DISABLE_SERVICE_ACCOUNT))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(updateServiceAccountStatusRequest)).contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  /**
   * The test use is to enable service account
   */
  @Test
  @Order(2)
  @Transactional
  public void enableServiceAccount() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(Optional.ofNullable(serviceAccounts)).when(serviceAccountsRepository).findByRefId(isA(String.class));
    doReturn(serviceAccounts).when(serviceAccountsRepository).save(serviceAccounts);

    UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest = new UpdateServiceAccountStatusRequest();
    updateServiceAccountStatusRequest.setReasonCode(ReasonCode.CUSTOMER_INITIATED);
    updateServiceAccountStatusRequest.setReasonDescription("some reason description with some . and - #");
    mvc.perform(MockMvcRequestBuilders.patch(buildServiceAccountStatusChangeApiUrl(ServiceAccountAPIConstant.ENABLE_SERVICE_ACCOUNT))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .content(JsonUtil.toString(updateServiceAccountStatusRequest))
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  /**
   * The test use is to retrieve service account response with permissions
   */
  @Test
  @Order(3)
  public void internalRetrieveServiceAccountByRefId() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(Optional.ofNullable(serviceAccounts)).when(serviceAccountsRepository).internalFindByRefIdWithContacts(isA(String.class));

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.get(buildInternalGetServiceAccontByRefIdUrl())
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk()).andReturn();
    String contentAsString = mvcResult.getResponse().getContentAsString();
    ServiceAccountResponse serviceAccountResponse = JsonUtil.toObject(contentAsString, ServiceAccountResponse.class);
    assertNotNull(serviceAccountResponse.getStatus());

  }

  /**
   * The test use is to retrieve service account response with permissions
   */
  @Test
  @Order(4)
  public void internalRetrieveServiceAccountByApiToken() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(Optional.ofNullable(serviceAccounts)).when(serviceAccountsRepository).internalFindByInboundApiTokenWithContacts(isA(String.class));
    serviceAccounts.setConfigs(Map.of("configuration",  ServiceAccountTestUtil.createServiceAccountConfigurations()));
    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.get(buildInternalGetServiceAccontByApiTokenUrl())
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk()).andReturn();
    String contentAsString = mvcResult.getResponse().getContentAsString();
    ServiceAccountResponse serviceAccountResponse = JsonUtil.toObject(contentAsString, ServiceAccountResponse.class);
    assertNotNull(serviceAccountResponse.getStatus());
    List<ServiceAccountConfiguration> configurations = serviceAccountResponse.getConfigurations();
    assertNotNull(configurations);
    assertFalse(configurations.isEmpty());
    com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations serviceAccountConfigurations = ServiceAccountTestUtil.createServiceAccountConfigurations();
    assertEquals(configurations.get(0).getKey(), serviceAccountConfigurations.getKey());
    assertEquals(configurations.get(0).getValue(), serviceAccountConfigurations.getValue());
  }

  /**
   * Testing invalid characters in reason description
   */
  @Test
  @Order(5)
  @Transactional
  public void failForNotAllowedCharacters() throws Exception {
    UpdateServiceAccountStatusRequest updateServiceAccountStatusRequest = new UpdateServiceAccountStatusRequest();
    updateServiceAccountStatusRequest.setReasonCode(ReasonCode.CUSTOMER_INITIATED);
    updateServiceAccountStatusRequest.setReasonDescription("some reason description with unacceptable chars: @@@");
    mvc.perform(MockMvcRequestBuilders.patch(buildServiceAccountStatusChangeApiUrl(ServiceAccountAPIConstant.DISABLE_SERVICE_ACCOUNT))
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .content(JsonUtil.toString(updateServiceAccountStatusRequest)).contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest()).andExpect(jsonPath("$.error[0].code").value("INVALID_INPUT"));
  }

  @Test
  @Order(6)
  @Transactional
  public void createServiceAccount() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
            ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(serviceAccounts).when(serviceAccountsRepository).save(any(ServiceAccounts.class));
    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);

    final MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.post(ServiceAccountAPIConstant.SERVICE_ACCOUNT_CREATE)
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isCreated()).andReturn();
    String contentAsString = mvcResult.getResponse().getContentAsString();
    CreateServiceAccountResponse response = JsonUtil.toObject(contentAsString, CreateServiceAccountResponse.class);
    assertEquals(request.getRefId(), response.getRefId());
  }

  @Test
  @Order(7)
  @Transactional
  public void createExistingServiceAccount() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
            ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(Optional.ofNullable(serviceAccounts)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders.post(ServiceAccountAPIConstant.SERVICE_ACCOUNT_CREATE)
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest()).andExpect(jsonPath("$.error[0].code").value("RESOURCE_EXISTS"));
  }

  @Test
  @Order(8)
  @Transactional
  public void failForInvalidLimitAmount() throws Exception {
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithoutLimits(
        ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT, ServiceAccountTestUtil.SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT);
    doReturn(serviceAccounts).when(serviceAccountsRepository).save(any(ServiceAccounts.class));
    //mock the serviceAccountCredentialAdapter call
    doNothing().when(serviceAccountCredentialAdapter).createServiceAccountCredential(any());

    CreateServiceAccountRequest request = ServiceAccountTestUtil.createServiceAccountFromServiceAccounts(serviceAccounts);

    request.getLimits().setReserveAmount(BigDecimal.valueOf(1.001));
    mvc.perform(MockMvcRequestBuilders.post(ServiceAccountAPIConstant.SERVICE_ACCOUNT_CREATE)
            .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
            .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
            .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andExpect(jsonPath("$.error[0].code").value("INVALID_AMOUNT"));
  }

}
