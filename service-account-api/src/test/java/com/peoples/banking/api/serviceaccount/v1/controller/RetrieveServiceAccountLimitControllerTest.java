package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.UUID;
import javax.transaction.Transactional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class RetrieveServiceAccountLimitControllerTest extends AbstractControllerTest {

  @Autowired
  private ServiceAccountsRepository serviceAccountsRepository;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @AfterEach
  @Transactional
  public void tearDown() {
    if (serviceAccounts != null) {
      serviceAccountsRepository.delete(serviceAccounts);
    }
  }

  /**
   * The test use is to retrieve service account limit
   */
  @Test
  public void retrieveLimit_success() throws Exception {

    String refId = RandomStringUtils.randomAlphabetic(8);
    String inboundApiToken = RandomStringUtils.randomAlphabetic(10);
    serviceAccounts = ServiceAccountTestUtil.createServiceAccountsWithLimits(
        refId, inboundApiToken);
    serviceAccountsRepository.save(serviceAccounts);

    mvc.perform(MockMvcRequestBuilders
        .get(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(serviceAccounts.getRefId()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk()).andReturn();

    if (serviceAccounts != null) {
      serviceAccountsRepository.delete(serviceAccounts);
    }
  }

  /**
   * The test retrieve service account limit throw account resource not found
   */
  @Test
  public void retrieveLimit_refIdNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders.get(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

  /**
   * The test retrieve service account limit throw limit resource not found
   */
  @Test
  public void retrieveLimit_limitNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders
        .get(ServiceAccountTestUtil.buildServiceAccountLimitApiUrl(ServiceAccountTestUtil.SERVICE_ACCOUNT_REF_ID_NO_LIMIT))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }

}
