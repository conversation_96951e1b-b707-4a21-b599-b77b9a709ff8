package com.peoples.banking.api.serviceaccount.v1.util;

import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.domain.serviceaccount.model.*;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest.ConnectorTypeEnum;
import com.peoples.banking.domain.serviceaccount.model.CreateServiceAccountRequest.OutboundApiAuthEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountAuthenticationMethod.MethodEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.PriorityEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact.TypeEnum;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountConfigurations;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountFeatures;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountLimits;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountsContact;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * The class to provide some basic PaymentTest Util
 *
 * <AUTHOR> Lei
 */
public class ServiceAccountTestUtil {

  public static final String SERVICE_ACCOUNT_REF_ID_WITH_LIMIT = "LIMIT123";
  public static final String SERVICE_ACCOUNT_API_TOKEN_WITH_LIMIT = "LimitApiToken";

  public static final String SERVICE_ACCOUNT_REF_ID_NO_LIMIT = "LIMIT234";
  public static final String SERVICE_ACCOUNT_API_TOKEN_NO_LIMIT = "LimitApiToken2";

  //use the cognito id-token style
  public final static String JWT_TOKEN = "eyJraWQiOiJyc0g5ZnN4R2V3YTcrU1kwemRqTEc1eFNidlA5ZTJnaDNqQ2R0N1wvdkVcL0k9IiwiYWxnIjoiUlMyNTYifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.rPBNa5Zyg_1o-TAmZMkgxNU0aHeIo1TLq6Ig5tofX6K7-D6Hn7Z5NTwRd8EUuxMKGC4IMI_-W8cIu73XX0YJbIQBdHKXX8ItEPmai8iUdUroAv3sLE9VxCSLh4-hnMCVdp79rfeYAtwO9R4RpeUJwzoPoPYzcXCN06cSGuUPwzh3cCYNQL5dWjYAX6jzLSuO_Km6_Mis9mV6C_SRah2-9EqHoVa5w1YK3tTE0tEBPyuZ-U-z7VlXJrObgug9C6IGBlvvg6JCRFxp_JpJkgTBb6g5lst8p_2YUeOaGp5luFt84e979pnszCprqJuOW0egb9kS1NpznKHE5YuyOmc4GQ";

  public static final String SERVICE_ACCOUNT_API_TOKEN = "AxYQ1GGl0";

  public static final String FIELD_MISSING = "MISSING_FIELD";

  public static final String INVALID_LIST_SIZE = "INVALID_LIST_SIZE";

  public static final String INVALID_INPUT = "INVALID_INPUT";

  public static String buildServiceAccountLimitApiUrl(String refId) {
    return ServiceAccountAPIConstant.SERVICE_ACCOUNT_LIMIT.replace("{refId}", refId);
  }

  public static String buildServiceAccountConfigApiUrlByRefId(String refId) {
    return ServiceAccountAPIConstant.INTERNAL_SERVICE_ACCOUNT_CONFIG
        .replace("{refId}", refId);
  }

  public static String buildServiceAccountContactApiUrl(String refId) {
    return ServiceAccountAPIConstant.SERVICE_ACCOUNT_CONTACT.replace("{refId}", refId);
  }

  public static String buildServiceAccountConfigurationApiUrl(String refId) {
    return ServiceAccountAPIConstant.UPDATE_SERVICE_ACCOUNT_CONFIGURATION.replace("{refId}", refId);
  }

  public static String buildServiceAccountApiUrl(String refId) {
    return ServiceAccountAPIConstant.SERVICE_ACCOUNT_BY_REF_ID.replace("{refId}", refId);
  }

  public static CreateServiceAccountRequest createServiceAccountFromServiceAccounts(ServiceAccounts serviceAccounts) {
    CreateServiceAccountRequest request = new CreateServiceAccountRequest();
    request.setRefId("LIMIT" + ThreadLocalRandom.current().nextInt(200, 800));
    //TODO https://stackoverflow.com/questions/22115/using-regex-to-generate-strings-rather-than-match-them
    //request.setRefId(RandomStringUtils.randomAlphabetic(8));
    request.setInboundApiTokenLength(new BigDecimal(26));
    request.setOutboundApiTokenLength(new BigDecimal(26));
    request.setCrmRefId(serviceAccounts.getCrmId());
    request.setSettlementAccountNumber(serviceAccounts.getSettlementAccountNum());
    request.setOutboundApiAuth(OutboundApiAuthEnum.fromValue(serviceAccounts.getOutboundApiAuth()));
    request.setOutboundApiAuthToken("uvRYvKY24hmT2wnzUs4oZ8p3cYMSY3Jj");
    request.setName(serviceAccounts.getName());
    request.setConnectorType(ConnectorTypeEnum.DIRECT);
    request.setAccountNumberRange(serviceAccounts.getAccountNumRange());
    request.setLimitGroupId(serviceAccounts.getLimitGroupId());
    request.setSettlementAccountNumber(serviceAccounts.getSettlementAccountNum());

    //set service account Authentication
    ServiceAccountAuthentication authentication = new ServiceAccountAuthentication();
    List<ServiceAccountAuthenticationMethod> authMethods = new ArrayList<>();
    ServiceAccountAuthenticationMethod serviceAccountAuthenticationMethod = new ServiceAccountAuthenticationMethod();
    serviceAccountAuthenticationMethod.setMethod(MethodEnum.BASIC_AUTH);
    authMethods.add(serviceAccountAuthenticationMethod);
    authentication.setAuthMethods(authMethods);

    List<ServiceAccountBaseAuthenticationCredentials> serviceAccountBaseAuthenticationCredentialLst = new ArrayList<>();
    ServiceAccountBaseAuthenticationCredentials accountBaseAuthenticationCredentials = new ServiceAccountBaseAuthenticationCredentials();
    accountBaseAuthenticationCredentials.setUserName("test1");
    accountBaseAuthenticationCredentials.setPassword(RandomStringUtils.randomAlphabetic(38));
    serviceAccountBaseAuthenticationCredentialLst.add(accountBaseAuthenticationCredentials);
    authentication.setBasicAuthCredentials(serviceAccountBaseAuthenticationCredentialLst);

    request.setAuthentication(authentication);

    //set service account contacts
    List<ServiceAccountContact> contactsLst = new ArrayList<>();
    ServiceAccountContact contacts = new ServiceAccountContact();
    contacts.setEmail("<EMAIL>");
    contacts.setFirstName("testF1");
    contacts.setLastName("testL1");
    contacts.setPriority(PriorityEnum.PRIMARY);
    contacts.setType(TypeEnum.OPERATIONAL);
    contacts.setPhone("**********");
    contactsLst.add(contacts);

    contacts = new ServiceAccountContact();
    contacts.setEmail("<EMAIL>");
    contacts.setFirstName("testF2");
    contacts.setLastName("testL2");
    contacts.setPriority(PriorityEnum.PRIMARY);
    contacts.setType(TypeEnum.TECHNICAL);
    contacts.setPhone("**********");
    contactsLst.add(contacts);

    contacts = new ServiceAccountContact();
    contacts.setEmail("<EMAIL>");
    contacts.setFirstName("testF3");
    contacts.setLastName("testL3");
    contacts.setPriority(PriorityEnum.PRIMARY);
    contacts.setType(TypeEnum.FRAUD);
    contacts.setPhone("**********");
    contactsLst.add(contacts);

    request.setContacts(contactsLst);

    ServiceAccountLimit limits = new ServiceAccountLimit();
    limits.setReserveAmount(BigDecimal.ONE);
    limits.setSuspendThresholdAmount(BigDecimal.ONE);
    limits.setNotificationThresholdAmount(BigDecimal.ONE);
    request.setLimits(limits);

    List<Endpoint> endpoints = new ArrayList<>();
    Endpoint oneEndpoint = new Endpoint();
    oneEndpoint.setType(Endpoint.TypeEnum.TRANSACTION);
    oneEndpoint.setUrl("http://aol.com");
    endpoints.add(oneEndpoint);
    request.setEndpoints(endpoints);

    request.setStatus("ENABLED");

    return request;
  }

  public static ServiceAccounts createServiceAccountsWithoutLimits(String refId, String inboundApiToken) {
    ServiceAccounts result = ServiceAccounts.builder()
        .refId(refId)
        .inboundApiToken(inboundApiToken)
        .outboundApiToken("outbound_api_token")
        .outboundApiAuth(ServiceAccountAuthType.BASIC_AUTH.name())
        .name("some name")
        .settlementAccountNum("621-00525-************")
        .limitGroupId("12345")
        .accountNumRange("621-16001-\\d{12-24}")
        .createdOn(LocalDateTime.now())
        .updatedOn(LocalDateTime.now())
        .status("ENABLED")
        .crmId("*********")
        .connectorType("DIRECT")
        .build();
    return result;
  }

  public static ServiceAccountConfigurations createServiceAccountConfigurations() {
    return ServiceAccountConfigurations.builder()
        .key("key")
        .value("value")
        .createdOn(LocalDateTime.now())
        .build();
  }

  public static ServiceAccountConfiguration createServiceAccountConfiguration() {
    ServiceAccountConfiguration result = new ServiceAccountConfiguration();
    result.setKey("GL_PROFILE");
    result.setValue("TEST");
    return result;
  }

  public static ServiceAccounts createServiceAccountsWithLimits(String refId, String inboundApiToken) {
    ServiceAccounts sa = createServiceAccountsWithoutLimits(refId, inboundApiToken);
    ServiceAccountLimits sal = ServiceAccountLimits.builder()
        .serviceAccounts(sa)
        .reserveAmount(new BigDecimal("1000"))
        .notificationThresholdAmount(new BigDecimal("222.22"))
        .suspendThresholdAmount(new BigDecimal("333.33"))
        .createdOn(LocalDateTime.now()).build();
    sa.setLimits(Collections.singletonList(sal));
    return sa;
  }

  public static ServiceAccounts createServiceAccountsWithConfigs(String refId, String inboundApiToken) {
    ServiceAccounts sa = createServiceAccountsWithoutLimits(refId, inboundApiToken);

    ServiceAccountConfigurations config = ServiceAccountConfigurations.builder()
        .serviceAccounts(sa)
        .key("AUTO_REVERSE2")
        .value("Y")
        .createdOn(LocalDateTime.now())
        .updatedOn(LocalDateTime.now())
        .build();

    Map<String, ServiceAccountConfigurations> configs = new HashMap<>();
    configs.put("AUTO_REVERSE2", config);

    sa.setConfigs(configs);

    return sa;
  }

  public static ServiceAccounts createServiceAccountsWithNoConfigs(String refId, String inboundApiToken) {

    return createServiceAccountsWithoutLimits(refId, inboundApiToken);
  }

  public static ServiceAccounts createServiceAccountsWithContacts(String refId, String inboundApiToken) {
    ServiceAccounts sa = createServiceAccountsWithoutLimits(refId, inboundApiToken);
    ServiceAccountsContact sac = ServiceAccountsContact.builder()
        .serviceAccounts(sa)
        .type("OPERATIONAL")
        .priority("PRIMARY")
        .firstName("testFirstName")
        .lastName("testFirstName")
        .email("<EMAIL>")
        .phone("**********")
        .updatedOn(LocalDateTime.now())
        .createdOn(LocalDateTime.now()).build();
    List<ServiceAccountsContact> contacts = new ArrayList<>();
    contacts.add(sac);
    sa.setContacts(contacts);
    return sa;
  }

  public static ServiceAccounts createServiceAccountsWithContactsWithId(String refId, String inboundApiToken, int saId, int saContactId) {
    ServiceAccounts sa = createServiceAccountsWithoutLimits(refId, inboundApiToken);
    ServiceAccountsContact sac = ServiceAccountsContact.builder()
        .id(saContactId)
        .serviceAccounts(sa)
        .type("OPERATIONAL")
        .priority("PRIMARY")
        .firstName("testFirstName")
        .lastName("testFirstName")
        .email("<EMAIL>")
        .phone("**********")
        .updatedOn(LocalDateTime.now())
        .createdOn(LocalDateTime.now()).build();
    sa.setId(saId);
    List<ServiceAccountsContact> contacts = new ArrayList<>();
    contacts.add(sac);
    sa.setContacts(contacts);
    return sa;
  }


  public static ServiceAccountLimit createServiceAccountLimitRequest() {
    ServiceAccountLimit sal = new ServiceAccountLimit();
    sal.setReserveAmount(new BigDecimal("2000"));
    sal.setNotificationThresholdAmount(new BigDecimal("234.56"));
    sal.setSuspendThresholdAmount(new BigDecimal("345.67"));
    return sal;
  }

  public static CreateServiceAccountContactRequest createAddServiceAccountContactRequest() {
    CreateServiceAccountContactRequest createServiceAccountContactRequest = new CreateServiceAccountContactRequest();

    createServiceAccountContactRequest.setEmail("<EMAIL>");
    createServiceAccountContactRequest.setFirstName("testFirstName");
    createServiceAccountContactRequest.setLastName("testLastName");
    createServiceAccountContactRequest.setPhone("**********");
    createServiceAccountContactRequest.setType(TypeEnum.FRAUD);
    createServiceAccountContactRequest.setPriority(PriorityEnum.PRIMARY);
    return createServiceAccountContactRequest;
  }

  public static UpdateServiceAccountContactRequest createUpdateServiceAccountContactRequest(String type, String priority, String email) {
    UpdateServiceAccountContactRequest updateServiceAccountContactRequest = new UpdateServiceAccountContactRequest();
    updateServiceAccountContactRequest.setEmail(email);
    updateServiceAccountContactRequest.setFirstName("testFirstName");
    updateServiceAccountContactRequest.setLastName("testLastName");
    updateServiceAccountContactRequest.setPhone("**********");
    updateServiceAccountContactRequest.setType(TypeEnum.fromValue(type));
    updateServiceAccountContactRequest.setPriority(PriorityEnum.fromValue(priority));
    return updateServiceAccountContactRequest;
  }

  public static UpdateServiceAccountContactRequest createUpdateServiceAccountContactRequest_NoType(String type, String priority, String email) {
    UpdateServiceAccountContactRequest updateServiceAccountContactRequest = new UpdateServiceAccountContactRequest();
    updateServiceAccountContactRequest.setEmail(email);
    updateServiceAccountContactRequest.setFirstName("testFirstName");
    updateServiceAccountContactRequest.setLastName("testLastName");
    updateServiceAccountContactRequest.setPhone("**********");
    updateServiceAccountContactRequest.setPriority(PriorityEnum.fromValue(priority));
    return updateServiceAccountContactRequest;
  }

  public static UpdateServiceAccountContactRequest createUpdateServiceAccountContactRequest_NoPriority(String type, String priority, String email) {
    UpdateServiceAccountContactRequest updateServiceAccountContactRequest = new UpdateServiceAccountContactRequest();
    updateServiceAccountContactRequest.setEmail(email);
    updateServiceAccountContactRequest.setFirstName("testFirstName");
    updateServiceAccountContactRequest.setLastName("testLastName");
    updateServiceAccountContactRequest.setPhone("**********");
    updateServiceAccountContactRequest.setType(TypeEnum.fromValue(type));
    return updateServiceAccountContactRequest;
  }

  public static UpdateServiceAccountLimitRequest createUpdateServiceAccountLimitRequest() {
    UpdateServiceAccountLimitRequest updateServiceAccountLimitRequest = new UpdateServiceAccountLimitRequest();
    ServiceAccountLimit sal = new ServiceAccountLimit();
    sal.setReserveAmount(new BigDecimal("2000"));
    sal.setNotificationThresholdAmount(new BigDecimal("234.56"));
    sal.setSuspendThresholdAmount(new BigDecimal("345.67"));
    updateServiceAccountLimitRequest.serviceAccountLimit(sal);
    updateServiceAccountLimitRequest.setReasonDescription("update service account limit unit test");
    return updateServiceAccountLimitRequest;
  }

  public static ServiceAccountFeatures createServiceAccountFeatures(ServiceAccounts sa, String feature) {
    ServiceAccountFeatures serviceAccountFeatures = new ServiceAccountFeatures();
    serviceAccountFeatures.setServiceAccounts(sa);
    serviceAccountFeatures.setFeature(feature);
    serviceAccountFeatures.setCreatedOn(LocalDateTime.now());
    return serviceAccountFeatures;
  }

  public static UpdateServiceAccountProfileRequest createUpdateServiceAccountProfileRequest(String refId) {
    UpdateServiceAccountProfileRequest updateServiceAccountProfileRequest = new UpdateServiceAccountProfileRequest();
    updateServiceAccountProfileRequest.setAccountNumberRange("621-16001-\\\\d{12-24}");
    updateServiceAccountProfileRequest.setLimitGroupId("PRODVAL");
    updateServiceAccountProfileRequest.setName("test name");
    updateServiceAccountProfileRequest.setRefId(refId);
    updateServiceAccountProfileRequest.setSettlementAccountNumber("621-10000-***********");
    return updateServiceAccountProfileRequest;
  }

  public static UpdateServiceAccountConfigurationsRequest createUpdateServiceAccountConfigurationsRequest(String refId) {
    UpdateServiceAccountConfigurationsRequest updateServiceAccountConfigurationsRequest= new UpdateServiceAccountConfigurationsRequest();

    List<ServiceAccountConfiguration> serviceAccountConfigurationList = new ArrayList<>();
    ServiceAccountConfiguration serviceAccountConfiguration = new ServiceAccountConfiguration();
    ServiceAccountConfiguration serviceAccountConfiguration2 = new ServiceAccountConfiguration();
    ServiceAccountConfiguration serviceAccountConfiguration3 = new ServiceAccountConfiguration();
    serviceAccountConfiguration.setKey("GL_ACCOUNT_ID");
    serviceAccountConfiguration.setValue("cd7bf786-5d18-11ee-8c99-0242ac120002");
    serviceAccountConfiguration2.setKey("GL_PROFILE_ID");
    serviceAccountConfiguration2.setValue("cd7bf786-5d18-11ee-8c99-0242ac120001");
    serviceAccountConfiguration3.setKey("GL_SERVICE_ENROLLMENT");
    serviceAccountConfiguration3.setValue("ENABLED_BYPASS.");
    serviceAccountConfigurationList.add(serviceAccountConfiguration);
    serviceAccountConfigurationList.add(serviceAccountConfiguration2);
    serviceAccountConfigurationList.add(serviceAccountConfiguration3);

    updateServiceAccountConfigurationsRequest.setConfigurations(serviceAccountConfigurationList);
    updateServiceAccountConfigurationsRequest.setReasonDescription("unit test");

    return updateServiceAccountConfigurationsRequest;
  }

}
