package com.peoples.banking.api.serviceaccount.v1.controller;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountConfigurationsRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountConfigurationsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class UpdateServiceAccountConfigurationsControllerTest extends AbstractControllerTest {

    public static final String REFERENCE_ID = "100011";
    public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

    @MockBean
    private ServiceAccountsRepository serviceAccountsRepository;

    @MockBean
    private ServiceAccountConfigurationsRepository serviceAccountConfigurationsRepository;
    @MockBean
    private ServiceAccountEventsRepository serviceAccountEventsRepository;

    static {
        System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
    }

    /**
     * The test use is to update service account Configurations
     */
    @Test
    public void updateServiceAccountConfigurations_success() throws Exception {

        UpdateServiceAccountConfigurationsRequest updateServiceAccountConfigurationsRequest = ServiceAccountTestUtil
                .createUpdateServiceAccountConfigurationsRequest(REFERENCE_ID);

        ServiceAccountEvents serviceAccountEvents = new ServiceAccountEvents();

        int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
        int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
        ServiceAccounts sa = ServiceAccountTestUtil
                .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
        doReturn(Optional.of(sa)).when(serviceAccountsRepository).internalFindByRefId(isA(String.class));

        doReturn(updateServiceAccountConfigurationsRequest.getConfigurations())
                .when(serviceAccountConfigurationsRepository).saveAll(any());
        doReturn(serviceAccountEvents).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

        mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountConfigurationApiUrl(REFERENCE_ID))
                        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
                        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
                        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
                        .content(JsonUtil.toString(updateServiceAccountConfigurationsRequest))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent()).andReturn();
    }


    @Test
    public void updateServiceAccountConfigurations_over_50_length_key_failed() throws Exception {

        UpdateServiceAccountConfigurationsRequest updateServiceAccountConfigurationsRequest = ServiceAccountTestUtil
                .createUpdateServiceAccountConfigurationsRequest(REFERENCE_ID);

        //Make the key over 50
        updateServiceAccountConfigurationsRequest.getConfigurations().get(0)
                .setKey(RandomStringUtils.randomAlphabetic(51));

        ServiceAccountEvents serviceAccountEvents = new ServiceAccountEvents();

        int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
        int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
        ServiceAccounts sa = ServiceAccountTestUtil
                .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
        doReturn(Optional.of(sa)).when(serviceAccountsRepository).internalFindByRefId(isA(String.class));

        doReturn(updateServiceAccountConfigurationsRequest.getConfigurations())
                .when(serviceAccountConfigurationsRepository).saveAll(any());
        doReturn(serviceAccountEvents).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

        mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountConfigurationApiUrl(REFERENCE_ID))
                        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
                        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
                        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
                        .content(JsonUtil.toString(updateServiceAccountConfigurationsRequest))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest()).andReturn();
    }

}
