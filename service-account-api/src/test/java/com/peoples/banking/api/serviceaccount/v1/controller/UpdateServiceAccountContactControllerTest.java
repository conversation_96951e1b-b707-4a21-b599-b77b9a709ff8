package com.peoples.banking.api.serviceaccount.v1.controller;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.serviceaccount.v1.config.ServiceAccountAPIConstant;
import com.peoples.banking.api.serviceaccount.v1.util.ServiceAccountTestUtil;
import com.peoples.banking.domain.serviceaccount.model.UpdateServiceAccountContactRequest;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountEventsRepository;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

public class UpdateServiceAccountContactControllerTest extends AbstractControllerTest {

  public static final String REFERENCE_ID = "100011";
  public static final String INBOUND_TOKEN_TEST = "inbound_token_test";

  @MockBean
  private ServiceAccountsRepository serviceAccountsRepository;

  @MockBean
  private ServiceAccountEventsRepository serviceAccountEventsRepository;

  private ServiceAccounts serviceAccounts;

  static {
    System.setProperty(ServiceAccountAPIConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  /**
   * The test use is to update service account contact
   */
  @Test
  public void updateContact_success() throws Exception {
    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.of(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    ServiceAccountEvents event = new ServiceAccountEvents();
    doReturn(event).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(REFERENCE_ID))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil
            .createUpdateServiceAccountContactRequest(sa.getContacts().get(0).getType(), sa.getContacts().get(0).getPriority(),
                sa.getContacts().get(0).getEmail())))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent()).andReturn();
  }

  /**
   * The test use is to update service account contact NoType
   */
  @Test
  public void updateContact_NoType_failed() throws Exception {
    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.ofNullable(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    ServiceAccountEvents event = new ServiceAccountEvents();
    doReturn(event).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(REFERENCE_ID))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil
            .createUpdateServiceAccountContactRequest_NoType(sa.getContacts().get(0).getType(), sa.getContacts().get(0).getPriority(),
                sa.getContacts().get(0).getEmail())))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
  }

  /**
   * The test use is to update service account contact NoPriority
   */
  @Test
  public void updateContact_NoPriority_failed() throws Exception {
    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.ofNullable(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    ServiceAccountEvents event = new ServiceAccountEvents();
    doReturn(event).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    UpdateServiceAccountContactRequest updateServiceAccountContactRequest=ServiceAccountTestUtil
        .createUpdateServiceAccountContactRequest(sa.getContacts().get(0).getType(), sa.getContacts().get(0).getPriority(),
            sa.getContacts().get(0).getEmail());
    updateServiceAccountContactRequest.setPhone("**********");

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(REFERENCE_ID))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(updateServiceAccountContactRequest))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
  }

  /**
   * The test use is to update service account contact invalid phone number
   */
  @Test
  public void updateContact_invalidPhoneNumber_failed() throws Exception {
    int saId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    int saContactId = ThreadLocalRandom.current().nextInt(100, 1000 + 1);
    ServiceAccounts sa = ServiceAccountTestUtil
        .createServiceAccountsWithContactsWithId(REFERENCE_ID, INBOUND_TOKEN_TEST, saId, saContactId);
    doReturn(Optional.ofNullable(sa)).when(serviceAccountsRepository).findByRefId(isA(String.class));

    ServiceAccountEvents event = new ServiceAccountEvents();
    doReturn(event).when(serviceAccountEventsRepository).save(isA(ServiceAccountEvents.class));

    MvcResult mvcResult = mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(REFERENCE_ID))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil
            .createUpdateServiceAccountContactRequest_NoPriority(sa.getContacts().get(0).getType(), sa.getContacts().get(0).getPriority(),
                sa.getContacts().get(0).getEmail())))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
  }

  /**
   * The test update service account contact throw exception resource not found due to missing account
   */
  @Test
  public void updateLimit_refIdNotFound() throws Exception {

    mvc.perform(MockMvcRequestBuilders.put(ServiceAccountTestUtil.buildServiceAccountContactApiUrl(UUID.randomUUID().toString()))
        .header(ServiceAccountAPIConstant.AUTHORIZATION_HEADER, ServiceAccountTestUtil.JWT_TOKEN)
        .header(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345")
        .header(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString())
        .content(JsonUtil.toString(ServiceAccountTestUtil
            .createUpdateServiceAccountContactRequest("TECHNICAL", "SECONDARY",
                "<EMAIL>")))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound()).andReturn();
  }


}
