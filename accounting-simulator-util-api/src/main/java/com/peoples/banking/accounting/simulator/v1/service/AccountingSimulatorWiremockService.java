package com.peoples.banking.accounting.simulator.v1.service;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ELIGIBILITY;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.REVERSAL;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ROOT_SERVICE_URL;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.TRANSACTION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.common.VeryShortIdGenerator;
import com.github.tomakehurst.wiremock.http.ContentTypeHeader;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import com.github.tomakehurst.wiremock.http.HttpHeaders;
import com.peoples.banking.domain.account.model.AccountEligibilityResponse;
import com.peoples.banking.domain.account.model.AccountTransactionResponse;
import com.peoples.banking.domain.account.model.AccountTransactionReversalResponse;
import com.peoples.banking.domain.account.model.EligibilityResultCode;
import com.peoples.banking.domain.account.model.ReversalResultCode;
import com.peoples.banking.domain.account.model.TransactionResultCode;
import javax.annotation.PostConstruct;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;

/**
 * Service that add accounting api stubbing to wiremock server
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class AccountingSimulatorWiremockService {

  public static final int TIMEOUT_DELAY = 20000;
  private final WireMockServer wireMockServer;
  private final ObjectMapper objectMapper = new ObjectMapper();
  private final VeryShortIdGenerator generator = new VeryShortIdGenerator();

  /**
   * add all eligibility stubs using account number patterns
   */
  @PostConstruct
  public void setupEligibility() {
    addEligibilityWithResultCode(".*", EligibilityResultCode.INVALID_ACCOUNT_FORMAT);

    addEligibilityWithResultCode("/383-\\d{5}-\\d{9,10}", EligibilityResultCode.SUCCESS);

    addEligibilityWithResultCode("/621-16001-\\d{12}", EligibilityResultCode.SUCCESS);
    addEligibilityWithResultCode("/621-00001-000000\\d{6}", EligibilityResultCode.SUCCESS);

    addEligibilityWithResultCode("/621-00001-********\\d{3}[02468]", EligibilityResultCode.NOT_AVAILABLE);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.FRAUD_REJECT);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.DECLINED);

    addEligibilityWithResultCode("/621-00001-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_TIMEOUT);

    addEligibilityWithResultCode("/621-00001-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_NOT_AVAILABLE);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.NOT_FOUND);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.COMPLIANCE_REJECT);

    addEligibilityWithResultCode("/621-00001-********\\d{3}[13579]", EligibilityResultCode.FROZEN);

    addEligibilityWithResultCode("/621-00001-********\\d{3}[13579]", EligibilityResultCode.NOT_AVAILABLE_TEMPORARY);

    addEligibilityWithResultCodeAndDelay("/621-00001-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_TIMEOUT, TIMEOUT_DELAY);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.DECLINED);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.LIMIT_EXCEEDED);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.NOT_FOUND);

    addEligibilityWithResultCode("/621-00001-********\\d{4}", EligibilityResultCode.CURRENCY_NOT_SUPPORTED);

    addEligibilityWithResultCode("/621-20002-000000\\d{6}", EligibilityResultCode.SUCCESS);

    addEligibilityWithResultCode("/621-20002-********\\d{3}[02468]", EligibilityResultCode.NOT_AVAILABLE);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.FRAUD_REJECT);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.DECLINED);

    addEligibilityWithResultCode("/621-20002-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_TIMEOUT);

    addEligibilityWithResultCode("/621-20002-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_NOT_AVAILABLE);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.NOT_FOUND);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.COMPLIANCE_REJECT);

    addEligibilityWithResultCode("/621-20002-********\\d{3}[13579]", EligibilityResultCode.FROZEN);

    addEligibilityWithResultCode("/621-20002-********\\d{3}[13579]", EligibilityResultCode.NOT_AVAILABLE_TEMPORARY);

    addEligibilityWithResultCodeAndDelay("/621-20002-********\\d{3}[13579]", EligibilityResultCode.SYSTEM_TIMEOUT, TIMEOUT_DELAY);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.DECLINED);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.LIMIT_EXCEEDED);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.NOT_FOUND);

    addEligibilityWithResultCode("/621-20002-********\\d{4}", EligibilityResultCode.CURRENCY_NOT_SUPPORTED);

    addSuccessForAllEligibility();

  }

  /**
   * add all transaction stubbing using account number patterns
   */
  @PostConstruct
  public void setupTransaction() {
    addTransactionWithResultCode("/383-\\d{5}-\\d{9,10}", TransactionResultCode.SUCCESS);

    addTransactionWithResultCode("/621-16001-\\d{12}", TransactionResultCode.SUCCESS);

    addTransactionWithResultCode("/621-00001-000000\\d{6}", TransactionResultCode.SUCCESS);

    addTransactionWithResultCode("/621-00001-********\\d{3}[13579]", TransactionResultCode.NOT_AVAILABLE);

    addTransactionWithResultCode("/621-00001-********\\d{3}[02468]", TransactionResultCode.NOT_AVAILABLE_TEMPORARY);

    addTransactionWithResultCode("/621-00001-********\\d{3}[02468]", TransactionResultCode.SYSTEM_TIMEOUT);

    addTransactionWithResultCode("/621-00001-********\\d{3}[02468]", TransactionResultCode.SYSTEM_NOT_AVAILABLE);

    addTransactionWithResultCode("/621-00001-********\\d{3}[02468]", TransactionResultCode.FROZEN);

    addTransactionWithResultCodeAndDelay("/621-00001-********\\d{3}[02468]", TransactionResultCode.SYSTEM_TIMEOUT, TIMEOUT_DELAY);

    addTransactionWithResultCode("/621-00001-********\\d{4}", TransactionResultCode.INVALID_TRANSACTION_DATA);

    addTransactionWithResultCode("/621-20002-000000\\d{6}", TransactionResultCode.SUCCESS);

    addTransactionWithResultCode("/621-20002-********\\d{3}[13579]", TransactionResultCode.NOT_AVAILABLE);

    addTransactionWithResultCode("/621-20002-********\\d{3}[02468]", TransactionResultCode.NOT_AVAILABLE_TEMPORARY);

    addTransactionWithResultCode("/621-20002-********\\d{3}[02468]", TransactionResultCode.SYSTEM_TIMEOUT);

    addTransactionWithResultCode("/621-20002-********\\d{3}[02468]", TransactionResultCode.SYSTEM_NOT_AVAILABLE);

    addTransactionWithResultCode("/621-20002-********\\d{3}[02468]", TransactionResultCode.FROZEN);

    addTransactionWithResultCodeAndDelay("/621-20002-********\\d{3}[02468]", TransactionResultCode.SYSTEM_TIMEOUT, TIMEOUT_DELAY);

    addTransactionWithResultCode("/621-20002-********\\d{4}", TransactionResultCode.INVALID_TRANSACTION_DATA);

    addSuccessForAllTransaction();

  }

  /**
   * add all reversal stubbing using account number patterns
   */
  @PostConstruct
  public void setupReversal() {
    addReversalWithResultCode("/383-\\d{5}-\\d{9,10}", "/\\w{16,36}", ReversalResultCode.SUCCESS);

    addReversalWithResultCode("/621-16001-\\d{12}", "/\\w{16,36}", ReversalResultCode.SUCCESS);
    addReversalWithResultCode("/621-00001-000000\\d{6}", "/\\w{16,36}", ReversalResultCode.SUCCESS);
    addReversalWithResultCode("/621-00001-********\\d{4}", "/\\w{16,36}", ReversalResultCode.NOT_AVAILABLE_TEMPORARY);
    addReversalWithResultCode("/621-00001-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.SYSTEM_TIMEOUT);
    addReversalWithResultCode("/621-00001-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.SYSTEM_NOT_AVAILABLE);
    addReversalWithResultCode("/621-00001-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.NOT_FOUND);
    addReversalWithResultCode("/621-00001-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.DECLINED);
    addReversalWithResultCode("/621-20002-********\\d{4}", "/\\w{16,36}", ReversalResultCode.NOT_AVAILABLE_TEMPORARY);
    addReversalWithResultCode("/621-20002-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.SYSTEM_TIMEOUT);
    addReversalWithResultCode("/621-20002-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.SYSTEM_NOT_AVAILABLE);
    addReversalWithResultCode("/621-20002-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.NOT_FOUND);
    addReversalWithResultCode("/621-20002-********\\d{3}[02468]", "/\\w{16,36}", ReversalResultCode.DECLINED);

  }

  /**
   * add specific eligibility pattern stubbing
   *
   * @param accountPattern - account number pattern
   * @param resultCode     - result code to use for building response
   */
  private void addEligibilityWithResultCode(String accountPattern, EligibilityResultCode resultCode) {
    addEligibilityWithResultCodeAndDelay(accountPattern, resultCode, 0);
  }

  /**
   * add specific eligibility pattern stubbing
   *
   * @param accountPattern - account number pattern
   * @param resultCode     - result code to use for building response
   * @param delay          - delay for response
   */
  private void addEligibilityWithResultCodeAndDelay(String accountPattern, EligibilityResultCode resultCode, int delay) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + ELIGIBILITY))
        //eligibility response with specified result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withFixedDelay(delay)
            .withBody(eligibilityResponseWithResultCode(resultCode))));

    addNullResultCodeEligibility(accountPattern);
    addEmptyResultCodeEligibility(accountPattern, resultCode);
    addEmptyAccountNameEligibility(accountPattern, resultCode);
    addNullAccountNameEligibility(accountPattern, resultCode);
  }

  /**
   * add success for second and more request with same payment reference id stubbing for eligibility
   *
   */
  private void addNullResultCodeEligibility(String accountPattern) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + ELIGIBILITY))
        .andMatching(new AccountNegativeScenariosRequestMatcher(ELIGIBILITY, "RESPONSE-NULL-resultCode"))
        //eligibility response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(eligibilityResponseWithResultCode(null))));
  }

  private void addEmptyResultCodeEligibility(String accountPattern, EligibilityResultCode resultCode) {
    String response = eligibilityResponseWithResultCode(resultCode);
    response = response.replace("COMPLIANCE_REJECT", "");
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + ELIGIBILITY))
        .andMatching(new AccountNegativeScenariosRequestMatcher(ELIGIBILITY, "RESPONSE-EMPTY-resultCode"))
        //eligibility response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(response)));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for eligibility
   *
   */
  private void addEmptyAccountNameEligibility(String accountPattern, EligibilityResultCode resultCode) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + ELIGIBILITY))
        .andMatching(new AccountNegativeScenariosRequestMatcher(ELIGIBILITY, "RESPONSE-EMPTY-accountName"))
        //eligibility response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(eligibilityResponseWithAccountName("", resultCode))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for eligibility
   *
   */
  private void addNullAccountNameEligibility(String accountPattern, EligibilityResultCode resultCode) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + ELIGIBILITY))
        .andMatching(new AccountNegativeScenariosRequestMatcher(ELIGIBILITY, "RESPONSE-NULL-accountName"))
        //eligibility response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(eligibilityResponseWithAccountName(null, resultCode))));
  }

  @SneakyThrows
  private void addEmptyRefIdReversal(String accountPattern, ReversalResultCode resultCode) {
    AccountTransactionReversalResponse response = new AccountTransactionReversalResponse();
    response.setRefId("");
    response.setResultCode(resultCode);
    response.additionalInformation("test AdditionalInformation");

    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + "/.*" + REVERSAL))
        .andMatching(new AccountNegativeScenariosRequestMatcher(REVERSAL, "RESPONSE-EMPTY-refId"))
        //reversal response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(objectMapper.writeValueAsString(response))));
  }

  @SneakyThrows
  private void addNullRefIdReversal(String accountPattern) {
    AccountTransactionReversalResponse response = new AccountTransactionReversalResponse();
    response.setRefId(null);
    response.setResultCode(ReversalResultCode.SUCCESS);
    response.additionalInformation("test AdditionalInformation");
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + "/.*" + REVERSAL))
        .andMatching(new AccountNegativeScenariosRequestMatcher(REVERSAL, "RESPONSE-NULL-refId"))
        //reversal response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(objectMapper.writeValueAsString(response))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for eligibility
   *
   */
  private void addSuccessForAllEligibility() {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + "/.*" + ELIGIBILITY))
        .andMatching(new AccountRequestMatcher(ELIGIBILITY))
        //eligibility response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(eligibilityResponseWithResultCode(EligibilityResultCode.SUCCESS))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for transaction
   *
   */
  private void addSuccessForAllTransaction() {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + "/.*" + TRANSACTION))
        .andMatching(new AccountRequestMatcher(TRANSACTION))
        //transaction response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(transactionResponseWithResultCode(TransactionResultCode.SUCCESS))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for transaction
   *
   */
  private void addResultCodeNullTransaction(String accountPattern) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + TRANSACTION))
        .andMatching(new AccountNegativeScenariosRequestMatcher(TRANSACTION, "RESPONSE-NULL-resultCode"))
        //transaction response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(transactionResponseWithResultCode(null))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for transaction
   *
   */
  private void addResultCodeEmptyTransaction(String accountPattern) {
    String response = transactionResponseWithResultCode(TransactionResultCode.INVALID_TRANSACTION_DATA);
    response = response.replace("INVALID_TRANSACTION_DATA", "");
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + TRANSACTION))
        .andMatching(new AccountNegativeScenariosRequestMatcher(TRANSACTION, "RESPONSE-EMPTY-resultCode"))
        //transaction response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(response)));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for transaction
   *
   */
  private void addRefIdEmptyResponseTransaction(String accountPattern, TransactionResultCode resultCode) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + TRANSACTION))
        .andMatching(new AccountNegativeScenariosRequestMatcher(TRANSACTION, "RESPONSE-EMPTY-refId"))
        //transaction response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(transactionResponseWithRefId("", resultCode))));
  }

  /**
   * add success for second and more request with same payment reference id stubbing for transaction
   *
   */
  private void addRefIdNullResponseTransaction(String accountPattern, TransactionResultCode resultCode) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + TRANSACTION))
        .andMatching(new AccountNegativeScenariosRequestMatcher(TRANSACTION, "RESPONSE-NULL-refId"))
        //transaction response with success result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(transactionResponseWithRefId(null, resultCode))));
  }

  /**
   * add specific account pattern stubbing
   *
   * @param accountPattern - stubbing pattern
   * @param resultCode     - result code to use for building response
   */
  private void addTransactionWithResultCode(String accountPattern, TransactionResultCode resultCode) {
    addTransactionWithResultCodeAndDelay(accountPattern, resultCode, 0);
  }

  /**
   * add specific account pattern stubbing
   *
   * @param accountPattern - stubbing pattern
   * @param resultCode     - result code to use for building response
   * @param delay          - delay for response
   */
  private void addTransactionWithResultCodeAndDelay(String accountPattern, TransactionResultCode resultCode, int delay) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + TRANSACTION))
        //transaction response with specified result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withFixedDelay(delay)
            .withBody(transactionResponseWithResultCode(resultCode))));

    addResultCodeNullTransaction(accountPattern);
    addResultCodeEmptyTransaction(accountPattern);
    addRefIdEmptyResponseTransaction(accountPattern, resultCode);
    addRefIdNullResponseTransaction(accountPattern, resultCode);
  }

  /**
   * add specific account pattern stubbing
   *
   * @param accountPattern       - stubbing pattern
   * @param transactionIdPattern -- stubbing pattern
   * @param resultCode           - result code to use for building response
   */
  private void addReversalWithResultCode(String accountPattern, String transactionIdPattern, ReversalResultCode resultCode) {
    wireMockServer.stubFor(post(urlMatching(ROOT_SERVICE_URL + accountPattern + transactionIdPattern + REVERSAL))
        //transaction response with specified result code
        .willReturn(aResponse()
            .withHeaders(getResponseHeaders())
            .withBody(reversalResponseWithResultCode(resultCode))));
    addNullRefIdReversal(accountPattern);
    addEmptyRefIdReversal(accountPattern, resultCode);
  }


  /**
   * Build AccountEligibilityResponse with specified result code and generated account name and convert to String using objectMapper
   *
   * @param resultCode - result code to use
   * @return - new AccountEligibilityResponse with specified result code in json
   */
  @SneakyThrows
  private String eligibilityResponseWithResultCode(EligibilityResultCode resultCode) {
    AccountEligibilityResponse response = buildAccountEligibilityResponse(resultCode);
    return objectMapper.writeValueAsString(response);
  }

  @SneakyThrows
  private String eligibilityResponseWithAccountName(String accountName, EligibilityResultCode resultCode) {
    AccountEligibilityResponse response = buildAccountEligibilityResponse(resultCode);
    response.setAccountName(accountName);
    return objectMapper.writeValueAsString(response);
  }

  private AccountEligibilityResponse buildAccountEligibilityResponse(EligibilityResultCode resultCode) {
    AccountEligibilityResponse response = new AccountEligibilityResponse();
    response.setResultCode(resultCode);
    response.setAccountName("Savings Account eSAV" + generator.generate());
    response.setAdditionalInformation("test AdditionalInformation");
    return response;
  }

  /**
   * Build AccountTransactionResponse with specified result code and generated refId and convert to String using objectMapper
   *
   * @param resultCode - result code to use
   * @return - new AccountTransactionResponse with specified result code in json
   */
  @SneakyThrows
  private String transactionResponseWithResultCode(TransactionResultCode resultCode) {
    AccountTransactionResponse response = buildAccountTransactionResponse(resultCode);
    return objectMapper.writeValueAsString(response);
  }

  @SneakyThrows
  private String transactionResponseWithRefId(String refId, TransactionResultCode resultCode) {
    AccountTransactionResponse response = buildAccountTransactionResponse(resultCode);
    response.setRefId(refId);
    return objectMapper.writeValueAsString(response);
  }

  private AccountTransactionResponse buildAccountTransactionResponse(TransactionResultCode resultCode) {
    AccountTransactionResponse response = new AccountTransactionResponse();
    response.setResultCode(resultCode);
    response.setRefId(RandomStringUtils.randomAlphabetic(16) + "-" + generator.generate());
    response.setAdditionalInformation("test AdditionalInformation");
    return response;
  }

  /**
   * Build AccountTransactionReversalResponse with specified result code and generated refId and convert to String using objectMapper
   *
   * @param resultCode - result code to use
   * @return - new AccountTransactionReversalResponse with specified result code in json
   */
  @SneakyThrows
  private String reversalResponseWithResultCode(ReversalResultCode resultCode) {
    AccountTransactionReversalResponse response = new AccountTransactionReversalResponse();
    response.setResultCode(resultCode);
    response.setRefId(RandomStringUtils.randomAlphabetic(16) + "-" + generator.generate());
    response.additionalInformation("test AdditionalInformation");
    return objectMapper.writeValueAsString(response);
  }

  /**
   * construct response headers with values from original request and generated random ones
   *
   * @return - wiremock HttpHeaders object
   */
  private HttpHeaders getResponseHeaders() {
    HttpHeader interactionHeader = new HttpHeader("x-pg-interaction-id", "{{request.headers.x-pg-interaction-id}}");
    String correspondentId = generator.generate();
    HttpHeader correspondentHeader = new HttpHeader("x-pg-correspondent-id", correspondentId);

    return new HttpHeaders(new ContentTypeHeader(ContentType.APPLICATION_JSON.getMimeType()),
        interactionHeader, correspondentHeader);
  }
}
