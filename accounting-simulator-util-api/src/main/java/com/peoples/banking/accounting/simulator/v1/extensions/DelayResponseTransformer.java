package com.peoples.banking.accounting.simulator.v1.extensions;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.common.FileSource;
import com.github.tomakehurst.wiremock.extension.Parameters;
import com.github.tomakehurst.wiremock.extension.ResponseTransformer;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.peoples.banking.accounting.simulator.v1.service.AccountNegativeScenariosRequestMatcher.JSON_PROPERTY_END_TO_END_ID;


/**
 * Custom implementation of response template transformer filling variables from global cache to templates
 */
@Log4j2
public class DelayResponseTransformer extends ResponseTransformer {

  public static final String DELAY = "DELAY-";
  private final ObjectMapper mapper = new ObjectMapper();

  @Override
  public Response transform(Request request, Response response, FileSource fileSource, Parameters parameters) {
    int delayMs = 0;

    String body = request.getBodyAsString();
    Map<String, Object> objectMap = new HashMap<>();
    try {
      if (!StringUtils.isEmpty(body)) {
        objectMap = mapper.readValue(body, Map.class);
      }
    } catch (JsonProcessingException e) {
      log.info("failed to read object map", e);
    }

    String endToEndId = (String) objectMap.get(JSON_PROPERTY_END_TO_END_ID);
    if (endToEndId != null && endToEndId.contains(DELAY)) {
      delayMs = Integer.parseInt(endToEndId.substring(DELAY.length(), endToEndId.indexOf("ms")).trim());
    }

    return Response.Builder.like(response).but().incrementInitialDelay(delayMs).build();
  }

  @Override
  public String getName() {
    return "custom-delay-transformer";
  }
}