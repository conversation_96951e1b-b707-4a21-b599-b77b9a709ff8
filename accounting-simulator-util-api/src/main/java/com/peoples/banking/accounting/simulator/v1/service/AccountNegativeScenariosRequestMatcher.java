package com.peoples.banking.accounting.simulator.v1.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.matching.MatchResult;
import com.github.tomakehurst.wiremock.matching.RequestMatcher;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;


@Log4j2
public class AccountNegativeScenariosRequestMatcher extends RequestMatcher {

  public static final String JSON_PROPERTY_END_TO_END_ID = "end_to_end_id";

  public final String type;
  public final String matchPattern;
  private static final ObjectMapper mapper = new ObjectMapper();

  public AccountNegativeScenariosRequestMatcher(String type, String matchPattern) {
    this.type = type;
    this.matchPattern = matchPattern;
  }

  @Override
  public String getName() {
    return type;
  }

  @Override
  public MatchResult match(Request request) {
    String url = request.getUrl();
    if (!url.endsWith(type)) {
      log.debug("skip matcher because it not relevant for type");
      return MatchResult.noMatch();
    }
    String body = request.getBodyAsString();
    Map<String, Object> objectMap = new HashMap<>();
    try {
      if (!StringUtils.isEmpty(body)) {
        objectMap = mapper.readValue(body, Map.class);
      }
    } catch (JsonProcessingException e) {
      log.info("failed to read object map", e);
    }

    String endToEndId = (String) objectMap.get(JSON_PROPERTY_END_TO_END_ID);
    if (endToEndId != null && endToEndId.contains(matchPattern)) {
      return MatchResult.exactMatch();
    }
    return MatchResult.noMatch();
  }


}
