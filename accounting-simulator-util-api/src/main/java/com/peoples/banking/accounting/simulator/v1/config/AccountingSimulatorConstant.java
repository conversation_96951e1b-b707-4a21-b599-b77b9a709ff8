package com.peoples.banking.accounting.simulator.v1.config;

public class AccountingSimulatorConstant {

  private AccountingSimulatorConstant() {
    throw new AssertionError();
  }

  public static final String ROOT_SERVICE_URL = "/account";
  public static final String ELIGIBILITY = "/eligibility";
  public static final String TRANSACTION = "/transaction";
  public static final String REVERSAL = "/reversal";
  public static final String ACCOUNT_NUMBER = "accountNumber";
  public static final String TRANSACTION_ID = "transactionId";

  public static final String ELIGIBILITY_URL = ROOT_SERVICE_URL + "/{" + ACCOUNT_NUMBER + "}" + ELIGIBILITY;
  public static final String TRANSACTION_URL = ROOT_SERVICE_URL + "/{" + ACCOUNT_NUMBER + "}" + TRANSACTION;
  public static final String REVERSAL_URL = ROOT_SERVICE_URL + "/{" + ACCOUNT_NUMBER + "}/{" + TRANSACTION_ID + "}" + REVERS<PERSON>;


}
