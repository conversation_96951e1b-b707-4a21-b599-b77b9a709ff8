package com.peoples.banking.accounting.simulator.v1.config;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.extension.responsetemplating.ResponseTemplateTransformer;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
import com.peoples.banking.accounting.simulator.v1.extensions.DelayResponseTransformer;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Log4j2
@Configuration
public class WireMockServerConfiguration {

  /**
   * server http port
   */
  @Value("${server.http.port:8080}")
  public int SERVER_PORT;
  /**
   * path for template files (wiremock adding __files folder to search templates)
   */
  public static final String RESOURCES_PATH = "src/main/resources/";

  @Bean
  public WireMockServer wireMockServer() {
    //Heart of all application mocks, should be started before use
    WireMockServer wireMockServer = new WireMockServer(wireMockConfig()
        .port(SERVER_PORT).withRootDirectory(RESOURCES_PATH)
        .extensions(new ResponseTemplateTransformer(true), new DelayResponseTransformer()));

    wireMockServer.addMockServiceRequestListener(WireMockServerConfiguration::requestReceived);
    wireMockServer.start();
    return wireMockServer;
  }

  /**
   * logs all information from supplied WireMock request and response objects.
   *
   * @param inRequest  information from received request.
   * @param inResponse response object
   */
  protected static void requestReceived(Request inRequest, Response inResponse) {
    log.info("WireMock request at URL: {}", inRequest.getAbsoluteUrl());
    log.info("WireMock request headers: \n{}", inRequest.getHeaders());
    log.info("WireMock request body: \n{}", inRequest.getBodyAsString());

    log.info("WireMock response body: \n{}", inResponse.getBodyAsString());
    log.info("WireMock response headers: \n{}", inResponse.getHeaders());
  }
}
