package com.peoples.banking.accounting.simulator.v1.service;

import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ELIGIBILITY;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ROOT_SERVICE_URL;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.TRANSACTION;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.matching.MatchResult;
import com.github.tomakehurst.wiremock.matching.RequestMatcher;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class AccountRequestMatcher extends RequestMatcher {

  public static final String JSON_PROPERTY_REQUEST_PAYMENT_REF_ID = "request_payment_ref_id";
  public static final String JSON_PROPERTY_AMOUNT = "amount";
  public static final Double FAILING_AMOUNT = 3.16;

  private final Cache<String, Integer> requestCountMap = Caffeine.newBuilder()
          .expireAfterWrite(12, TimeUnit.HOURS)
          .build();

  public final String type;
  private final ObjectMapper mapper = new ObjectMapper();

  public AccountRequestMatcher(String type) {
    this.type = type;
  }

  @Override
  public String getName() {
    return type;
  }

  @Override
  public MatchResult match(Request request) {
    String url = request.getUrl();
    if (!url.endsWith(type)) {
      log.debug("skip matcher because it not relevant for type");
      return MatchResult.noMatch();
    }
    String accountNumber = extractAccountNumber(url);
    String body = request.getBodyAsString();
    Map<String, Object> objectMap = new HashMap<>();
    try {
      if (!StringUtils.isEmpty(body)) {
        objectMap = mapper.readValue(body, Map.class);
      }
    } catch (JsonProcessingException e) {
      log.info("failed to read object map", e);
    }
    String paymentReferenceId = extractPaymentReferenceId(objectMap);
    Object value = objectMap.get(JSON_PROPERTY_AMOUNT);
    Double amount = null;
    if (value instanceof Double) {
      amount = (Double) value;
    } else if (value instanceof Integer) {
      amount = Double.valueOf(value.toString()) ;
    } else if (value instanceof String) {
      amount = Double.parseDouble((String)(value));
    }
    if (!FAILING_AMOUNT.equals(amount)) {
      return MatchResult.noMatch();
    }

    String key = buildMapKey(accountNumber, paymentReferenceId);
    Integer requestCount = requestCountMap.getIfPresent(key);
    if (requestCount == null) {
      requestCountMap.put(key, 1);
      return MatchResult.noMatch();
    }
    requestCountMap.put(key, ++requestCount);
    return MatchResult.exactMatch();
  }

  private String extractPaymentReferenceId(Map<String, Object> objectMap ) {
      return (String)objectMap.get(JSON_PROPERTY_REQUEST_PAYMENT_REF_ID);
  }

  private String extractAccountNumber(String url) {
    String result = "";
    if (url.endsWith(TRANSACTION)) {
      result = url.substring(ROOT_SERVICE_URL.length(), url.indexOf(TRANSACTION));
      return result;
    }
    if (url.endsWith(ELIGIBILITY)) {
      result = url.substring(ROOT_SERVICE_URL.length(), url.indexOf(ELIGIBILITY));
    }
    if (result.length() > 1) {
      result = result.substring(1);
    }
    return result;
  }

  private String buildMapKey(String accountNumber, String paymentReferenceId) {
    return accountNumber + "_" + paymentReferenceId;
  }

}
