package com.peoples.banking.accounting.simulator.v1.controller;

import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ACCOUNT_NUMBER;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ELIGIBILITY;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.REVERSAL;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.ROOT_SERVICE_URL;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.TRANSACTION;
import static com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant.TRANSACTION_ID;
import static org.junit.jupiter.api.Assertions.*;

import com.github.tomakehurst.wiremock.common.VeryShortIdGenerator;
import com.peoples.banking.accounting.simulator.v1.config.AccountingSimulatorConstant;
import com.peoples.banking.domain.account.model.*;

import java.math.BigDecimal;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.client.RestClientException;

@SpringBootTest
@TestPropertySource("classpath:application.properties")
public class AccountingSimulatorControllerTest {

  @Value("${server.host:http://localhost:}")
  public String LOCAL_ADDRESS;

  @Value("${server.http.port}")
  protected int LOCAL_PORT;

  private static final String ACCOUNT_INVALID_FORMAT_1 = "1-2-3";
  private static final String ACCOUNT_INVALID_FORMAT_2 = "11-22-33";
  private static final String ACCOUNT_INVALID_FORMAT_3 = "111-222-333";
  private static final String ACCOUNT_INVALID_FORMAT_4 = "111-2222-33333";
  private static final String ACCOUNT_INVALID_FORMAT_5 = "111-22222-333333";
  private static final String ACCOUNT_INVALID_FORMAT_6 = "111-11111-3333333";
  private static final String ACCOUNT_INVALID_FORMAT_7 = "111-11111-********";
  private static final String ACCOUNT_INVALID_FORMAT_8 = "111-11111-********3";
  private static final String ACCOUNT_INVALID_FORMAT_9 = "111-11111-********33";
  private static final String ACCOUNT_INVALID_FORMAT_10 = "111-11111-********333";
  private static final String ACCOUNT_INVALID_FORMAT_11 = "111-11111-************";
  private static final String ACCOUNT_INVALID_FORMAT_12 = "621-11111-************";
  private static final String ACCOUNT_INVALID_FORMAT_13 = "621-22222-************";
  private static final String ACCOUNT_INVALID_FORMAT_14 = "621-99999-************";
  public String HOSTNAME;
  private static final String ACCOUNT_INVALID_FORMAT_15 = "383-10001-************";
  private static final String ACCOUNT_INVALID_FORMAT_16 = "383-00001-************";
  public static final BigDecimal AMOUNT = new BigDecimal("10.10");
  public static final String REF_TRANSACTION_ID = "*********1012141618";

  private final TestRestTemplate testRestTemplate = new TestRestTemplate();
  private final VeryShortIdGenerator generator = new VeryShortIdGenerator();
  private AccountEligibilityRequest request;

  @BeforeEach
  public void setup () {
    request = new AccountEligibilityRequest();
    request.setCurrency(Currency.CAD);
    request.setTransactionType(TransactionTypeCode.CREDIT);
    request.setRequestPaymentRefId(UUID.randomUUID().toString());
    request.setAmount(AMOUNT);
    HOSTNAME = LOCAL_ADDRESS + LOCAL_PORT;
  }

  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  public void accountEligibility_nullOrEmpty(String accountNumber) {
    AccountEligibilityResponse response = testRestTemplate
        .postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), null, AccountEligibilityResponse.class);
    // unit under test
    assertNotNull(response);
    assertEquals(EligibilityResultCode.INVALID_ACCOUNT_FORMAT, response.getResultCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      ACCOUNT_INVALID_FORMAT_1, ACCOUNT_INVALID_FORMAT_2, ACCOUNT_INVALID_FORMAT_3,
      ACCOUNT_INVALID_FORMAT_4, ACCOUNT_INVALID_FORMAT_5, ACCOUNT_INVALID_FORMAT_6,
      ACCOUNT_INVALID_FORMAT_7, ACCOUNT_INVALID_FORMAT_8, ACCOUNT_INVALID_FORMAT_9,
      ACCOUNT_INVALID_FORMAT_10, ACCOUNT_INVALID_FORMAT_11, ACCOUNT_INVALID_FORMAT_12,
      ACCOUNT_INVALID_FORMAT_13, ACCOUNT_INVALID_FORMAT_14, ACCOUNT_INVALID_FORMAT_15,
      ACCOUNT_INVALID_FORMAT_16})
  public void accountEligibility_invalidAccountNumber(String accountNumber) {
    AccountEligibilityResponse response = testRestTemplate
        .postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), null, AccountEligibilityResponse.class);
    assertNotNull(response);
    assertEquals(EligibilityResultCode.INVALID_ACCOUNT_FORMAT, response.getResultCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "621-12111-************",
      "621-00002-************"
  })
  void accountEligibility_invalidTransitNumber(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), null, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.INVALID_ACCOUNT_FORMAT, response.getResultCode());

    //trying to call second time to validate it will still fail
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    assertNotNull(response);
    assertEquals(EligibilityResultCode.INVALID_ACCOUNT_FORMAT, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SUCCESS
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountEligibility_null_result_code_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      AccountEligibilityRequest request = new AccountEligibilityRequest();
      request.setEndToEndId("RESPONSE-NULL-resultCode");
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNull(response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SUCCESS
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountEligibility_null_accountName_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      AccountEligibilityRequest request = new AccountEligibilityRequest();
      request.setEndToEndId("RESPONSE-NULL-accountName");
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNull(response.getAccountName());
    assertEquals(EligibilityResultCode.SUCCESS, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SUCCESS
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountEligibility_empty_accountName_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      AccountEligibilityRequest request = new AccountEligibilityRequest();
      request.setEndToEndId("RESPONSE-EMPTY-accountName");
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals("", response.getAccountName());
    assertEquals(EligibilityResultCode.SUCCESS, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SUCCESS with delay, to see big delay - change endToEndId
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountEligibility_delay_accountName_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      AccountEligibilityRequest request = new AccountEligibilityRequest();
      request.setEndToEndId("DELAY-1ms");
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNotNull(response.getAccountName());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SUCCESS
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "383-12345-**********",
      "383-12345-*********",
      "383-98765-**********",
      "383-54321-**********",
      "383-67890-*********",
      "383-11111-**********",
      "383-00000-*********",
      "383-00000-**********",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-16001-************",
      "621-16001-**********12",
      "621-16001-000*********",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
  })
  void accountEligibility_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), null, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.SUCCESS, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=NOT_FOUND
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_NOT_FOUND(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_FOUND, response.getResultCode());


    //trying to call second time to validate it will still fail
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_FOUND, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountTransactionReversal
   * <pre>
   *   RC=NOT_FOUND
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountReversal_NOT_FOUND(String accountNumber) {
    // unit under test
    AccountTransactionReversalResponse response = null;
    request.setEndToEndId("RESPONSE-EMPTY-refId");
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(ReversalResultCode.NOT_FOUND, response.getResultCode());

    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(ReversalResultCode.NOT_FOUND, response.getResultCode());

  }

  /**
   * Unit under test is AccountAdapter::accountTransactionReversal
   * <pre>
   *   RC=DECLINED
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountReversal_DECLINED(String accountNumber) {
    // unit under test
    AccountTransactionReversalResponse response = null;
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(ReversalResultCode.DECLINED, response.getResultCode());
   try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(ReversalResultCode.DECLINED, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=NOT_AVAILABLE
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_NOT_AVAILABLE(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_AVAILABLE, response.getResultCode());

    //trying to call second time to validate it will still fail
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_AVAILABLE, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=NOT_AVAILABLE
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-000000030009",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-000000030009"
  })
  void accountTransaction_NOT_AVAILABLE(String accountNumber) {
    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.NOT_AVAILABLE, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=NOT_AVAILABLE_TEMPORARY
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_NOT_AVAILABLE_TEMPORARY(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_AVAILABLE_TEMPORARY, response.getResultCode());
   try {
     response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.NOT_AVAILABLE_TEMPORARY, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=FROZEN
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_FROZEN(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FROZEN, response.getResultCode());
   try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FROZEN, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FROZEN, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=CREDIT_REJECTED
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_CREDIT_REJECTED(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.DECLINED, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=LIMIT_EXCEEDED
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_LIMIT_EXCEEDED(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.LIMIT_EXCEEDED, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.LIMIT_EXCEEDED, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=CURRENCY_NOT_SUPPORTED
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_CURRENCY_NOT_SUPPORTED(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.CURRENCY_NOT_SUPPORTED, response.getResultCode());
   try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.CURRENCY_NOT_SUPPORTED, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SYSTEM_TIMEOUT
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  //very long test because of timeouts
  //@ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_SYSTEM_TIMEOUT(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response;
    // unit under test

    response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);

    assertEquals(EligibilityResultCode.SYSTEM_TIMEOUT, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=SYSTEM_NOT_AVAILABLE
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_SYSTEM_NOT_AVAILABLE(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.SYSTEM_NOT_AVAILABLE, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.SYSTEM_NOT_AVAILABLE, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=DECLINED
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_DECLINED(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.DECLINED, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.DECLINED, response.getResultCode());
   try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.DECLINED, response.getResultCode());
  }
  @Test
  void accountTransaction_INVALID_TRANSACTION_DATA(){
    String accountNumber = "621-00001-************";


    //setup for account eligibility
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), null, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    //Setup for testing transaction eligibility
    AccountTransactionRequest request1 = buildAccountTransactionRequest();

    // unit under test
    AccountTransactionResponse response1 = null;
    try {
      response1 = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request1, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions for transactionEligibility
    assertNotNull(response1);
    assertEquals(TransactionResultCode.INVALID_TRANSACTION_DATA, response1.getResultCode());

    // assertions for accountEligibility
    assertNotNull(response);
    assertEquals(EligibilityResultCode.SUCCESS, response.getResultCode());

  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=FRAUD_REJECT
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_FRAUD_REJECT(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FRAUD_REJECT, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FRAUD_REJECT, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=FRAUD_REJECT
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_FRAUD_REJECT_then_success(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    request.setAmount(BigDecimal.valueOf(3.16));
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.FRAUD_REJECT, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.SUCCESS, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   RC=COMPLIANCE_REJECT
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_COMPLIANCE_REJECT(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.COMPLIANCE_REJECT, response.getResultCode());
   try {
     response = testRestTemplate.postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(EligibilityResultCode.COMPLIANCE_REJECT, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountEligibility
   * <pre>
   *   timeout exception from accounting system
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @SneakyThrows
  //Commented because its long test
  //@ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountEligibility_timeout(String accountNumber) {
    // unit under test
    AccountEligibilityResponse response = testRestTemplate
        .postForObject(HOSTNAME + buildEligibilityUrl(accountNumber), request, AccountEligibilityResponse.class);
    assertEquals(EligibilityResultCode.SYSTEM_TIMEOUT, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=FROZEN
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountTransaction_FROZEN(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-EMPTY-refId");

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.FROZEN, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.FROZEN, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SYSTEM_TIMEOUT
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @SneakyThrows
  //commented because its long test
  //@ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountTransaction_SYSTEM_TIMEOUT(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();

    // unit under test

    AccountTransactionResponse response = testRestTemplate
        .postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    assertEquals(TransactionResultCode.SYSTEM_TIMEOUT, response.getResultCode());
  }


  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SYSTEM_NOT_AVAILABLE
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountTransaction_SYSTEM_NOT_AVAILABLE(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.SYSTEM_NOT_AVAILABLE, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.SYSTEM_NOT_AVAILABLE, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.SYSTEM_NOT_AVAILABLE, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   timeout exception from accounting system
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @SneakyThrows
  //commented because of long test
  //@ParameterizedTest
  @ValueSource(strings = {
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-00001-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************",
      "621-20002-************"
  })
  void accountTransaction_timeout(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    // unit under test
    AccountTransactionResponse response = testRestTemplate
        .postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    assertEquals(TransactionResultCode.SYSTEM_TIMEOUT, response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "383-12345-**********",
      "383-12345-*********",
      "383-98765-**********",
      "383-54321-**********",
      "383-67890-*********",
      "383-11111-**********",
      "383-00000-*********",
      "383-00000-**********",
      "621-16001-************",
      "621-16001-**********12",
      "621-16001-000*********",
  })
  void accountTransaction_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.SUCCESS, response.getResultCode());
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(TransactionResultCode.SUCCESS, response.getResultCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "383-12345-**********",
      "383-12345-*********",
      "383-98765-**********",
      "383-54321-**********",
      "383-67890-*********",
      "383-11111-**********",
      "383-00000-*********",
      "383-00000-**********",
      "621-16001-************",
      "621-16001-**********12",
      "621-16001-000*********",
  })
  void accountTransactionalReversal_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-EMPTY-accountName");
    // unit under test
    AccountTransactionReversalResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals(ReversalResultCode.SUCCESS, response.getResultCode());
  }

  private AccountTransactionRequest buildAccountTransactionRequest() {
    AccountTransactionRequest request = new AccountTransactionRequest();
    request.setAmount(AMOUNT);
    request.setCurrency(Currency.CAD);
    request.setTransactionType(TransactionTypeCode.CREDIT);
    request.setTransactionRefId(generator.generate());
    return request;
  }


  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountTransaction_response_null_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-NULL-resultCode");

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNull(response.getResultCode());
  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountTransaction_response_empty_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-EMPTY-resultCode");

    assertThrows(RestClientException.class, () ->
            testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class));

  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********"
  })
  void accountTransaction_response_empty_ref_id_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-EMPTY-refId");

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals("", response.getRefId());
  }

  /**
   * Unit under test is AccountAdapter::accountTransaction
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********",
          "383-12345-*********"
  })
  void accountTransaction_response_null_ref_id_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-NULL-refId");

    // unit under test
    AccountTransactionResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildTransactionUrl(accountNumber), request, AccountTransactionResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNull(response.getRefId());
  }

  /**
   * Unit under test is AccountAdapter::accountReversal
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********"
  })
  void accountReversal_response_empty_ref_id_success(String accountNumber) {
    // initialize
    AccountTransactionReversalRequest request = new AccountTransactionReversalRequest();
    request.setEndToEndId("RESPONSE-EMPTY-refId");

    // unit under test
    AccountTransactionReversalResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertEquals("", response.getRefId());
  }

  /**
   * Unit under test is AccountAdapter::accountReversal
   * <pre>
   *   RC=SUCCESS as not applicable for eligibility
   * </pre>
   *
   * @param accountNumber inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {
          "383-12345-**********"
  })
  void accountReversal_response_null_ref_id_success(String accountNumber) {
    // initialize
    AccountTransactionRequest request = buildAccountTransactionRequest();
    request.setEndToEndId("RESPONSE-NULL-refId");

    // unit under test
    AccountTransactionReversalResponse response = null;
    try {
      response = testRestTemplate.postForObject(HOSTNAME + buildReversalUrl(accountNumber, REF_TRANSACTION_ID), request, AccountTransactionReversalResponse.class);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    assertNotNull(response);
    assertNull(response.getRefId());
  }

  private String buildEligibilityUrl(String accountNumber) {
    if (accountNumber == null) {
      return ROOT_SERVICE_URL + "/{}" + ELIGIBILITY;
    }
    return AccountingSimulatorConstant.ELIGIBILITY_URL.replace("{" + ACCOUNT_NUMBER + "}", accountNumber);
  }

  private String buildTransactionUrl(String accountNumber) {
    if (accountNumber == null) {
      return ROOT_SERVICE_URL + "/{}" + TRANSACTION;
    }
    return AccountingSimulatorConstant.TRANSACTION_URL.replace("{" + ACCOUNT_NUMBER + "}", accountNumber);
  }

  private String buildReversalUrl(String accountNumber, String transactionId) {
    if (accountNumber == null) {
      return ROOT_SERVICE_URL + "/{}/{}" + REVERSAL;
    }
    return AccountingSimulatorConstant.REVERSAL_URL.replace("{" + ACCOUNT_NUMBER + "}", accountNumber).replace("{" + TRANSACTION_ID + "}", transactionId);
  }

}