<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-006.fiint.interac.ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-005.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-002_fiint_interac_ca.pem" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-003.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
  <configuration default="false" name="Payment::API" type="Application" factoryName="Application">
    <envs>
      <env name="CUSTOMER_API_HOST" value="http://localhost:8081" />
      <env name="GL_API_HOST" value="http://af197bf67415e479abedd739fc136513-be49773705f27d9a.elb.ca-central-1.amazonaws.com:8080" />
      <env name="INTERAC_JWT_PUBLIC_CERT" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000999-006.fiint.interac.ca.pem" />
      <env name="PAYMENT_GL_ENABLED" value="false" />
      <env name="PEOPLES_JWT_PRIVATE_KEY" value="$PROJECT_DIR$/environment-tools/certificates/etapi-sign-key-ca000621-005.etransfer-dev.peoplespayments.com.key.pem" />
      <env name="SERVICE_ACCOUNT_API_HOST" value="http://localhost:8087" />
      <env name="SYSTEM_API_HOST" value="http://localhost:8084" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.peoples.banking.api.payment.v1.PaymentApiApplication" />
    <module name="payment-api" />
    <option name="VM_PARAMETERS" value="-Dserver.port=8085" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>