package com.peoples.banking.api.network.notification.interac.v1.util;

import static com.peoples.banking.api.network.notification.interac.v1.util.NotificationTestUtil.buildNotificationDetails;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.peoples.banking.partner.domain.interac.notification.model.EventDetails;
import com.peoples.banking.partner.domain.interac.notification.model.FraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.FraudInformation;
import com.peoples.banking.partner.domain.interac.notification.model.FraudStatus;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentFraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.RequestforPaymentFraudDetails;
import com.peoples.banking.util.api.common.exception.ValidationException;
import javax.validation.constraints.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class InteracNotificationPayloadValidatorTest {

  @Test
  public void payloadValidator_success() {
    NotificationDetails notificationDetails = buildNotificationDetails(EventEnum.PAYMENT_AUTHENTICATION_FAILURE);
    EventDetails eventPayload = new EventDetails();
    eventPayload.setPayment(new PaymentDetails());
    notificationDetails.setEventPayload(eventPayload);
    notificationDetails.setContactNotificationHandle(new NotificationHandle());
    assertDoesNotThrow(() -> {
      InteracNotificationPayloadValidator.checkPayload(notificationDetails);
    });
  }


  @ParameterizedTest
  @ValueSource(strings = {"fraud.payment.fraud_status_updated", "payment.authentication_failure", "registration.account_alias.blocked",
      "registration.routed_notification.complete", "request_for_payment.available", "payment_schedule.cancelled",
      "request_for_payment_return.expired"})
  public void emptyPaymentNotificationHandle_fail(String eventType) {
    NotificationDetails notificationDetails = buildNotificationDetails(EventEnum.fromValue(eventType));
    assertThrows(ValidationException.class, () -> {
      InteracNotificationPayloadValidator.checkPayload(notificationDetails);
    });
  }

  @Test
  public void payloadValidator_fraud_event_missing_fraud_status_fail() {
    NotificationDetails notificationDetails = buildFraudNotificationDetails();
    notificationDetails.setContactNotificationHandle(new NotificationHandle());
    assertThrows(ValidationException.class, () -> {
      InteracNotificationPayloadValidator.checkPayload(notificationDetails);
    });
  }

  @Test
  public void payloadValidator_fraud_event_missing_fraud_status_success() {
    NotificationDetails notificationDetails = buildFraudNotificationDetails();
    notificationDetails.setContactNotificationHandle(new NotificationHandle());
    RequestforPaymentFraudDetails requestForPaymentFraudDetails = new RequestforPaymentFraudDetails();
    requestForPaymentFraudDetails.setPreviousFraudStatus(FraudStatus.SUSPICIOUS);
    notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().setRequestForPaymentFraudDetails(requestForPaymentFraudDetails);
    notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails()
        .setPreviousFraudStatus(FraudStatus.CONFIRM_FRAUD);

    assertDoesNotThrow(() -> {
      InteracNotificationPayloadValidator.checkPayload(notificationDetails);
    });
  }

  @NotNull
  private NotificationDetails buildFraudNotificationDetails() {
    NotificationDetails notificationDetails = buildNotificationDetails(EventEnum.FRAUD_REQUEST_FOR_PAYMENT_REPORTED);
    EventDetails eventPayload = new EventDetails();
    FraudDetails fraud = new FraudDetails();
    FraudInformation fraudInfoPayload = new FraudInformation();
    PaymentFraudDetails paymentFraudDetails = new PaymentFraudDetails();
    fraudInfoPayload.setPaymentFraudDetails(paymentFraudDetails);
    fraud.setFraudInfoPayload(fraudInfoPayload);
    eventPayload.setFraud(fraud);
    notificationDetails.setEventPayload(eventPayload);
    return notificationDetails;
  }

}
