package com.peoples.banking.api.network.notification.interac.v1.util;

import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationTriggerTypeEnum.CONTACT_HANDLE_REGISTRATION;

import com.peoples.banking.partner.domain.interac.notification.model.AmountWithCurrency;
import com.peoples.banking.partner.domain.interac.notification.model.AmountWithCurrency.CurrencyEnum;
import com.peoples.banking.partner.domain.interac.notification.model.Contact;
import com.peoples.banking.partner.domain.interac.notification.model.ContactType;
import com.peoples.banking.partner.domain.interac.notification.model.EventDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationIntendedForEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle.TypeEnum;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentStatus;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentType;
import com.peoples.banking.partner.domain.interac.notification.model.SenderData;
import com.peoples.banking.util.api.common.DateUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class NotificationTestUtil {

  private final String PAYMENT_CREATED = "{\n"
      + "   \"event\":\"payment.declined\",\n"
      + "   \"notification_trigger_type\":\"PARTICIPANT_MANDATED\",\n"
      + "   \"notification_intended_for\":\"CUSTOMER\",\n"
      + "   \"event_payload\":{\n"
      + "      \"payment\":{\n"
      + "         \"bulk_indicator\":\"false\",\n"
      + "         \"payment_reference\":\"CAr8nX5W\",\n"
      + "         \"participant_payment_reference\":\"3766104\",\n"
      + "         \"sender_info\":{\n"
      + "            \"registration_name\":\"QASIT PaymentCancel_Sender\",\n"
      + "            \"notification_preference\":[\n"
      + "               {\n"
      + "                  \"type\":\"EMAIL\",\n"
      + "                  \"value\":\"<EMAIL>\"\n"
      + "               }\n"
      + "            ]\n"
      + "         },\n"
      + "         \"amount\":{\n"
      + "            \"amount\":20,\n"
      + "            \"currency\":\"CAD\"\n"
      + "         },\n"
      + "         \"payment_type\":\"REGULAR_PAYMENT\",\n"
      + "         \"creation_date\":\"2020-04-20T14:56:49Z\",\n"
      + "         \"expiry_date\":\"2020-04-21T14:56:49Z\",\n"
      + "         \"recipient_memo\":\"Test Decline Transfer\",\n"
      + "         \"sent_to\":{\n"
      + "            \"contact_id\":\"CAVJtkKVUWhN\",\n"
      + "            \"name\":\"QASIT PmtCancel_Recipient\",\n"
      + "            \"notification_preference\":[\n"
      + "               {\n"
      + "                  \"type\":\"EMAIL\",\n"
      + "                  \"value\":\"<EMAIL>\"\n"
      + "               }\n"
      + "            ],\n"
      + "            \"customer_type\":\"RETAIL\",\n"
      + "            \"legal_name\":{\n"
      + "               \"retail_name\":{\n"
      + "                  \"first_name\":\"QASIT\",\n"
      + "                  \"last_name\":\"PmtCancel_Recipient\"\n"
      + "               }\n"
      + "            }\n"
      + "         },\n"
      + "         \"status\":\"DECLINED\",\n"
      + "         \"is_authentication_required\":true\n"
      + "      }\n"
      + "   },\n"
      + "   \"event_timestamp\":\"2020-04-20T14:57:10.555Z\"\n"
      + "}";

  public static NotificationDetails buildNotificationDetails(EventEnum event) {
    NotificationDetails notificationDetails = new NotificationDetails();
    notificationDetails.setEvent(event);
    notificationDetails.setNotificationTriggerType(CONTACT_HANDLE_REGISTRATION);

    notificationDetails.setNotificationIntendedFor(NotificationIntendedForEnum.CUSTOMER);

    EventDetails eventDetails = new EventDetails();
    PaymentDetails paymentDetails = new PaymentDetails();
    paymentDetails.setBulkIndicator(false);
    paymentDetails.setPaymentReference("CAr8nX5W");

    SenderData senderData = new SenderData();
    senderData.setRegistrationName("PaymentCancel_Sender");
    senderData.setNotificationPreference(getNotificationPreference());
    paymentDetails.senderInfo(senderData);


    paymentDetails.senderInfo(senderData);

    AmountWithCurrency amountWithCurrency = new AmountWithCurrency();
    amountWithCurrency.amount(new BigDecimal("20.00"));
    amountWithCurrency.currency(CurrencyEnum.CAD);

    paymentDetails.setAmount(amountWithCurrency);

    paymentDetails.setPaymentType(PaymentType.ACCOUNT_ALIAS_PAYMENT);
    paymentDetails.setCreationDate(DateUtil.getCurrentUTCDateTime());
    paymentDetails.setExpiryDate(DateUtil.getCurrentUTCDateTime().plusDays(1));
    paymentDetails.setRecipientMemo("Test Decline Transfer");

    Contact sendTo = new Contact();
    sendTo.setContactId("CAVJtkKVUWhN");
    sendTo.setName("QASIT PmtCancel_Recipient");
    sendTo.setNotificationPreference(getNotificationPreference());
    sendTo.setCustomerType(ContactType.RETAIL);

    paymentDetails.setSentTo(sendTo);

    paymentDetails.setStatus(PaymentStatus.DECLINED);
    paymentDetails.setIsAuthenticationRequired(true);
    eventDetails.setPayment(paymentDetails);

    notificationDetails.setEventTimestamp(DateUtil.getCurrentUTCDateTime());
    notificationDetails.setEventPayload(eventDetails);

    return notificationDetails;
  }

  public static List<NotificationHandle> getNotificationPreference() {
    List<NotificationHandle> notificationPreference = new ArrayList<>();
    NotificationHandle notificationHandle = new NotificationHandle();
    notificationHandle.setType(TypeEnum.EMAIL);
    notificationHandle.setValue("<EMAIL>");
    notificationPreference.add(notificationHandle);
    return notificationPreference;
  }
}
