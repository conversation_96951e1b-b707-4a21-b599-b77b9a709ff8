package com.peoples.banking.api.network.notification.interac.v1.controller;

import static com.peoples.banking.api.network.notification.interac.v1.util.HttpTestUtil.buildHttpHeaders;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.network.notification.interac.v1.config.InteracNotificationConstant;
import com.peoples.banking.api.network.notification.interac.v1.service.InteracNotificationService;
import com.peoples.banking.api.network.notification.interac.v1.util.NotificationTestUtil;
import com.peoples.banking.partner.domain.interac.notification.model.ContactType;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationIntendedForEnum;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentStatus;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentType;
import com.peoples.banking.util.api.common.JsonUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
public class PushInteracNotificationControllerTest {

  // TODO eventually add valid JWS tokens to test
  static {
    System.setProperty(InteracNotificationConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");
  }

  @Autowired
  private MockMvc mvc;
  @MockBean
  private InteracNotificationService interacNotificationService;

  @ParameterizedTest
  @ValueSource(strings = {
      "fraud.payment.fraud_status_updated",
      "fraud.payment.funds_recovered",
      "fraud.payment.funds_recovery_request",
      "fraud.payment.reported",
      "fraud.registration.account_alias.reported",
      "fraud.request_for_payment.fraud_status_updated",
      "fraud.request_for_payment.reported"
  })
  public void pushNotification_differentEvent_success(String eventType) throws Exception {
    EventEnum eventEnum = EventEnum.fromValue(eventType);

    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(eventEnum);

    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "DEBTOR",
      "CREDITOR",
      "DEBTOR_AGENT",
      "CREDITOR_AGENT",
      "CUSTOMER",
      "PARTICIPANT_FI"
  })
  public void pushNotification_differentNotificationIntendedFor_success(String notificationIntendedFor) throws Exception {
    EventEnum eventEnum = EventEnum.FRAUD_PAYMENT_FRAUD_STATUS_UPDATED;
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(eventEnum);
    notificationDetails.setNotificationIntendedFor(NotificationIntendedForEnum.fromValue(notificationIntendedFor));

    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "REGULAR_PAYMENT",
      "FULFILL_REQUEST_FOR_PAYMENT",
      "ACCOUNT_ALIAS_PAYMENT",
      "REALTIME_ACCOUNT_ALIAS_PAYMENT",
      "ACCOUNT_DEPOSIT_PAYMENT",
      "REALTIME_ACCOUNT_DEPOSIT_PAYMENT"
  })
  public void pushNotification_differentPaymentType_success(String paymentType) throws Exception {
    EventEnum eventEnum = EventEnum.FRAUD_PAYMENT_FRAUD_STATUS_UPDATED;
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(eventEnum);
    notificationDetails.getEventPayload().getPayment().setPaymentType(PaymentType.fromValue(paymentType));

    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "RETAIL",
      "SMALL_BUSINESS",
      "CORPORATION"
  })
  public void pushNotification_differentContactType_success(String contactType) throws Exception {
    EventEnum eventEnum = EventEnum.FRAUD_PAYMENT_FRAUD_STATUS_UPDATED;
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(eventEnum);
    notificationDetails.getEventPayload().getPayment().getSentTo().setCustomerType(ContactType.fromValue(contactType));

    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "INITIATED",
      "AVAILABLE",
      "AUTHENTICATION_FAILURE",
      "AUTHENTICATION_SUCCESSFUL",
      "DECLINED",
      "EXPIRED",
      "DIRECT_DEPOSIT_PENDING",
      "DIRECT_DEPOSIT_FAILED",
      "CANCELLED",
      "COMPLETED",
      "REALTIME_INITIATED",
      "REALTIME_DEPOSIT_COMPLETED",
      "REALTIME_DEPOSIT_FAILED"
  })
  public void pushNotification_differentPaymentStatus_success(String paymentStatus) throws Exception {
    EventEnum eventEnum = EventEnum.FRAUD_PAYMENT_FRAUD_STATUS_UPDATED;
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(eventEnum);
    notificationDetails.getEventPayload().getPayment().setStatus(PaymentStatus.fromValue(paymentStatus));

    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @Test
  public void pushNotification_fail_bad_event() throws Exception {
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails(EventEnum.PAYMENT_NOTIFICATION_FAILURE);
    notificationDetails.setEventPayload(null);
    mvc.perform(MockMvcRequestBuilders.post(InteracNotificationConstant.NOTIFICATIONS_PUSH)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .content(JsonUtil.toString(notificationDetails))
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

}
