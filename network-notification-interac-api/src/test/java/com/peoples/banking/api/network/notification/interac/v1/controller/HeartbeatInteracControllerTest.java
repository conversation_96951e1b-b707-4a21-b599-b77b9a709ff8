package com.peoples.banking.api.network.notification.interac.v1.controller;

import static com.peoples.banking.api.network.notification.interac.v1.util.HttpTestUtil.buildHttpHeaders;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoples.banking.api.network.notification.interac.v1.config.InteracNotificationConstant;
import com.peoples.banking.api.network.notification.interac.v1.service.InteracNotificationService;
import com.peoples.banking.partner.domain.interac.deposit.model.HeartbeatResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.HeartbeatResponse.ResponseCodeEnum;
import com.peoples.banking.util.api.common.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
public class HeartbeatInteracControllerTest {

  // TODO eventually add valid JWS tokens to test
  static {
    System.setProperty(InteracNotificationConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");
  }

  @Autowired
  private MockMvc mvc;
  @MockBean
  private InteracNotificationService interacNotificationService;

  @Test
  public void heartbeat_success() throws Exception {
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.TRUE).when(interacNotificationService)
        .heartbeat();

    MvcResult result = mvc.perform(MockMvcRequestBuilders.get(InteracNotificationConstant.HEALTH_CHECK)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    HeartbeatResponse response = assertDoesNotThrow(
        () -> JsonUtil.toObject(result.getResponse().getContentAsString(), HeartbeatResponse.class));

    assertNotNull(response);
    assertEquals(ResponseCodeEnum.SUCCESS, response.getResponseCode());
    assertNotNull(response.getResponseSystemTime());
  }

  @Test
  public void heartbeat_system_not_available() throws Exception {
    doNothing().when(interacNotificationService)
        .checkHeartbeatHeader(isA(String.class), isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(Boolean.FALSE).when(interacNotificationService)
        .heartbeat();

    MvcResult result = mvc.perform(MockMvcRequestBuilders.get(InteracNotificationConstant.HEALTH_CHECK)
        .headers(buildHttpHeaders())
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    HeartbeatResponse response = assertDoesNotThrow(
        () -> JsonUtil.toObject(result.getResponse().getContentAsString(), HeartbeatResponse.class));

    assertNotNull(response);
    assertEquals(ResponseCodeEnum.SYS_NOT_AVAILABLE, response.getResponseCode());
    assertNotNull(response.getResponseSystemTime());
  }
}
