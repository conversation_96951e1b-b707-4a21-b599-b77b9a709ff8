package com.peoples.banking.api.network.notification.interac.v1.util;

import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.SignatureType;
import java.time.Instant;
import org.springframework.http.HttpHeaders;

public class HttpTestUtil {

  public final static String JWT_TOKEN = "eyJraWQiOiJyc0g5ZnN4R2V3YTcrU1kwemRqTEc1eFNidlA5ZTJnaDNqQ2R0N1wvdkVcL0k9IiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SHbw_GWoVbfk-t09945kRFjaWQ1v8MGHd4_HF4MBCbIbpRN8_L2UcbA97NFiQ9KYAyllOQWxQZHuUvGrAaLFPR_z_T9IPh4wnzhqFYFV1FBPeZ2mjI57DmakAf8kOxDz4pVlhxLmsoAHVKlAPSutM-9ev0UbK7hMeEpvksWm-ekQmnsVSOWNCvbmTsDFieIMmXHfI10cNlw7VjLPxt5p9kDatFRxOJYyyh97CDRBgU7p49sQIKTROBQ2nzUN8TnpUvvnMjGJUXQDmXOYiK0RrpuoKN94thVNJSLaJZkn2wbTWg_eQ81s6nyLLOIvynBD9HvhChmE7r4FtVJR37R4QA";

  public static HttpHeaders buildHttpHeaders() {
    HttpHeaders result = new HttpHeaders();
    result.add(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, "participant_id");
    result.add(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12");
    result.add(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, "false");
    result.add(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, "channelIndicator");
    result.add(InteracRestAdapterConstant.HEADER_SIGNATURE, JWT_TOKEN);
    result.add(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SignatureType.PAYLOAD_DIGEST_SHA256.name());
    result.add(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString());
    return result;
  }
}
