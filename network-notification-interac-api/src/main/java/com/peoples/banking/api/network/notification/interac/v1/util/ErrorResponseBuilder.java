package com.peoples.banking.api.network.notification.interac.v1.util;

import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import org.springframework.stereotype.Component;

@Component
public class ErrorResponseBuilder {

  public ErrorModel buildErrorResponseEntity(String errorCode, String additionalInformation) {
    ErrorModel errorModel = new ErrorModel();
    errorModel.setCode(errorCode);
    errorModel.setText(additionalInformation);
    return errorModel;
  }

}
