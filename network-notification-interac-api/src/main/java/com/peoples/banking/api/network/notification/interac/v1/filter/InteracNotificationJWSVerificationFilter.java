package com.peoples.banking.api.network.notification.interac.v1.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.api.network.notification.interac.v1.adapter.InteracNotificationResponseAdapter;
import com.peoples.banking.api.network.notification.interac.v1.config.InteracNotificationConstant;
import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.api.network.notification.interac.v1.util.ErrorResponseBuilder;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature.JwsVerificationResult;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.util.api.common.MultiReadHttpServletRequestWrapper;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * JWS Signature Verification Filter
 */
@Component
@Log4j2
public class InteracNotificationJWSVerificationFilter extends OncePerRequestFilter {

  @Autowired
  protected ObjectMapper objectMapper;
  /**
   * <i>Interac</i> runtime properties.
   */
  @Autowired
  protected InteracAdapterProperty interacProperties;
  /**
   * Adapter to handle JW[S/E]
   */
  @Autowired
  private InteracNotificationResponseAdapter adapter;

  @Autowired
  private ErrorResponseBuilder errorResponseBuilder;
  @Autowired
  private Environment env;

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws IOException {

    ErrorModel errorModel = null;
    MultiReadHttpServletRequestWrapper requestWrapper = new MultiReadHttpServletRequestWrapper(request);
    ContentCachingResponseWrapper responseWrapper = (ContentCachingResponseWrapper) response;
    try {
      String disableJws = env.getProperty(InteracNotificationConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION);
      // validate signature and TTLs only if disable environment variable is false
      if (!Boolean.parseBoolean(disableJws)) {
        log.debug("Verify TTL and signature start");
        checkJws(requestWrapper);
        log.debug("Verify TTL and signature end");
      }

      //continue the process if the verification is ok
      filterChain.doFilter(requestWrapper, responseWrapper);
    } catch (ValidationException ex) {
      log.warn(ex.getMessage(), ex);
      errorModel = errorResponseBuilder.buildErrorResponseEntity(ex.getError().getErrorCode(), ex.getError().getAdditionalInformation());
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
      errorModel = errorResponseBuilder
          .buildErrorResponseEntity(InteracNotificationResponseCode.UNEXPECTED_ERROR.getCode(),
              InteracNotificationResponseCode.UNEXPECTED_ERROR.getDescription());
    } finally {
      //construct response
      if (errorModel != null) {
        byte[] errorResponseByte = convertToByteArray(errorModel);
        responseWrapper.setContentType(APICommonUtilConstant.DEFAULT_CONTENT_TYPE);
        responseWrapper.setStatus(HttpStatus.BAD_REQUEST.value());
        responseWrapper.getOutputStream().write(errorResponseByte);
      }
    }
  }

  /**
   * Checks JWS (signature and TTL) against validation rules.
   *
   * @param -- all parameters same as initiateDeposit, submitDeposit or reverseDeposit.
   */
  private void checkJws(MultiReadHttpServletRequestWrapper requestWrapper) {
    String signature = requestWrapper.getHeader(InteracRestAdapterConstant.HEADER_SIGNATURE);
    String transactionTime = requestWrapper.getHeader(InteracRestAdapterConstant.HEADER_TRANS_TIME);
    String requestRawData = convertToString(requestWrapper.getContentAsByteArray());

    JwsVerificationResult result;

    try {
      result = adapter.validateSignature(requestRawData, signature, transactionTime, interacProperties.getInteracKId());
    } catch (AdapterException e) {
      // handle invalid/missing signatures
      throw new ValidationException(InteracNotificationResponseCode.INVALID_SIGNATURE.getCode(),
          "error with JWS signature, missing or invalid");
    }

    if (JwsVerificationResult.FAILED_TTL.equals(result)) {
      log.warn("signature validation failed due to TTL violation");

      throw new ValidationException(InteracNotificationResponseCode.EXPIRED_TTL.getCode(),
          InteracNotificationResponseCode.EXPIRED_TTL.getDescription());
    } else if (JwsVerificationResult.FAILED_SIGNATURE.equals(result)) {
      log.warn("signature validation failed due to signature failure");

      throw new ValidationException(InteracNotificationResponseCode.INVALID_SIGNATURE.getCode(),
          InteracNotificationResponseCode.INVALID_SIGNATURE.getDescription());
    }
  }

  /**
   * Convert byte array to String
   *
   * @param contentAsByteArray byte array
   * @return String the string of byte array
   */
  private String convertToString(byte[] contentAsByteArray) {
    if (contentAsByteArray == null || contentAsByteArray.length == 0) {
      return null;
    }
    return new String(contentAsByteArray, StandardCharsets.UTF_8);

  }

  /**
   * Convert object to byte array
   *
   * @param payload the payload domain
   * @return byte[] byte array
   * @throws IOException - if something wrong with object
   */
  private byte[] convertToByteArray(Object payload) throws IOException {
    String serialized = objectMapper.writeValueAsString(payload);
    return serialized.getBytes();
  }

}
