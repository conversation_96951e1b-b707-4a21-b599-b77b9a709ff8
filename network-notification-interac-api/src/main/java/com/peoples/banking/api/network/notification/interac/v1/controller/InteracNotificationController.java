package com.peoples.banking.api.network.notification.interac.v1.controller;

import com.peoples.banking.api.network.notification.interac.v1.config.InteracNotificationConstant;
import com.peoples.banking.api.network.notification.interac.v1.service.InteracNotificationService;
import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.HeartbeatResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.HeartbeatResponse.ResponseCodeEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * The REST controller for Network::Interac::Notification Processing
 */
@RestController
@Log4j2
public class InteracNotificationController {

  /**
   * Interac response header
   */
  private static final String INTERAC_HEADER_RESPONSE_CODE = "x-et-response-code";

  /**
   * Service implementation.
   */
  @Autowired
  private InteracNotificationService service;

  /**
   * <i>Interac</i> healthcheck Heartbeat
   * <p>
   * Service is used by Interac to monitor the status of the participant system(s).
   * </p>
   * {@code GET}
   *
   * @param participantId     Direct Participant Identifier as defined in e-Transfer system.
   * @param indirectConnector Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined in e-Transfer system.
   * @param authorization     Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
   * @param requestId         Unique ID generated for each request used for message tracking purposes. In case of a request retry use the
   *                          same ID as in the original message.
   * @param channelIndicator  Indicates the source channel of the request
   * @param signature         JWS detached signature of the payload (body only). Required for all API calls.
   * @param signatureType     The type of the JWT. Required. Allowed values are - PAYLOAD_DIGEST_SHA256
   * @param transactionTime   Participant transaction timestamp in utc while invoking api's, required to be part of digital sign.
   * @return heartbeat response result
   */
  @PerfLogger
  @GetMapping(value = InteracNotificationConstant.HEALTH_CHECK, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<HeartbeatResponse> healthCheck(
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, required = true) String participantId,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, required = false) String indirectConnector,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_AUTHORIZATION, required = false) String authorization,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_REQUEST_ID, required = true) String requestId,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, required = true) String channelIndicator,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_SIGNATURE, required = true) String signature,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, required = true) String signatureType,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_TRANS_TIME, required = true) String transactionTime) {
    // 1. validate the header & signature & TTL
    service.checkHeartbeatHeader(participantId, requestId,
        channelIndicator, signatureType, transactionTime);

    // service
    Boolean result = service.heartbeat();
    log.debug("Service heartbeat result {}", result);
    // Validate downstream system

    // return response
    HeartbeatResponse response = new HeartbeatResponse();
    response.setResponseSystemTime(OffsetDateTime.now());
    response.setResponseCode(result ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.SYS_NOT_AVAILABLE);

    return new ResponseEntity<>(response, null, HttpStatus.OK);
  }

  /**
   * <i>Interac</i> ISO20022 Notifications push
   * <p>
   * This service is used by Interac to send push routed notifications to a Participant FI, as replacement for, or in addition to, the
   * existing email and SMS notification.  The notification message contains the event type and, depending on that event type, a customized
   * data payload that contains more specific data pertinent to that event.
   * <p>
   * {@code POST}
   *
   * @param participantId     Direct Participant Identifier as defined in e-Transfer system.
   * @param participantUserId Present for all API calls initiated on behalf of a customer. Customer ID provided as defined in the
   *                          Participant system and Customer must be registered in the e-Transfer system.
   * @param indirectConnector Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined in e-Transfer system.
   * @param authorization     Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
   * @param requestId         Unique ID generated for each request used for message tracking purposes. In case of a request retry use the
   *                          same ID as in the original message.
   * @param retryIndicator    indicating if the operation is a retry of a previous request (true/false). Always to be specified for requests
   *                          where it is important to avoid the cases where the same operation is accidentally performed twice.
   * @param channelIndicator  Indicates the source channel of the request
   * @param signature         JWS detached signature of the payload (body only). Required for all API calls.
   * @param signatureType     The type of the JWT. Required. Allowed values are - PAYLOAD_DIGEST_SHA256
   * @param transactionTime   Participant transaction timestamp in utc while invoking api's, required to be part of digital sign.
   * @param request           the DepositPaymentRequest
   * @return - empty response (in case of successful response), error if something goes wrongly
   */
  @PerfLogger
  @PostMapping(value = InteracNotificationConstant.NOTIFICATIONS_PUSH, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> notificationsPush(
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, required = true) String participantId,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, required = false) String participantUserId,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, required = false) String indirectConnector,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_AUTHORIZATION, required = false) String authorization,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_REQUEST_ID, required = true) String requestId,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, required = true) String retryIndicator,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, required = true) String channelIndicator,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_SIGNATURE, required = true) String signature,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, required = true) String signatureType,
      @RequestHeader(name = InteracRestAdapterConstant.HEADER_TRANS_TIME, required = true) String transactionTime,
      @Valid @RequestBody NotificationDetails request) {
    // flag to indicate whether non-zero response code needs to be returned
    MutableBoolean alreadyProcessedFlag = new MutableBoolean();
    // 1. validate the header & signature & TTL
    service.checkHeader(participantId, participantUserId, requestId,
        retryIndicator, channelIndicator, signatureType, transactionTime);
    // 2. validate payload
    service.checkPayload(request);

    // service call
    service.saveNotification(request, alreadyProcessedFlag);

    // set appropriate response code (in header)
    HttpHeaders httpHeaders;
    if (alreadyProcessedFlag.isFalse()) {
      httpHeaders = createResponseHttpHeaders(InteracNotificationResponseCode.SUCCESS.getCode());
    } else {
      httpHeaders = createResponseHttpHeaders(InteracNotificationResponseCode.TRANSACTION_ALREADY_COMPLETED.getCode());
    }

    return new ResponseEntity<>(null, httpHeaders, HttpStatus.NO_CONTENT);
  }

  /**
   * construct http headers to include x-et-response-code
   *
   * @param responseCode response code to return back
   * @return HttpHeaders
   */
  protected HttpHeaders createResponseHttpHeaders(String responseCode) {
    HttpHeaders headers = new HttpHeaders();

    if (responseCode == null || responseCode.isBlank()) {
      responseCode = InteracNotificationResponseCode.SUCCESS.getCode();
    }

    headers.set(INTERAC_HEADER_RESPONSE_CODE, responseCode);

    return headers;
  }
}
