package com.peoples.banking.api.network.notification.interac.v1.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

@Configuration
@EnableKafka
@Log4j2
public class KafkaConfig {
  private static final String SSL = "SSL";

  @Autowired
  private InteracNotificationProperty interacNotificationProperty;

  @Autowired
  private ObjectMapper objectMapper;

  @Bean
  public ProducerFactory<String, NotificationDetails> producerFactory() {
    Map<String, Object> configProps = createGenericConfigProps();
    configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, interacNotificationProperty.getKafkaBootstrapAddress());

    return new DefaultKafkaProducerFactory<>(configProps, new StringSerializer(), new JsonSerializer<>(objectMapper));
  }

  @Bean
  public KafkaTemplate<String, NotificationDetails> kafkaTemplate() {
    return new KafkaTemplate<>(producerFactory());
  }

  @Bean
  public KafkaAdmin kafkaAdminClient() {
    return new KafkaAdmin(createGenericConfigProps());
  }

  @Bean
  public NewTopic pushNotificationTopics() {
    //TODO debug line, we will remove it later
    String kafkaTopic = interacNotificationProperty.getKafkaTopic();
    log.info("create kafka topic:{}", kafkaTopic);
    return TopicBuilder.name(kafkaTopic).build();
  }

  private Map<String, Object> createGenericConfigProps() {
    Map<String, Object> configProps = new HashMap<>();
    configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, interacNotificationProperty.getKafkaBootstrapAddress());
    if (interacNotificationProperty.isSslEnabled()) {
      configProps.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, SSL);
      configProps.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, interacNotificationProperty.getSslTruststoreLocation());
      configProps.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, interacNotificationProperty.getSslTruststorePassword());
      configProps.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, interacNotificationProperty.getSslKeyPassword());
      configProps.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, interacNotificationProperty.getSslKeystoreLocation());
      configProps.put(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG, interacNotificationProperty.getSslKeystorePassword());
    }
    return configProps;
  }
}
