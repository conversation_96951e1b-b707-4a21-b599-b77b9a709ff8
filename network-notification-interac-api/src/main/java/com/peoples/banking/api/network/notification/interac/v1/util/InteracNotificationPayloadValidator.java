package com.peoples.banking.api.network.notification.interac.v1.util;

import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.FRAUD_PAYMENT_REPORTED;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.FRAUD_REQUEST_FOR_PAYMENT_REPORTED;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.REGISTRATION_ROUTED_NOTIFICATION_COMPLETE;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.REGISTRATION_ROUTED_NOTIFICATION_DEACTIVATED;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.REGISTRATION_ROUTED_NOTIFICATION_EXPIRED;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum.REGISTRATION_ROUTED_NOTIFICATION_EXPIRY_REMINDER;
import static com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationTriggerTypeEnum.CONTACT_HANDLE_REGISTRATION;

import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.partner.domain.interac.notification.model.FraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentType;
import com.peoples.banking.util.api.common.exception.ValidationException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

/**
 * Validations for <i>Notification Event</i> payload.
 */
@Log4j2
public class InteracNotificationPayloadValidator {

  /**
   * String format of header validation error.
   */
  public static final String FIELD_MISSING = "MISSING_FIELD:";


  /**
   * Check payload for <i>Notification Payload</i> against validation rules (see {@code https://confluence.interac.ca/confluence/display/ETD/Push+Notification}
   *
   * @param request request payload
   */
  public static void checkPayload(NotificationDetails request) {
    // contactNotificationHandle Provided if the notificationTriggerType is set to CONTACT_HANDLE_REGISTRATION, showing where the dispatching would have initially been sent to.
    if (request.getNotificationTriggerType() == CONTACT_HANDLE_REGISTRATION && request.getContactNotificationHandle() == null) {
      log.warn("contact notification handle is null when NotificationTriggerType is CONTACT_HANDLE_REGISTRATION");

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "notification.contactNotificationHandle");
    }

    // check if event payload is null
    if (request.getEventPayload() == null) {
      log.warn("event payload is null");

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "notification.eventPayload");
    }

    //payment Present if event context is payments.
    if (isPaymentEvent(request.getEvent())) {
      checkPaymentEvent(request);
    }

    //requestForPayment Present if event context is request for payment.
    if (isRequestForPayment(request.getEvent()) && request.getEventPayload().getRequestForPayments() == null) {
      log.warn("requestForPayments payload is missing in RequestForPayments event with type {}", request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.requestForPayments");
    }

    //accountAliasRegistrations Present if event context account alias registration.
    if (isRequestForPayment(request.getEvent()) && request.getEventPayload().getRequestForPayments() == null) {
      log.warn("requestForPayments payload is missing in RequestForPayments event with type {}", request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.requestForPayments");
    }

    //routedNotificationRegistrations Present if event context routed notification registration.
    if (isRoutedNotificationRegistrations(request.getEvent()) && request.getEventPayload().getRoutedNotificationRegistrations() == null) {
      log.warn("routedNotificationRegistrations payload is missing in routedNotificationRegistrations event with type {}",
          request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.routedNotificationRegistrations");
    }
    //requestForPaymentReturn Present if event context is request for payment return.
    if (isRequestForPaymentReturn(request.getEvent()) && request.getEventPayload().getRequestToPaymentReturn() == null) {
      log.warn("requestForPaymentReturn payload is missing in requestForPaymentReturn event with type {}",
          request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.paymentSchedule");
    }
    //paymentSchedule Present if event context is payment schedule.
    if (isPaymentSchedule(request.getEvent()) && request.getEventPayload().getPaymentSchedule() == null) {
      log.warn("paymentSchedule payload is missing in paymentSchedule event with type {}",
          request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.paymentSchedule");
    }
    //requestForPaymentSchedule Present if event context is request for payment schedule.
    if (isRequestForPaymentSchedule(request.getEvent()) && request.getEventPayload().getRequestToPaySchedule() == null) {
      log.warn("requestToPaySchedule payload is missing in requestToPaySchedule event with type {}", request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.requestToPaySchedule");
    }

    //fraud present if event context is fraud.
    if (isFraudEvent(request.getEvent())) {
      checkFraudEvent(request);
    }

  }

  private static void checkPaymentEvent(NotificationDetails request) {
    PaymentDetails payment = request.getEventPayload().getPayment();
    if (payment == null) {
      log.warn("payment payload is missing in payment event with type {}", request.getEvent());
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.payment");
    }
    if (payment.getPaymentType() == PaymentType.FULFILL_REQUEST_FOR_PAYMENT && StringUtils
        .isEmpty(payment.getRequestForPaymentReference())) {
      log.warn("RequestForPaymentReference is missing for payment type payment event with type {}", payment.getPaymentType());
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.payment.requestForPaymentReference");
    }
    if (payment.getPaymentType() == PaymentType.REGULAR_PAYMENT && payment.getIsAuthenticationRequired() == null) {
      log.warn("RequestForPaymentReference is missing for payment type payment event with type {}", payment.getPaymentType());
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.payment.requestForPaymentReference");
    }

  }

  private static void checkFraudEvent(NotificationDetails request) {
    FraudDetails fraud = request.getEventPayload().getFraud();
    if (fraud == null) {
      log.warn("fraud payload is missing in fraud event with type {}", request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.fraud");
    }

    if (request.getEvent() == FRAUD_PAYMENT_REPORTED && (fraud.getFraudInfoPayload() == null
        || fraud.getFraudInfoPayload().getPaymentFraudDetails() == null ||
        fraud.getFraudInfoPayload().getPaymentFraudDetails().getFraudCheckResult() == null)) {
      log.warn("fraudCheckResult payload is missing in fraud event with type {}", request.getEvent());

      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "eventPayload.fraud.fraudInfoPayload.fraudCheckResult");
    }

    if ((request.getEvent() == FRAUD_REQUEST_FOR_PAYMENT_REPORTED || request.getEvent() == FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED)) {
      if (fraud.getFraudInfoPayload() == null) {
        log.warn("fraudInfoPayload payload is missing in fraud event with type {}", request.getEvent());
        throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "eventPayload.fraud.fraudInfoPayload");
      }

      if (fraud.getFraudInfoPayload().getRequestForPaymentFraudDetails() == null) {
        log.warn("fraudInfoPayload.requestForPaymentFraudDetails payload is missing in fraud event with type {}", request.getEvent());
        throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "eventPayload.fraud.fraudInfoPayload.requestForPaymentFraudDetails");
      }

      if (fraud.getFraudInfoPayload().getRequestForPaymentFraudDetails().getPreviousFraudStatus() == null) {
        log.warn("fraudInfoPayload.requestForPaymentFraudDetails.previousFraudStatus payload is missing in fraud event with type {}", request.getEvent());
        throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "eventPayload.fraud.fraudInfoPayload.requestForPaymentFraudDetails.previousFraudStatus");
      }

    }


  }

  private static boolean isFraudEvent(EventEnum event) {
    return "fraud".equals(extractEventType(event));
  }

  private static boolean isRoutedNotificationRegistrations(EventEnum event) {
    return event == REGISTRATION_ROUTED_NOTIFICATION_COMPLETE || event == REGISTRATION_ROUTED_NOTIFICATION_DEACTIVATED
        || event == REGISTRATION_ROUTED_NOTIFICATION_EXPIRED || event == REGISTRATION_ROUTED_NOTIFICATION_EXPIRY_REMINDER;
  }

  private static boolean isRequestForPayment(EventEnum event) {
    return "request_for_payment".equals(extractEventType(event));
  }

  private static boolean isPaymentSchedule(EventEnum event) {
    return "payment_schedule".equals(extractEventType(event));
  }

  private static boolean isRequestForPaymentReturn(EventEnum event) {
    return "request_for_payment_return".equals(extractEventType(event));
  }

  private static boolean isRequestForPaymentSchedule(EventEnum event) {
    return "request_for_payment_schedule".equals(extractEventType(event));
  }

  private static String extractEventType(EventEnum event) {
    return event.getValue().substring(0, event.getValue().indexOf("."));
  }

  private static boolean isPaymentEvent(EventEnum event) {
    return "payment".equals(extractEventType(event));
  }

}

