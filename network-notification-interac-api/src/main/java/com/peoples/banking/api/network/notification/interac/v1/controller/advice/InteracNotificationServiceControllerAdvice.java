package com.peoples.banking.api.network.notification.interac.v1.controller.advice;


import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.api.network.notification.interac.v1.util.ErrorResponseBuilder;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.util.api.common.controller.advice.APIServiceControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * The controller advice to handle application and services related exceptions
 *
 * <AUTHOR>
 */
@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
public class InteracNotificationServiceControllerAdvice extends APIServiceControllerAdvice {

  private static final Logger LOGGER = LogManager.getLogger(APIServiceControllerAdvice.class);

  @Autowired
  private ErrorResponseBuilder errorResponseBuilder;

  /**
   * Exception, i.e unexpected errors are thrown in the application
   *
   * @param ex      the Exception
   * @param request the HttpServletRequest
   * @return ResponseEntity<?> the error response
   */
  @ExceptionHandler(Exception.class)
  protected ResponseEntity<?> handleException(Exception ex, HttpServletRequest request) {

    HttpHeaders headers = buildResponseHttpHeaders(request);

    ErrorResponseEntity<?> errorResponseEntity = buildErrorResponseEntity(InteracNotificationResponseCode.UNEXPECTED_ERROR.getCode(),
        InteracNotificationResponseCode.UNEXPECTED_ERROR.getDescription());

    LOGGER.error(ex.getMessage(), ex);

    return new ResponseEntity<>(errorResponseEntity.getContent(), headers, HttpStatus.BAD_REQUEST);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(String errorCode, String additionalInformation) {
    ErrorModel errorModel = errorResponseBuilder.buildErrorResponseEntity(errorCode, additionalInformation);
    return new ErrorResponseEntity<>(errorModel);
  }

  /**
   * null for Interac response Header
   */
  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return null;
  }

  /**
   * {@inheritDoc}
   */
  @Override
  protected String convertToErrorCode(String responseCode) {
    //not applicable for network-interac-api as the ResponseException is wrapped into ValidationException.
    return null;
  }

}
