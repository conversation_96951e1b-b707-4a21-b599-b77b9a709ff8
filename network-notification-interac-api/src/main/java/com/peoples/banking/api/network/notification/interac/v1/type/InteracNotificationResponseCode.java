package com.peoples.banking.api.network.notification.interac.v1.type;

public enum InteracNotificationResponseCode {
  SUCCESS("0", "Success"),
  SYSTEM_UNAVAILABLE("1", "Error: System Unavailable"),
  SYSTEM_TEMP_UNAVAILABLE("2", "Error: System temporarily unavailable (retry)"),
  SYSTEM_BUSY("3", "Error: System busy - Unknown outcome of the request (to be treated as a timeout)"),
  INVALID_PARTICIPANT_ID("20", "Error: Invalid participantId"),
  INVALID_PARTICIPANT_USER_ID("21", "Error: Invalid participantUserId"),
  ACCOUNT_NOT_EXISTS("22", "Error: Account does not exist"),
  INVALID_ACCOUNT_INFO("23", "Error: Invalid account info"),
  ACCOUNT_INACTIVE("24", "Error: Account not active"),
  ACCOUNT_TEMP_INACTIVE("25", "Error: Account temporarily inactive (retry)"),
  ACCOUNT_CANNOT_ACCEPT_DIRECT_DEPOSIT("26", "Error: Account cannot accept direct deposits"),
  DISABLED_PARTICIPANT_USER_ID("28", "Error: Disabled participantUserId"),
  ACCOUNT_FROZEN("29", "Error: Account is frozen / unavailable (no retry)"),
  INVALID_TRANSACTION_AUTHORIZATION_TYPE("31", "Error: Invalid transaction authorization type"),
  INVALID_TRANSACTION_AUTHORIZATION_TOKEN("32", "Error: Invalid transaction authorization token"),
  DIRECT_DEPOSIT_LIMIT_EXCEEDED("33", "Error: Direct deposit limit exceeded"),
  AMOUNT_MISMATCH("34", "Error: amount doesn't match original request ( no retry )"),
  DIRECT_DEPOSIT_NOT_SUPPORT("35", "Error: Direct Deposit not supported for this customer (no retry)"),
  CURRENCY_MISMATCH("39", "Creditor Account/Currency mismatch"),
  BEGIN_TRANSACTION_NOT_FOUND("40", "Error: Begin transaction not found"),
  TRANSACTION_CANNOT_BE_ROLLED_BACK("41", "Error: Committed transaction cannot be rolled back"),
  TRANSACTION_CANNOT_BE_COMMITTED("43", "Error: Rolled back transaction cannot be committed"),
  DUPLICATE_PAYMENT_REF_ID("45", "Duplicate transaction, transaction having the same payment reference was already processed."),
  FRAUD_RISK_EXCEEDED("46", "Error: Fraud risk exceeded"),
  DUPLICATE_MESSAGE_ID("47", "Error: Duplicate transaction, transaction indicated by MessageIdentifiction (MsgId) was already processed"),
  COMPLIANCE_RISK_EXCEEDED("48", "Compliance risk exceeded"),
  ACCOUNT_CLOSED("49", "Account closed, Creditor/Recipient deceased"),
  INVALID_CREDITOR_AGENT_MEMBER_ID("51", "Error: Invalid Creditor Agent member Id"),
  PARTICIPANT_ID_MISMATCH("52", "Error: participantId and Instructed Agent member Id do not match"),
  PARTICIPANT_USER_ID_MISMATCH("53", "Error: participantUserId and Creditor identifier do not match"),
  PAYMENT_NOT_FOUND("54", "Error: Payment not found"),
  PAYMENT_CANCELLED("55", "Error: Payment has already been cancelled"),
  UNEXPECTED_ERROR("999", "Error: Unspecified Application error"),
  TRANSACTION_ALREADY_COMPLETED("200", "Warning: Begin Transaction already completed"),
  TRANSACTION_ALREADY_COMMITTED("201", "Warning: Transaction already committed"),
  TRANSACTION_ALREADY_ROLLED_BACK("202", "Warning: Transaction already rolled back"),
  SCHEMA_ERROR("5000", ""),
  INVALID_SIGNATURE("5001", "Digital signature verification failure"),
  EXPIRED_TTL("5002", "Time-to-live expired");

  private final String code;
  private final String description;

  InteracNotificationResponseCode(String code, String description) {
    this.code = code;
    this.description = description;
  }

  public String getCode() {
    return this.code;
  }

  public String getDescription() {
    return this.description;
  }
}
