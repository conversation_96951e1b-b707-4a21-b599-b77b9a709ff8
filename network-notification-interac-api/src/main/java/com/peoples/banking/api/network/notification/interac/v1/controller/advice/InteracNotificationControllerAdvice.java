package com.peoples.banking.api.network.notification.interac.v1.controller.advice;

import com.google.common.collect.Multimap;

import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.api.network.notification.interac.v1.util.ErrorResponseBuilder;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.controller.advice.APIControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import com.peoples.banking.util.api.common.type.SchemaErrorType;
import java.util.Collection;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.util.WebUtils;

/**
 * Global controller advice to handle standard Spring MVC exceptions, such as schema validation, JSON format, etc.
 */
@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
public class InteracNotificationControllerAdvice extends APIControllerAdvice {

  private static final Logger LOGGER = LogManager.getLogger(InteracNotificationControllerAdvice.class);

  @Autowired
  private ErrorResponseBuilder errorResponseBuilder;

  private static final String SEPARATOR_COLUMN = ":";

  /**
   * {@inheritDoc} Override standard Spring MVC exceptions and customize the output. The status code is default to 400 as according
   * Interac's API, the unexpected error should be 400
   */
  @Override
  protected ResponseEntity<Object> handleExceptionInternal(Exception ex, @Nullable Object body, HttpHeaders headers, HttpStatus status,
      WebRequest request) {

    if (HttpStatus.INTERNAL_SERVER_ERROR.equals(status)) {
      request.setAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE, ex, WebRequest.SCOPE_REQUEST);
    }

    headers = buildHttpHeaders(request);

    ErrorResponseEntity<?> errorResponseEntity = convertInternalExceptionToErrorResponse(ex);

    if (status.is5xxServerError()) {
      LOGGER.error(ex.getMessage(), ex);
    } else {
      LOGGER.warn(ex.getMessage(), ex);
    }

    return new ResponseEntity<>(errorResponseEntity.getContent(), headers, HttpStatus.BAD_REQUEST);
  }


  /**
   * {@inheritDoc} Customize processMethodArgumentNotValidException AKA schema validation error for Interac Network API to return 1 error at
   * a time.
   */
  @Override
  protected ErrorResponseEntity<?> processMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
    String errorCode = null;
    String fieldPath = null;

    List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
    if (fieldErrors != null && fieldErrors.size() > 0) {
      FieldError fieldError = fieldErrors.get(0);

      SchemaErrorType errorType = SchemaErrorType.fromString(fieldError.getCode());

      switch (errorType) {
        case NOT_NULL:
          errorCode = ErrorProperty.MISSING_FIELD.name();
          break;
        case SIZE:
          if (fieldError.getRejectedValue() instanceof Collection) {
            errorCode = ErrorProperty.INVALID_LIST_SIZE.name();
          } else {
            errorCode = ErrorProperty.INVALID_FIELD_LENGTH.name();
          }
          break;
        case PATTERN:
          errorCode = ErrorProperty.INVALID_INPUT.name();
          break;
        case DECIMAL_MIN:
          errorCode = ErrorProperty.INVALID_AMOUNT.name();
          break;
        default:
          errorCode = ErrorProperty.INVALID_INPUT.name();
          break;
      }

      fieldPath = convertToJsonPath(fieldError.getField());

    }

    ErrorResponseEntity<?> errorResponseEntity = buildErrorResponseEntity(errorCode, fieldPath);

    return errorResponseEntity;
  }

  /**
   * {@inheritDoc} Implement Interac Network API error response.
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(String errorCode, String additionalInformation) {
    StringBuilder interacText =
        new StringBuilder(InteracNotificationResponseCode.SCHEMA_ERROR.getDescription());
    interacText.append(errorCode);
    interacText.append(SEPARATOR_COLUMN);
    interacText.append(additionalInformation);

    ErrorModel errorModel = errorResponseBuilder.buildErrorResponseEntity(null, interacText.toString());

    if (errorCode.equals(ErrorProperty.UNEXPECTED_ERROR.name())) {
      errorModel.setCode(InteracNotificationResponseCode.UNEXPECTED_ERROR.getCode());
    } else {
      errorModel.setCode(InteracNotificationResponseCode.SCHEMA_ERROR.getCode());
    }

    ErrorResponseEntity<ErrorModel> errorResponseEntity = new ErrorResponseEntity<>(errorModel);
    return errorResponseEntity;
  }

  /**
   * {@inheritDoc} Implement null as it is not applicable for Interact Network API.
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(Multimap<String, String> errorMap) {
    return null;
  }

  /**
   * {@inheritDoc} null for Interac response Header
   */
  @Override
  protected HttpHeaders buildHttpHeaders(WebRequest request) {
    return null;
  }

}
