package com.peoples.banking.api.network.notification.interac.v1.service;

import com.peoples.banking.api.network.notification.interac.v1.config.InteracNotificationProperty;
import com.peoples.banking.api.network.notification.interac.v1.util.InteracNotificationHeaderValidator;
import com.peoples.banking.api.network.notification.interac.v1.util.InteracNotificationPayloadValidator;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import javax.validation.constraints.NotNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Log4j2
@Service
public class InteracNotificationService {

  /**
   * Application runtime configuration - Interac connection properties
   */
  @Autowired
  private InteracAdapterProperty interacAdapterProperty;

  @Autowired
  private KafkaTemplate<String, NotificationDetails> kafkaTemplate;

  @Autowired
  private InteracNotificationProperty interacNotificationProperty;

  /**
   * Heartbeat service implementation.
   *
   * @return true if service is working (hardcoded for now)
   */
  public boolean heartbeat() {
    return true;
  }

  /**
   * Checks header against validation rules.
   *
   * @param -- all parameters same.
   */
  public void checkHeader(String participantId, String participantUserId, String requestId,
      String retryIndicator, String channelIndicator, String signatureType, String transactionTime) {

    // first, validate mandatory/optional headers
    InteracNotificationHeaderValidator
        .validateHeaders(participantId, participantUserId, requestId, retryIndicator, channelIndicator, signatureType, transactionTime);
  }

  /**
   * Checks header against validation rules.
   *
   * @param -- all parameters same as heartbeat
   */
  @PerfLogger
  public void checkHeartbeatHeader(String participantId, String requestId,
      String channelIndicator, String signatureType, String transactionTime) {

    // first, validate mandatory/optional headers
    InteracNotificationHeaderValidator
        .validateHeaders(participantId, requestId, channelIndicator, signatureType, transactionTime);
  }

  /**
   * Check payload for notification request against validation rules (see {@code https://confluence.interac.ca/confluence/display/ETD/Push+Notification}
   *
   * @param request request payload
   */
  public void checkPayload(NotificationDetails request) {
    InteracNotificationPayloadValidator.checkPayload(request);
  }

  @PerfLogger
  public void saveNotification(NotificationDetails notificationDetails, MutableBoolean alreadyProcessedFlag) {
    sendKafkaMessage(notificationDetails);
  }

  private void sendKafkaMessage(final NotificationDetails message) {
    final String topicName = interacNotificationProperty.getKafkaTopic();
    kafkaTemplate.send(topicName, message).addCallback(
        new ListenableFutureCallback<>() {
          @Override
          public void onFailure(@NotNull Throwable ex) {
            log.error("Error while sending kafka message to topic {}", topicName, ex);
          }

          @Override
          public void onSuccess(SendResult<String, NotificationDetails> sendResult) {
            log.info("Sent message to topic {} type {}", topicName, message.getNotificationTriggerType());
          }
        });
  }

}


