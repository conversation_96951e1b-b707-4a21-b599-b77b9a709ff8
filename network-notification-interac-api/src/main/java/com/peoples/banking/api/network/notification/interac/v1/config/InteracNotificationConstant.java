package com.peoples.banking.api.network.notification.interac.v1.config;

/**
 * Constants used for <i>Network::InteracNotification</i> APIs.
 */
public final class InteracNotificationConstant {

  /**
   * Environment variable to disable <PERSON><PERSON> token validation.
   */
  public static final String ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION = "DISABLE_SIGNATURE_VERIFICATION";
  /**
   * Root of all URLs.
   */
  private static final String ROOT_CONTEXT = "/network/interac/v1/notifications";
  private static final String HEARTBEAT_ROOT_CONTEXT = ROOT_CONTEXT + "/heartbeat";
  /**
   * <i>Interac</i> heartbeat.
   */

  public static final String HEALTH_CHECK = HEARTBEAT_ROOT_CONTEXT;
  private static final String PUSH = "/push";
  /**
   * <i>Interac</i> ISO20022 Notifications push.
   */
  public static final String NOTIFICATIONS_PUSH = ROOT_CONTEXT + PUSH;

  // TODO rename static constants so a reorg on this class doesn't ruin the flow
  private InteracNotificationConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
