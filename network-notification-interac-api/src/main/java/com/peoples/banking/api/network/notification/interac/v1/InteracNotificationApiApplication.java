package com.peoples.banking.api.network.notification.interac.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.peoples.banking.api.network.notification.interac",
    "com.peoples.banking.util",
    "com.peoples.banking.partner.adapter.interac.common",
    "com.peoples.banking.partner.adapter.interac.payment"})
public class InteracNotificationApiApplication {

  /**
   * Spring Boot main application starter.
   *
   * @param args runtime arguments
   */
  public static void main(String[] args) {
    SpringApplication.run(InteracNotificationApiApplication.class, args);
  }
}
