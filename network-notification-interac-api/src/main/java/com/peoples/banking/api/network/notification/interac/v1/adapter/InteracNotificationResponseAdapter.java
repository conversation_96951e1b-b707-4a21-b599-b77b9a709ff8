package com.peoples.banking.api.network.notification.interac.v1.adapter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.InteracRestAdapter;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature.JwsVerificationResult;
import java.util.Map;
import java.util.function.Function;

import com.peoples.banking.partner.domain.interac.notification.model.ErrorModel;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class InteracNotificationResponseAdapter extends InteracRestAdapter<ErrorModel> {

  // TODO
  /**
   * Create Interac
   */
// TODO create InteracAdapter, which does NOT implement any of the REST aspects, that we can re-use in there.

  /**
   * TODO
   * <p>
   * Create InteracAdapter, which includes these methods:
   *
   * <code>public void init() throws AdapterException</code>
   * <code>private void initSender() throws AdapterException</code>
   * <code>private void initReceiver() throws AdapterException</code>
   * and associated variables.
   * <p>
   * and this class should extend it.
   * <p>
   * And then InteracRestAdapter should extend InteracAdapter.
   */


  /**
   * @inheritDoc
   */
  @Override
  protected void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams) {
  }

  /**
   * @inheritDoc
   */
  @Override
  protected String getServiceDomain() {
    return null;
  }

  @Override
  protected Class<ErrorModel> getErrorClass() {
    return ErrorModel.class;
  }

  @Override
  protected Function<ErrorModel, String> getErrorCodeFunction() {
    return ErrorModel::getCode;
  }

  @Override
  protected Function<ErrorModel, String> getErrorDescriptionFunction() {
    return ErrorModel::getText;
  }

  /**
   * Verify the JWS token
   *
   * @param payloadObj      HTTP payload body
   * @param signature       JWS token
   * @param transactionTime transaction timestamp from the request
   * @param keyId           expected kid from header
   * @return
   * @throws AdapterException
   */
  public JwsVerificationResult validateSignature(Object payloadObj, String signature, String transactionTime, String keyId)
      throws AdapterException {

    String httPayload = null;
    if (payloadObj != null) {
      // convert request to raw string (only if there is one)
      try {
        httPayload = objectMapper.writeValueAsString(payloadObj);
      } catch (JsonProcessingException e) {
        log.error("failed to convert HTTP body", e);
        throw new AdapterException();
      }
    }

    // validate signature
    return this.jws.validateJWS(httPayload, signature, transactionTime, keyId);
  }

  /**
   * Verify the JWS token
   *
   * @param payloadObj      HTTP payload body
   * @param signature       JWS token
   * @param transactionTime transaction timestamp from the request
   * @param keyId           expected kid from header
   * @return result of jws verification
   * @throws AdapterException - if something goes wrong with adapter
   */
  public JwsVerificationResult validateSignature(String payloadObj, String signature, String transactionTime, String keyId)
      throws AdapterException {
    // validate signature
    return this.jws.validateJWS(payloadObj, signature, transactionTime, keyId);
  }
}
