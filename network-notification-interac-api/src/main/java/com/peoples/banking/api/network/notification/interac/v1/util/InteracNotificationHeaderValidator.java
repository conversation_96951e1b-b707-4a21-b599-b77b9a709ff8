package com.peoples.banking.api.network.notification.interac.v1.util;

import com.peoples.banking.api.network.notification.interac.v1.type.InteracNotificationResponseCode;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.ChannelIndicator;
import com.peoples.banking.partner.domain.interac.deposit.model.SignatureType;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Validations for the x-et-* series of headers.
 */
public class InteracNotificationHeaderValidator {

  /**
   * String format of header validation error.
   */
  public static final String HEADER_INVALID = "INVALID_HEADER:";
  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(InteracNotificationHeaderValidator.class);
  /**
   * String equivalent of Boolean.TRUE.
   */
  private static final String BOOLEAN_TRUE = "TRUE";
  /**
   * String equivalent of Boolean.FALSE.
   */
  private static final String BOOLEAN_FALSE = "FALSE";
  /**
   * String format of participant id.
   */
  private static final String PARTICIPANT_ID_FORMAT = "CA000";

  /**
   * Validates the header field values for <i>Deposit Payment</i>, <i>Submit Deposit Payment</i> or <i>Reverse Deposit Payment</i>.
   *
   * @param participantId     mandatory header
   * @param participantUserId optional header
   * @param requestId         mandatory
   * @param retryIndicator    mandatory (boolean)
   * @param channelIndicator  mandatory (constrained string)
   * @param signatureType     mandatory (constrained string)
   * @param transactionTime   mandatory (constrained format)
   */
  public static void validateHeaders(String participantId, String participantUserId, String requestId,
      String retryIndicator, String channelIndicator, String signatureType, String transactionTime) {

    // mandatory header values
    checkParticipantId(participantId);
    checkRequestId(requestId);
    checkRetryIndicator(retryIndicator);
    checkChannelIndicator(channelIndicator);
    checkSignatureType(signatureType);
    checkTransactionTime(transactionTime);

    // optional header values
    checkParticipantUserId(participantUserId);
  }

  /**
   * Validates the header field values for <i>Health Check</i>.
   *
   * @param participantId    mandatory header
   * @param requestId        mandatory
   * @param channelIndicator mandatory (constrained string)
   * @param signatureType    mandatory (constrained string)
   * @param transactionTime  mandatory (constrained format)
   */
  public static void validateHeaders(String participantId, String requestId,
      String channelIndicator, String signatureType, String transactionTime) {

    // mandatory header values
    checkParticipantId(participantId);
    checkRequestId(requestId);
    checkChannelIndicator(channelIndicator);
    checkSignatureType(signatureType);
    checkTransactionTime(transactionTime);
  }

  /**
   * Utility function to validate x-et-participant-id.
   *
   * @param participantId header value
   */
  private static void checkParticipantId(String participantId) {
    boolean isValid = true;

    // sanity check
    if (participantId == null) {
      isValid = false;
    } else {
      // must have length == 8 (min=max=8)
      if (participantId.isBlank() || participantId.length() != 8) {
        isValid = false;
      }
      // must start with "CA000"
      else if (!PARTICIPANT_ID_FORMAT.equals(participantId.substring(0, 5))) {
        isValid = false;
      } else {
        // and must end with Institution Number [0-9]{3}
        try {
          Integer.parseInt(participantId.substring(5));
        } catch (NumberFormatException e) {
          isValid = false;
        }
      }
    }

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_PARTICIPANT_ID);
    }
  }

  /**
   * Utility function to validate x-et-participant-user-id.
   *
   * @param participantUserId header value
   */
  private static void checkParticipantUserId(String participantUserId) {
    boolean isValid = true;

    // sanity check (optional, so fine if not provided, valid)
    if (participantUserId != null) {
      // validate
      if (participantUserId.isBlank()
          || participantUserId.length() < 1
          || participantUserId.length() > 35) {
        isValid = false;
      }
    }

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID);
    }
  }


  /**
   * Utility function to validate x-et-request-id.
   *
   * @param requestId header value
   */
  private static void checkRequestId(String requestId) {
    boolean isValid = true;

    // sanity check
    if (requestId == null) {
      isValid = false;
    } else if (requestId.isBlank()
        || requestId.length() < 1
        || requestId.length() > 36) {
      isValid = false;
    }

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_REQUEST_ID, requestId, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_REQUEST_ID);
    }
  }


  /**
   * Utility function to validate x-et-transaction-time.
   *
   * @param transactionTime header value
   */
  private static void checkTransactionTime(String transactionTime) {
    boolean isValid = true;

    // sanity check
    if (transactionTime == null ||
        transactionTime.isBlank()) {
      isValid = false;
    } else {
      // validate
      try {
        LocalDateTime.parse(transactionTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
      } catch (Exception e) {
        LOGGER.warn("failed to parse x-et-transaction-time", e);
        isValid = false;
      }
    }

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_TRANS_TIME, transactionTime, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_TRANS_TIME);
    }
  }

  /**
   * Utility function to validate x-et-api-signature-type.
   *
   * @param signatureType header value
   */
  private static void checkSignatureType(String signatureType) {
    boolean isValid = true;
    boolean isFound = false;

    // sanity check
    if (signatureType == null ||
        signatureType.isBlank()) {
      isValid = false;
    } else {
      // validate
      for (SignatureType b : SignatureType.values()) {
        if (b.getValue().equals(signatureType)) {
          isFound = true;
          break;
        }
      }
    }

    // isValid
    isValid = isValid && isFound;

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE);
    }
  }

  /**
   * Utility function to validate x-et-retry-indicator
   *
   * @param retryIndicator header retryIndicator
   */
  private static void checkRetryIndicator(String retryIndicator) {
    boolean isValid = true;

    // sanity check
    if (retryIndicator == null
        || retryIndicator.isBlank()) {
      isValid = false;
    } else {
      // validate
      if (!(retryIndicator.equalsIgnoreCase(BOOLEAN_TRUE)
          || retryIndicator.equalsIgnoreCase(BOOLEAN_FALSE))) {
        isValid = false;
      }
    }

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, retryIndicator, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_RETRY_INDICATOR);
    }
  }

  /**
   * Utility function to validate x-et-channel-indicator.
   *
   * @param channelIndicator header value
   */
  private static void checkChannelIndicator(String channelIndicator) {
    boolean isValid = true;
    boolean isFound = false;

    // sanity check
    if (channelIndicator == null ||
        channelIndicator.isBlank()) {
      isValid = false;
    } else {
      // validate
      for (ChannelIndicator b : ChannelIndicator.values()) {
        if (b.getValue().equals(channelIndicator)) {
          isFound = true;
          break;
        }
      }
    }

    // isValid
    isValid = isValid && isFound;

    LOGGER.debug("{}={}, isValid={}", InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator, isValid);

    if (!isValid) {
      throw new ValidationException(InteracNotificationResponseCode.SCHEMA_ERROR.getCode(),
          HEADER_INVALID + InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR);
    }
  }
}