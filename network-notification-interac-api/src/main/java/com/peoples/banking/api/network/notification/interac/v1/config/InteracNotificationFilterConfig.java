package com.peoples.banking.api.network.notification.interac.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.network.notification.interac.v1.filter.InteracNotificationJWSVerificationFilter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Interac Notification API Filter Configuration
 */
@Configuration
public class InteracNotificationFilterConfig {

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  @Autowired
  private InteracNotificationJWSVerificationFilter jwsVerificationFilter;

  @Autowired
  private InteracNotificationProperty networkInteracNotificationProperty;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> networkInteracNotificationAPILoggingFilter() {
    LoggingFilter networkInteracNotificationLoggingFilter = new LoggingFilter();
    networkInteracNotificationLoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    networkInteracNotificationLoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    networkInteracNotificationLoggingFilter.setSystemName(networkInteracNotificationProperty.getSystemName());
    FilterRegistrationBean<LoggingFilter> loggingFilterBean = new FilterRegistrationBean<>();
    loggingFilterBean.setFilter(networkInteracNotificationLoggingFilter);
    loggingFilterBean.setOrder(Integer.MIN_VALUE);
    loggingFilterBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_NETWORK_API_URL);

    return loggingFilterBean;
  }

  /**
   * JWS Verification Filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<InteracNotificationJWSVerificationFilter> jwsVerificationFilter() {
    FilterRegistrationBean<InteracNotificationJWSVerificationFilter> jwsVerificationFilterBean = new FilterRegistrationBean<>();
    jwsVerificationFilterBean.setFilter(jwsVerificationFilter);
    jwsVerificationFilterBean.setOrder(Integer.MAX_VALUE);
    jwsVerificationFilterBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_NETWORK_API_URL);
    return jwsVerificationFilterBean;
  }

  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
