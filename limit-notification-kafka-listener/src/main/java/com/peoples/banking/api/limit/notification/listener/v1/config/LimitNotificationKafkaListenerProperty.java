package com.peoples.banking.api.limit.notification.listener.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class LimitNotificationKafkaListenerProperty {

  @Value("${kafka.topic}")
  private String kafkaTopic;
  @Value("${kafka.group.id}")
  private String kafkaConsumerGroup;
  @Value("${emailsender.api.timetolive:30000}")
  private int timeToLive;
  @Value("${mail.sendgrid.api.key}")
  private String sendgridApiKey;
  @Value("#{'${mail.cc.contacts:}'.split(',')}")
  private List<String> mailCcContacts;
  @Value("${mail.from.email}")
  private String fromEmail;
  @Value("${mail.ptc.contact.info}")
  private String mailPtcContactInfo;
  @Value("${limit.listener.fallback.email.rate.minutes}")
  private int fallbackEmailRateMinutes;

}
