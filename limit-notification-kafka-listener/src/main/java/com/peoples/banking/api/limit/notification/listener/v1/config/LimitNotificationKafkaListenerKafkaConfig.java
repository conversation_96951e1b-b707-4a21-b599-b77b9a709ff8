package com.peoples.banking.api.limit.notification.listener.v1.config;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaConsumerGroupDetails;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerConfigProperty;
import com.peoples.banking.api.limit.notification.listener.v1.dto.LimitBreachKafkaMessage;
import com.peoples.banking.api.limit.notification.listener.v1.service.LimitNotificationKafkaListenerService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.function.Consumer;

@Configuration
@Log4j2
public class LimitNotificationKafkaListenerKafkaConfig {

    @Autowired
    private LimitNotificationKafkaListenerProperty limitNotificationKafkaListenerProperty;
    @Autowired
    private LimitNotificationKafkaListenerService limitNotificationKafkaListenerService;

    @Bean
    public KafkaListenerConfigProperty config() {
        return new KafkaListenerConfigProperty(Collections.singletonList(
                new KafkaConsumerGroupDetails(LimitBreachKafkaMessage.class,
                        limitNotificationKafkaListenerProperty.getKafkaTopic(),
                        limitNotificationKafkaListenerProperty.getKafkaConsumerGroup(),
                        (Consumer<LimitBreachKafkaMessage>) limitNotificationKafkaListenerService::sendLimitEmail)));
    }

}
