package com.peoples.banking.api.limit.notification.listener.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = {KafkaAutoConfiguration.class})
@ComponentScan(basePackages = {
        "com.peoples.banking.api.common.kafka.listener",
        "com.peoples.banking.api.limit.notification.listener",
        "com.peoples.banking.util",
        "com.peoples.banking.adapter.pb.serviceaccount"})
public class LimitNotificationKafkaListenerApplication {

  public static void main(String[] args) {
    SpringApplication.run(LimitNotificationKafkaListenerApplication.class, args);
  }

}
