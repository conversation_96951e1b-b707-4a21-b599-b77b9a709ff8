package com.peoples.banking.api.limit.notification.listener.v1.service;

import com.peoples.banking.api.limit.notification.listener.v1.dto.LimitBreachKafkaMessageType;

public class LimitUtil {
    private static final String DELIMITER = "_";

    public static String buildKeyForNotification(String ref, int level) {
        return ref + DELIMITER + LimitBreachKafkaMessageType.NOTIFICATION + DELIMITER + level;
    }
    public static String buildKeyForSuspension(String ref) {
        return ref + DELIMITER + LimitBreachKafkaMessageType.SUSPENSION;
    }
}
