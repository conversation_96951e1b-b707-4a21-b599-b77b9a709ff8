package com.peoples.banking.api.limit.notification.listener.v1.service;

import static java.time.Instant.*;

import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class ExternalLimitService {
    public static final ZoneId TORONTO_ZONE_ID = ZoneId.of("America/Toronto", ZoneId.SHORT_IDS);

    @Autowired
    private RedisTemplate<String, Object> redis;

    public void resetLimits(String serviceAccountRef) {
        List<String> keys = new ArrayList<>();
        for (int i = 0; i <= 100; i += 5) {
            keys.add(LimitUtil.buildKeyForNotification(serviceAccountRef, i));
        }
        keys.add(LimitUtil.buildKeyForSuspension(serviceAccountRef));
        redis.delete(keys);
    }

    public boolean shouldSendSuspensionEmail(String serviceAccountRef) {
        final String key = LimitUtil.buildKeyForSuspension(serviceAccountRef);
        final int count = redis.opsForSet().add(key, 1).intValue();
        setExpirationIfNecessary(count, key);
        return count == 1;
    }

    public boolean shouldSendNotificationEmail(String serviceAccountRef, int level) {
        final String key = LimitUtil.buildKeyForNotification(serviceAccountRef, level);
        final int count = redis.opsForSet().add(key, 1).intValue();
        setExpirationIfNecessary(count, key);
        return count == 1;
    }

    private void setExpirationIfNecessary(int count, String key) {
        if (count == 1) {
            redis.expire(key, Duration.between(ZonedDateTime.ofInstant(now(), TORONTO_ZONE_ID),
                            LocalDate.now().plusDays(1).atStartOfDay().atZone(TORONTO_ZONE_ID))
                    .getSeconds(), TimeUnit.SECONDS);
        }
    }
}
