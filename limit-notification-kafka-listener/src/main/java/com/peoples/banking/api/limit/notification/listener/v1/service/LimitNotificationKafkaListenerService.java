package com.peoples.banking.api.limit.notification.listener.v1.service;

import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerNotRetryableException;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.limit.notification.listener.v1.config.LimitNotificationKafkaListenerProperty;
import com.peoples.banking.api.limit.notification.listener.v1.dto.LimitBreachKafkaMessage;
import com.peoples.banking.api.limit.notification.listener.v1.dto.LimitBreachKafkaMessageType;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountContact;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountLimit;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import java.math.RoundingMode;

import dev.failsafe.RateLimiter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Log4j2
public class LimitNotificationKafkaListenerService {

  private static final String LIMIT_AMOUNT = "limitAmount";
  private static final String NOTIFICATION_PERCENTAGE = "notificationPercentage";
  private static final String NOTIFICATION_LIMIT = "notificationLimit";
  private static final String PTC_CONTACT_INFO = "peoplesTrustContactInfo";
  private static final String NOTIFICATION_TEMPLATE = "notification.html";
  private static final String SUSPENSION_TEMPLATE = "suspension.html";
  private static final String NOTIFICATION_SUBJECT = "WARNING: Peoples Interac e-Transfer Volume Notification";
  private static final String SUSPENSION_SUBJECT = "SERVICE SUSPENSION ALERT: Peoples Interac e-Transfer Service Suspension Notification";
  private static final String SENDGRID_SEND_ENDPOINT = "mail/send";

  @Autowired
  private ServiceAccountAdapter serviceAccountAdapter;
  @Autowired
  private SpringTemplateEngine thymeleafTemplateEngine;
  @Autowired
  private LimitNotificationKafkaListenerProperty limitNotificationKafkaListenerProperty;
  @Autowired
  private SendGrid sendGridClient;
  @Autowired
  private ExternalLimitService externalLimitService;
  private Map<String, Map<String, RateLimiter>> rateLimiters = new ConcurrentHashMap<>();

  @Transactional
  @SneakyThrows
  public void sendLimitEmail(LimitBreachKafkaMessage message) {
    log.info("Sending limit breach email of type {} to SA {}", message.getType(), message.getServiceAccountRefId());
    try {
      if (message.getType() == LimitBreachKafkaMessageType.RESET) {
        resetLimitCounters(message.getServiceAccountRefId());
        log.info("Notification limits reset for {}", message.getServiceAccountRefId());
        return;
      }
      ServiceAccountResponse serviceAccountResponse = getServiceAccount(message);
      if (shouldSendEmail(message, serviceAccountResponse)) {
        generateAndSendEmail(message, serviceAccountResponse);
      } else {
          log.info("{} email for {} was blocked", message.getType(), message.getServiceAccountRefId());
      }
    } catch (KafkaListenerNotRetryableException | KafkaListenerRetryableException e) {
      log.error("Failed to send email for SA: " + message.getServiceAccountRefId(), e);
      throw e;
    } catch (Exception e) {
      log.error("Failed to send email for SA: " + message.getServiceAccountRefId(), e);
      throw new KafkaListenerNotRetryableException(e);
    }
  }

  private boolean shouldSendEmail(LimitBreachKafkaMessage message, ServiceAccountResponse serviceAccountResponse) {
    if (message.getType() == LimitBreachKafkaMessageType.SUSPENSION) {
      return shouldSendSuspensionEmail(serviceAccountResponse.getRefId());
    }
    if (message.getType() == LimitBreachKafkaMessageType.NOTIFICATION) {
      return shouldSendWarningNotification(message.getAmount(), serviceAccountResponse);
    }
    return false;
  }

  private boolean shouldSendSuspensionEmail(String serviceAccountRef) {
    try {
      return externalLimitService.shouldSendSuspensionEmail(serviceAccountRef);
    } catch (Exception e) {
      log.error("Error while getting suspension limit from external service", e);
      return acquirePermissionFromRateLimiter(serviceAccountRef, LimitBreachKafkaMessageType.SUSPENSION.toString());
    }
  }

  private boolean acquirePermissionFromRateLimiter(String serviceAccountRef, String key) {
    return rateLimiters.computeIfAbsent(serviceAccountRef, k -> new ConcurrentHashMap<>())
            .computeIfAbsent(key,
                    k -> RateLimiter.smoothBuilder(1, Duration.ofMinutes(limitNotificationKafkaListenerProperty.getFallbackEmailRateMinutes()))
                            .build())
            .tryAcquirePermit();
  }

  private boolean shouldSendWarningNotification(BigDecimal amount, ServiceAccountResponse serviceAccountResponse) {
    int level = amount.divide(serviceAccountResponse.getLimits().getSuspendThresholdAmount(), 2, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100)).intValue() / 5 * 5;
    try {
      return externalLimitService.shouldSendNotificationEmail(serviceAccountResponse.getRefId(), level);
    } catch (Exception e) {
      log.error("Error while getting notification limit from external service", e);
      return acquirePermissionFromRateLimiter(serviceAccountResponse.getRefId(), LimitUtil.buildKeyForNotification(serviceAccountResponse.getRefId(), level));
    }
  }

  private void resetLimitCounters(String serviceAccountRef) {
    rateLimiters.remove(serviceAccountRef);
    externalLimitService.resetLimits(serviceAccountRef);
  }

  private ServiceAccountResponse getServiceAccount(final LimitBreachKafkaMessage message) {
    try {
      return serviceAccountAdapter.retrieveServiceAccountByRefId(message.getServiceAccountRefId());
    } catch (TBDException | TimeoutException e) {
      throw new KafkaListenerRetryableException(e);
    } catch (Exception e) {
      throw new KafkaListenerNotRetryableException(e);
    }
  }

  private void generateAndSendEmail(final LimitBreachKafkaMessage message, final ServiceAccountResponse sa) throws IOException {
    log.debug("Generating email for SA {}", sa.getRefId());
    String templateFile = null;
    String subject = null;
    if (message.getType() == LimitBreachKafkaMessageType.NOTIFICATION) {
      templateFile = NOTIFICATION_TEMPLATE;
      subject = NOTIFICATION_SUBJECT;
    } else if (message.getType() == LimitBreachKafkaMessageType.SUSPENSION) {
      templateFile = SUSPENSION_TEMPLATE;
      subject = SUSPENSION_SUBJECT;
    }
    Mail email = new Mail();
    email.setFrom(new Email(limitNotificationKafkaListenerProperty.getFromEmail()));
    Personalization personalization = new Personalization();
    email.addPersonalization(personalization);
    log.debug("sa {} Contacts{}", sa.getRefId(), sa.getContacts());
    if ((CollectionUtils.isEmpty(sa.getContacts())
        || (sa.getContacts() != null && sa.getContacts().stream().filter(c -> ServiceAccountContact.TypeEnum.FRAUD.equals(c.getType())
        || ServiceAccountContact.TypeEnum.OPERATIONAL.equals(c.getType()))
        .filter(c -> c.getEmail() != null && c.getEmail().length() > 0).count() == 0))
        && Optional.ofNullable(limitNotificationKafkaListenerProperty.getMailCcContacts())
        .map(s -> s.stream().noneMatch(StringUtils::isNotBlank)).orElse(true)
    ) {
      log.warn("{} : service account refId = {} contacts and PTC contacts are not found", APICommonUtilConstant.INVESTIGATE, sa.getRefId());
      throw new KafkaListenerNotRetryableException("No recipients found for SA " + sa.getRefId());
    }
    if (sa.getContacts() != null) {
      sa.getContacts().stream()
          .filter(c -> ServiceAccountContact.TypeEnum.FRAUD.equals(c.getType())
              || ServiceAccountContact.TypeEnum.OPERATIONAL.equals(c.getType()))
          .filter(c -> c.getEmail() != null && c.getEmail().length() > 0)
          .map(ServiceAccountContact::getEmail)
          .forEach(e -> personalization.addTo(new Email(e)));
    }
    if (limitNotificationKafkaListenerProperty.getMailCcContacts() != null) {
      //when all sa contact is empty we will set this to be true
      boolean saContactsPresent = personalization.getTos().size() > 0;
      for (String ptcCCEmail : limitNotificationKafkaListenerProperty.getMailCcContacts()) {
        if (StringUtils.isNoneBlank(ptcCCEmail)) {
          if (saContactsPresent) {
            personalization.addCc(new Email(ptcCCEmail));
          } else {
            personalization.addTo(new Email(ptcCCEmail));
          }
        }
      }
    }
    email.setSubject(subject);
    String content = getEmailContent(message.getType(), templateFile, sa);
    email.addContent(new Content(MediaType.TEXT_PLAIN_VALUE, content));
    send(email);
  }

  private String getEmailContent(final LimitBreachKafkaMessageType type, final String templateFile, final ServiceAccountResponse sa) {
    Map<String, Object> context = populateContext(type, sa);
    Context thymeleafContext = new Context();
    thymeleafContext.setVariables(context);
    return thymeleafTemplateEngine.process(templateFile, thymeleafContext);
  }

  private Map<String, Object> populateContext(final LimitBreachKafkaMessageType type, final ServiceAccountResponse serviceAccount) {
    Map<String, Object> context = new HashMap<>();
    ServiceAccountLimit limit = serviceAccount.getLimits();

    context.put(PTC_CONTACT_INFO, limitNotificationKafkaListenerProperty.getMailPtcContactInfo());
    if (limit == null) {
      throw new KafkaListenerNotRetryableException("No limit set for service account " + serviceAccount.getRefId());
    }
    context.put(LIMIT_AMOUNT, limit.getSuspendThresholdAmount());
    if (type == LimitBreachKafkaMessageType.NOTIFICATION && limit.getNotificationThresholdAmount() != null
        && limit.getReserveAmount().compareTo(BigDecimal.ZERO) > 0) {
      context.put(NOTIFICATION_PERCENTAGE,
          limit.getNotificationThresholdAmount().divide(limit.getSuspendThresholdAmount(), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue());
    } else {
      context.put(NOTIFICATION_PERCENTAGE, 0);
    }
    context.put(NOTIFICATION_LIMIT, ObjectUtils.defaultIfNull(limit.getNotificationThresholdAmount().intValue(), 0));
    return context;
  }

  private void send(Mail mail) throws IOException {
    if (log.isDebugEnabled()) {
      log.info("About to send email to: {}, cc: {} with subject: {}",
          mail.getPersonalization().stream().flatMap(p -> p.getTos().stream()).map(Email::getEmail).collect(Collectors.joining(",")),
          mail.getPersonalization().stream().flatMap(p -> p.getCcs().stream()).map(Email::getEmail).collect(Collectors.joining(",")),
          mail.getSubject()
      );
    }
    Request request = new Request();
    request.setMethod(Method.POST);
    request.setEndpoint(SENDGRID_SEND_ENDPOINT);
    request.setBody(mail.build());
    try {
      Response response = sendGridClient.api(request);
      if (response.getStatusCode() != 202) {
        String message = MessageFormat
            .format("Received status {0} with error body {1} when sending email", response.getStatusCode(), response.getBody());
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED.value()) {
          throw new KafkaListenerRetryableException(message);
        } else {
          throw new KafkaListenerNotRetryableException(message);
        }
      }
    } catch (IOException e) {
      throw new KafkaListenerRetryableException(e);
    }
  }

}
