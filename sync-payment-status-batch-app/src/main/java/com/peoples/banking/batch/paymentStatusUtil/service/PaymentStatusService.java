package com.peoples.banking.batch.paymentStatusUtil.service;

import java.util.Collections;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Log4j2
public class PaymentStatusService {

  /**
   * REST Template.
   */
  protected RestTemplate restTemplate = new RestTemplate();

  /**
   * (Internal) Retrieve payment URL.
   */
  @Value("${app.payment.api.host}/v1/internal/payment/")
  private String syncPaymentUrl;

  /**
   * Synchronize status for payment
   *
   * @param externalRefId external payment reference ID.
   * @return
   */
  public boolean callSyncStatusApi(String externalRefId) {
    boolean result = false;

    String finalUrl = syncPaymentUrl + externalRefId;

    try {
      log.info("payment_ref_id={}", externalRefId);

      ResponseEntity<String> responseEntity = restTemplate.exchange(finalUrl, HttpMethod.GET,
          new HttpEntity<>(Collections.emptyMap()), String.class);

      if (responseEntity.getStatusCode().is2xxSuccessful()) {
        result = true;
        log.info("successfully called API to update record");
      } else {
        result = false;
        log.warn("failed calling API to update record {}", responseEntity.getBody());
      }
    } catch (Exception e) {
      log.error("exception encountered calling API to update record", e);
    }

    return result;
  }
}
