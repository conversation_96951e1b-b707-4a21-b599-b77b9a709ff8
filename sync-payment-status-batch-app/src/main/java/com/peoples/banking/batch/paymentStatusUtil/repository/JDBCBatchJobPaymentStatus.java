package com.peoples.banking.batch.paymentStatusUtil.repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

import com.peoples.banking.batch.paymentStatusUtil.SimplePayment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class JDBCBatchJobPaymentStatus implements BatchJobPaymentStatus {

  @Autowired
  private JdbcTemplate jdbcTemplate;
  private final ZoneId utc = ZoneId.of("UTC");
  private final ZoneId easternTimeZone = ZoneId.of("America/Toronto", ZoneId.SHORT_IDS);

  @Override
  public String fetchPaymentStatus(String externalRefId) {
    return jdbcTemplate.queryForObject(
        "select status  from transaction.payments where"
            + " external_ref_id='" + externalRefId + "' ",
        String.class);
  }

  @Override
  public List<SimplePayment> fetchNotCompletedPayments(int dateRangeStart, boolean lastDay) {

    String query = "select external_ref_id, status from transaction.payments where"
                    + " type_cd = 'OUTBOUND' "
                    + " and status not in ('COMPLETE', 'CANCELLED' , 'FAILED' ,'DEPOSIT_FAILED' ,'ACCEPTED') "
                    + " and created_on >= " + getDateRangeString(dateRangeStart)
                    + " and created_on <  " + getDateRangeEnd(dateRangeStart, lastDay);

    return jdbcTemplate.query(query, (rs, rowNum) -> (new SimplePayment(rs.getString("external_ref_id"), rs.getString("status"))));
  }

  public int currentEtHour() {
    return LocalDateTime.now(easternTimeZone).getHour();
  }

  private String getDateRangeString(int dateRange) {
    LocalDateTime dateStart = LocalDate.now().minusDays(dateRange).atStartOfDay(easternTimeZone).withZoneSameInstant(utc).toLocalDateTime();
    return "'" + dateStart.toString() + "'";
  }

  private String getDateRangeEnd(int dateRangeStart, boolean lastDay) {
    if (lastDay) {
      LocalDateTime dateEnd = LocalDate.now().atStartOfDay(easternTimeZone).withHour(currentEtHour()).withZoneSameInstant(utc).toLocalDateTime();
      return "'" + dateEnd.toString() + "'";
    }
    return getDateRangeString(dateRangeStart - 1);
  }
}
