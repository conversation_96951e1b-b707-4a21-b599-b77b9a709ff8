package com.peoples.banking.batch.paymentStatusUtil;

import com.peoples.banking.batch.paymentStatusUtil.repository.JDBCBatchJobPaymentStatus;
import com.peoples.banking.batch.paymentStatusUtil.service.PaymentStatusService;
import java.util.List;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.peoples.banking.batch.paymentStatusUtil",
    "com.peoples.banking.batch.paymentStatusUtil.repository",
    "com.peoples.banking.batch.paymentStatusUtil.service"})
@Log4j2
public class PaymentStatusApplication implements CommandLineRunner {

  @Autowired
  private JDBCBatchJobPaymentStatus jdbcBatchJobPaymentStatus;

  @Autowired
  private PaymentStatusService paymentStatusService;

  @Value("${app.check.date.range}")
  private Integer checkDateRange;

  @Value("${app.records.per.iteration}")
  private Integer maxItemsPerIter;

  public static void main(String[] args) {
    log.info("STARTING THE APPLICATION");
    SpringApplication.run(PaymentStatusApplication.class, args);
    log.info("APPLICATION FINISHED");
  }

  /**
   * Runnable.
   *
   * @param args
   */
  @Override
  public void run(String... args) {

    log.info("Number of days to process, num_days={}", checkDateRange);

    for (int i = checkDateRange; i > -1; i--) {
      if (jdbcBatchJobPaymentStatus.currentEtHour() == 0 && i == 0) {
        break;
      }
      // first, determine payments in database eligible for updating
      List<SimplePayment> notCompletedPayments = jdbcBatchJobPaymentStatus.fetchNotCompletedPayments(i, i == 0);
      log.info("Number of records eligible for updating, record_count={}", notCompletedPayments.size());

      // process only if NON-ZERO number of records to run
      for (SimplePayment payment : notCompletedPayments) {
        // process all records in current batch
        String paymentRefId = payment.getExternalRefId();
        log.info("payment_ref_id={}, status={}", paymentRefId, payment.getStatus());

        boolean result = paymentStatusService.callSyncStatusApi(paymentRefId);
        if (result) {
          String newPaymentStatus = jdbcBatchJobPaymentStatus.fetchPaymentStatus(paymentRefId);
          log.info("successfully called API to update record {}, old_status={}, new_status={}", paymentRefId, payment.getStatus(), newPaymentStatus);
        } else {
          log.info("call for API to update record {} was unsuccessful", paymentRefId);
        }
      }
    }
    System.exit(0);
  }
}
