package com.peoples.banking.api.update.customer.v1.converter;

import java.util.List;
import org.springframework.stereotype.Component;
import com.peoples.banking.domain.customer.model.Error;
import com.peoples.banking.domain.customer.model.ErrorResponse;

/**
 * A helper class to convert the  error details into ErrorResponse
 */
@Component
public class ErrorResponseConverter {
  
  public Error buildError(String errorCode, String additionalInformation) {
    Error error = new Error();

    error.setCode(errorCode);
    error.setAdditionalInformation(additionalInformation);

    return error;
  }
  
  public ErrorResponse buildErrorResponse(List<Error> errorList) {
    ErrorResponse errorResponse = new ErrorResponse();
    errorResponse.setError(errorList);

    return errorResponse;
  }

}
