package com.peoples.banking.api.update.customer.v1.controller.advice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import com.google.common.collect.Multimap;
import com.peoples.banking.api.update.customer.v1.converter.ErrorResponseConverter;
import com.peoples.banking.domain.customer.model.Error;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.util.api.common.HttpHeadersUtil;
import com.peoples.banking.util.api.common.controller.advice.APIControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;

/**
 * Global controller advice to handle standard Spring MVC exceptions, such as schema validation,
 * JSON format, etc.
 *
 */

@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class UpdateCustomerTypeControllerAdvice extends APIControllerAdvice {

  @Autowired
  private ErrorResponseConverter errorConverter;

  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(String errorCode,
      String additionalInformation) {

    Error error = errorConverter.buildError(errorCode, additionalInformation);
    ErrorResponse errorResponse = errorConverter.buildErrorResponse(Arrays.asList(error));

    ErrorResponseEntity<ErrorResponse> errorResponseEntity =
        new ErrorResponseEntity<>(errorResponse);
    return errorResponseEntity;
  }

  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(Multimap<String, String> errors) {
    List<Error> errorList = new ArrayList<>();

    for (Map.Entry<String, String> entry : errors.entries()) {
      Error error = errorConverter.buildError(entry.getKey(), entry.getValue());
      errorList.add(error);

    }
    ErrorResponse errorResponse = errorConverter.buildErrorResponse(errorList);

    ErrorResponseEntity<ErrorResponse> errorResponseEntity =
        new ErrorResponseEntity<>(errorResponse);
    return errorResponseEntity;
  }

  @Override
  protected HttpHeaders buildHttpHeaders(WebRequest request) {
    return HttpHeadersUtil.buildResponseHttpHeaders(request);
  }
}
