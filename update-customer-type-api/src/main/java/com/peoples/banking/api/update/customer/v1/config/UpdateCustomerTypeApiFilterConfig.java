package com.peoples.banking.api.update.customer.v1.config

;

import static com.peoples.banking.api.update.customer.v1.config.UpdateCustomerTypeApiConstant.ROOT_INTERNAL_SERVICE_URL;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.BaasJWSVerificationFilter;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Customer API Filter Configuration
 *
 * <AUTHOR>
 */
@Configuration
public class UpdateCustomerTypeApiFilterConfig {


  @Autowired
  private com.peoples.banking.api.update.customer.v1.config.UpdateCustomerTypeApiProperty updateCustomerTypeApiProperty;

  @Autowired
  protected ObjectMapper objectMapper;

  @Autowired
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> customerAPILoggingFilter() {
    LoggingFilter customerAPILoggingFilter = new LoggingFilter();
    customerAPILoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    customerAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    customerAPILoggingFilter.setSystemName(updateCustomerTypeApiProperty.getSystemName());
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(customerAPILoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);

    return registrationBean;
  }

  @Bean
  public FilterRegistrationBean<BaasJWSVerificationFilter> baasJWSVerificationFilter() {
    FilterRegistrationBean<BaasJWSVerificationFilter> jwsVerificationFilterBean = new FilterRegistrationBean<>();
    jwsVerificationFilterBean.setFilter(new BaasJWSVerificationFilter(objectMapper, serviceAccountAdapter));
    jwsVerificationFilterBean.setOrder(Integer.MAX_VALUE);
    jwsVerificationFilterBean.addUrlPatterns(ROOT_INTERNAL_SERVICE_URL + "/*");
    return jwsVerificationFilterBean;
  }

  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
