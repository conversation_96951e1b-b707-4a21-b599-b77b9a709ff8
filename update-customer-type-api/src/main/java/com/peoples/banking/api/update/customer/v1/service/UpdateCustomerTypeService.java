package com.peoples.banking.api.update.customer.v1.service;

import java.util.Optional;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

import com.peoples.banking.adapter.base.exception.common.BaseException;
import com.peoples.banking.api.customer.v1.dto.CustomerDto;
import com.peoples.banking.api.customer.v1.dto.NameDto;
import com.peoples.banking.api.customer.v1.mapper.CustomerEntityMapper;
import com.peoples.banking.api.customer.v1.mapper.UpdateCustomerMapper;
import com.peoples.banking.api.update.customer.v1.dto.UpdateCustomerTypeRequest;
import com.peoples.banking.domain.customer.model.CustomerName;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.customer.CustomerAdapter;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.persistence.customer.entity.CustomerCodeType;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Enrollments;
import com.peoples.banking.persistence.customer.entity.Products;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import com.peoples.banking.util.api.common.service.InteracService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class UpdateCustomerTypeService extends InteracService {

  @Autowired
  private CustomersRepository customersRepository;
  @Autowired
  private UpdateCustomerMapper updateCustomerMapper;
  @Autowired
  private CustomerEntityMapper customerEntityMapper;
  @Autowired
  private CustomerAdapter customerAdapter;
  @Autowired
  private JsonConverter jsonConverter;


  public void updateCustomerType(String customerId, UpdateCustomerTypeRequest request, ServiceAccountResponse serviceAccountResponse) {
    try {
      Optional<Customers> customersOption = customersRepository
              .findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountResponse.getRefId());
      if (customersOption.isPresent()) {
        log.debug("customer={} with service account id={} found", customerId, serviceAccountResponse.getRefId());
        Customers customer = customersOption.get();
        validateCustomerName(customer.getTypeCd(), request.getType(), request.getName());
        String enrollmentId = null;
        for (Enrollments enrollments : customer.getEnrollments()) {
          Products products = enrollments.getProducts();
          if (products.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
            if (enrollments.isActive()) {
              enrollmentId = enrollments.getNetworkEnrollmentId();
            } else {
              throw new ValidationException(ErrorProperty.PRODUCT_DISABLED.name());
            }

            break;
          }
        }
        customer.setTypeCd(request.getType());
        if (request.getName() != null) {
          final NameDto nameDto = updateCustomerMapper.updateCustomerRequestToNameDto(request.getName());
          customer.setNameJson(jsonConverter.toJsonString(nameDto.getNameJsonDto()));
        }
        final CustomerDto dto = customerEntityMapper.customersToCustomerDto(customer);
        final Customer interacCustomer = updateCustomerMapper.customerDtoToCustomer(dto);
        customerAdapter.updateCustomer(enrollmentId, interacCustomer, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
        customersRepository.save(customer);
        log.debug("customer updated");
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountResponse.getRefId());
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) {// Exceptions when calling Adapter
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

  }

  private void validateCustomerName(CustomerCodeType currentType, CustomerCodeType newType, CustomerName name) {
    if (currentType == CustomerCodeType.INDIVIDUAL
            && (newType == CustomerCodeType.CORPORATION || newType == CustomerCodeType.SMALL_BUSINESS)
            && (name == null || name.getBusinessName() == null)) {
      throw new ValidationException(BaseException.ErrorCode.INVALID_INPUT.name(), "missing business name for business customer");
    }
    if ((currentType == CustomerCodeType.CORPORATION || currentType == CustomerCodeType.SMALL_BUSINESS)
            && newType == CustomerCodeType.INDIVIDUAL
            && (name == null || name.getIndividualName() == null)) {
      throw new ValidationException(BaseException.ErrorCode.INVALID_INPUT.name(), "missing individual name for individual customer");
    }
    if (name != null && StringUtils.isBlank(name.getDisplayName())) {
      throw new ValidationException(BaseException.ErrorCode.INVALID_INPUT.name(), "missing display name");
    }
  }

  /**
   * implement the customer validation error and assign corresponding error code
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    ErrorEntity error = new ErrorEntity();

    if (annotation instanceof NotNull) {
      error.setErrorCode(ErrorProperty.MISSING_FIELD.name());
    } else if (annotation instanceof NotBlank) {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());
    } else {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());
    }
    return error;
  }


}
