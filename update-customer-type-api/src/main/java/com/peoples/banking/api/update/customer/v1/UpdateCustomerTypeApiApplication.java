package com.peoples.banking.api.update.customer.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.peoples.banking.api.update.customer",
    "com.peoples.banking.api.customer.v1.mapper",
    "com.peoples.banking.persistence.customer",
    "com.peoples.banking.util",
    "com.peoples.banking.adapter.pb.system",
    "com.peoples.banking.adapter.pb.serviceaccount",
    "com.peoples.banking.partner.adapter.interac.common",
    "com.peoples.banking.partner.adapter.interac.customer",
    "com.peoples.banking.partner.adapter.interac.registration"})
@EntityScan("com.peoples.banking.persistence.customer")

/**
 * Spring Boot main application.
 */
public class UpdateCustomerTypeApiApplication {

  public static void main(String[] args) {
    SpringApplication.run(UpdateCustomerTypeApiApplication.class, args);
  }
}
