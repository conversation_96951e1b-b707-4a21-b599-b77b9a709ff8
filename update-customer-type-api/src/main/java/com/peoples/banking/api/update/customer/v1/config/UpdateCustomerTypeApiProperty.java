package com.peoples.banking.api.update.customer.v1.config

;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class UpdateCustomerTypeApiProperty {
  
  /**
   * Customer API System Name.
   */
  @Value("${customer.api.system.name}")
  private String systemName;

  /**
   * Customer API time to live value.
   */
  @Value("${customer.api.timetolive:30000}")
  private int timeToLive;
  
}
