package com.peoples.banking.api.update.customer.v1.controller.advice;

import com.peoples.banking.api.update.customer.v1.converter.ErrorResponseConverter;
import com.peoples.banking.domain.customer.model.Error;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.util.api.common.HttpHeadersUtil;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.controller.advice.APIServiceControllerAdvice;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import java.util.Collections;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.ControllerAdvice;

@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
public class CustomerServiceControllerAdvice extends APIServiceControllerAdvice {

  @Autowired
  private ErrorResponseConverter errorConverter;

  /**
   * {@inheritDoc}
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(String errorCode, String additionalInformation) {
    Error error = errorConverter.buildError(errorCode, additionalInformation);
    ErrorResponse errorResponse = errorConverter.buildErrorResponse(Collections.singletonList(error));

    return new ErrorResponseEntity<>(errorResponse);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return HttpHeadersUtil.buildResponseHttpHeaders(request);
  }

  /**
   * {@inheritDoc}
   */
  @Override
  protected String convertToErrorCode(String responseCode) {
    return ErrorProperty.UNEXPECTED_ERROR.name();
  }
}
