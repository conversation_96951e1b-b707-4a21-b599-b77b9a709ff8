package com.peoples.banking.api.update.customer.v1.controller;

import com.peoples.banking.api.update.customer.v1.config.UpdateCustomerTypeApiConstant;
import com.peoples.banking.api.update.customer.v1.config.UpdateCustomerTypeApiProperty;
import com.peoples.banking.api.update.customer.v1.dto.UpdateCustomerTypeRequest;
import com.peoples.banking.api.update.customer.v1.service.UpdateCustomerTypeService;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.controller.APIController;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * The Rest controller for Customer Management.
 */
@RestController
public class UpdateCustomerTypeController extends APIController {

  @Autowired
  private UpdateCustomerTypeService service;

  @Autowired
  private UpdateCustomerTypeApiProperty properties;

  @PerfLogger
  @PatchMapping(value = UpdateCustomerTypeApiConstant.UPDATE_CUSTOMER_TYPE_URL, produces = {APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> updateCustomerType(
          @RequestHeader(name = APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, required = true) String serviceAccountApiToken,
          @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
          @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
          @PathVariable String customerId,
          @Valid @RequestBody UpdateCustomerTypeRequest request) throws Exception {
    // check time to live and service account
    ServiceAccountResponse serviceAccountResponse = initialCheck(interactionTime, serviceAccountApiToken, null, interactionId, true, false, true);

    // call service for business check and process
    service.updateCustomerType(customerId, request, serviceAccountResponse);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId, false);

    // return response
    return new ResponseEntity<>(null, headers, HttpStatus.NO_CONTENT);
  }

  /**
   * return Customer API's time to live value
   */
  @Override
  protected int getApiTimeToLiveValue() {
    return properties.getTimeToLive();
  }
}
