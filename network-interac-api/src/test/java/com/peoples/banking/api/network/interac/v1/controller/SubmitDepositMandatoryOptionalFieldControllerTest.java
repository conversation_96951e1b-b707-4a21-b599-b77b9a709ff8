package com.peoples.banking.api.network.interac.v1.controller;

import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.config.InteracConstant;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalStatusReason1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.StatusReason6Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.StatusReasonInformation12;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositRequest;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestInstance(Lifecycle.PER_CLASS)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class SubmitDepositMandatoryOptionalFieldControllerTest {

    /**
     * Instance variables -- deposit payment request
     */
    private static final String PARTICIPANT_USER_ID = "SB3s9pbxIqRK";

    /**
     * Instance variables -- clearing system reference id
     */
    private static final String CLEARING_SYSTEM_REF_ID = "CAJBQDJf";

    // TODO eventually add valid JWS tokens to test
    static {
        System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");
    }

    /**
     * Local mock controller -- port.
     */
    @LocalServerPort
    private int port;

    /**
     * Local mock controller -- base URL.
     */
    private URL base;

    /**
     * Local mock controller -- RestTemplate (test).
     */
    @Autowired
    private TestRestTemplate template;

    /**
     * Instance variables -- account number
     */
    private SubmitDepositRequest request;

    /**
     * Initialize request objects before each invocation.
     *
     * @throws Exception
     */
    @BeforeAll
    public void setUp() throws Exception {
        this.base = new URL("http://localhost:" + port);
    }

    /**
     * Builds valid payload header.
     *
     * @return
     */
    private HttpHeaders buildHttpHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("x-et-participant-id", "CA000621");
        headers.add("x-et-participant-user-id", PARTICIPANT_USER_ID);
        headers.add("x-et-indirect-connector-id", null);
        headers.add("x-et-request-id", java.util.UUID.randomUUID().toString());
        headers.add("x-et-retry-indicator", "true");
        headers.add("x-et-channel-indicator", "ETRANSFER_SYSTEM");
        headers.add("x-et-api-signature",
                "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        headers.add("x-et-api-signature-type", "PAYLOAD_DIGEST_SHA256");
        headers.add("x-et-transaction-time", Instant.now().toString());
        return headers;
    }

    /**
     * Builds valid payload.
     *
     * @throws Exception
     */
    private SubmitDepositRequest buildPayload(String participantUserId) {
        return DepositPaymentTestUtil.createSubmitDepositRequest(participantUserId);
    }

    /**
     * Builds valid payload with optional StatusReasonInformation
     *
     * @throws Exception
     */
    private SubmitDepositRequest buildPayloadWithStatusReason(String participantUserId) {

        StatusReasonInformation12 statusReasonInfo = new StatusReasonInformation12();
        StatusReason6Choice reason = new StatusReason6Choice();
        reason.setCode(ExternalStatusReason1Code.CH21);
        statusReasonInfo.setReason(reason);

        SubmitDepositRequest request = DepositPaymentTestUtil.createSubmitDepositRequest(PARTICIPANT_USER_ID);
        request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).addStatusReasonInformationItem(statusReasonInfo);

        return request;
    }
    
    @Test
    public void submitDepositPayment_noFIToFIPaymentStatusReport_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.setFiToFiPaymentStatusReport(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noGroupHeader_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().setGroupHeader(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noMessageIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().setMessageIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noCreationDateTime_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().setCreationDatetime(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructingAgent_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().setInstructingAgent(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructingAgentFinancialInstitutionIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructingAgent().setFinancialInstitutionIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructingAgentClearingSystemMemberIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructingAgent().getFinancialInstitutionIdentification()
                .setClearingSystemMemberIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructingAgentMemberIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructingAgent().getFinancialInstitutionIdentification()
                .getClearingSystemMemberIdentification().setMemberIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructedAgent_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().setInstructedAgent(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructedAgentFinancialInstitutionIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent().setFinancialInstitutionIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructedAgentClearingSystemMemberIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent().getFinancialInstitutionIdentification()
                .setClearingSystemMemberIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noInstructedAgentMemberIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent().getFinancialInstitutionIdentification()
                .getClearingSystemMemberIdentification().setMemberIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }


    @Test
    public void submitDepositPayment_noTransactionInformationAndStatus_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().setTransactionInformationAndStatus(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_invalidTransactionInformationAndStatus_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().addTransactionInformationAndStatusItem(new PaymentTransaction110());

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noOriginalGroupInformation_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setOriginalGroupInformation(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noOriginalGroupInformationOriginalMessageIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .getOriginalGroupInformation().setOriginalMessageIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noOriginalGroupInformationOriginalMessageNameIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .getOriginalGroupInformation().setOriginalMessageNameIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noOriginalEndToEndIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setOriginalEndToEndIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noOriginalTransactionIdentification_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setOriginalTransactionIdentification(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noTransactionStatus_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setTransactionStatus(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noStatusReasonInfoReasonCode_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .getStatusReasonInformation().get(0).getReason().setCode(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noStatusReasonInfoReasonCodeNARRNoAdditionalInfo_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
            .getStatusReasonInformation().get(0).getReason().setCode(ExternalStatusReason1Code.NARR);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_StatusReasonInfoReasonCodeNARRAdditionalInfo_success() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- add element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
            .getStatusReasonInformation().get(0).getReason().setCode(ExternalStatusReason1Code.NARR);
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
            .getStatusReasonInformation().get(0).addAdditionalInformationItem("Some narrative");

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_StatusReasonInfoReasonCode_success() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noStatusReasonInfoReason_success() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .getStatusReasonInformation().get(0).setReason(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noStatusReasonInfo_success() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayloadWithStatusReason(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
            .setStatusReasonInformation(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noAccountServicerReference_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setAccountServicerReference(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }

    @Test
    public void submitDepositPayment_noClearingSystemReference_fail() {
        // URL
        String postUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", CLEARING_SYSTEM_REF_ID);

        // Header
        HttpHeaders header = buildHttpHeader();

        // Payload (Body)
        SubmitDepositRequest payload = buildPayload(CLEARING_SYSTEM_REF_ID);
        HttpEntity<SubmitDepositRequest> request = new HttpEntity<>(payload, header);

        // Unit under test -- remove element
        payload.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
                .setClearingSystemReference(null);

        // Unit under test
        ResponseEntity<ErrorModel> responseEntity = template.exchange(postUrl, HttpMethod.PUT, request, ErrorModel.class);

        // assertions (validate)
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        Assertions.assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }
}
