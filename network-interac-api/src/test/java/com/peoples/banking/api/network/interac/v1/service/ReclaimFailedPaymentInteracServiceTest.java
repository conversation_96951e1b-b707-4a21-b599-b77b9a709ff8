package com.peoples.banking.api.network.interac.v1.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.adapter.InteracResponseAdapter;
import com.peoples.banking.api.network.interac.v1.config.InteracErrorConstant;
import com.peoples.banking.api.network.interac.v1.config.InteracProperty;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.ReclaimFailedPaymentRequestMapper;
import com.peoples.banking.api.network.interac.v1.mapper.ServiceAccountProfileMapper;
import com.peoples.banking.api.network.interac.v1.mapper.ServiceAccountProfileMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.SubmitDepositPaymentRequestMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.converter.MessageIdConverter;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.domain.account.model.AccountTransactionReversalResponse;
import com.peoples.banking.domain.account.model.ReversalResultCode;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.domain.interac.deposit.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.deposit.model.ReclaimFailedPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReclaimFailedPaymentResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import lombok.SneakyThrows;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {SubmitDepositPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    PaymentStatusHistoryEntityMapperImpl.class, MessageIdConverter.class, ServiceAccountProfileMapperImpl.class,})
@SpringJUnitConfig()
public class ReclaimFailedPaymentInteracServiceTest {

  public static final String PAYMENT_ID = "CAJBQDJf";
  @InjectMocks
  private InteracService service;

  @Mock
  private InteracResponseAdapter adapter;

  @Mock
  private CustomerAdapter customerAdapter;

  @Mock
  private AccountAdapter accountAdapter;

  @Mock
  private SystemAdapter systemAdapter;

  @Mock
  private ServiceAccountAdapter serviceAccountAdapter;

  @Mock
  private InteracAdapterProperty interacAdapterProperty;

  @Mock
  private PaymentsRepository paymentsRepository;

  @Mock
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private InteracProperty interacProperty;

  @MockBean
  private ReclaimFailedPaymentRequestMapper reclaimFailedPaymentRequestMapper;

  @MockBean
  private InteracPaymentService interacPaymentService;

  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;

  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;

  private Payments depositPayment;

  private PaymentStatusHistory paymentStatusHistory;

  private ReclaimFailedPaymentRequest request;

  private AccountTransactionReversalResponse accountTransactionReversalResponse;

  private String correspondentId;

  private ServiceAccountResponse serviceAccountResponse;

  @SpyBean
  private ServiceAccountProfileMapper serviceAccountProfileMapper;

  @BeforeEach
  private void setup() {
    request = DepositPaymentTestUtil.createReclaimFailedPaymentRequest();
    depositPayment = DepositPaymentTestUtil.createDepositPayment(PaymentStatus.DEPOSIT_INITIATED);
    paymentStatusHistory = new PaymentStatusHistory();
    accountTransactionReversalResponse = DepositPaymentTestUtil.createAccountTransactionReversalResponse();
    correspondentId = "db11ec38-aa97-4835-8db9-82be60bdb779";
    serviceAccountResponse = DepositPaymentTestUtil
        .createServiceAccountResponse(DepositPaymentTestUtil.IN_API_TOKEN, DepositPaymentTestUtil.OUT_API_TOKEN,
            DepositPaymentTestUtil.SERVICE_ACCOUNT_REF_ID);

    MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
    RequestContextHolder.getRequestAttributes().setAttribute(APICommonUtilConstant.LOGGING_FIELD_GUIID, correspondentId,
        RequestAttributes.SCOPE_REQUEST);

    ReflectionTestUtils.setField(service, "reclaimFailedPaymentRequestMapper", reclaimFailedPaymentRequestMapper);
    ReflectionTestUtils.setField(service, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(interacPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
  }

  @AfterEach
  private void tearDown() {
    request = null;
    depositPayment = null;
    paymentStatusHistory = null;
    accountTransactionReversalResponse = null;
    correspondentId = null;
    serviceAccountResponse = null;
  }

  @SneakyThrows
  @ValueSource(strings = {"ACCEPTED", "AVAILABLE", "CANCELLED", "COMPLETE", "DECLINED", "DEPOSIT_INITIATED", "DEPOSIT_PENDING",
                          "DEPOSIT_FAILED", "EXPIRED", "FAILED", "FAILED_PENDING", "FAILED_PENDING_REVERSAL", "SECURITY_ANSWER_FAILURE",
                          "QUEUED"})
  @ParameterizedTest
  public void reclaimFailedPayment_status_success(String paymentStatus) {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doReturn(PaymentStatus.valueOf(paymentStatus)).when(reclaimFailedPaymentRequestMapper).interacPaymentStatusToPTCPaymentStatus(isA(
        com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.class));

    doReturn(accountTransactionReversalResponse).when(accountAdapter)
        .accountTransactionReversal(any(), any(), any(), any());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    doReturn(DepositPaymentTestUtil.createReclaimFailedPaymentResponse()).when(reclaimFailedPaymentRequestMapper).reclaimFailedPaymentRequestToReclaimFailedPaymentResponse(any(),
       any(), any());

    // Sync the request AccountServiceReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    ReclaimFailedPaymentResponse response = assertDoesNotThrow(
        () -> service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
            new MutableBoolean()));

    assertNotNull(response);
  }

  @Test
  public void reclaimFailedPayment_wrong_input_params_failed() {

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, null,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.MISSING_HEADERS_PATH_PARAM, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_wrong_participant_failed() {

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID + "x", PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_ID.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.INVALID_PARTICIPANT_ID, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_invalid_message_id_failed() {
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).setClearingSystemReference(null);

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.UNEXPECTED_ERROR, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_wrong_participant_id_failed() {
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    BranchAndFinancialInstitutionIdentification6 instructedAgent = request
        .getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent();

    instructedAgent.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().setMemberIdentification(null);


    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_ID.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.INVALID_PARTICIPANT_ID, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_wrong_payment_id_failed() {
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID + "x", request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.DEPOSIT_ID_CLEARING_SYS_REF_MISMATCH, error.getAdditionalInformation());
  }

  @Test
  public void reclaimFailedPayment_payment_record_select_failed() {
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doThrow(new IncorrectResultSizeDataAccessException(2)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ServiceException exception = assertThrows(ServiceException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), error.getAdditionalInformation());
  }

  @Test
  public void reclaimFailedPayment_wrong_payment_external_ref_id_failed() {
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setExternalRefId("bad_ref_id");
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.PAYMENT_NOT_FOUND, error.getAdditionalInformation());
  }

  @Test
  public void reclaimFailedPayment_wrong_account_number_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setAccountNumber(depositPayment.getAccountNumber() + "-11");
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.INVALID_ACCOUNT_NUMBER, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_payment_already_cancelled_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.CANCELLED.name());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PAYMENT_CANCELLED.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.PAYMENT_CANCELLED, error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_payment_amount_mismatch_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setAmount(depositPayment.getAmount().add(BigDecimal.TEN));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.AMOUNT_MISMATCH.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.AMOUNT_MISMATCH.getDescription(), error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_payment_transition_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.FAILED_PENDING.name());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getDescription(), error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_payment_not_found_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.empty()).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getDescription(), error.getAdditionalInformation());

  }

  @Test
  public void reclaimFailedPayment_wrong_network_enrollment_id_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setNetworkEnrollmentId(null);
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getDescription(), error.getAdditionalInformation());

  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_serviceException_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new ServiceException(new ErrorEntity())).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_timeoutException_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new TimeoutException()).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_responseException_service_account_not_found_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new ResponseException(404, ErrorProperty.RESOURCE_NOT_FOUND.name(), "service account not found")).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_responseException_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new ResponseException(404, ErrorProperty.INVALID_CUSTOMER.name(), "service account not found")).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_adapterException_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new AdapterException()).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_retrieveServiceAccountByRefId_exception_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));

    doThrow(new RuntimeException()).when(serviceAccountAdapter)
        .retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), error.getAdditionalInformation());
  }


  @SneakyThrows
  @Test
  public void reclaimFailedPayment_accountTransactionReverse_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));
    accountTransactionReversalResponse.setResultCode(ReversalResultCode.SYSTEM_NOT_AVAILABLE);
    doReturn(accountTransactionReversalResponse).when(accountAdapter)
        .accountTransactionReversal(any(), any(), any(), any());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), error.getAdditionalInformation());
  }


  @SneakyThrows
  @Test
  public void reclaimFailedPayment_accountTransactionReverse_not_found_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));
    accountTransactionReversalResponse.setResultCode(ReversalResultCode.NOT_FOUND);
    doReturn(accountTransactionReversalResponse).when(accountAdapter)
        .accountTransactionReversal(any(), any(), any(), any());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PAYMENT_NOT_FOUND.getDescription(), error.getAdditionalInformation());
  }

  @SneakyThrows
  @Test
  public void reclaimFailedPayment_accountTransactionReverse_declined_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageId(isA(String.class), isA(String.class));
    accountTransactionReversalResponse.setResultCode(ReversalResultCode.DECLINED);
    doReturn(accountTransactionReversalResponse).when(accountAdapter)
        .accountTransactionReversal(any(), any(), any(), any());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reclaimFailedPayment(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), PAYMENT_ID, request,
          new MutableBoolean());
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_CANNOT_ACCEPT_DIRECT_DEPOSIT.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_CANNOT_ACCEPT_DIRECT_DEPOSIT.getDescription(), error.getAdditionalInformation());
  }

}
