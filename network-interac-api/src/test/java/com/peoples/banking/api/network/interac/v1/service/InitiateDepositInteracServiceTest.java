package com.peoples.banking.api.network.interac.v1.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.adapter.InteracResponseAdapter;
import com.peoples.banking.api.network.interac.v1.config.InteracErrorConstant;
import com.peoples.banking.api.network.interac.v1.config.InteracProperty;
import com.peoples.banking.api.network.interac.v1.mapper.InitiateDepositPaymentRequestMapper;
import com.peoples.banking.api.network.interac.v1.mapper.InitiateDepositPaymentRequestMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.ServiceAccountProfileMapper;
import com.peoples.banking.api.network.interac.v1.mapper.ServiceAccountProfileMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.converter.MessageIdConverter;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.domain.account.model.AccountEligibilityRequest;
import com.peoples.banking.domain.account.model.AccountEligibilityResponse;
import com.peoples.banking.domain.account.model.EligibilityResultCode;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.system.model.FinancialInstitutionInformation;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2ChoiceOneOf.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.TransactionAuthorization.AuthorizationTypeEnum;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {InitiateDepositPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class,
    ServiceAccountProfileMapperImpl.class, PaymentStatusHistoryEntityMapperImpl.class, MessageIdConverter.class})
@SpringJUnitConfig()
@MockitoSettings(strictness = Strictness.LENIENT)
public class InitiateDepositInteracServiceTest {

  private static final String DEPOSIT_CLEARING_REF_ID = "CAJBQDJf";
  @InjectMocks
  private InteracService service;
  @Mock
  private InteracResponseAdapter adapter;
  @Mock
  private CustomerAdapter customerAdapter;
  @Mock
  private AccountAdapter accountAdapter;
  @Mock
  private SystemAdapter systemAdapter;
  @Mock
  private ServiceAccountAdapter serviceAccountAdapter;
  @Mock
  private InteracAdapterProperty interacAdapterProperty;
  @Mock
  private PaymentsRepository paymentsRepository;
  @Mock
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
  @Mock
  private InteracProperty interacProperty;
  @SpyBean
  private InitiateDepositPaymentRequestMapper initiateDepositPaymentRequestMapper;
  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;
  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;
  @SpyBean
  private ServiceAccountProfileMapper serviceAccountProfileMapper;
  @SpyBean
  private MessageIdConverter messageIdConverter;
  @MockBean
  private InteracPaymentService interacPaymentService;
  private RetrieveCustomerProductResponse customerProductResponse;
  private DepositPaymentRequest depositPaymentRequest;
  private Payments requestPayment;
  private Payments depositPayment;
  private PaymentStatusHistory paymentStatusHistory;
  private AccountEligibilityResponse accountEligibilityResponse;
  private String correspondentId;
  private ServiceAccountResponse serviceAccountResponse;
  private FinancialInstitutionInformation financialInstitutionInformation;

  @BeforeEach
  private void setup() throws AdapterException, ResponseException, TimeoutException, TBDException, JsonProcessingException {
    String requestId = "REQU123";
    String accountNumber = "621-00001-************";
    Double amount = 100.15;

    requestPayment =
        DepositPaymentTestUtil.createRequestPayments(requestId, DepositPaymentTestUtil.PARTICIPANT_USER_ID, accountNumber, amount);
    depositPayment = DepositPaymentTestUtil.createDepositPayment(requestId, DepositPaymentTestUtil.PARTICIPANT_USER_ID, accountNumber,
        amount, PaymentStatus.ACCEPTED);

    customerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(DepositPaymentTestUtil.PARTICIPANT_USER_ID);
    depositPaymentRequest = DepositPaymentTestUtil.createDepositPaymentRequest(DepositPaymentTestUtil.PARTICIPANT_USER_ID,
        DEPOSIT_CLEARING_REF_ID, requestId, amount, accountNumber);

    paymentStatusHistory = new PaymentStatusHistory();
    accountEligibilityResponse = DepositPaymentTestUtil.createAccountEligibilityResponse();
    correspondentId = "db11ec38-aa97-4835-8db9-82be60bdb779";
    Answer<Void> answer = invocation -> {
      Payments payment = invocation.getArgument(0, Payments.class);
      PaymentStatus status = invocation.getArgument(1, PaymentStatus.class);
      payment.setStatus(status.getValue());
      return null;
    };
    doAnswer(answer).when(interacPaymentService).updatePaymentAndStatus(any(), any(), any());

    serviceAccountResponse = DepositPaymentTestUtil
        .createServiceAccountResponse(DepositPaymentTestUtil.IN_API_TOKEN, DepositPaymentTestUtil.OUT_API_TOKEN,
            DepositPaymentTestUtil.SERVICE_ACCOUNT_REF_ID);

    MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
    RequestContextHolder.getRequestAttributes().setAttribute(APICommonUtilConstant.LOGGING_FIELD_GUIID, correspondentId,
        RequestAttributes.SCOPE_REQUEST);

    ReflectionTestUtils.setField(service, "initiateDepositPaymentRequestMapper", initiateDepositPaymentRequestMapper);
    ReflectionTestUtils.setField(service, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(interacPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(initiateDepositPaymentRequestMapper, "messageIdConverter", messageIdConverter);
    lenient().doReturn(Boolean.TRUE).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    financialInstitutionInformation = DepositPaymentTestUtil.createFinancialInstitutionInformation();
  }

  @AfterEach
  private void teardown() {
    customerProductResponse = null;
    depositPaymentRequest = null;
    requestPayment = null;
    depositPayment = null;
    paymentStatusHistory = null;
    accountEligibilityResponse = null;
    correspondentId = null;
    serviceAccountResponse = null;
    financialInstitutionInformation = null;
  }

  @Test
  public void initiateDeposit_success() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(response);
    assertEquals(correspondentId.replace("-", ""), response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());

  }

  @Test
  public void initiateDeposit_requestPaymentInAvailableStatus_success()
      throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    requestPayment.setStatus(PaymentStatus.AVAILABLE.toString());
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(response);
    assertEquals(correspondentId.replace("-", ""), response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
  }

  @Test
  public void initiateDeposit_depositPaymentIsFailedPendingStatus_success()
      throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.FAILED_PENDING.toString());
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(response);
    assertEquals(correspondentId.replace("-", ""), response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
  }

  @Test
  public void initiateDeposit_indirect_connector_id_success() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    String indirectConnectorId = "CA000621";
    serviceAccountResponse.setIndirectConnectorId(indirectConnectorId);
    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), indirectConnectorId);
    });

    assertNotNull(response);
    assertEquals(correspondentId.replace("-", ""), response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
  }

  @Test
  public void initiateDeposit_requestPaymentInQueuedStatus_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    requestPayment.setStatus(PaymentStatus.QUEUED.toString());
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));
    lenient().doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.INVALID_STATUS, error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_invalidParticipantId_fail() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit("CA999999", DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID, depositPaymentRequest,
          new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_ID.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_ParticipantIdInstructedAgentIdNotMatch_fail() {

    depositPaymentRequest.getFiToFiCustomerCreditTransfer().getGroupHeader().getInstructedAgent().getFinancialInstitutionIdentification()
        .getClearingSystemMemberIdentification().setMemberIdentification("CA000000");
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PARTICIPANT_ID_MISMATCH.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PARTICIPANT_ID_MISMATCH.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_IndirectConnectorCreditorAgentIdNotMatch_fail() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
       service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), "bad_indirect_connector");
    });

    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_ParticipantIdCreditorAgentMemberIdNotMatch_fail()
      throws AdapterException, ResponseException, TimeoutException, TBDException {

    depositPaymentRequest.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getCreditorAgent()
        .getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().setMemberIdentification("CA000000");
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_participantUserIdCreditorOrganizationIdNotMatch_fail() {

    depositPaymentRequest.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getCreditor()
        .getIdentification().getOrganisationIdentification().getOther().get(0).setIdentification("participantUserId");
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.PARTICIPANT_USER_ID_MISMATCH.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.PARTICIPANT_USER_ID_MISMATCH.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_emptyTransactionAuthorization_fail() {

    depositPaymentRequest.setTransactionAuthorization(null);
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    // custom description, so do not check explicitly here.

  }

  @Test
  public void initiateDeposit_requestPaymentNotFound_fail() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class),
            isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_TRANSACTION_AUTHORIZATION_TOKEN.getCode(), error.getErrorCode());
    // returning custom error message with details, so don't check it here
  }

  @Test
  public void initiateDeposit_depositPaymentIsAlreadyComplete_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.DUPLICATE_PAYMENT_REF_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.DUPLICATE_PAYMENT_REF_ID.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_depositPaymentIsDepositInitiated_success()
      throws AdapterException, ResponseException, TimeoutException, TBDException {

    depositPayment.setStatus(PaymentStatus.DEPOSIT_INITIATED.toString());

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.SUCCESS);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(response);
    assertEquals(correspondentId.replace("-", ""), response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());

  }

  @Test
  public void initiateDeposit_invalidParticipantUserId_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doThrow(new ResponseException(400, ErrorProperty.RESOURCE_NOT_FOUND.name(), "")).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_requestPaymentNotInValidStatus_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    requestPayment.setStatus(PaymentStatus.EXPIRED.toString());
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), error.getErrorCode());
    assertEquals(InteracErrorConstant.INVALID_STATUS, error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_authorizationTypeIsAccountAliasPaymentAndPaymentTypeIsFulfillRequestForPayment_fail() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPaymentRequest.getTransactionAuthorization().setAuthorizationType(AuthorizationTypeEnum.ACCOUNT_ALIAS_PAYMENT);
    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_TRANSACTION_AUTHORIZATION_TYPE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_TRANSACTION_AUTHORIZATION_TYPE.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_authorizationTypeIsRequestForPaymentAndPaymentTypeIsAccountAliasPayment_fail() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPaymentRequest.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getPaymentTypeInformation()
        .getLocalInstrument().setProprietary(ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT);
    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_TRANSACTION_AUTHORIZATION_TYPE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_TRANSACTION_AUTHORIZATION_TYPE.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void initiateDeposit_amountNotMatch_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    depositPaymentRequest.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getInterbankSettlementAmount()
        .setAmount(new BigDecimal(20));
    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.AMOUNT_MISMATCH.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.AMOUNT_MISMATCH.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsInvalidAccountFormat() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.INVALID_ACCOUNT_FORMAT);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.INVALID_ACCOUNT_INFO.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.INVALID_ACCOUNT_INFO.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsNotFound() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.NOT_FOUND);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_NOT_EXISTS.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_NOT_EXISTS.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsNotAvailable() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.NOT_AVAILABLE);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_INACTIVE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_INACTIVE.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsNotAvailableTemporary() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.NOT_AVAILABLE_TEMPORARY);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_TEMP_INACTIVE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_TEMP_INACTIVE.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsFrozen() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.FROZEN);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsLimitExceeded() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.LIMIT_EXCEEDED);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.DIRECT_DEPOSIT_LIMIT_EXCEEDED.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.DIRECT_DEPOSIT_LIMIT_EXCEEDED.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsCurrencyNotSupported() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.CURRENCY_NOT_SUPPORTED);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.CURRENCY_MISMATCH.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.CURRENCY_MISMATCH.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsSystemTimeOut() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.SYSTEM_TIMEOUT);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ServiceException exception = assertThrows(ServiceException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(), error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsDeclined() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.DECLINED);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.ACCOUNT_CANNOT_ACCEPT_DIRECT_DEPOSIT.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.ACCOUNT_CANNOT_ACCEPT_DIRECT_DEPOSIT.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsFraudRejected() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.FRAUD_REJECT);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.FRAUD_RISK_EXCEEDED.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.FRAUD_RISK_EXCEEDED.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsComplianceRejected() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.COMPLIANCE_REJECT);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.COMPLIANCE_RISK_EXCEEDED.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.COMPLIANCE_RISK_EXCEEDED.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_AccountIsSystemNotAvailable() throws AdapterException, ResponseException, TimeoutException, TBDException {
    doReturn(customerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    accountEligibilityResponse.setResultCode(EligibilityResultCode.SYSTEM_NOT_AVAILABLE);
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    ServiceException exception = assertThrows(ServiceException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, DEPOSIT_CLEARING_REF_ID,
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_customerDisabled_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {

    String customerDisabledErrorCode = "CUSTOMER_DISABLED";

    ResponseException responseException = new ResponseException(400, customerDisabledErrorCode, null);

    doThrow(responseException).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf",
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.DISABLED_PARTICIPANT_USER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.DISABLED_PARTICIPANT_USER_ID.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void initiateDeposit_productDisabled_fail() throws AdapterException, ResponseException, TimeoutException, TBDException {

    String productDisabledErrorCode = "PRODUCT_DISABLED";

    ResponseException responseException = new ResponseException(400, productDisabledErrorCode, null);

    doThrow(responseException).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
        isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.ofNullable(null)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.initiateDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf",
          depositPaymentRequest, new MutableBoolean(), null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.DISABLED_PARTICIPANT_USER_ID.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.DISABLED_PARTICIPANT_USER_ID.getDescription(),
        error.getAdditionalInformation());
  }

}
