package com.peoples.banking.api.network.interac.v1.controller;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.adapter.InteracResponseAdapter;
import com.peoples.banking.api.network.interac.v1.config.InteracConstant;
import com.peoples.banking.api.network.interac.v1.config.InteracErrorConstant;
import com.peoples.banking.api.network.interac.v1.service.InteracService;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.domain.account.model.AccountTransactionRequest;
import com.peoples.banking.domain.account.model.AccountTransactionResponse;
import com.peoples.banking.domain.account.model.TransactionResultCode;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature.JwsVerificationResult;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.deposit.model.OriginalGroupInformation29;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.time.Instant;
import javax.persistence.PersistenceException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class SubmitDepositInteracControllerTest {

  // TODO eventually add valid JWS tokens to test
  static {
    System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");
  }

  @Autowired
  private MockMvc mvc;
  @MockBean
  private InteracResponseAdapter adapter;
  @MockBean
  private PaymentsRepository paymentsRepository;
  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
  @MockBean
  private CustomerAdapter customerAdapter;
  @MockBean
  private AccountAdapter accountAdapter;
  @InjectMocks
  private InteracService interacService;
  @MockBean
  private SystemAdapter systemAdapter;
  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;
  @InjectMocks
  private InteracController controller;
  private SubmitDepositRequest request;
  private Payments depositPayment;
  private PaymentStatusHistory paymentStatusHistory;
  private String channelIndicator;
  private String participantUserId;
  private String signatureType;
  private RetrieveCustomerProductResponse retrieveCustomerProductResponse;
  private AccountTransactionResponse accountTransactionResponse;
  private ServiceAccountResponse serviceAccountResponse;

  @BeforeEach
  public void setup() throws AdapterException, ResponseException, TimeoutException, TBDException {
    request = DepositPaymentTestUtil.createSubmitDepositRequest();
    depositPayment = DepositPaymentTestUtil.createDepositPayment(PaymentStatus.DEPOSIT_INITIATED);
    paymentStatusHistory = new PaymentStatusHistory();
    channelIndicator = "ETRANSFER_SYSTEM";
    signatureType = "PAYLOAD_DIGEST_SHA256";

    participantUserId = "SB3s9pbxIqRK";

    retrieveCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
    accountTransactionResponse = DepositPaymentTestUtil.createAccountTransactionResponse();
    serviceAccountResponse = DepositPaymentTestUtil
        .createServiceAccountResponse(DepositPaymentTestUtil.IN_API_TOKEN, DepositPaymentTestUtil.OUT_API_TOKEN,
            DepositPaymentTestUtil.SERVICE_ACCOUNT_REF_ID);
    lenient().doReturn(Boolean.TRUE).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void teardown() {
    request = null;
    depositPayment = null;
    paymentStatusHistory = null;
    channelIndicator = null;
    signatureType = null;
    retrieveCustomerProductResponse = null;
    accountTransactionResponse = null;
    serviceAccountResponse = null;
  }


  @Test
  public void submitDeposit_success() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    SubmitDepositResponse response = assertDoesNotThrow(
        () -> JsonUtil.toObject(result.getResponse().getContentAsString(), SubmitDepositResponse.class));

    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime());
    assertEquals(DepositPaymentTestUtil.FI_ID_PTC,
        response.getFiToFiPaymentStatusReport().getGroupHeader().getInstructingAgent().getFinancialInstitutionIdentification()
            .getClearingSystemMemberIdentification().getMemberIdentification());
    assertEquals(DepositPaymentTestUtil.FI_ID_BMO,
        response.getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent().getFinancialInstitutionIdentification()
            .getClearingSystemMemberIdentification().getMemberIdentification());

    PaymentTransaction110 reqTransactionInfoAndStatus = request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0);
    OriginalGroupInformation29 reqOriginalGroupInfo = reqTransactionInfoAndStatus.getOriginalGroupInformation();

    PaymentTransaction110 resTransactionInfoAndStatus = response.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0);
    OriginalGroupInformation29 resOriginalGroupInfo = resTransactionInfoAndStatus.getOriginalGroupInformation();

    assertEquals(reqOriginalGroupInfo.getOriginalMessageIdentification(), resOriginalGroupInfo.getOriginalMessageIdentification());
    assertEquals(reqOriginalGroupInfo.getOriginalMessageNameIdentification(), resOriginalGroupInfo.getOriginalMessageNameIdentification());

    assertEquals(reqTransactionInfoAndStatus.getOriginalInstructionIdentification(),
        resTransactionInfoAndStatus.getOriginalInstructionIdentification());
    assertEquals(reqTransactionInfoAndStatus.getOriginalEndToEndIdentification(),
        resTransactionInfoAndStatus.getOriginalEndToEndIdentification());
    assertEquals(reqTransactionInfoAndStatus.getOriginalTransactionIdentification(),
        resTransactionInfoAndStatus.getOriginalTransactionIdentification());
    assertNotNull(resTransactionInfoAndStatus.getAcceptanceDatetime());
    assertEquals(reqTransactionInfoAndStatus.getAccountServicerReference(), resTransactionInfoAndStatus.getAccountServicerReference());
    assertEquals(reqTransactionInfoAndStatus.getClearingSystemReference(), resTransactionInfoAndStatus.getClearingSystemReference());
  }

  @Test
  public void submitDeposit_CommittedAlready_success() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, DepositPaymentTestUtil.PARTICIPANT_USER_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    SubmitDepositResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), SubmitDepositResponse.class);
    });

    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime());
    assertEquals(DepositPaymentTestUtil.FI_ID_PTC,
        response.getFiToFiPaymentStatusReport().getGroupHeader().getInstructingAgent().getFinancialInstitutionIdentification()
            .getClearingSystemMemberIdentification().getMemberIdentification());
    assertEquals(DepositPaymentTestUtil.FI_ID_BMO,
        response.getFiToFiPaymentStatusReport().getGroupHeader().getInstructedAgent().getFinancialInstitutionIdentification()
            .getClearingSystemMemberIdentification().getMemberIdentification());

    PaymentTransaction110 reqTransactionInfoAndStatus = request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0);
    OriginalGroupInformation29 reqOriginalGroupInfo = reqTransactionInfoAndStatus.getOriginalGroupInformation();

    PaymentTransaction110 resTransactionInfoAndStatus = response.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0);
    OriginalGroupInformation29 resOriginalGroupInfo = resTransactionInfoAndStatus.getOriginalGroupInformation();

    assertEquals(reqOriginalGroupInfo.getOriginalMessageIdentification(), resOriginalGroupInfo.getOriginalMessageIdentification());
    assertEquals(reqOriginalGroupInfo.getOriginalMessageNameIdentification(), resOriginalGroupInfo.getOriginalMessageNameIdentification());

    assertEquals(reqTransactionInfoAndStatus.getOriginalInstructionIdentification(),
        resTransactionInfoAndStatus.getOriginalInstructionIdentification());
    assertEquals(reqTransactionInfoAndStatus.getOriginalEndToEndIdentification(),
        resTransactionInfoAndStatus.getOriginalEndToEndIdentification());
    assertEquals(reqTransactionInfoAndStatus.getOriginalTransactionIdentification(),
        resTransactionInfoAndStatus.getOriginalTransactionIdentification());
    assertNotNull(resTransactionInfoAndStatus.getAcceptanceDatetime());
    assertEquals(reqTransactionInfoAndStatus.getAccountServicerReference(), resTransactionInfoAndStatus.getAccountServicerReference());
    assertEquals(reqTransactionInfoAndStatus.getClearingSystemReference(), resTransactionInfoAndStatus.getClearingSystemReference());
  }

  @Test
  public void submitDeposit_depositId_empty_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "    "))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, DepositPaymentTestUtil.PARTICIPANT_USER_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracErrorConstant.MISSING_HEADERS_PATH_PARAM, response.getText());

  }

  @Test
  public void submitDeposit_participantId_invalid_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, " ")
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, DepositPaymentTestUtil.PARTICIPANT_USER_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), response.getCode());
  }

  @Test
  public void submitDepositPayment_jpaException_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    doThrow(new JpaSystemException(new RuntimeException())).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), response.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_transaction_not_found_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.empty()).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, DepositPaymentTestUtil.PARTICIPANT_USER_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), response.getCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_StatusNotValid_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setStatus(PaymentStatus.ACCEPTED.toString());
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, DepositPaymentTestUtil.PARTICIPANT_USER_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), response.getCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        response.getText());

  }

  @Test
  public void submitDeposit_Account_Frozen_failed() throws JsonProcessingException, Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to FROZEN
    accountTransactionResponse.setResultCode(TransactionResultCode.FROZEN);

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getCode(), response.getCode());
    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_NotAvailable_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to NOT_AVAILABLE
    accountTransactionResponse.setResultCode(TransactionResultCode.NOT_AVAILABLE);

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getCode(), response.getCode());
    assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_SystemNotAvailable_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to SYSTEM_NOT_AVAILABLE
    accountTransactionResponse.setResultCode(TransactionResultCode.SYSTEM_NOT_AVAILABLE);

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), response.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_NotAvailableTemporary_failed() throws Exception {

    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to NOT_AVAILABLE_TEMPORARY
    accountTransactionResponse.setResultCode(TransactionResultCode.NOT_AVAILABLE_TEMPORARY);

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServiceReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_InvalidTransactionData_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to INVALID_TRANSACTION_DATA
    accountTransactionResponse.setResultCode(TransactionResultCode.INVALID_TRANSACTION_DATA);

    //Sync the request AccountServiceReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.DEPOSIT_FAILED.getCode(), response.getCode());
    assertEquals(InteracResponseCode.DEPOSIT_FAILED.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_SystemTimeout_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Set the account transaction to SYSTEM_TIMEOUT
    accountTransactionResponse.setResultCode(TransactionResultCode.SYSTEM_TIMEOUT);

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), response.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_TimeoutException_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doThrow(new TimeoutException()).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), response.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_TBDException_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doThrow(new TBDException()).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), response.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_Account_ParticipantIdInstructedMemberIdNotMatched_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Set the MemberIdentification to be null
    request.getFiToFiPaymentStatusReport().getGroupHeader()
        .getInstructedAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification()
        .setMemberIdentification("*********");

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.PARTICIPANT_ID_MISMATCH.getCode(), response.getCode());
    assertEquals(InteracResponseCode.PARTICIPANT_ID_MISMATCH.getDescription(),
        response.getText());
  }

  @Test
  public void submitDeposit_missingClearingSystemRefNumber_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Set the MemberIdentification to be null
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).setClearingSystemReference(null);
    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), response.getCode());
  }

  @Test
  public void submitDeposit_Account_AdapterException_failed() throws  Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doThrow(new AdapterException()).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServiceReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), response.getText());

  }

  @Test
  public void submitDeposit_Account_RuntimeException_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doThrow(new RuntimeException()).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), response.getText());

  }

  @Test
  public void submitDeposit_findById_PersistenceException_failed() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setParentId(1);
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doThrow(new PersistenceException("test exception")).when(paymentsRepository).findById(isA(Integer.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), response.getText());

  }

  @Test
  public void submitDeposit_FindNetworkPaymentRefIdAndNetworkRefId_PersistenceException_failed() throws  Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(adapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    depositPayment.setParentId(1);
    doThrow(new PersistenceException("test exception")).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));
    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(accountTransactionResponse).when(accountAdapter)
        .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));
    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    //Sync the request AccountServicerReference
    request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .setAccountServicerReference(depositPayment.getExternalRefId());

    MvcResult result = mvc.perform(MockMvcRequestBuilders.put(InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", "CAJBQDJf"))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, DepositPaymentTestUtil.FI_ID_PTC)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, depositPayment.getNetworkEnrollmentId())
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), response.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), response.getText());

  }


}
