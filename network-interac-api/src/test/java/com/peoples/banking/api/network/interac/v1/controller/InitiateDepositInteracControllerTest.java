package com.peoples.banking.api.network.interac.v1.controller;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.adapter.InteracResponseAdapter;
import com.peoples.banking.api.network.interac.v1.config.InteracConstant;
import com.peoples.banking.api.network.interac.v1.service.InteracService;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.domain.account.model.AccountEligibilityRequest;
import com.peoples.banking.domain.account.model.AccountEligibilityResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.system.model.FinancialInstitutionInformation;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature.JwsVerificationResult;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyCode;
import com.peoples.banking.partner.domain.interac.deposit.model.CreditTransferTransaction39;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentIdentification7;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.dto.PtcPaymentKafkaDto;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.time.Instant;
import javax.persistence.PersistenceException;
import org.hibernate.hql.internal.ast.QuerySyntaxException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest
@AutoConfigureMockMvc
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class InitiateDepositInteracControllerTest {

  private static final String CHANNEL_INDICATOR = "ETRANSFER_SYSTEM";
  private static final String SIGNATURE_TYPE = "PAYLOAD_DIGEST_SHA256";
  private static final String PARTICIPANT_ID = "CA000621";

  @Autowired
  private MockMvc mvc;
  @MockBean
  private InteracResponseAdapter interacResponseAdapter;
  @MockBean
  private PaymentsRepository paymentsRepository;
  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
  @MockBean
  private CustomerAdapter customerAdapter;
  @MockBean
  private AccountAdapter accountAdapter;
  @MockBean
  private SystemAdapter systemAdapter;
  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;
  @MockBean
  private KafkaTemplate<String, PtcPaymentKafkaDto> kafkaTemplate;

  @InjectMocks
  private InteracService interacService;
  @InjectMocks
  private InteracController controller;
  private DepositPaymentRequest request;
  private RetrieveCustomerProductResponse retrieveCustomerProductResponse;
  private Payments requestPayment;
  private Payments depositPayment;
  private PaymentStatusHistory paymentStatusHistory;
  private AccountEligibilityResponse accountEligibilityResponse;
  private ServiceAccountResponse serviceAccountResponse;
  private String participantId;
  private String channelIndicator;
  private String signatureType;
  private String participantUserId = "JWICK123";
  private String depositId = "DEPO123";
  private String requestId = "REQU123";
  private String accountNumber = "621-00001-************";
  private Double amount = 100.15;
  private FinancialInstitutionInformation financialInstitutionInformation;

  @BeforeEach
  public void setup() throws AdapterException, ResponseException, TimeoutException, TBDException {
    System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");

    request = DepositPaymentTestUtil.
        createDepositPaymentRequest(participantUserId, depositId, requestId, amount, accountNumber);
    requestPayment = DepositPaymentTestUtil.
        createRequestPayments(requestId, participantUserId, accountNumber, amount);
    depositPayment = DepositPaymentTestUtil
        .createDepositPayment(requestId, participantUserId, accountNumber, amount, PaymentStatus.ACCEPTED);

    retrieveCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);

    paymentStatusHistory = new PaymentStatusHistory();
    accountEligibilityResponse = DepositPaymentTestUtil.createAccountEligibilityResponse();
    serviceAccountResponse = DepositPaymentTestUtil
        .createServiceAccountResponse(DepositPaymentTestUtil.IN_API_TOKEN, DepositPaymentTestUtil.OUT_API_TOKEN,
            DepositPaymentTestUtil.SERVICE_ACCOUNT_REF_ID);
    participantId = "CA000621";
    channelIndicator = "ETRANSFER_SYSTEM";
    signatureType = "PAYLOAD_DIGEST_SHA256";
    lenient().doReturn(Boolean.TRUE).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));
    financialInstitutionInformation = DepositPaymentTestUtil.createFinancialInstitutionInformation();
  }

  @AfterEach
  public void teardown() {
    retrieveCustomerProductResponse = null;
    requestPayment = null;
    depositPayment = null;
    paymentStatusHistory = null;
    accountEligibilityResponse = null;
    serviceAccountResponse = null;
    financialInstitutionInformation = null;
  }

  @Test
  public void initiateDeposit_success() throws Exception {

    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(
        isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(
        isA(PaymentStatusHistory.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders
        .post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, PARTICIPANT_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, CHANNEL_INDICATOR)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SIGNATURE_TYPE)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), DepositPaymentResponse.class);
    });

    verifySuccessResponse(request, response);

  }

  /**
   * assume the accounting system accept USD currency.  This unit test is to ensure CAD and USD can be passed to the accounting system.
   */
  @Test
  public void initiateDeposit_usdCurrency_success() throws JsonProcessingException, Exception {

    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(
        isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(
        isA(PaymentStatusHistory.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    request.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getInterbankSettlementAmount()
        .setCurrency(ActiveCurrencyCode.USD);

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders
        .post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, PARTICIPANT_ID)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, CHANNEL_INDICATOR)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SIGNATURE_TYPE)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), DepositPaymentResponse.class);
    });

    verifySuccessResponse(request, response);

  }

  @Test
  public void initiateDeposit_MessageIdNotUnique_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(true).when(systemAdapter).checkMessageIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.DUPLICATE_MESSAGE_ID.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.DUPLICATE_MESSAGE_ID.getDescription(), errorModel.getText());
  }


  @Test
  public void initiateDeposit_missingParticipantId_fail() throws JsonProcessingException, Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingParticipantUserId_fail() throws JsonProcessingException, Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingRetryIndicator_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingRequestId_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_REQUEST_ID, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingChannelIndicator_fail() throws JsonProcessingException, Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingSignature_fail() throws JsonProcessingException, Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_SIGNATURE, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingSignatureType_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, errorModel.getText());
  }

  @Test
  public void initiateDeposit_missingTransactionTime_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertEquals(DepositPaymentTestUtil.MISSING_HEADER + InteracRestAdapterConstant.HEADER_TRANS_TIME, errorModel.getText());
  }

  @Test
  public void initiateDeposit_nullFIToFICustomerCreditTransfer_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    request.setFiToFiCustomerCreditTransfer(null);

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertTrue(errorModel.getText().contains(DepositPaymentTestUtil.MISSING_FIELD));
  }

  @Test
  public void initiateDeposit_nullCustomerType_success() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    request.setCustomerType(null);

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));
    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), DepositPaymentResponse.class);
    });

    verifySuccessResponse(request, response);

  }

  @Test
  public void initiateDeposit_nullTransactionAuthorization_fail() throws Exception {
    request.setTransactionAuthorization(null);

    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    request.setTransactionAuthorization(null);

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    // returns custom error description to explain the error
    assertEquals("Missing transactionAuthorization when conditional mandatory", errorModel.getText());
  }

  @Test
  public void initiateDeposit_nullFraudCheckResult_fail() throws Exception {
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));

    request.setFraudCheckResult(null);

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), errorModel.getCode());
    assertTrue(errorModel.getText().contains(DepositPaymentTestUtil.MISSING_FIELD));
  }

  @Test
  public void initiateDeposit_nullInstructionIdentification_success() throws Exception {
    request.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getPaymentIdentification()
        .setInstructionIdentification(null);
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter)
        .validateSignature(isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));
    doReturn(accountEligibilityResponse).when(accountAdapter)
        .accountEligibility(isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), DepositPaymentResponse.class);
    });

    verifySuccessResponse(request, response);
  }

  private void verifySuccessResponse(DepositPaymentRequest request, DepositPaymentResponse response) {
    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification());
    assertNotNull(response.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime());
    assertEquals(
        PARTICIPANT_ID,
        response.getFiToFiPaymentStatusReport().getGroupHeader()
            .getInstructingAgent().getFinancialInstitutionIdentification()
            .getClearingSystemMemberIdentification().getMemberIdentification());

    PaymentTransaction110 transactionInfoAndStatus = response.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0);
    assertEquals(request.getFiToFiCustomerCreditTransfer().getGroupHeader().getMessageIdentification(),
        transactionInfoAndStatus.getOriginalGroupInformation().getOriginalMessageIdentification());
    assertEquals("pacs.008.001.08", transactionInfoAndStatus.getOriginalGroupInformation().getOriginalMessageNameIdentification());

    CreditTransferTransaction39 creditTransferTransactionInformation = request.getFiToFiCustomerCreditTransfer()
        .getCreditTransferTransactionInformation().get(0);
    PaymentIdentification7 paymentIdentification = creditTransferTransactionInformation.getPaymentIdentification();
    assertEquals(paymentIdentification.getInstructionIdentification(), transactionInfoAndStatus.getOriginalInstructionIdentification());
    assertEquals(paymentIdentification.getEndToEndIdentification(), transactionInfoAndStatus.getOriginalEndToEndIdentification());
    assertEquals(paymentIdentification.getTransactionIdentification(), transactionInfoAndStatus.getOriginalTransactionIdentification());
    assertEquals(paymentIdentification.getClearingSystemReference(), transactionInfoAndStatus.getClearingSystemReference());
    assertNotNull(transactionInfoAndStatus.getAccountServicerReference());
    assertNotNull(transactionInfoAndStatus.getAcceptanceDatetime());
  }

  @Test
  public void initiateDeposit_persistenceExceptionOnRetrieve_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Throw exception during findByNetworkPaymentRefIdAndNetworkRefId
    doThrow(new PersistenceException("test exception")).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_persistenceExceptionOnSave_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doThrow(new PersistenceException("test exception")).when(paymentsRepository).save(isA(Payments.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_jpaSystemExceptionOnRetrieve_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Throw exception during findByNetworkPaymentRefIdAndNetworkRefId
    doThrow(new JpaSystemException(new QuerySyntaxException("query error"))).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_jpaSystemExceptionOnSave_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doThrow(new JpaSystemException(new QuerySyntaxException("query error"))).when(paymentsRepository).save(isA(Payments.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_runtimeExceptionOnRetrieve_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Throw exception during findByNetworkPaymentRefIdAndNetworkRefId
    doThrow(new RuntimeException()).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_runtimeExceptionOnSave_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doThrow(new RuntimeException()).when(paymentsRepository).save(isA(Payments.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_applicationExceptionOnCustomerAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doThrow(new ApplicationException()).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_adapterExceptionOnCustomerAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doThrow(new AdapterException()).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_serviceExceptionOnCustomerAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doThrow(new ServiceException("Service Exception message", null, "ErrorCode")).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_timeoutExceptionOnCustomerAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doThrow(new TimeoutException()).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_adapterExceptionOnAccountAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // AccountAPI::AccountEligibility
    doThrow(new AdapterException()).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_serviceExceptionOnAccountAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doThrow(new ServiceException("Service Exception", null, "ErrorCode")).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_TEMP_UNAVAILABLE.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_timeoutExceptionOnAccountAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doThrow(new TimeoutException()).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_tbdExceptionOnAccountAdapter_fail() throws Exception {
    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct, throw some exception
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doThrow(new TBDException()).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.SYSTEM_BUSY.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_nullPointerException_fail() throws Exception {
    System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "");

    // JWS token validation
    doThrow(new NullPointerException()).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getDescription(), errorModel.getText());
  }

  @Test
  public void initiateDeposit_IndirectConnectorCreditAgentMemberIdMismatch_fail() throws Exception {
    System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "");

    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));
    MvcResult result = mvc.perform(MockMvcRequestBuilders.post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, participantId)
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, "*********")
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, channelIndicator)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, signatureType)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    ErrorModel errorModel = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), ErrorModel.class);
    });

    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getCode(), errorModel.getCode());
    assertEquals(InteracResponseCode.INVALID_CREDITOR_AGENT_MEMBER_ID.getDescription(), errorModel.getText());
  }



  @Test
  public void initiateDeposit_IndirectConnectorCreditAgentMemberIdMatch_Success() throws Exception {

    // JWS token validation
    doReturn(JwsVerificationResult.VERIFIED).when(interacResponseAdapter).validateSignature(
        isA(DepositPaymentRequest.class), isA(String.class), isA(String.class), isA(String.class));

    // CustomerAPI::RetrieveCustomerProduct
    doReturn(retrieveCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(
        isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    // AccountAPI::AccountEligibility
    doReturn(accountEligibilityResponse).when(accountAdapter).accountEligibility(
        isA(String.class), isA(AccountEligibilityRequest.class), isA(ServiceAccountProfile.class));

    // SystemAPI::retrieveServiceAccountByRefId
    serviceAccountResponse.setIndirectConnectorId("*********");
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
    doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));

    // Database::Payment (Request for Payment), (Deposit Payment)
    doReturn(java.util.Optional.of(requestPayment)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentRefIdAndNetworkRefId(isA(String.class), isA(String.class), isA(String.class));

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkRefIdAndTypeCdAndStatusNot(isA(String.class), isA(String.class), isA(PaymentCdType.class),
            isA(String.class));

    doReturn(depositPayment).when(paymentsRepository).save(
        isA(Payments.class));
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(
        isA(PaymentStatusHistory.class));

    doReturn(financialInstitutionInformation).when(systemAdapter).fiInfoByFiId(isA(String.class));

    //set creditor agent member id same as indirect connector id
    request.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getCreditorAgent()
        .getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().setMemberIdentification("*********");

    MvcResult result = mvc.perform(MockMvcRequestBuilders
        .post(InteracConstant.INITIATE_DEPOSIT_PAYMENT.replace("{deposit_id}", depositId))
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, PARTICIPANT_ID)
        .header(InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, "*********")
        .header(InteracRestAdapterConstant.HEADER_REQUEST_ID, "12")
        .header(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantUserId)
        .header(InteracRestAdapterConstant.HEADER_RETRY_INDICATOR, false)
        .header(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, CHANNEL_INDICATOR)
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE, "few93")
        .header(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SIGNATURE_TYPE)
        .header(InteracRestAdapterConstant.HEADER_TRANS_TIME, Instant.now().toString())
        .content(JsonUtil.toString(request))
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    DepositPaymentResponse response = assertDoesNotThrow(() -> {
      return JsonUtil.toObject(result.getResponse().getContentAsString(), DepositPaymentResponse.class);
    });

    verifySuccessResponse(request, response);

  }

}
