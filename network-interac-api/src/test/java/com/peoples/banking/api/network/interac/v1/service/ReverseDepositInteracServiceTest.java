package com.peoples.banking.api.network.interac.v1.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.adapter.InteracResponseAdapter;
import com.peoples.banking.api.network.interac.v1.config.InteracErrorConstant;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentStatusHistoryEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapper;
import com.peoples.banking.api.network.interac.v1.mapper.PaymentsEntityMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.ReverseDepositPaymentRequestMapper;
import com.peoples.banking.api.network.interac.v1.mapper.ReverseDepositPaymentRequestMapperImpl;
import com.peoples.banking.api.network.interac.v1.mapper.converter.MessageIdConverter;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.domain.interac.deposit.model.ReverseDepositPaymentReturnRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReverseDepositPaymentReturnResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(
    classes = {ReverseDepositPaymentRequestMapperImpl.class, PaymentsEntityMapperImpl.class, PaymentStatusHistoryEntityMapperImpl.class,
        MessageIdConverter.class})
@SpringJUnitConfig()
public class ReverseDepositInteracServiceTest {

  @InjectMocks
  private InteracService service;

  @Mock
  private InteracResponseAdapter adapter;

  @Mock
  private CustomerAdapter customerAdapter;

  @Mock
  private AccountAdapter accountAdapter;

  @Mock
  private InteracAdapterProperty interacAdapterProperty;

  @Mock
  private PaymentsRepository paymentsRepository;

  @Mock
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @SpyBean
  private ReverseDepositPaymentRequestMapper reverseDepositPaymentRequestMapper;

  @SpyBean
  private PaymentsEntityMapper paymentsEntityMapper;

  @SpyBean
  private PaymentStatusHistoryEntityMapper paymentStatusHistoryEntityMapper;

  @SpyBean
  private MessageIdConverter messageIdConverter;

  @MockBean
  private InteracPaymentService interacPaymentService;

  private Payments depositPayment;

  private PaymentStatusHistory paymentStatusHistory;

  private ReverseDepositPaymentReturnRequest request;

  private String correspondentId;

  @BeforeEach
  private void setup() {
    request = DepositPaymentTestUtil.createReverseDepositRequest();
    depositPayment = DepositPaymentTestUtil.createDepositPayment(PaymentStatus.DEPOSIT_INITIATED);
    paymentStatusHistory = new PaymentStatusHistory();
    correspondentId = "db11ec38-aa97-4835-8db9-82be60bdb779";

    MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
    RequestContextHolder.getRequestAttributes()
        .setAttribute(APICommonUtilConstant.LOGGING_FIELD_GUIID, correspondentId, RequestAttributes.SCOPE_REQUEST);

    ReflectionTestUtils.setField(service, "reverseDepositPaymentRequestMapper", reverseDepositPaymentRequestMapper);
    ReflectionTestUtils.setField(service, "paymentsEntityMapper", paymentsEntityMapper);
    ReflectionTestUtils.setField(interacPaymentService, "paymentStatusHistoryEntityMapper", paymentStatusHistoryEntityMapper);
    ReflectionTestUtils.setField(reverseDepositPaymentRequestMapper, "messageIdConverter", messageIdConverter);
  }

  @AfterEach
  private void teardown() {
    request = null;
    depositPayment = null;
    paymentStatusHistory = null;
    correspondentId = null;
  }

  @Test
  public void reverseDeposit_DepositInitiated_status_success() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    MutableBoolean alreadyProcessedFlag = new MutableBoolean();
    ReverseDepositPaymentReturnResponse response = assertDoesNotThrow(() -> {
      return service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC,
          depositPayment.getNetworkEnrollmentId(), "CAJBQDJf", request, alreadyProcessedFlag);
    });

    assertNotNull(response);
  }

  @Test
  public void reverseDeposit_Accepted_status_success() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.ACCEPTED.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    MutableBoolean alreadyProcessedFlag = new MutableBoolean();
    ReverseDepositPaymentReturnResponse response = assertDoesNotThrow(() -> {
      return service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC,
          depositPayment.getNetworkEnrollmentId(), "CAJBQDJf", request, alreadyProcessedFlag);
    });

    assertNotNull(response);
  }

  @Test
  public void reverseDeposit_FailedPending_status_success() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.FAILED_PENDING.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    MutableBoolean alreadyProcessedFlag = new MutableBoolean();
    ReverseDepositPaymentReturnResponse response = assertDoesNotThrow(() -> {
      return service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), "CAJBQDJf", request,
          alreadyProcessedFlag);
    });

    assertNotNull(response);
  }

  @Test
  public void reverseDeposit_Queue_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.QUEUED.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void reverseDeposit_Available_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.AVAILABLE.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        error.getAdditionalInformation());

  }

  @Test
  public void reverseDeposit_Cancelled_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.CANCELLED.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        error.getAdditionalInformation());

  }


  @Test
  public void reverseDeposit_Expired_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.EXPIRED.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void reverseDeposit_Declined_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.DECLINED.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(),
        error.getAdditionalInformation());
  }

  @Test
  public void reverseDeposit_Deposit_Pending_status_success() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.DEPOSIT_PENDING.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    MutableBoolean alreadyProcessedFlag = new MutableBoolean();

    ReverseDepositPaymentReturnResponse response = assertDoesNotThrow(() -> {
      return service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, depositPayment.getNetworkEnrollmentId(), "CAJBQDJf", request,
          alreadyProcessedFlag);
    });

    assertNotNull(response);
  }

  @Test
  public void reverseDeposit_Complete_status_failed() {

    doReturn(DepositPaymentTestUtil.FI_ID_PTC).when(interacAdapterProperty).getFiid();

    depositPayment.setStatus(PaymentStatus.COMPLETE.toString());
    doReturn(java.util.Optional.of(depositPayment)).when(paymentsRepository)
            .findByNetworkPaymentRefIdAndNetworkMessageIdAndStatusNot(isA(String.class), isA(String.class), isA(String.class));

    ValidationException exception = assertThrows(ValidationException.class, () -> {
      service.reverseDeposit(DepositPaymentTestUtil.FI_ID_PTC, DepositPaymentTestUtil.PARTICIPANT_USER_ID, "CAJBQDJf", request, null);
    });

    assertNotNull(exception);
    ErrorEntity error = exception.getError();
    assertEquals(InteracResponseCode.TRANSACTION_CANNOT_BE_ROLLED_BACK.getCode(), error.getErrorCode());
    assertEquals(InteracResponseCode.TRANSACTION_CANNOT_BE_ROLLED_BACK.getDescription(),
        error.getAdditionalInformation());
  }

}
