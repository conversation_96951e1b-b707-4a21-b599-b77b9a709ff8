package com.peoples.banking.api.network.interac.v1.controller;

import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.config.InteracConstant;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;

@NoArgsConstructor
public class DBUtil {

  public static Payments createTestPayments_In_DB(PaymentsRepository paymentsRepository, PaymentStatus status, BigDecimal amount) {

    if (paymentsRepository == null) {
      return null;
    }

    Payments payments = new Payments();
    payments.setServiceAccountRefId("ABC123");
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("cid136");
    String paymentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(paymentsRepository.getNextPaymentRefIdSequence(),
        InteracConstant.PAYMENT_REF_ID_LENGTH, InteracConstant.PAYMENT_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);
    payments.setExternalRefId(paymentRefId);
    payments.setTypeCd(PaymentCdType.INBOUND);
    short version = 1;
    payments.setContactJsonVersion(version);
    payments.setContactJson("{\"name\": \"Cloyd Lebsack\", \"financialInstitution\": \"CA000621\"}");

    payments.setAmount(amount);

    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-16001-************");

    payments.setOptionJsonVersion(version);
    payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");

    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson(
        "{\"unstructured\": {\"memo\": \"In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    String networkPaymentRefId = RandomStringUtils.random(12, true, true);

    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/" + networkPaymentRefId);

    payments.setNetworkPaymentType(NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name());
    String customerEnrollmentId = "SB3s9pbxIqRK";
    payments.setNetworkEnrollmentId(customerEnrollmentId);

    payments.setNetworkTransactionId("CAJBQDJf-69adc");
    payments.setNetworkCreatedDate(LocalDateTime.now());

    payments.setStatus(status.toString());

    payments.setStatusReasonJsonVersion((short) 0);
    payments.setStatusReasonJson(null);

    payments.setNetworkFraudResultJsonVersion(version);
    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setCreatedOn(LocalDateTime.now());

    paymentsRepository.save(payments);

    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd(paymentRefId, NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name(),
            PaymentCdType.OUTBOUND);
    if (paymentsOptional.isPresent()) {
      return paymentsOptional.get();
    } else {
      return null;
    }
  }

  public static Payments createTestPayments_In_DB(PaymentsRepository paymentsRepository, Payments requestPayment, PaymentStatus status,
      BigDecimal amount) {

    if (paymentsRepository == null) {
      return null;
    }

    Payments payments = new Payments();
    payments.setParentId(requestPayment.getId());
    payments.setServiceAccountRefId(requestPayment.getServiceAccountRefId());
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId(requestPayment.getCustomerRefId());
    String paymentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(paymentsRepository.getNextPaymentRefIdSequence(),
        InteracConstant.PAYMENT_REF_ID_LENGTH, InteracConstant.PAYMENT_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);
    payments.setExternalRefId(paymentRefId);
    payments.setTypeCd(PaymentCdType.INBOUND);
    short version = 1;
    payments.setContactJsonVersion(version);
    payments.setContactJson("{\"name\": \"Jasonwang .\", \"email\": \"<EMAIL>\", \"mobile\": \"**********\"}");

    payments.setAmount(amount);

    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-16001-************");

    payments.setOptionJsonVersion(version);
    payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");

    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson(
        "{\"unstructured\": {\"memo\": \"In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    String networkPaymentRefId = RandomStringUtils.random(12, true, true);

    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/" + networkPaymentRefId);

    payments.setNetworkPaymentType(NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name());
    payments.setNetworkEnrollmentId(requestPayment.getNetworkEnrollmentId());

    payments.setNetworkTransactionId("CAJBQDJf-69adc");
    payments.setNetworkCreatedDate(LocalDateTime.now());

    payments.setStatus(status.toString());

    payments.setStatusReasonJsonVersion((short) 0);
    payments.setStatusReasonJson(null);

    payments.setNetworkFraudResultJsonVersion(version);
    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setCreatedOn(LocalDateTime.now());

    paymentsRepository.save(payments);

    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(paymentRefId, NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name(),
            PaymentCdType.INBOUND);
    if (paymentsOptional.isPresent()) {
      return paymentsOptional.get();
    } else {
      return null;
    }
  }

  public static Payments createTestPayments_Abr_In_DB(PaymentsRepository paymentsRepository, Payments requestPayment, PaymentStatus status,
      BigDecimal amount, NetworkPaymentType networkPaymentType) {

    if (paymentsRepository == null) {
      return null;
    }

    Payments payments = new Payments();
    payments.setParentId(requestPayment.getId());
    payments.setServiceAccountRefId(requestPayment.getServiceAccountRefId());
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId(requestPayment.getCustomerRefId());
    String paymentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(paymentsRepository.getNextPaymentRefIdSequence(),
        InteracConstant.PAYMENT_REF_ID_LENGTH, InteracConstant.PAYMENT_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);
    payments.setExternalRefId(paymentRefId);
    payments.setTypeCd(PaymentCdType.INBOUND);
    short version = 1;
    payments.setContactJsonVersion(version);
    payments.setContactJson("{\"name\": \"Jasonwang .\", \"email\": \"<EMAIL>\", \"mobile\": \"**********\"}");

    payments.setAmount(amount);

    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-16001-************");

    payments.setOptionJsonVersion(version);
    payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");

    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson(
        "{\"unstructured\": {\"memo\": \"In computer programming, a string is traditionally a sequence of characters, either as a literal constant or as some kind of variable. The latter may allow its elements to be mutated and the length changed, or it may be fixed (after creation). A string is generally considered as a data type and is often implemented as an array data structure of bytes (or words) that stores a sequence of elements, typically\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    String networkPaymentRefId = RandomStringUtils.random(12, true, true);

    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/" + networkPaymentRefId);

    payments.setNetworkPaymentType(networkPaymentType.name());
    payments.setNetworkEnrollmentId(requestPayment.getNetworkEnrollmentId());

    payments.setNetworkTransactionId("CAJBQDJf-69adc");
    payments.setNetworkCreatedDate(LocalDateTime.now());

    payments.setStatus(status.toString());

    payments.setStatusReasonJsonVersion((short) 0);
    payments.setStatusReasonJson(null);

    payments.setNetworkFraudResultJsonVersion(version);
    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setCreatedOn(LocalDateTime.now());

    paymentsRepository.save(payments);

    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(paymentRefId, NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name(),
            PaymentCdType.INBOUND);
    if (paymentsOptional.isPresent()) {
      return paymentsOptional.get();
    } else {
      return null;
    }
  }

  public static Payments createTestRequestPayments_In_DB(PaymentsRepository paymentsRepository, PaymentStatus status,
      String participantUserId, String accountNumber, String networkPaymentRefId, BigDecimal amount) {
    Payments payments = new Payments();
    payments.setParentId(0);
    payments.setServiceAccountRefId("ABC123");
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("CUST1235");
    payments.setExternalRefId(DepositPaymentTestUtil.REQ_PAYMENT_EXTERNAL_REF_ID);
    payments.setTypeCd(PaymentCdType.REQUEST);

    short version = 1;
    payments.setContactJson("{\"name\": \"Jasonwang .\", \"email\": \"<EMAIL>\", \"mobile\": \"**********\"}");
    payments.setContactJsonVersion(version);

    payments.setAmount(amount);
    payments.setAccountName("testing acct");
    payments.setAccountNumber(accountNumber);

    payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");
    payments.setRemittanceJsonVersion(version);

    payments.setNetworkRefId("INTERAC_EMT");
    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6jDK7tB");
    payments.setNetworkPaymentType("REQUEST_FOR_PAYMENT");
    payments.setNetworkEnrollmentId(participantUserId);
    payments.setNetworkTransactionId(RandomStringUtils.randomAlphanumeric(12));
    payments.setNetworkMessageId(RandomStringUtils.randomAlphanumeric(12));
    payments.setStatus(status.toString());

    payments.setStatusReasonJson("{\"reasonCode\": \"CUSTOMER_INITIATED\", \"description\": \"tesing\"}");
    payments.setStatusReasonJsonVersion(version);

    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setNetworkFraudResultJsonVersion(version);

    payments.setExpiryDate(LocalDateTime.now());

    payments.setCreatedOn(LocalDateTime.now());
    payments.setUpdatedOn(LocalDateTime.now());

    payments = paymentsRepository.save(payments);

    return payments;
  }

  public static Payments createTestRequestPayments_empty_option_In_DB(PaymentsRepository paymentsRepository, PaymentStatus status,
      String participantUserId, String accountNumber, String networkPaymentRefId, BigDecimal amount) {
    Payments payments = new Payments();
    payments.setParentId(0);
    payments.setServiceAccountRefId("ABC123");
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("CUST1235");
    payments.setExternalRefId(DepositPaymentTestUtil.REQ_PAYMENT_EXTERNAL_REF_ID);
    payments.setTypeCd(PaymentCdType.REQUEST);

    short version = 1;
    payments.setContactJson("{\"name\": \"Jasonwang .\", \"email\": \"<EMAIL>\", \"mobile\": \"**********\"}");
    payments.setContactJsonVersion(version);

    payments.setAmount(amount);
    payments.setAccountName("testing acct");
    payments.setAccountNumber(accountNumber);

    //payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");
    payments.setRemittanceJsonVersion(version);

    payments.setNetworkRefId("INTERAC_EMT");
    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6jDK7tB");
    payments.setNetworkPaymentType("REQUEST_FOR_PAYMENT");
    payments.setNetworkEnrollmentId(participantUserId);
    payments.setNetworkTransactionId(RandomStringUtils.randomAlphanumeric(12));
    payments.setNetworkMessageId(RandomStringUtils.randomAlphanumeric(12));
    payments.setStatus(status.toString());

    payments.setStatusReasonJson("{\"reasonCode\": \"CUSTOMER_INITIATED\", \"description\": \"tesing\"}");
    payments.setStatusReasonJsonVersion(version);

    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setNetworkFraudResultJsonVersion(version);

    payments.setExpiryDate(LocalDateTime.now());

    payments.setCreatedOn(LocalDateTime.now());
    payments.setUpdatedOn(LocalDateTime.now());

    payments = paymentsRepository.save(payments);

    return payments;
  }

  public static Payments createSendPayments_In_DB(PaymentsRepository paymentsRepository, PaymentStatus status, BigDecimal amount) {

    if (paymentsRepository == null) {
      return null;
    }

    Payments payments = new Payments();
    payments.setServiceAccountRefId("ABC123");
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("cid136");
    String paymentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(paymentsRepository.getNextPaymentRefIdSequence(),
        InteracConstant.PAYMENT_REF_ID_LENGTH, InteracConstant.PAYMENT_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);
    payments.setExternalRefId(paymentRefId);
    payments.setTypeCd(PaymentCdType.OUTBOUND);
    short version = 1;
    payments.setContactJsonVersion(version);
    payments.setContactJson("{\"name\": \"Json\", \"contactMethodDto\": {\"email\": \"<EMAIL>\"}}");

    payments.setAmount(amount);

    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-16001-************");

    payments.setOptionJsonVersion(version);
    payments.setOptionJson("{\"expiredAfterDays\": 30, \"amountModification\": false, \"enableNotification\": false}");

    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson("{\"unstructured\": {\"memo\": \"this is some memo\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    String networkPaymentRefId = RandomStringUtils.random(12, true, true);

    payments.setNetworkPaymentRefId(networkPaymentRefId);
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/" + networkPaymentRefId);

    payments.setNetworkPaymentType(NetworkPaymentType.REGULAR_PAYMENT.name());
    String customerEnrollmentId = "SB3s9pbxIqRK";
    payments.setNetworkEnrollmentId(customerEnrollmentId);

    String networkTransactionId = RandomStringUtils.randomAlphanumeric(10);
    payments.setNetworkTransactionId(networkTransactionId);
    payments.setNetworkMessageId(RandomStringUtils.randomAlphanumeric(10));
    payments.setNetworkCreatedDate(LocalDateTime.now());
    payments.setNetworkMessageId(RandomStringUtils.randomAlphanumeric(10));

    payments.setStatus(status.toString());

    payments.setStatusReasonJsonVersion((short) 0);
    payments.setStatusReasonJson(null);

    payments.setNetworkFraudResultJsonVersion(version);
    payments.setNetworkFraudResultJson("{\"action\": \"ALLOW\"}");
    payments.setCreatedOn(LocalDateTime.now());

    paymentsRepository.save(payments);

    Optional<Payments> paymentsOptional = paymentsRepository
        .findByNetworkPaymentRefIdAndNetworkTransactionId(payments.getNetworkPaymentRefId(), networkTransactionId);
    if (paymentsOptional.isPresent()) {
      return paymentsOptional.get();
    } else {
      return null;
    }
  }

}
