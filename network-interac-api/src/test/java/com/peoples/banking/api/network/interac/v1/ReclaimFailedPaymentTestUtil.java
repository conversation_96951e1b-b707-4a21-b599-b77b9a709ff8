package com.peoples.banking.api.network.interac.v1;

import com.peoples.banking.adapter.base.type.ServiceAccountEndpointType;
import com.peoples.banking.domain.account.model.AccountTransactionReversalResponse;
import com.peoples.banking.domain.account.model.ReversalResultCode;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.serviceaccount.model.ApiEndpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountIdentification4Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountSchemeName1Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountSchemeName1ChoiceOneOf;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveOrHistoricCurrencyAndAmount;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveOrHistoricCurrencyCode;
import com.peoples.banking.partner.domain.interac.deposit.model.AmountType4Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.deposit.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3ChoiceOneOf.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemMemberIdentification2;
import com.peoples.banking.partner.domain.interac.deposit.model.CustomerType;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalPaymentTransactionStatus1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.FIToFIPaymentStatusReportV10;
import com.peoples.banking.partner.domain.interac.deposit.model.FinancialInstitutionIdentification18;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericAccountIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader91;
import com.peoples.banking.partner.domain.interac.deposit.model.OriginalGroupInformation29;
import com.peoples.banking.partner.domain.interac.deposit.model.OriginalTransactionReference28;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.ReclaimFailedPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementInstruction7;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementMethod1Code;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.RandomStringUtils;

public class ReclaimFailedPaymentTestUtil {

  public static final String PARTICIPANT_USER_ID = "JUNITCUST456";

  public static final String FI_ID_PTC = "CA000621";

  public static final String NETWORK_TRANSACTION_ID = "CAeu8o23jf3829";
  public static final String SERVICE_ACCOUNT_REF_ID = "ABC123";

  public static RetrieveCustomerProductResponse createRetrieveCustomerProductResponse() {
    return createRetrieveCustomerProductResponse(PARTICIPANT_USER_ID);
  }


  public static RetrieveCustomerProductResponse createRetrieveCustomerProductResponse(String participantUserId) {
    RetrieveCustomerProductResponse customerProductResponse = new RetrieveCustomerProductResponse();
    customerProductResponse.setActive(true);
    customerProductResponse.setEnrollmentRefId(participantUserId);
    customerProductResponse.setCreatedDate(OffsetDateTime.now());

    return customerProductResponse;
  }

  public static ReclaimFailedPaymentRequest createReclaimFailedPaymentRequest() {
    return createReclaimFailedPaymentRequest(SERVICE_ACCOUNT_REF_ID, new BigDecimal("100.00"), "621-00001-************");
  }

  public static ReclaimFailedPaymentRequest createReclaimFailedPaymentRequest(String depositId,
                                                                              BigDecimal amount, String accountNumber) {
    ReclaimFailedPaymentRequest request = new ReclaimFailedPaymentRequest();

    FIToFIPaymentStatusReportV10 fiToFiCustomerCreditTransfer = new FIToFIPaymentStatusReportV10();

    // Group header
    GroupHeader91 groupHeader = new GroupHeader91();
    groupHeader.setMessageIdentification(depositId + "-69adc");
    groupHeader.setCreationDatetime(OffsetDateTime.now(ZoneOffset.UTC));

    SettlementInstruction7 settlementInfo = new SettlementInstruction7();
    settlementInfo.setSettlementMethod(SettlementMethod1Code.CLRG);
    ClearingSystemIdentification3Choice clrSystem = new ClearingSystemIdentification3Choice();
    clrSystem.setProprietary(ProprietaryEnum.ETR);
    settlementInfo.setClearingSystem(clrSystem);

    BranchAndFinancialInstitutionIdentification6 instructingAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 instructingAgentFI = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 instructingAgentFICLSMI = new ClearingSystemMemberIdentification2();
    instructingAgentFICLSMI.setMemberIdentification(FI_ID_PTC);
    instructingAgentFI.setClearingSystemMemberIdentification(instructingAgentFICLSMI);
    instructingAgent.setFinancialInstitutionIdentification(instructingAgentFI);
    groupHeader.setInstructingAgent(instructingAgent);

    BranchAndFinancialInstitutionIdentification6 instructedAgent = new BranchAndFinancialInstitutionIdentification6();
    FinancialInstitutionIdentification18 instructedAgentFI = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 instructedAgentFICLSMI = new ClearingSystemMemberIdentification2();
    instructedAgentFICLSMI.setMemberIdentification(FI_ID_PTC);
    instructedAgentFI.setClearingSystemMemberIdentification(instructedAgentFICLSMI);
    instructedAgent.setFinancialInstitutionIdentification(instructedAgentFI);
    groupHeader.setInstructedAgent(instructedAgent);

    fiToFiCustomerCreditTransfer.setGroupHeader(groupHeader);

    // creditTransferTransactionInformation
    List<PaymentTransaction110>  paymentTransaction110List  = new ArrayList<>();
    PaymentTransaction110 paymentTransaction110 = new PaymentTransaction110();

    //original_group_information
    OriginalGroupInformation29 originalGroupInformation29=new OriginalGroupInformation29();
    originalGroupInformation29.setOriginalMessageIdentification("CApDPXcx3Ebn5r");
    originalGroupInformation29.setOriginalMessageNameIdentification("pacs.002.001.10");
    paymentTransaction110.setOriginalGroupInformation(originalGroupInformation29);

    paymentTransaction110.setOriginalInstructionIdentification("CA1MRhpZ3pVP");
    paymentTransaction110.setOriginalEndToEndIdentification("NOTPROVIDED");
    paymentTransaction110.setOriginalTransactionIdentification("4c8ea432343b4f7389f1bf4a90cc7673");

    paymentTransaction110.setTransactionStatus(ExternalPaymentTransactionStatus1Code.ACTC);
    paymentTransaction110.setAccountServicerReference("RQGz5YWBK");
    paymentTransaction110.setClearingSystemReference("CAHEDnGe");

    OriginalTransactionReference28 originalTransactionReference28=new OriginalTransactionReference28();
    // creditor account
    CashAccount38 creditorAccount = new CashAccount38();
    AccountIdentification4Choice identification = new AccountIdentification4Choice();
    GenericAccountIdentification1 creditorAccountIdOther = new GenericAccountIdentification1();
    creditorAccountIdOther.setIdentification(accountNumber);
    AccountSchemeName1Choice creditorAccountIdSchemeName = new AccountSchemeName1Choice();
    creditorAccountIdSchemeName.setProprietary(AccountSchemeName1ChoiceOneOf.ProprietaryEnum.BANK_ACCT_NO);
    creditorAccountIdOther.setSchemeName(creditorAccountIdSchemeName);
    identification.setOther(creditorAccountIdOther);
    creditorAccount.setIdentification(identification);

    originalTransactionReference28.setCreditorAccount(creditorAccount);
    AmountType4Choice amountType4Choice=new AmountType4Choice();
    ActiveOrHistoricCurrencyAndAmount activeOrHistoricCurrencyAndAmount=new ActiveOrHistoricCurrencyAndAmount();
    activeOrHistoricCurrencyAndAmount.setAmount(amount);
    activeOrHistoricCurrencyAndAmount.setCurrency(ActiveOrHistoricCurrencyCode.CAD);
    amountType4Choice.setInstructedAmount(activeOrHistoricCurrencyAndAmount);
    originalTransactionReference28.setAmount(amountType4Choice);

    //Set the debtorAccount information here
    CashAccount38 cashAccount38=new CashAccount38();
    AccountIdentification4Choice accountIdentification4Choice=new AccountIdentification4Choice();
    GenericAccountIdentification1 genericAccountIdentification1=new GenericAccountIdentification1();
    genericAccountIdentification1.setIdentification("621-00525-*********012");
    accountIdentification4Choice.setOther(genericAccountIdentification1);
    cashAccount38.setIdentification(accountIdentification4Choice);
    originalTransactionReference28.setDebtorAccount(cashAccount38);

    paymentTransaction110.setOriginalTransactionReference(originalTransactionReference28);

    paymentTransaction110List.add(paymentTransaction110);

    fiToFiCustomerCreditTransfer.setTransactionInformationAndStatus(paymentTransaction110List);

    request.setFiToFiPaymentStatusReport(fiToFiCustomerCreditTransfer);

    request.setCustomerType(CustomerType.RETAIL);
    request.setPaymentStatus(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.CANCELLED);
    return request;
  }


  public static ServiceAccountResponse createServiceAccountResponse(String serviceAccountApiToken, String outApiToken,
      String serviceAccountRefId) {
    ServiceAccountResponse serviceAccountResponse = new ServiceAccountResponse();
    serviceAccountResponse.setRefId(serviceAccountRefId);
    serviceAccountResponse.setInboundApiToken(serviceAccountApiToken);
    serviceAccountResponse.setOutboundApiToken(outApiToken);
    serviceAccountResponse.setName("Test ServiceAccount");
    serviceAccountResponse.setLimitGroupId("PRODVAL");

    Map<String, ApiEndpoint> apiEndpointMap = new HashMap<>();
    ApiEndpoint elgty = new ApiEndpoint();
    elgty.setUrl("http://localhost:8080/account/{account_num}/eligibility");
    apiEndpointMap.put(ServiceAccountEndpointType.ELIGIBILITY.toString(), elgty);

    ApiEndpoint trns = new ApiEndpoint();
    trns.setUrl("http://localhost:8080/account/{account_num}/transaction");
    apiEndpointMap.put(ServiceAccountEndpointType.TRANSACTION.toString(), trns);

    ApiEndpoint rvs = new ApiEndpoint();
    rvs.setUrl("http://localhost:8080/account/{account_num}/{transaction_id}/reversal");
    apiEndpointMap.put(ServiceAccountEndpointType.REVERSAL.toString(), rvs);

    serviceAccountResponse.setApiEndpoints(apiEndpointMap);
    serviceAccountResponse.setCreatedDate(DateUtil.getCurrentUTCDateTime());
    serviceAccountResponse.setUpdatedDate(DateUtil.getCurrentUTCDateTime());
    return serviceAccountResponse;
  }

  public static Payments createSendPayments() {
    Payments payments = new Payments();
    payments.setServiceAccountRefId(SERVICE_ACCOUNT_REF_ID);
    payments.setEndToEndId("nKIWy7kjJ11QoRr8R63s");
    payments.setCustomerRefId("CUST1235");
    payments.setExternalRefId(RandomStringUtils.randomAlphanumeric(20));
    payments.setTypeCd(PaymentCdType.OUTBOUND);

    short version = 1;
    payments.setContactJson(
        "{\"name\": \"test ptc\", \"aliasReferenceId\": \"tesstststst\", \"contactMethodDto\": {\"email\": \"<EMAIL>\"}}");
    payments.setContactJsonVersion(version);

    payments.setAmount(new BigDecimal("100.25"));
    payments.setAccountName("testing acct");
    payments.setAccountNumber("621-00525-*********");

    payments.setOptionJsonVersion((short) 1);
    payments.setOptionJson("{\"expiredAfterDays\": 30}");
    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson("{\"unstructured\": {\"memo\": \"some simple memo\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    payments.setNetworkPaymentRefId("CA" + RandomStringUtils.randomAlphanumeric(8));
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6jDK7tA");
    payments.setNetworkPaymentType(NetworkPaymentType.REGULAR_PAYMENT.name());
    payments.setNetworkEnrollmentId("2LO7MqgRE");
    payments.setNetworkTransactionId(NETWORK_TRANSACTION_ID);
    payments.setStatus(PaymentStatus.AVAILABLE.getValue());

    payments.setStatusReasonJson("{\"reasonCode\": \"CUSTOMER_INITIATED\", \"description\": \"tesing\"}");
    payments.setStatusReasonJsonVersion(version);

    payments.setNetworkFraudResultJson("{\"score\":0 , \"action\": \"ALLOW\"}");
    payments.setNetworkFraudResultJsonVersion(version);

    payments.setExpiryDate(LocalDateTime.now());
    payments.setNetworkSettled(false);
    payments.setSaSettled(false);

    payments.setCreatedOn(LocalDateTime.now());
    payments.setUpdatedOn(LocalDateTime.now());

    return payments;
  }

  public static AccountTransactionReversalResponse createAccountTransactionReversalResponse() {
    AccountTransactionReversalResponse accountTransactionReversalResponse=new AccountTransactionReversalResponse();
    accountTransactionReversalResponse.setResultCode(ReversalResultCode.SUCCESS);
    accountTransactionReversalResponse.setRefId("p1kyBPN1I1QFJgBd");
    return accountTransactionReversalResponse;
  }
}
