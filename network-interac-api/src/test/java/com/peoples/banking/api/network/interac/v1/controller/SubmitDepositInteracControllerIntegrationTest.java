package com.peoples.banking.api.network.interac.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.account.AccountAdapter;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.network.interac.v1.DepositPaymentTestUtil;
import com.peoples.banking.api.network.interac.v1.config.InteracConstant;
import com.peoples.banking.api.network.interac.v1.config.InteracErrorConstant;
import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.domain.account.model.AccountTransactionRequest;
import com.peoples.banking.domain.account.model.AccountTransactionResponse;
import com.peoples.banking.domain.account.model.TransactionResultCode;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositResponse;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import java.net.URL;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class SubmitDepositInteracControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private SystemAdapter systemAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private AccountAdapter accountAdapter;

  private SubmitDepositRequest request;

  private Payments depositPayment;

  private Payments requestPayment;

  private List<PaymentStatusHistory> paymentStatusHistoryList;

  private List<PaymentStatusHistory> requestPaymentStatusHistoryList;

  private String participantUserId;

  private DBUtil dbUtil;

  private ServiceAccountResponse serviceAccountResponse;

  private AccountTransactionResponse accountTransactionResponse;

  // TODO eventually add valid JWS tokens to test
  static {
    System.setProperty(InteracConstant.ENVIRONMENT_VARIABLE_DISABLE_SIGNATURE_VERIFICATION, "true");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port);
    request = DepositPaymentTestUtil.createSubmitDepositRequest();
    participantUserId = "SB3s9pbxIqRK";
    dbUtil = new DBUtil();

    serviceAccountResponse = DepositPaymentTestUtil
        .createServiceAccountResponse(DepositPaymentTestUtil.IN_API_TOKEN, DepositPaymentTestUtil.OUT_API_TOKEN,
            DepositPaymentTestUtil.SERVICE_ACCOUNT_REF_ID);
    accountTransactionResponse = DepositPaymentTestUtil.createAccountTransactionResponse();
  }

  @AfterEach
  public void tearDown() {
    if (paymentStatusHistoryList != null) {
      paymentStatusHistoryRepository.deleteInBatch(paymentStatusHistoryList);
      paymentStatusHistoryList = null;
    }

    if (depositPayment != null) {
      paymentsRepository.delete(depositPayment);
      depositPayment = null;
    }

    if (requestPaymentStatusHistoryList != null) {
      paymentStatusHistoryRepository.deleteInBatch(requestPaymentStatusHistoryList);
      requestPaymentStatusHistoryList = null;
    }

    if (requestPayment != null) {
      paymentsRepository.delete(requestPayment);
      requestPayment = null;
    }

    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    paymentStatusHistoryRepository = null;
    base = null;
    request = null;
    dbUtil = null;
    participantUserId = null;
    serviceAccountResponse = null;
    accountTransactionResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add("x-et-participant-id", "CA000621");
    headers.add("x-et-participant-user-id", participantUserId);
    headers.add("x-et-indirect-connector-id", null);
    headers.add("x-et-request-id", java.util.UUID.randomUUID().toString());
    headers.add("x-et-retry-indicator", "true");
    headers.add("x-et-channel-indicator", "ETRANSFER_SYSTEM");
    headers.add("x-et-api-signature",
        "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
    headers.add("x-et-api-signature-type", "PAYLOAD_DIGEST_SHA256");
    headers.add("x-et-transaction-time", Instant.now().toString());
    return headers;
  }

  /**
   * The test submitDepositPayment happy path
   */
  @Test
  public void submitDepositPayment_statusDepositInitiated_success() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<SubmitDepositResponse> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, SubmitDepositResponse.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      assert (responseEntity.getStatusCode() == HttpStatus.OK);

      // the successful response should have x-et-response-code and the value should be 0
      assert (responseEntity.getHeaders().get("x-et-response-code").get(0).equals("0"));

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.COMPLETE.toString(), depositPayment.getStatus());
      assertEquals("{\"reasonCode\": \"Interac:0\", \"description\": \"Interac: SubmitDepositPayment Success\"}",
          depositPayment.getStatusReasonJson());
      assertEquals(accountTransactionResponse.getRefId(), depositPayment.getAccountTransactionRefId());

      //request payment is complete status
      assertEquals(PaymentStatus.COMPLETE.toString(), requestPayment.getStatus());

      // we can also verify the depposit payment history now
      PaymentStatusHistory paymentStatusHistory0 = null;
      PaymentStatusHistory paymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      paymentStatusHistory1 = paymentStatusHistoryList.get(1);
      assertEquals(depositPayment.getId(), paymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), paymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory1.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;
      PaymentStatusHistory requestPaymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(2, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());

      requestPaymentStatusHistory1 = requestPaymentStatusHistoryList.get(1);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), requestPaymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory1.getPreviousStatus());

    }
  }

  /**
   * The test submitDepositPayment happy path when service account is disabled
   */
  @Test
  public void submitDepositPayment_serviceAccountDisabled_success() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      //Set service account to be disabled
      serviceAccountResponse.setStatus(StatusEnum.DISABLED);
      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      doReturn(true).when(systemAdapter).storeMessageIdPerPartner(isA(String.class), isA(String.class));
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<SubmitDepositResponse> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, SubmitDepositResponse.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      assert (responseEntity.getStatusCode() == HttpStatus.OK);

      // the successful response should have x-et-response-code and the value should be 0
      assert (responseEntity.getHeaders().get("x-et-response-code").get(0).equals("0"));

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.COMPLETE.toString(), depositPayment.getStatus());
      assertEquals("{\"reasonCode\": \"Interac:0\", \"description\": \"Interac: SubmitDepositPayment Success\"}",
          depositPayment.getStatusReasonJson());
      assertEquals(accountTransactionResponse.getRefId(), depositPayment.getAccountTransactionRefId());

      //request payment is complete status
      assertEquals(PaymentStatus.COMPLETE.toString(), requestPayment.getStatus());

      // we can also verify the deposit payment history now
      PaymentStatusHistory paymentStatusHistory0 = null;
      PaymentStatusHistory paymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      paymentStatusHistory1 = paymentStatusHistoryList.get(1);
      assertEquals(depositPayment.getId(), paymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), paymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory1.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;
      PaymentStatusHistory requestPaymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(2, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());

      requestPaymentStatusHistory1 = requestPaymentStatusHistoryList.get(1);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), requestPaymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory1.getPreviousStatus());

    }
  }

  /**
   * The test submitDepositPayment happy path
   */
  @Test
  public void submitDepositPayment_statusDepositPending_success() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_PENDING;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<SubmitDepositResponse> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, SubmitDepositResponse.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      assert (responseEntity.getStatusCode() == HttpStatus.OK);

      // the successful response should have x-et-response-code and the value should be 0
      assert (responseEntity.getHeaders().get("x-et-response-code").get(0).equals("0"));

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.COMPLETE.toString(), depositPayment.getStatus());
      assertEquals("{\"reasonCode\": \"Interac:0\", \"description\": \"Interac:Success\"}", depositPayment.getStatusReasonJson());

      // we can also verify the payment history now
      PaymentStatusHistory paymentStatusHistory0 = null;
      PaymentStatusHistory paymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      paymentStatusHistory1 = paymentStatusHistoryList.get(1);
      assertEquals(depositPayment.getId(), paymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), paymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory1.getPreviousStatus());

    }
  }

  @Test
  public void submitDepositPayment_statusComplete_success() throws Exception {

    PaymentStatus initStatus = PaymentStatus.COMPLETE;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<SubmitDepositResponse> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, SubmitDepositResponse.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      assert (responseEntity.getStatusCode() == HttpStatus.OK);

      // the successful response should have x-et-response-code and the value should be 201
      assertEquals("201", responseEntity.getHeaders().get("x-et-response-code").get(0));

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.COMPLETE.toString(), depositPayment.getStatus());
      assertEquals(null, depositPayment.getStatusReasonJson());

      // status is not updated therefore no history records
      assertNotNull(paymentStatusHistoryList);
      assertEquals(0, paymentStatusHistoryList.size());

    }

  }

  @Test
  public void submitDepositPayment_statusExpired_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.EXPIRED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracErrorConstant.INVALID_STATUS, responseEntity.getBody().getText());

      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    }

  }

  @Test
  public void submitDepositPayment_statusAccepted_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.ACCEPTED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(), responseEntity.getBody().getText());

      // Since during unit test the payments status changed, we need refresh it from DB
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());
    }

  }

  @Test
  public void submitDepositPayment_statusFailed_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.FAILED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.TRANSACTION_CANNOT_BE_COMMITTED.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracResponseCode.TRANSACTION_CANNOT_BE_COMMITTED.getDescription(), responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

    }

  }

  @Test
  public void submitDepositPayment_statusFailedPending_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.FAILED_PENDING;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracResponseCode.BEGIN_TRANSACTION_NOT_FOUND.getDescription(), responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

    }

  }

  @Test
  public void submitDepositPayment_statusAvailable_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.AVAILABLE;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracErrorConstant.INVALID_STATUS, responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

    }
  }

  @Test
  public void submitDepositPayment_statusCancelled_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.CANCELLED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracErrorConstant.INVALID_STATUS, responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());
    }

  }

  @Test
  public void submitDepositPayment_statusDeclined_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DECLINED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracErrorConstant.INVALID_STATUS, responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

    }

  }

  @Test
  public void submitDepositPayment_statusQueue_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.QUEUED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracErrorConstant.INVALID_STATUS, responseEntity.getBody().getText());

      // Since during unit test the payments status should not be changed, we need refresh it from DB to
      // double verify
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();
      assertEquals(initStatus.toString(), depositPayment.getStatus());

    }

  }

  @Test
  public void submitDepositPayment_invalidAccount_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, PaymentStatus.DEPOSIT_INITIATED, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      // make the account not match
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId() + "1");

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

      assertEquals(InteracResponseCode.UNEXPECTED_ERROR.getCode(), responseEntity.getBody().getCode());
      assertEquals("DepositId does not match clearingSystemReference", responseEntity.getBody().getText());

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.DEPOSIT_INITIATED.toString(), depositPayment.getStatus());
      assertNull(depositPayment.getStatusReasonJson());

      // we can also verify the payment history now, make sure nothing been saved to db
      assertNotNull(paymentStatusHistoryList);
      assertEquals(0, paymentStatusHistoryList.size());
    }
  }

  @Test
  public void submitDepositPayment_invalid_ParticipantId_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.COMPLETE;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      // Set the header participant to be invalid
      headers.set("x-et-participant-id", "something_not_right");

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.SCHEMA_ERROR.getCode(), responseEntity.getBody().getCode());
    }
  }

  @Test
  public void submitDepositPayment_invalid_ParticipantUserId_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      // Set the header participant to be empty
      headers.set("x-et-participant-user-id", "asdf");

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServicerReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      assert (responseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

      assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getCode(), responseEntity.getBody().getCode());
      assertEquals(InteracResponseCode.INVALID_PARTICIPANT_USER_ID.getDescription(), responseEntity.getBody().getText());
    }

  }

  /**
   * The test reverseDepositPayment happy path
   */
  @Test
  public void submitDepositPayment_differentOriginalMessageId_success() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServicerReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      String messageId = depositPayment.getNetworkPaymentRefId() + "-12abc";
      OffsetDateTime createdDate = DateUtil.getCurrentUTCDateTime();

      request.getFiToFiPaymentStatusReport().getGroupHeader().setCreationDatetime(createdDate);
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getOriginalGroupInformation()
          .setOriginalMessageIdentification(messageId);

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<SubmitDepositResponse> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, SubmitDepositResponse.class);

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      assert (responseEntity.getStatusCode() == HttpStatus.OK);

      // the successful response should have x-et-response-code and the value should be 0
      assert (responseEntity.getHeaders().get("x-et-response-code").get(0).equals("0"));

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.COMPLETE.toString(), depositPayment.getStatus());
      assertEquals("{\"reasonCode\": \"Interac:0\", \"description\": \"Interac:Success\"}", depositPayment.getStatusReasonJson());

      // we can also verify the payment history now
      PaymentStatusHistory paymentStatusHistory0 = null;
      PaymentStatusHistory paymentStatusHistory1 = null;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      paymentStatusHistory1 = paymentStatusHistoryList.get(1);
      assertEquals(depositPayment.getId(), paymentStatusHistory1.getPayments().getId());
      assertEquals(PaymentStatus.COMPLETE.toString(), paymentStatusHistory1.getStatus());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory1.getPreviousStatus());

    }
  }

  /**
   * The test submitDepositPayment when the accountTransaction return system busy
   */
  @Test
  public void submitDepositPayment_account_systemTimeOut_failed() throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    String additionalInformation = "test additional information";

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      accountTransactionResponse.setResultCode(TransactionResultCode.SYSTEM_TIMEOUT);
      accountTransactionResponse.setAdditionalInformation(additionalInformation);

      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      ErrorModel response = responseEntity.getBody();

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      // verify response status
      assertEquals(HttpStatus.SERVICE_UNAVAILABLE, responseEntity.getStatusCode());

      assertEquals(InteracResponseCode.SYSTEM_BUSY.getCode(), response.getCode());

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), depositPayment.getStatus());

      // we can also verify the deposit payment history now
      PaymentStatusHistory paymentStatusHistory0;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      //since now additionalInformation save to DB this should be a null value
      assertEquals("{\"reasonCode\": \"Account:SYSTEM_TIMEOUT\", \"description\": \"Account:Transaction - test additional information\"}", depositPayment.getStatusReasonJson());

      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(1, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());
    }
  }


  @ParameterizedTest
  @ValueSource(strings = {
      "SYSTEM_TIMEOUT",
      "SYSTEM_NOT_AVAILABLE"
  })
  //As Account Transaction only update the payment status when the result code is ACCOUNT_FROZEN,
  // there’s the additional_information is only
  // persisted when this happened.  Rest of the status is only logged in the log
  public void submitDepositPayment_account_saveAdditionInformation_timeout_failed(String accountSystemStatus) throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    String additionalInformation = "test additional information";

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      accountTransactionResponse.setResultCode(TransactionResultCode.fromValue(accountSystemStatus));
      accountTransactionResponse.setAdditionalInformation(additionalInformation);

      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      ErrorModel response = responseEntity.getBody();

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      // verify response status
      assertEquals(HttpStatus.SERVICE_UNAVAILABLE, responseEntity.getStatusCode());

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), depositPayment.getStatus());

      // we can also verify the deposit payment history now
      PaymentStatusHistory paymentStatusHistory0;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      //since now additionalInformation save to DB this should be a null value
      assertEquals("{\"reasonCode\": \"Account:" + accountSystemStatus + "\", \"description\": \"Account:Transaction - test additional information\"}", depositPayment.getStatusReasonJson());

      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(1, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "NOT_AVAILABLE_TEMPORARY",
      "INVALID_TRANSACTION_DATA",
  })
  //As Account Transaction only update the payment status when the result code is ACCOUNT_FROZEN,
  // there’s the additional_information is only
  // persisted when this happened.  Rest of the status is only logged in the log
  public void submitDepositPayment_account_saveAdditionInformation_invalidData_failed(String accountSystemStatus) throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    String additionalInformation = "test additional information";

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));
      accountTransactionResponse.setResultCode(TransactionResultCode.fromValue(accountSystemStatus));
      accountTransactionResponse.setAdditionalInformation(additionalInformation);

      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      ErrorModel response = responseEntity.getBody();

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      // verify response status
      assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), depositPayment.getStatus());

      // we can also verify the deposit payment history now
      PaymentStatusHistory paymentStatusHistory0;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());

      assertEquals("{\"reasonCode\": \"Account:" + accountSystemStatus + "\", \"description\": \"Account:Transaction - test additional information\"}", depositPayment.getStatusReasonJson());

      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(1, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "NOT_AVAILABLE",
      "FROZEN"
  })
  public void submitDepositPayment_account_frozen_saved_additionalInformation_failed(String accountSystemStatus) throws Exception {

    PaymentStatus initStatus = PaymentStatus.DEPOSIT_INITIATED;

    String additionalInformation = "test additional information";

    requestPayment = dbUtil
        .createTestRequestPayments_In_DB(paymentsRepository, initStatus, participantUserId, "621-16001-************",
            "XYZ123", new BigDecimal("100.1"));

    // first create test payments in DB
    depositPayment = dbUtil.createTestPayments_In_DB(paymentsRepository, requestPayment, initStatus, new BigDecimal("100.1"));

    if (depositPayment != null) {

      RetrieveCustomerProductResponse getCustomerProductResponse = DepositPaymentTestUtil.createRetrieveCustomerProductResponse(participantUserId);
      // sync the header and body participantUserId
      getCustomerProductResponse.setEnrollmentRefId(participantUserId);
      doReturn(getCustomerProductResponse).when(customerAdapter).retrieveCustomerProduct(isA(String.class), isA(String.class),
          isA(String.class), isA(String.class));

      doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByRefId(isA(String.class));

      //mock the accountTransactionResponse
      accountTransactionResponse.setResultCode(TransactionResultCode.fromValue(accountSystemStatus));
      accountTransactionResponse.setAdditionalInformation(additionalInformation);
      doReturn(accountTransactionResponse).when(accountAdapter)
          .accountTransaction(isA(String.class), isA(AccountTransactionRequest.class), isA(ServiceAccountProfile.class));

      String finalUrl = base + InteracConstant.SUBMIT_DEPOSIT_PAYMENT.replace("{deposit_id}", depositPayment.getNetworkPaymentRefId());

      HttpHeaders headers = getHttpHeader();

      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setClearingSystemReference(depositPayment.getNetworkPaymentRefId());

      // Sync the request AccountServiceReference
      request.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
          .setAccountServicerReference(depositPayment.getExternalRefId());

      HttpEntity requestSubmit = new HttpEntity<>(request, headers);

      ResponseEntity<ErrorModel> responseEntity =
          template.exchange(finalUrl, HttpMethod.PUT, requestSubmit, ErrorModel.class);

      ErrorModel response = responseEntity.getBody();

      // retrieve DB records first so AfterEach can clean them
      // retrieve status history
      paymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(depositPayment);

      requestPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(requestPayment);

      // retrieve deposit payment
      Optional<Payments> paymentsOption = paymentsRepository.findById(depositPayment.getId());
      assertTrue(paymentsOption.isPresent());
      depositPayment = paymentsOption.get();

      //retrieve request payment
      Optional<Payments> requestPaymentsOption = paymentsRepository.findById(requestPayment.getId());
      assertTrue(requestPaymentsOption.isPresent());
      requestPayment = requestPaymentsOption.get();

      // verify response status
      assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

      assertEquals(InteracResponseCode.ACCOUNT_FROZEN.getCode(), response.getCode());

      // Since during unit test the payments status changed, we need refresh it from DB
      assertEquals(PaymentStatus.FAILED_PENDING_REVERSAL.toString(), depositPayment.getStatus());

      // we can also verify the deposit payment history now
      PaymentStatusHistory paymentStatusHistory0;

      // For successful payment history they should have 2
      assertNotNull(paymentStatusHistoryList);
      assertEquals(2, paymentStatusHistoryList.size());

      paymentStatusHistory0 = paymentStatusHistoryList.get(0);
      assertEquals(depositPayment.getId(), paymentStatusHistory0.getPayments().getId());
      assertTrue(depositPayment.getStatusReasonJson().contains(additionalInformation));

      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), paymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), paymentStatusHistory0.getPreviousStatus());

      // we can also verify the request payment history now
      PaymentStatusHistory requestPaymentStatusHistory0 = null;

      // For successful payment history they should have 2
      assertNotNull(requestPaymentStatusHistoryList);
      assertEquals(1, requestPaymentStatusHistoryList.size());

      requestPaymentStatusHistory0 = requestPaymentStatusHistoryList.get(0);
      assertEquals(requestPayment.getId(), requestPaymentStatusHistory0.getPayments().getId());
      assertEquals(PaymentStatus.DEPOSIT_PENDING.toString(), requestPaymentStatusHistory0.getStatus());
      assertEquals(initStatus.toString(), requestPaymentStatusHistory0.getPreviousStatus());
    }
  }

}

