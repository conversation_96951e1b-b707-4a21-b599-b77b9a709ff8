package com.peoples.banking.api.network.interac.v1.util;

import com.peoples.banking.api.network.interac.v1.type.InteracResponseCode;
import com.peoples.banking.partner.domain.interac.deposit.model.CreditTransferTransaction39;
import com.peoples.banking.partner.domain.interac.deposit.model.DepositPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalOrganisationIdentification1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalStatusReason1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader91;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader93;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.ReclaimFailedPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.ReverseDepositPaymentReturnRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.StatusReasonInformation12;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitDepositRequest;
import com.peoples.banking.util.api.common.AccountNumberUtil;
import com.peoples.banking.util.api.common.exception.ValidationException;
import java.util.List;
import lombok.extern.log4j.Log4j2;

/**
 * Validations for <i>Deposit Payment</i>, <i>Submit Deposit Payment</i> and <i>Reverse Deposit Payment</i> payloads.
 */
@Log4j2
public class PayloadValidator {

  /**
   * String format of header validation error.
   */
  public static final String FIELD_INVALID = "INVALID_FIELD:";

  /**
   * String format of header validation error.
   */
  public static final String FIELD_MISSING = "MISSING_FIELD:";

  /**
   * Check payload for <i>Deposit Payment</i> against validation rules (see
   * {@code https://confluence.interac.ca/confluence/display/ETD/ISO+20022+Deposit+Payment}
   *
   * @param request request payload
   */
  public static void checkPayload(DepositPaymentRequest request) {
    // fi_to_fi_customer_credit_transfer.group_header
    GroupHeader93 groupHeader = request.getFiToFiCustomerCreditTransfer().getGroupHeader();

    // fi_to_fi_customer_credit_transfer.group_header.number_of_transactions
    int numberOfTransactions = Integer.parseInt(groupHeader.getNumberOfTransactions());

    if (numberOfTransactions != 1) {
      log.warn("invalid fi_to_fi_customer_credit_transfer.group_header.number_of_transactions={}, expected=1", numberOfTransactions);

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_INVALID + "fi_to_fi_customer_credit_transfer.group_header.number_of_transactions");
    }

    // fi_to_fi_customer_credit_transfer.group_header.settlement_information.clearing_system.proprietary
    if (groupHeader.getSettlementInformation().getClearingSystem().getProprietary() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.group_header.settlement_information.clearing_system.proprietary");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.group_header.settlement_information.clearing_system.proprietary");
    }

    // fi_to_fi_customer_credit_transfer.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructingAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_customer_credit_transfer.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructedAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information
    List<CreditTransferTransaction39> creditTransferTransactionList = request.getFiToFiCustomerCreditTransfer()
        .getCreditTransferTransactionInformation();

    if (creditTransferTransactionList == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information");
    }

    if (creditTransferTransactionList.size() != 1) {
      log.warn("invalid fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information, expected 1 but received={}",
          creditTransferTransactionList.size());

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_INVALID + "fi_to_fi_customer_credit_transfer.group_header.number_of_transactions");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information
    CreditTransferTransaction39 creditTransferTransaction = creditTransferTransactionList.get(0);
    if (creditTransferTransaction.getPaymentTypeInformation() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information.local_instrument.proprietary
    if (creditTransferTransaction.getPaymentTypeInformation().getLocalInstrument().getProprietary() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information.local_instrument.proprietary");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].payment_type_information.local_instrument.proprietary");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].interbank_settlement_date
    if (creditTransferTransaction.getInterbankSettlementDate() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].interbank_settlement_date");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].interbank_settlement_date");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor.name
    if (creditTransferTransaction.getDebtor().getName() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor.name");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor.name");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor_agent.financial_institution_identification.clearing_system_member_identification
    if (creditTransferTransaction.getDebtorAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification()
        == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].debtor_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_agent.financial_institution_identification.clearing_system_member_identification
    if (creditTransferTransaction.getCreditorAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification()
        == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.name
    if (creditTransferTransaction.getCreditor().getName() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.name");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.name");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification
    if (creditTransferTransaction.getCreditor().getIdentification() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification
    if (creditTransferTransaction.getCreditor().getIdentification().getOrganisationIdentification() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other
    if (creditTransferTransaction.getCreditor().getIdentification().getOrganisationIdentification().getOther() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other.scheme_name.code
    if (creditTransferTransaction.getCreditor().getIdentification().getOrganisationIdentification().getOther().get(0).getSchemeName()
        != null) {
      ExternalOrganisationIdentification1Code schemeNameCode = creditTransferTransaction.getCreditor().getIdentification()
          .getOrganisationIdentification().getOther().get(0).getSchemeName().getCode();

      if (schemeNameCode == null) {
        log.warn(
            "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other.scheme_name.code");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING
                + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other.scheme_name.code");
      } else if (!ExternalOrganisationIdentification1Code.CUST.equals(schemeNameCode)) {
        log.warn(
            "invalid fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other.scheme_name.code, expected CUST");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_INVALID
                + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor.identification.organisation_identification.other.scheme_name.code");
      }
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account
    if (creditTransferTransaction.getCreditorAccount() == null) {
      log.warn("missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other
    if (creditTransferTransaction.getCreditorAccount().getIdentification().getOther() == null) {
      log.warn(
          "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other");
    }

    // fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other.scheme_name.proprietary
    if (creditTransferTransaction.getCreditorAccount().getIdentification().getOther().getSchemeName() != null) {
      if (creditTransferTransaction.getCreditorAccount().getIdentification().getOther().getSchemeName().getProprietary() == null) {
        log.warn(
            "missing fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other.scheme_name.proprietary");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING
                + "fi_to_fi_customer_credit_transfer.credit_transfer_transaction_information[0].creditor_account.identification.other.scheme_name.proprietary");
      }
    }

    // fi_to_fi_customer_credit_transfer.payment_type_information.category_purpose.code can't be null if we have category_purpose
    if (creditTransferTransaction.getPaymentTypeInformation() != null
        && creditTransferTransaction.getPaymentTypeInformation().getCategoryPurpose() != null) {
      if (creditTransferTransaction.getPaymentTypeInformation().getCategoryPurpose().getCode() == null) {
        log.warn("missing fi_to_fi_customer_credit_transfer.payment_type_information.category_purpose.code");
        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING
                + "fi_to_fi_customer_credit_transfer.payment_type_information.category_purpose.code");
      }
    }

  }

  /**
   * Check payload for <i>Submit Deposit Payment</i> against validation rules (see
   * {@code https://confluence.interac.ca/confluence/display/ETD/ISO+20022+Submit+Deposit+Payment}
   *
   * @param request request payload
   */
  public static void checkPayload(SubmitDepositRequest request) {
    // fi_to_fi_payment_status_report.group_header
    GroupHeader91 groupHeader = request.getFiToFiPaymentStatusReport().getGroupHeader();

    // fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructingAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructedAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status
    List<PaymentTransaction110> transactionInformationAndStatusList = request.getFiToFiPaymentStatusReport()
        .getTransactionInformationAndStatus();
    if (transactionInformationAndStatusList == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    if (transactionInformationAndStatusList.size() != 1) {
      log.warn("invalid fi_to_fi_payment_status_report.transaction_information_and_status, expected 1 but received={}",
          transactionInformationAndStatusList.size());

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_INVALID + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information
    PaymentTransaction110 transactionInformationAndStatus = transactionInformationAndStatusList.get(0);
    if (transactionInformationAndStatus.getOriginalGroupInformation() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification
    if (transactionInformationAndStatus.getOriginalEndToEndIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification
    if (transactionInformationAndStatus.getOriginalTransactionIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status
    if (transactionInformationAndStatus.getTransactionStatus() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code
    List<StatusReasonInformation12> statusReasonInfo = transactionInformationAndStatus.getStatusReasonInformation();
    if (statusReasonInfo != null
        && statusReasonInfo.size() == 1
        && statusReasonInfo.get(0).getReason() != null) {
      if (statusReasonInfo.get(0).getReason().getCode() == null) {
        log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");
      }

      // if reason.code == NARR
      if (statusReasonInfo.get(0).getReason().getCode().equals(ExternalStatusReason1Code.NARR)) {
        if (statusReasonInfo.get(0).getAdditionalInformation() == null
            || statusReasonInfo.get(0).getAdditionalInformation().size() != 1) {
          log.warn(
              "missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");

          throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
              FIELD_MISSING
                  + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");
        }
      }
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].account_servicer_reference
    if (transactionInformationAndStatus.getAccountServicerReference() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].account_servicer_reference");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].account_servicer_reference");
    } else {
      if (transactionInformationAndStatus.getAccountServicerReference().isEmpty()) {
        log.warn("invalid fi_to_fi_payment_status_report.transaction_information_and_status[0].account_servicer_reference");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_INVALID + "fi_to_fi_payment_status_report.transaction_information_and_status[0].account_servicer_reference");
      }
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference
    if (transactionInformationAndStatus.getClearingSystemReference() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");
    }
  }

  /**
   * Check payload for <i>Reverse Deposit Payment</i> against validation rules (see
   * {@code https://confluence.interac.ca/confluence/display/ETD/ISO+20022+Reverse+Deposit+Payment}
   *
   * @param request request payload
   */
  public static void checkPayload(ReverseDepositPaymentReturnRequest request) {
    // fi_to_fi_payment_status_report.group_header
    GroupHeader91 groupHeader = request.getFiToFiPaymentStatusReport().getGroupHeader();

    // fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructingAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructedAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status
    List<PaymentTransaction110> transactionInformationAndStatusList = request.getFiToFiPaymentStatusReport()
        .getTransactionInformationAndStatus();
    if (transactionInformationAndStatusList == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    if (transactionInformationAndStatusList.size() != 1) {
      log.warn("invalid fi_to_fi_payment_status_report.transaction_information_and_status, expected 1 but received={}",
          transactionInformationAndStatusList.size());

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_INVALID + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information
    PaymentTransaction110 transactionInformationAndStatus = transactionInformationAndStatusList.get(0);
    if (transactionInformationAndStatus.getOriginalGroupInformation() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification
    if (transactionInformationAndStatus.getOriginalEndToEndIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification
    if (transactionInformationAndStatus.getOriginalTransactionIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status
    if (transactionInformationAndStatus.getTransactionStatus() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code
    List<StatusReasonInformation12> statusReasonInfo = transactionInformationAndStatus.getStatusReasonInformation();
    if (statusReasonInfo != null
        && statusReasonInfo.size() == 1
        && statusReasonInfo.get(0).getReason() != null) {
      if (statusReasonInfo.get(0).getReason().getCode() == null) {
        log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");
      }

      // if reason.code == NARR
      if (statusReasonInfo.get(0).getReason().getCode().equals(ExternalStatusReason1Code.NARR)) {
        if (statusReasonInfo.get(0).getAdditionalInformation() == null
            || statusReasonInfo.get(0).getAdditionalInformation().size() != 1) {
          log.warn(
              "missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");

          throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
              FIELD_MISSING
                  + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");
        }
      }
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference
    if (transactionInformationAndStatus.getClearingSystemReference() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");
    }
  }

  /**
   * Check payload for <i>Reclaim Failed Payment</i> against validation rules (see
   * {@code https://confluence.interac.ca/confluence/display/ETD/ISO+20022+Reclaim+Failed+Payment}
   *
   * @param request request payload
   */
  public static void checkPayload(ReclaimFailedPaymentRequest request) {
    // fi_to_fi_payment_status_report.group_header
    GroupHeader91 groupHeader = request.getFiToFiPaymentStatusReport().getGroupHeader();

    // fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructingAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructing_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification
    if (groupHeader.getInstructedAgent().getFinancialInstitutionIdentification().getClearingSystemMemberIdentification() == null) {
      log.warn(
          "missing fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING
              + "fi_to_fi_payment_status_report.group_header.instructed_agent.financial_institution_identification.clearing_system_member_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status
    List<PaymentTransaction110> transactionInformationAndStatusList = request.getFiToFiPaymentStatusReport()
        .getTransactionInformationAndStatus();
    if (transactionInformationAndStatusList == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    if (transactionInformationAndStatusList.size() != 1) {
      log.warn("invalid fi_to_fi_payment_status_report.transaction_information_and_status, expected 1 but received={}",
          transactionInformationAndStatusList.size());

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_INVALID + "fi_to_fi_payment_status_report.transaction_information_and_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information
    PaymentTransaction110 transactionInformationAndStatus = transactionInformationAndStatusList.get(0);
    if (transactionInformationAndStatus.getOriginalGroupInformation() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_group_information");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification
    if (transactionInformationAndStatus.getOriginalEndToEndIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_end_to_end_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification
    if (transactionInformationAndStatus.getOriginalTransactionIdentification() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_identification");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status
    if (transactionInformationAndStatus.getTransactionStatus() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].transaction_status");
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code
    List<StatusReasonInformation12> statusReasonInfo = transactionInformationAndStatus.getStatusReasonInformation();
    if (statusReasonInfo != null
        && statusReasonInfo.size() == 1
        && statusReasonInfo.get(0).getReason() != null) {
      if (statusReasonInfo.get(0).getReason().getCode() == null) {
        log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.reason.code");
      }

      // if reason.code == NARR
      if (statusReasonInfo.get(0).getReason().getCode().equals(ExternalStatusReason1Code.NARR)) {
        if (statusReasonInfo.get(0).getAdditionalInformation() == null
            || statusReasonInfo.get(0).getAdditionalInformation().size() != 1) {
          log.warn(
              "missing fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");

          throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
              FIELD_MISSING
                  + "fi_to_fi_payment_status_report.transaction_information_and_status[0].status_reason_information.additional_information");
        }
      }
    }

    // fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference
    if (transactionInformationAndStatus.getClearingSystemReference() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].clearing_system_reference");
    }

    if (transactionInformationAndStatus.getOriginalTransactionReference() == null) {
      log.warn("missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference");

      throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
          FIELD_MISSING + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference");
    } else {
      if (transactionInformationAndStatus.getOriginalTransactionReference().getDebtorAccount() == null) {
        log.warn(
            "missing fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference.debtor_account");

        throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
            FIELD_MISSING
                + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference.debtor_account");
      } else {
        String debtorAccountNumber = transactionInformationAndStatus.getOriginalTransactionReference().getDebtorAccount().getIdentification().getOther()
            .getIdentification();
        if (!AccountNumberUtil.isValidAccountNumber(debtorAccountNumber)) {
          log.warn(
              "invalid fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference.debtor_account.identification.other.identification");

          throw new ValidationException(InteracResponseCode.SCHEMA_ERROR.getCode(),
              FIELD_INVALID
                  + "fi_to_fi_payment_status_report.transaction_information_and_status[0].original_transaction_reference.debtor_account.identification.other.identification");
        }
      }
    }
  }
}

