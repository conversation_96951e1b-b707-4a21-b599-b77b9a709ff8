package com.peoples.banking.api.network.interac.v1.controller.advice;

import com.peoples.banking.api.network.interac.v1.util.ErrorResponseBuilder;
import com.peoples.banking.partner.domain.interac.deposit.model.ErrorModel;
import com.peoples.banking.util.api.common.controller.advice.APIValidationControllerAdvice;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.exception.domain.ErrorResponseEntity;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;

/**
 * Validation controller advice to handle business validation exceptions
 */
@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class InteracValidationControllerAdvice extends APIValidationControllerAdvice {

  private static final Logger LOGGER = LogManager.getLogger(InteracValidationControllerAdvice.class);

  @Autowired
  private ErrorResponseBuilder errorResponseBuilder;

  /**
   * override the existing implementation
   */
  @Override
  protected ResponseEntity<?> handleValidationException(ValidationException ex, HttpServletRequest request) {

    HttpHeaders headers = buildResponseHttpHeaders(request);

    ErrorResponseEntity<?> errorResponseEntity = buildErrorResponseEntity(ex.getError());

    HttpStatus status = HttpStatus.BAD_REQUEST;

    // if actual exception (rather than soft warning as above), log the exception/failure
    LOGGER.warn(ex.getMessage(), ex);

    return new ResponseEntity<>(errorResponseEntity.getContent(), headers, status);
  }

  /**
   * implement Interac error response
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(ErrorEntity validationError) {
    ErrorModel errorModel = errorResponseBuilder.buildErrorResponseEntity(validationError);
    ErrorResponseEntity<ErrorModel> errorResponseEntity = new ErrorResponseEntity<>(errorModel);
    return errorResponseEntity;
  }

  /**
   * Implement null as it is not applicable for Interact Network API.
   */
  @Override
  protected ErrorResponseEntity<?> buildErrorResponseEntity(List<ErrorEntity> validationErrorList) {
    return null;
  }

  /**
   * null for Interac response Header
   */
  @Override
  protected HttpHeaders buildResponseHttpHeaders(HttpServletRequest request) {
    return null;
  }

}
