---
# Generated-By: OA3 Linker Tool
# Input-File: customer-api/customer-api-35.yaml
# Options: --encoding=utf-8; --oa3fix; --overwrite; --prune
# -----
openapi: 3.0.0
info:
  description: Customer Services
  version: "3.5.0"
  title: Customer Services
  contact:
    name: eTransfer Support
    url: https://www.interac.ca/en/contact-us-2.html
    email: <EMAIL>
  termsOfService: https://www.interac.ca/en/terms-and-conditions.html
  x-last-updated-date: "20-Mar-2020"
tags:
- name: customers
  description: Customer Management
- name: contact-groups
  description: Contact Groups Management
- name: contacts
  description: Contacts Management
servers:
- description: Production Environment
  url: https://etransfer-services.interac.ca/customer-api/v3.5.0
- description: FI Test Environment
  url: https://etransfer-services.fit.interac.ca/customer-api/v3.5.0
- description: Beta Test Environment
  url: https://etransfer-services.beta.interac.ca/customer-api/v3.5.0
paths:
  /customers:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - customers
      description: This service can be used to register a new customer on the Interac
        e-Transfer platform.
      operationId: addCustomerRegistration
      summary: Adds a new customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddCustomerRequest"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "201":
          description: customer registration created
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /customers/{id}:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    get:
      tags:
      - customers
      description: This service can be used to retrieve the details of an existing
        customer that is registered on the Interac e-Transfer platform.
      operationId: getCustomer
      summary: Retrieves the details of a customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Retrive Customer Registration information
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCustomerRegistrationResponse"
    put:
      tags:
      - customers
      description: This service can be used to update the profile details of an existing
        customer that is already registered on the Interac e-Transfer platform.
      operationId: updateCustomerRegistration
      summary: Updates an existing customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Customer"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: customer updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /customers/{id}/enable:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    post:
      tags:
      - customers
      description: This service can be used to enable (i.e. re-activate) an existing
        customer as well as optionally provide a reason for the re-activation.
      operationId: enableCustomer
      summary: Enables (re-activates) a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerStatusChangeRequest"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Customer status changed
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /customers/{id}/disable:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    post:
      tags:
      - customers
      description: This service can be used to disable (i.e. deactivate) an existing
        customer as well as optionally provide a reason for the deactivation.
      operationId: disableCustomer
      summary: Disables (deactivates) a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerStatusChangeRequest"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Customer status changed
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /customers/{id}/amount-limits:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    get:
      tags:
      - customers
      description: This service can be used to retrieve the incoming and outgoing
        amount limits assigned to a customer.
      operationId: getCustomerAmountLimitGroups
      summary: Retrieves the amount limits that are assigned to a customer.
      parameters:
      - in: query
        name: productCode
        description: >-
          'Product for which the customer limit groups are will be returned. Product
          Code: <br/> DOMESTIC - e-Transfer domestic <br/> INTERNATIONAL - e-Transfer
          International Remittance'
        schema:
          $ref: '#/components/schemas/ProductCode'
        required: true
      - in: query
        name: currencyCode
        description: Currency of the transfer amounts. (CAD, USD)
        schema:
          type: string
          enum: ['CAD', 'USD']
          example: 'CAD'
        required: true
        example: CAD
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Customer limit groups retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCustomerAmountLimitGroupsResponse"
  /customers/{id}/contact-groups:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    post:
      tags:
      - contact-groups
      description: This service can be used to allow a customer to create a group
        for his/her contacts. A contact group must be created before the customer
        can add a contact to that contact group.
      operationId: addGroup
      summary: Creates a new contact group for a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ContactGroupName"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "201":
          description: Contact group created
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddContactGroupResponse"
    get:
      tags:
      - contact-groups
      description: This services can be used to retrieve all the contact groups for
        a customer.
      operationId: getCustomerGroups
      summary: Retrieves all the contact groups for a customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Contact Groups Retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetContactGroupsResponse"
  /customers/{id}/contact-groups/{contact_group_id}:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    - $ref: "#/components/parameters/ContactGroupId"
    get:
      tags:
      - contact-groups
      description: The service can be used to retrieve the high level details of a
        contact group, such as the contact group name.
      operationId: getCustomerGroup
      summary: Retrieves the details of a contact group.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Contact Group Retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetContactGroupResponse"
    put:
      tags:
      - contact-groups
      description: The service can be used to update the details of an existing contact
        group for a customer.
      operationId: updateGroup
      summary: Updates the name of a contact group for a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ContactGroupName"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Contact group updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
    delete:
      tags:
      - contact-groups
      description: This service can be used to delete a contact group for a customer.
        Please note that deleting a contact group does not automatically deletes any
        contact that is associated with that contact group. It simply removes any
        grouping association that any contact may have with that contact group.
      operationId: deleteGroup
      summary: Deletes a contact group for a customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Contact group deleted
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /customers/{id}/contacts:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    post:
      tags:
      - contacts
      description: This service can be used to create a new contact for an existing
        customer.
      operationId: addContact
      summary: Creates a new contact for a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddContact"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "201":
          description: Contact created
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddContactResponse"
    get:
      tags:
      - contacts
      description: This service can be used to retrieve the contacts for a customer.
        The service can be used with request filters such as contact group id, a specific
        product code, or contact last update date.
      operationId: getContacts
      summary: Retrieves a list of contacts for a customer.
      parameters:
      - in: query
        name: product_code
        description: Filter by Customer's Product Registration (default return Contacts
          eligible for any Product).
        schema:
          $ref: '#/components/schemas/ProductCode'
      - in: query
        name: group_id
        description: Recipient/Contact group ID as stored at Interac
        schema:
          type: string
          maxLength: 35
      - in: query
        name: offset
        description: offset is starting point of contacts filter; if offset is not
          provided it would be defaulted to zero;
        schema:
          type: integer
      - in: query
        name: max_response_items
        description: Maximum number of response items to be returned. All items are
          returned if this field is absent.
        schema:
          type: integer
          maximum: 999
      - in: query
        name: contact_updated_date
        description: If present the system will check if any Customer's contacts got
          updated since the provided date. If any contact was updated after the specified
          date then all the contacts are returned. If no contacts were updated then
          an empty list is returned.
        schema:
          type: string
          format: date-time   #YYYY-MM-DDThh:mm:ss.sssZ
          example: 2019-05-29T17:19:26.951000Z
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Contact retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-page-size-limit:
              $ref: "#/components/headers/x-et-page-size-limit"
            x-et-page-total-records:
              $ref: "#/components/headers/x-et-page-total-records"
            x-et-page-next-offset:
              $ref: "#/components/headers/x-et-page-next-offset"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetContactResponse"
  /customers/{id}/contacts/{contact_id}:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/CustomerId"
    - $ref: "#/components/parameters/ContactId"
    get:
      tags:
      - contacts
      description: This service can be used to retrieve the details of en existing
        contact for a customer.
      summary: Retrieves the details of an existing contact for a customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "200":
          description: Contact retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ViewContact"
    put:
      tags:
      - contacts
      description: This service can be used to allow a customer to update the details
        of one of their existing contacts.
      summary: Update an existing contact for a customer.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateContact"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Contact updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
    delete:
      tags:
      - contacts
      description: This service can be used to delete an existing contact for a customer.
      operationId: deleteContact
      summary: Deletes a contact for a customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        "204":
          description: Contact deleted
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
components:
  headers:
    x-et-page-next-offset:
      description: >
        Pagination parameter - the offset of the 1st record in the next requested
        record set
        against the total records in backend
      schema:
        type: integer
    x-et-page-size-limit:
      description: Pagination parameter - the number of record on each returned page
      schema:
        type: integer
    x-et-page-total-records:
      description: Pagination parameter - the total number of record in the backend
        system
      schema:
        type: integer
    x-et-rate-limit-ceiling:
      description: >
        The rate limit ceiling for that given endpoint in the given window.
      schema:
        type: integer
        format: int64
    x-et-rate-limit-remaining:
      description: >
        The number of requests left for the current window. Since the eTransfer is
        calculating the rate limit based on GMT in HOUR window, the timezones that
        have 30-minute shift should realize the remaining count is based on the clock
        in the hourly-shift zones.
      schema:
        type: integer
        format: int64
    x-et-rate-limit-window:
      description: >
        The rate limit gauging and resetting window. To simplify the complexity of
        the impact of timezone on the rate limiting, eTransfer will use GMT for rate
        limiting window. MINUTE is the default setting unless specified otherwise.
      schema:
        type: string
        enum: ['HOUR', 'MINUTE']
        example: HOUR
    x-et-response-code:
      description: >
        A numeric response code specifying the outcome of the message. A successful
        call will
        return a response code of 0, along with any additional response data.
      schema:
        type: integer
  parameters:
    Authorization:
      in: header
      name: Authorization
      description: >-
        Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
      schema:
        type: string
      required: false
      example: 12345
    ChannelIndicator:
      in: header
      name: x-et-channel-indicator
      description: see './types.yaml#/ChannelIndicator'
      schema:
        $ref: '#/components/schemas/ChannelIndicator'
      required: true
      example: ONLINE
    ContactGroupId:
      description: Contact group id at Interac.
      in: path
      name: contact_group_id
      required: true
      schema:
        type: string
        maxLength: 35
    ContactId:
      description: Contact ID at Interac.
      in: path
      name: contact_id
      required: true
      schema:
        type: string
        maxLength: 35
    CustomerId:
      description: Participant user ID in the participant's system.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ParticipantUserId"
    IndirectConnectorId:
      in: header
      name: x-et-indirect-connector-id
      description: >-
        Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined
        in e-Transfer system.
      schema:
        type: string
        maxLength: 35
      required: false
      example: 1
    ParticipantId:
      in: header
      name: x-et-participant-id
      description: >-
        Direct Participant Identifier as defined in e-Transfer system. Participant
        must ensure conformity to the following pattern before transmitting this data
        - CA000xxx where xxx is the Financial Institution Identifier as defined by
        the Canadian Payment Association. If customer's FI connects indirectly through
        a Participant, the participant-id field identifies the direct connector.
      schema:
        type: string
        minLength: 8
        maxLength: 8
      required: true
      example: CA000001
    ParticipantUserId:
      description: Present for all API calls initiated on behalf of a customer. Customer
        ID provided as defined in the Participant system and Customer must be registered
        in the e-Transfer system.
      in: header
      name: x-et-participant-user-id
      schema:
        type: string
        maxLength: 35
      required: true
      example: CA000001user
    RequestId:
      in: header
      name: x-et-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes. In
        case of a request retry use the same ID as in the original message.
      schema:
        type: string
        maxLength: 36
      required: true
      example: 12345
    Signature:
      in: header
      name: x-et-api-signature
      description: >-
        JWS detached signature of the payload (body only), required for all API calls.
      schema:
        type: string
      required: true
      example: 12345
    SignatureType:
      in: header
      name: x-et-api-signature-type
      description: >-
        The type of the JWT. Required. Allowed values are "PAYLOAD_DIGEST_SHA256"
      schema:
        $ref: '#/components/schemas/SignatureType'
      required: true
      example: PAYLOAD_DIGEST_SHA256
    TransactionTime:
      in: header
      name: x-et-transaction-time
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      schema:
        $ref: '#/components/schemas/TransactionTime'
      required: true
      example: 2019-05-29T17:19:26.951000Z
  responses:
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    401-unauthorized:
      description: Unauthorized
    403-forbidden:
      description: Forbidden
    404-not-found:
      description: Resources Not Found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    429-too-many-requests:
      description: Too many requests; blocked due to rate limiting.
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    500-internal-server-error:
      description: Internal Server Error
    503-service-unavailable:
      description: Service Unavailable - The server cannot handle the request for
        a service due to temporary maintenance.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
  schemas:
    AddContact:
      description: "Add Contact details"
      allOf:
      - $ref: "#/components/schemas/Contact"
      - type: object
        required:
        - one_time_contact
        - transfer_authentication
        properties:
          contact_group_ids:
            description: List of contact group Id's where contact belongs to
            type: array
            items:
              type: string
              maxLength: 35
          one_time_contact:
            $ref: "#/components/schemas/OneTimeContact"
          transfer_authentication:
            $ref: "#/components/schemas/TransferAuthentication"
          product_options:
            description: List of products the contact/recipient is registered for,
              default is set to DOMESTIC if not specified.
            type: array
            items:
              $ref: "#/components/schemas/ProductCode"
    AddContactGroupResponse:
      description: Add contact group response
      type: object
      required:
      - group_id
      properties:
        group_id:
          description: Contact group ID at Interac
          type: string
          maxLength: 35
    AddContactResponse:
      description: Add contact Response
      type: object
      required:
      - contact_id
      properties:
        contact_id:
          description: Contact's ID at Interac
          type: string
          maxLength: 35
    AddCustomerRequest:
      allOf:
      - $ref: "#/components/schemas/Customer"
      - $ref: "#/components/schemas/CustomerCreationDate"
    AmountWithCurrency:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          description: Transaction amount, with the specified currency.
          type: number
          multipleOf: 0.01
          x-multipleOf: 0.01
          example: 100.50
      #maxLength: 19
      #format: decimal #totalDigits 18 fractionDigits 5
        currency:
          description: ISO 4217 currency code
          type: string
          enum: ['CAD', 'USD']
          example: CAD
    AuthenticationType:
      description: >-
        'Flag indicating the type of authentication required to complete the Transfer.
        <br/>
          CONTACT_LEVEL - Security question and answer defined at contact level. <br/>
          PAYMENT_LEVEL - Security question and answer defined at payment level. <br/>
          NOT_REQUIRED - No authentication. </br> '
      type: string
      enum: ['CONTACT_LEVEL', 'PAYMENT_LEVEL', 'NOT_REQUIRED']
      example: CONTACT_LEVEL
    AvailableProducts:
      type: object
      properties:
        product_code:
          $ref: "#/components/schemas/ProductCode"
    BankAccountIdentifier:
      type: object
      required:
      - type
      - account
      properties:
        type:
          description: Bank Account Identifier (CANADIAN - Canadian bank account)
          type: string
          enum: ['CANADIAN']
          example: CANADIAN
        account:
          description: Bank account number. Canadian bank account format is aaa-bbbbb-cccccccccccccccccccc
            where 'aaa' is the Financial Institution Identifier 'bbbbb' is the Transit
            Number 'cccccccc...' is the Account Number.
          type: string
          minLength: 1
          maxLength: 34
          example: aaa-bbbbb-cccccccccccccccccccc
    BusinessName:
      description: Business name. This is required for type 1 (small business) or
        2 (corporation)
      type: object
      properties:
        company_name:
          description: Business/company name, is required while creating customer
            profile.
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
        trade_name:
          description: Trade name
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
    ChannelIndicator:
      description: >-
        Method Sender accessed the servicedescription:  Identifies the channel that
        the customer is using when making the
        request if the request is initiated by the customer. For requests
        initiated by an e- Transfer system or a participants system component it
        identifies the system that makes the request. Integer, values <br/>
        ONLINE = Customer online initiated transaction <br/>
        MOBILE = Customer mobile initiated transaction <br/>
        PARTICIPANT_BULK_PAYMENT = Participant payment system initiated Bulk file
        transaction (*) <br/>
        PARTICIPANT_ETRANSFER_SYSTEM = Participant payment system initiated transaction
        <br/>
        PARTICIPANT_FRAUD_SYSTEM = Participant fraud detection system initiated transaction
        <br/>
        ETRANSFER_SYSTEM = e-Transfer system initiated transaction (*) <br/>
        ETRANSFER_FRAUD = e-Transfer fraud detection system initiated transaction
        (*) <br/>
        EXTERNAL_APPS = External API initiated transaction (*) <br/>
        INTERAC_SDK = Interac Proximity SDK initiated transaction <br/> (*) - values
        not accepted
        through Participant initiated requests
      type: string
      enum: ['ONLINE', 'MOBILE', 'PARTICIPANT_BULK_PAYMENT', 'PARTICIPANT_ETRANSFER_SYSTEM',
        'PARTICIPANT_FRAUD_SYSTEM', 'ETRANSFER_SYSTEM', 'ETRANSFER_FRAUD', 'EXTERNAL_APPS',
        'INTERAC_SDK']
      example: ONLINE
    Contact:
      description: "Contact details"
      type: object
      required:
      - contact_name
      - language
      properties:
        contact_type:
          $ref: "#/components/schemas/ContactType"
        contact_name:
          $ref: "#/components/schemas/ContactName"
        language:
          $ref: "#/components/schemas/Language"
        notification_preference:
          type: array
          items:
            $ref: "#/components/schemas/NotificationPreference"
        contact_address:
          $ref: "#/components/schemas/PostalAddress"
        direct_deposit_data:
          $ref: "#/components/schemas/DirectDepositData"
        account_deposit_data:
          $ref: "#/components/schemas/BankAccountIdentifier"
        ir_recipient_data:
          $ref: "#/components/schemas/IrRecipientData"
    ContactGroup:
      description: Contact group details
      type: object
      required:
      - group_id
      properties:
        group_id:
          type: string
          maxLength: 35
        group_name:
          type: string
          maxLength: 50
    ## Contact  ##
    ContactGroupName:
      description: Recipient/Contact group name or label, as defined by the customer.
      type: object
      properties:
        group_name:
          type: string
          maxLength: 50
    ContactName:
      description: Recipient's Name
      type: object
      required:
      - alias_name
      properties:
        alias_name:
          description: >-
            'Contact's Name/Alias, as known by the customer.'
          type: string
          minLength: 1
          maxLength: 80
        legal_name:
          $ref: "#/components/schemas/LegalName"
    ContactType:
      description: >-
        'Flag indicating whether the Contact is a business or not. Required if legal
        name is required. (e.g. for for e-Transfer International Remittance) <br/>
        retail <br/> small business <br/> corporation'
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      example: RETAIL
    CountryCode:
      description: Only ISO 3166 Alpha-2 codes are allowed.
      type: string
      enum: ['AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AO', 'AQ', 'AR', 'AS', 'AT',
        'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ',
        'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW', 'BY', 'BZ', 'CA',
        'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM', 'CN', 'CO', 'CR', 'CU',
        'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM', 'DO', 'DZ', 'EC', 'EE',
        'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM', 'FO', 'FR', 'GA', 'GB',
        'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN', 'GP', 'GQ', 'GR', 'GS',
        'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT', 'HU', 'ID', 'IE', 'IL',
        'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM', 'JO', 'JP', 'KE', 'KG',
        'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY', 'KZ', 'LA', 'LB', 'LC', 'LI',
        'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA', 'MC', 'MD', 'ME', 'MF', 'MG',
        'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ', 'MR', 'MS', 'MT', 'MU', 'MV',
        'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF', 'NG', 'NI', 'NL', 'NO', 'NP',
        'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG', 'PH', 'PK', 'PL', 'PM', 'PN',
        'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'RE', 'RO', 'RS', 'RU', 'RW', 'SA', 'SB',
        'SC', 'SD', 'SE', 'SG', 'SH', 'SI', 'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR',
        'SS', 'ST', 'SV', 'SX', 'SY', 'SZ', 'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK',
        'TL', 'TM', 'TN', 'TO', 'TR', 'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US',
        'UY', 'UZ', 'VA', 'VC', 'VE', 'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'YE', 'YT',
        'ZA', 'ZM', 'ZW']
      example: CA
    CustomDateTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: 2019-05-29T17:19:26.951000Z
    Customer:
      type: object
      required:
      - product_registration
      - customer_type
      - customer_name
      - language
      - notification_preference
      properties:
        product_registration:
          description: Interac e-Transfer product(s) registered for the customer.
          type: array
          minItems: 1
          items:
            $ref: "#/components/schemas/ProductRegistration"
        customer_type:
          $ref: "#/components/schemas/CustomerType"
        customer_name:
          $ref: "#/components/schemas/CustomerName"
        language:
          $ref: "#/components/schemas/Language"
        notification_preference:
          type: array
          minItems: 1
          items:
            $ref: "#/components/schemas/NotificationPreference"
        customer_address:
          #description: Customer phone number. Required for product code INTERNATIONAL.
          $ref: "#/components/schemas/PostalAddress"
        customer_phone_number:
          description: Customer phone number. Required for e-Transfer International
            Remittance account to cash transfers.
          type: string
          maxLength: 32
          example: ******-555-1212
        sender_ir_data:
          description: Dynamic list of data elements for a customer registered for
            e-Transfer International Remittance. Specific data elements such as nationality
            and date-of-birth are required depending on the corridor the remittance
            transfer is sent.
          type: array
          items:
            $ref: "#/components/schemas/DataField"
        parent_id:
          description: -> This is in support of parent-child customer (business) hierarchies
            - If the customer has a parent, this element is used to identify the parent
            by an ID defined in the Participant FI system.
          type: string
          minLength: 1
          maxLength: 35
    CustomerAmountGroup:
      description: Customer amounts sent/received
      type: object
      properties:
        total_daily_outgoing_amount:
          #description: Current cumulative outgoing daily transfer amount for the customer
          $ref: "#/components/schemas/AmountWithCurrency"
        # total_daily_incoming_amount:
        #   #description: Current cumulative incoming daily transfer amount for the customer
        #   $ref: "../common/v3.5/types.yaml#/AmountWithCurrency"
        total_L1_outgoing_amount:
          #description: Current cumulative outgoing first rolling limit transfer amount for the customer
          $ref: "#/components/schemas/AmountWithCurrency"
        total_L2_outgoing_amount:
          #description: Current cumulative outgoing second rolling limit transfer amount for the customer
          $ref: "#/components/schemas/AmountWithCurrency"
        total_outstanding_money_requests:
          #description: Current number of outstanding money requests
          type: integer
    CustomerCreationDate:
      type: object
      properties:
        customer_creation_date:
          $ref: "#/components/schemas/CustomDateTime"
    CustomerLimitsGroup:
      description: Current limit group of the Customer
      type: object
      required:
      - limits_group_id
      - limits_group_name
      properties:
        limits_group_id:
          description: Limit group identifier
          type: string
          maxLength: 35
        limits_group_name:
          description: Limit group name
          type: string
          maxLength: 120
        outgoing_limits:
          $ref: "#/components/schemas/OutgoingLimits"
        incoming_limits:
          $ref: "#/components/schemas/IncomingLimits"
        request_limits:
          $ref: "#/components/schemas/RequestLimits"
    CustomerName:
      description: Customer's name
      type: object
      required:
      - legal_name
      - registration_name
      properties:
        registration_name:
          description: Customer's primary registration name or alias
          type: string
          minLength: 1
          maxLength: 80
        legal_name:
          $ref: "#/components/schemas/LegalName"
    CustomerStatusChangeRequest:
      description: Enable/Disable customer request
      type: object
      properties:
        reason:
          $ref: "#/components/schemas/Reason"
    ## Contact Group ##
    CustomerType:
      description: >-
        Identifies the customer type based on the following codes - <br/>  retail
        <br/> small business <br/> corporation
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      example: RETAIL
    DataField:
      description: Data element specified in a name/value pair format.
      type: object
      properties:
        name:
          description: Data element name. For example nationality or date-of-birth.
          type: string
          minLength: 1
        value:
          description: Data element value
          type: string
          minLength: 1
    DirectDepositData:
      description: >-
        Direct Deposit Data required to deposit a transfer directly into Recipient's/Payee's
        account. If present the Contact is eligible to receive Direct Deposit e-Transfer
        domestic transfers.
      type: object
      required:
      - direct_deposit_handle
      - service_type
      properties:
        direct_deposit_handle:
          description: >-
            UUID associated with the Payee/Recipient. The UUID must exist in the system
            and it uniquely identifies the Payee's/Recipient's direct deposit registration.
          type: string
          minLength: 1
          maxLength: 64
        service_type:
          $ref: "#/components/schemas/ServiceType"
        sender_account_identifier:
          description: >-
            Sender's/Customer's account identifier in Payee's/Recipient's system.
          type: string
          maxLength: 40
          pattern: "[a-zA-Z0-9_./-]{1,40}"
          example: ABC123
    EmailNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via Email
        required:
        - email
        properties:
          email:
            type: string
            maxLength: 64
            format: email
            description: The email of the contact
            example: <EMAIL>
    ErrorModel:
      type: object
      required:
      - code
      properties:
        code:
          type: string
          description: |
            Error list for all the APIs, please refer to the example for API-specific error codes.
          maxLength: 80
          example: 999
        text:
          description: Short error explanation.
          type: string
          maxLength: 2000
          example: Unknown error
    GetContactGroupResponse:
      description: Get contact group response
      type: object
      properties:
        group:
          $ref: "#/components/schemas/ContactGroupName"
    GetContactGroupsResponse:
      description: Get contact groups response
      type: object
      properties:
        groups:
          type: array
          items:
            $ref: "#/components/schemas/ContactGroup"
    GetContactResponse:
      description: Get Contact response
      type: object
      required:
      - contact
      properties:
        contacts:
          type: array
          items:
            $ref: "#/components/schemas/ViewContact"
    GetCustomerAmountLimitGroupsResponse:
      type: object
      description: custom get customer amount response
      required:
      - customer_limits_group
      - customer_amount_group
      properties:
        customer_limits_group:
          #description: Current limit group of the Customer.
          $ref: "#/components/schemas/CustomerLimitsGroup"
        customer_amount_group:
          #description: Customer amounts sent/received.
          $ref: "#/components/schemas/CustomerAmountGroup"
    GetCustomerRegistrationResponse:
      type: object
      required:
      - enabled
      properties:
        enabled:
          description: Indicates if customer is enabled in the e-Transfer system.
          type: string
          enum: ['ENABLED', 'DISABLED']
          example: ENABLED
        customer_enabled_for_money_request:
          description: Indicates if customer is enabled in e-Transfer system to send
            requests for payments, present for if product code is DOMESTIC.
          type: string
          enum: ['ENABLED', 'DISABLED']
          example: ENABLED
        customer_information:
          $ref: "#/components/schemas/Customer"
        last_updated_date:
          #description: Date of last Customer update.
          $ref: "#/components/schemas/CustomDateTime"
    IncomingLimits:
      description: Required for e-Transfer Domestic customers or e-Transfer US remittance
        recipients
      type: object
      required:
      - max_transfer_incoming_amount
      properties:
        max_transfer_incoming_amount:
          #description: Maximum incoming amount per transfer
          $ref: "#/components/schemas/AmountWithCurrency"
    IrRecipientData:
      description: Recipient information stored at the contact level to facilitate
        e-Transfer International Remittance transfers
      type: object
      required:
      - recipient_corridor
      properties:
        recipient_corridor:
          description: >-
            'Recipient corridor in which the Contact/Recipient is receiving the transfers.
            ISO-3166-1 Alpha 2 (two letter Country code, e.g., CA).'
          type: string
        recipient_data:
          description: >-
            'Dynamic list of fields are required for recipient depending on corridors
            money is sent, Reuired dynamic data fields.</br>nationality </br> bank-name
            </br> bank-account-number </br> branch-code'
          type: array
          items:
            $ref: "#/components/schemas/DataField"
    Language:
      description: Recipient's Language Preference as defined by Sender
      type: string
      enum: ['EN', 'FR']
      default: EN
      example: 'EN'
    LegalName:
      description: Legal name
      type: object
      oneOf:
      - type: object
        properties:
          retail_name:
            $ref: "#/components/schemas/RetailName"
      - type: object
        properties:
          business_name:
            $ref: "#/components/schemas/BusinessName"
    LimitType:
      description: >-
        Limits are applicable based on transaction types,
        STANDARD - Standard e-Transfer transactions
        PROXIMITY - Proximity transactions
        LOW_RISK - Low risk transactions
      type: string
      enum: ['STANDARD', 'PROXIMITY', 'LOW_RISK']
      example: STANDARD
    MaxOutgoingAmountLimits:
      description: Not required for e-Transfer US remittance recipients
      type: object
      required:
      - max_L2_outgoing_amount
      properties:
        limit_type:
          $ref: "#/components/schemas/LimitType"
        max_transfer_outgoing_amount:
          #description: Maximum outgoing amount per transfer for retail or unknown.
          $ref: "#/components/schemas/AmountWithCurrency"
    MaxRequestOutgoingAmountLimits:
      description: Not required for e-Transfer US remittance recipients
      type: object
      required:
      - max_L2_outgoing_amount
      properties:
        limit_type:
          $ref: "#/components/schemas/LimitType"
        max_request_outgoing_amount:
          #description: Maximum outgoing amount per money request
          $ref: "#/components/schemas/AmountWithCurrency"
    NotificationActive:
      description: >-
        'false - Notifications will NOT be sent <br/> true -Notifications will be
        sent'
      type: boolean
      example: true
    NotificationPreference:
      description: List of customer's notifications. Not required for e-Transfer US
        Remittance recipient only customers. Sender's method to notify the recipient.
        At least one notification has to be defined and active for the contact to
        be eligible to receive e-Transfer domestic or e-Transfer US Remittance products.
        If the directDepositData field is present then the notificationPreference
        at contact level cannot be defined.
      type: object
      discriminator:
        propertyName: type
        mapping:
          EMAIL: '#/components/schemas/EmailNotification'
          SMS: '#/components/schemas/SMSNotification'
          RN: '#/components/schemas/RoutedNotification'
      required:
      - type
      - active
      properties:
        type:
          $ref: '#/components/schemas/NotificationType'
        active:
          $ref: '#/components/schemas/NotificationActive'
    NotificationType:
      type: string
      description: Email <br/> SMS <br/> RN ( Participant_Routed_Notification ) channel
      enum: ['EMAIL', 'SMS', 'RN']
      example: EMAIL
    OneTimeContact:
      description: >-
        'Flag indicating if the Recipient/Contact will be used in a single transfer
        or money request. <br/>
        false - Recipient/Contact will be stored in the Recipient/Contact list <br/>
        ( default is set to false in case of POST )
        true - One time Recipient/Contact. A one time Recipient/Contact cannot belong
        to a group and is not returned in GET Contacts.'
      type: boolean
    OutgoingLimits:
      description: Not required for e-Transfer US remittance recipients
      type: object
      required:
      - min_transfer_outgoing_amount
      - max_transfer_outgoing_amount
      - max_daily_outgoing_amount
      - max_L1_outgoing_amount
      - max_L2_outgoing_amount
      properties:
        min_transfer_outgoing_amount:
          #description: Minimum outgoing amount per transfer
          $ref: "#/components/schemas/AmountWithCurrency"
        max_transfer_outgoing_amount_limits:
          type: array
          minItems: 1
          items:
            $ref: "#/components/schemas/MaxOutgoingAmountLimits"
        max_daily_outgoing_amount:
          #description: Maximum daily cumulative outgoing amount per customer
          $ref: "#/components/schemas/AmountWithCurrency"
        max_L1_outgoing_amount:
          #description: Maximum first rolling limit cumulative outgoing transfer amount per customer
          $ref: "#/components/schemas/AmountWithCurrency"
        max_L2_outgoing_amount:
          #description: Maximum second rolling limit cumulative outgoing transfer amount per customer
          $ref: "#/components/schemas/AmountWithCurrency"
    ParticipantUserId:
      description: Participant user ID in the participant's system.
      type: string
      maxLength: 35
      example: 12345678
    PhoneNumber:
      description: >-
        The collection of information which identifies a specific phone or FAX number
        as defined by telecom services.
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).
      type: string
      maxLength: 30
      format: '\+[0-9]{1,3}-[0-9()+\-]{1,30}'
      example: ******-555-1212
    PostalAddress:
      description: Postal Address information.
      type: object
      required:
      - address_1
      - city
      - country
      properties:
        address_1:
          description: Part 1 of the address
          type: string
          minLength: 1
          maxLength: 80
        address_2:
          description: Part 2 of the address
          type: string
          maxLength: 80
        city:
          description: City of residence
          type: string
          minLength: 1
          maxLength: 50
        province:
          description: State/Province of residence (2 letter code). Required for Canadian
            and US addresses.
          type: string
          maxLength: 20
        postal_code:
          description: Postal code. Required for Canadian and US addresses.
          type: string
          maxLength: 40
        country:
          $ref: "#/components/schemas/CountryCode"
    ProductCode:
      description: >-
        Identifies an Interac e-Transfer product by code. The valid code values are:
        <br/> DOMESTIC - e-Transfer domestic <br/>  INTERNATIONAL - e-Transfer International
        Remittance
      type: string
      enum: ['DOMESTIC', 'INTERNATIONAL']
      example: DOMESTIC
    ProductRegistration:
      description: Identifies the details of an e-Transfer product registration.
      type: object
      required:
      - product_code
      - currency_code
      properties:
        product_code:
          $ref: "#/components/schemas/ProductCode"
        currency_code:
          description: List of currencies associated with customer.
          type: array
          minItems: 1
          items:
            description: Currency associated with the product. Valid values are CAD
              and USD
            type: string
            enum: ['CAD', 'USD']
            example: CAD
        customer_limits_group_id:
          description: Identifies a limit group to be associated with this product
            registration. If element is not specified then the 'DEFAULT' limit group
            is applied.
          type: string
          minLength: 1
          maxLength: 50
    Reason:
      description: Reason for action performed ( cancel, decline, disable etc...)
      type: string
      maxLength: 400

    RequestLimits:
      description: ""
      type: object
      properties:
        max_request_outgoing_amount_limits:
          type: array
          minItems: 1
          items:
            $ref: "#/components/schemas/MaxRequestOutgoingAmountLimits"
        max_number_outstanding_requests:
          description: Maximum number of outstanding money requests per customer
          type: integer
    RetailName:
      description: Retail name. This is required for type 0 (retail)
      type: object
      required:
      - first_name
      - last_name
      properties:
        first_name:
          description: First name
          type: string
          minLength: 1
          maxLength: 100
          example: first_name
        middle_name:
          description: Middle name
          type: string
          minLength: 1
          maxLength: 100
          example: middle_name
        last_name:
          description: Last name
          type: string
          minLength: 1
          maxLength: 100
          example: last_name
    RoutedNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via Participant Routing system
        properties:
          handle:
            type: string
            maxLength: 64
            description: The handle of the notification registration at the participant.
            example: support
# NotificationHandle used in case of registration #
    SMSNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via SMS
        required:
        - phone_number
        properties:
          phone_number:
            $ref: '#/components/schemas/PhoneNumber'
    ServiceType:
      description: >-
        Flag indicating the type of the Direct Deposit registration <br/>
        Email - Email based Direct Deposit <br/>
        UUID - UUID based Direct Deposit. Multiple types of unique identifiers are
        supported. <br/>
        SMS - Phone based Direct Deposit
      type: string
      enum: ['EMAIL', 'UUID', 'SMS']
      example: EMAIL
    SignatureType:
      description: >-
        The type of the JWT. Required. Allowed values are "PAYLOAD_DIGEST_SHA256"
      type: string
      enum: ['PAYLOAD_DIGEST_SHA256']
      example: PAYLOAD_DIGEST_SHA256
    TransactionTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.ssssssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: 2019-05-29T17:19:26.951000Z
    TransferAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      allOf:
      - $ref: "#/components/schemas/ViewTransferAuthentication"
      - type: object
        required:
        - authentication_type
        properties:
          hash_salt:
            description: Required if authenticationType is CONTACT_LEVEL
            type: string
            minLength: 1
            maxLength: 44
          security_answer:
            description: >-
              'Answer to the security question (as provided by the customer) with
              leading and trailing whitespace trimmed, uppercased, postfixed with
              hashSalt if present, hashed using the alghoritm identified by hashType
              and then Base64 encoded. ISO-8859-1encoding to be used when the hash
              is generated. Required if authenticationType is CONTACT_LEVEL.'
            type: string
            minLength: 1
            maxLength: 64
    UpdateContact:
      description: "Update Contact details"
      allOf:
      - $ref: "#/components/schemas/Contact"
      - type: object
        required:
        - contact_name
        properties:
          contact_group_ids:
            description: List of contact group Id's where contact belongs to
            type: array
            items:
              type: string
              maxLength: 35
          transfer_authentication:
            $ref: "#/components/schemas/TransferAuthentication"
          product_options:
            description: List of products the contact/recipient is registered for,
              default is set to DOMESTIC if not specified.
            type: array
            items:
              $ref: "#/components/schemas/ProductCode"
    ViewContact:
      description: "View Contact details"
      allOf:
      - $ref: "#/components/schemas/Contact"
      - type: object
        required:
        - contact_id
        - one_time_contact
        - contact_name
        - language
        - transfer_authentication
        properties:
          contact_groups:
            description: List of contact group Id's where contact belongs to
            type: array
            items:
              $ref: "#/components/schemas/ContactGroup"
          one_time_contact:
            $ref: "#/components/schemas/OneTimeContact"
          contact_id:
            description: Unique Recipient/Contact id at Interac
            type: string
            maxLength: 35
          transfer_authentication:
            $ref: "#/components/schemas/ViewTransferAuthentication"
          available_products:
            description: List of products the contact is eligible for.
            type: array
            items:
              $ref: "#/components/schemas/AvailableProducts"
    ViewTransferAuthentication:
      description: The below fields are returned when Participant view or retrieve
        contacts
      type: object
      properties:
        authentication_type:
          $ref: "#/components/schemas/AuthenticationType"
        security_question:
          description: Question text. Required if authenticationType is CONTACT_LEVEL
          type: string
          maxLength: 40
        hash_type:
          description: Algorithm used to hash the security answer. It has to be one
            of values supported by the system. Required if authenticationType is CONTACT_LEVEL
          type: string
          enum: ['SHA2']
          example: SHA2
