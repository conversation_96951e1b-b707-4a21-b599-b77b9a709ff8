package com.peoples.banking.persistence.customer.entity;

import org.junit.jupiter.api.Test;

class CustomersTest {


    @Test
    void getExternalRefId() {

        Customers customers=Customers.builder().build();
        customers.setId(1);
        customers.setExternalRefId("aaaa1111");
        assert(customers.getId()==1);
        assert(customers.getExternalRefId().equals("aaaa1111"));
    }

    @Test
    void builder() {

    }
}