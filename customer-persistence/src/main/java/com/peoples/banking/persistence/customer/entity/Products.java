package com.peoples.banking.persistence.customer.entity;
// Generated May 5, 2020, 11:57:53 a.m. by Hibernate Tools 5.4.12.Final


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Products generated by hbm2java
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="products"
    ,schema="relationship"
)
public class Products  implements Serializable {

     @Id
     @Column(name="id", unique=true, nullable=false)
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     private short id;

     @Column(name="external_ref_id", nullable=false)
     private String externalRefId;

     @Type(type = "jsonb")
     @Column(name="name_json", nullable=false,columnDefinition = "jsonb")
     private String nameJson;

     @Column(name="name_json_ver", nullable=false)
     private short nameJsonVersion;

     @Column(name="active", nullable=false)
     private boolean active;

     @Column(name="created_on", nullable=false, length=29)
     private LocalDateTime createdOn;

     @Column(name="updated_on", length=29)
     private LocalDateTime updatedOn;

     @OneToMany(fetch=FetchType.LAZY, mappedBy="products")
     private Set<Enrollments> enrollments = new HashSet(0);
}


