package com.peoples.banking.persistence.customer.entity;
// Generated May 5, 2020, 11:57:53 a.m. by Hibernate Tools 5.4.12.Final


import com.vladmihalcea.hibernate.type.basic.PostgreSQLEnumType;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

/**
 * Customers generated by hbm2java
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="customers"
    ,schema="relationship"
    , uniqueConstraints = @UniqueConstraint(columnNames={"external_ref_id", "service_account_ref_id"})
)
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@TypeDef(
        name = "pgsql_enum",
        typeClass = PostgreSQLEnumType.class
)
@NamedQueries({
        @NamedQuery(name = "allCustomersByUpdatedDate", query = "select c from Customers c where coalesce(c.updatedOn, c.createdOn) >= :start and coalesce(c.updatedOn, c.createdOn) <= :end"),
})
public class Customers implements java.io.Serializable {

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     @Column(name="id", unique=true, nullable=false)
     private int id;

     @Column(name="external_ref_id", nullable=false)
     private String externalRefId;

     @Column(name="service_account_ref_id", nullable=false)
     private String serviceAccountRefId;

     @Column(name="crm_ref_id")
     private String crmRefId;

     @Type(type = "jsonb")
     @Column(name="name_json", nullable=false,columnDefinition = "jsonb")
     private String nameJson;

     @Column(name="name_json_ver", nullable=false)
     private short nameJsonVersion;

     @Enumerated(EnumType.STRING)
     @Column(name="type_cd", nullable=false)
     @Type( type = "pgsql_enum" )
     private CustomerCodeType typeCd;

     @Enumerated(EnumType.STRING)
     @Column(name="language_iso", nullable=false)
     @Type( type = "pgsql_enum" )
     private LanguageISOType languageIso;

     @Column(name="date_of_birth", length=13)
     private LocalDate dateOfBirth;

     @Type(type = "jsonb")
     @Column(name="address_json", columnDefinition = "jsonb")
     private String addressJson;

     @Column(name="address_json_ver")
     private short addressJsonVersion;

     @Type(type = "jsonb")
     @Column(name="email_json", nullable=false,columnDefinition = "jsonb")
     private String emailJson;

     @Column(name="email_json_ver", nullable=false)
     private short emailJsonVersion;

     @Type(type = "jsonb")
     @Column(name="phone_json",columnDefinition = "jsonb")
     private String phoneJson;

     @Column(name="phone_json_ver", nullable=false)
     private short phoneJsonVersion;

     @Column(name="active", nullable=false)
     private boolean active;

     @Column(name="created_on", nullable=false, length=29)
     private LocalDateTime createdOn;

     @Column(name="updated_on", length=29)
     private LocalDateTime updatedOn;

     @OneToMany(fetch = FetchType.LAZY, mappedBy = "customers", cascade = CascadeType.ALL)
     private Set<Enrollments> enrollments = new HashSet<>(0);
}


