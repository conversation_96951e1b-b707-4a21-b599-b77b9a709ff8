package com.peoples.banking.persistence.customer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import com.peoples.banking.persistence.customer.entity.Enrollments;

public interface EnrollmentsRepository extends JpaRepository<Enrollments, Integer> {

  @Query("SELECT case when count(e)> 0 then true else false end FROM Enrollments e WHERE e.networkEnrollmentId = ?1")
  boolean checkNetworkEnrollmentIdExist(String enrollmentRefId);

  @Query(value = "select nextval('relationship.enrollment_id_seq')", nativeQuery =true)
  long getNextEnrollmentSequence();
}
