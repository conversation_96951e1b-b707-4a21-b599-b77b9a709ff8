package com.peoples.banking.persistence.customer.repository;

import java.util.List;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.peoples.banking.persistence.customer.entity.Products;

@Repository
public interface ProductsRepository extends JpaRepository<Products, Integer> {

    @Override
    @Cacheable("Products")
    List<Products> findAll();

}
