package com.peoples.banking.persistence.customer.repository;

import com.peoples.banking.persistence.customer.entity.Aliases;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AliasesRepository extends JpaRepository<Aliases, Integer> {

  Optional<Aliases> findByNetworkAliasRefIdAndExternalRefId(String networkAliasId, String externalRefId);

  List<Aliases> findByNetworkAliasRefId(String networkAliasId);

  Optional<Aliases> findByCustomerIdAndExternalRefId(Integer customerId, String aliasId);

  List<Aliases> findByCustomerIdAndEnrollmentId(Integer customerId, Integer enrollmentId);

  List<Aliases> findByCustomerId(Integer customerId);

  @Query(value = "select nextval('relationship.alias_id_seq')", nativeQuery = true)
  long getNextAliasSequence();

}
