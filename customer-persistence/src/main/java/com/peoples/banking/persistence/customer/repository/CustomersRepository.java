package com.peoples.banking.persistence.customer.repository;

import com.peoples.banking.persistence.customer.entity.Customers;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CustomersRepository extends JpaRepository<Customers, Integer> {

  // new method support find by CustomerID + Service Account ID
  @Query("select a from Customers a where a.externalRefId=?1 and a.serviceAccountRefId=?2")
  Optional<Customers> findByCustomerIdAndServiceAccountRefId(String externalRefId,
      String serviceAccountId);


  @Query("SELECT case when count(c)> 0 then true else false end FROM Customers c WHERE c.externalRefId = ?1 and c.serviceAccountRefId = ?2")
  boolean checkCustomerIdExist(String externalRefId, String serviceAccountId);

  @Query(value = "select nextval('relationship.customer_id_seq')", nativeQuery = true)
  long getNextCustomerSequence();

  // internal method to find customer record
  Optional<Customers> findByExternalRefId(String customerId);

  @Query(value = "select service_account_ref_id from relationship.customers where email_json @> cast (?1 as jsonb)", nativeQuery = true)
  String findServiceAccountByEmailInternal(String email);

  default String findServiceAccountByEmail(String email) {
    return findServiceAccountByEmailInternal("[{\"type\": \"PRIMARY\", \"email\": \"" + email + "\"}]");
  }

}
