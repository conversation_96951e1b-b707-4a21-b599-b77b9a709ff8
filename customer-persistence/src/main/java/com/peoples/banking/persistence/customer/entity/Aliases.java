package com.peoples.banking.persistence.customer.entity;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TypeDef;

/**
 * Aliases entity class
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "aliases"
    , schema = "relationship"
)
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class Aliases implements java.io.Serializable {

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     @Column(name = "id", unique = true, nullable = false)
     private int id;

     @Column(name = "customer_id", unique = true, nullable = false)
     private int customerId;

     @Column(name = "enrollment_id")
     private int enrollmentId;

     @Column(name = "external_ref_id", nullable = false)
     private String externalRefId;

     @Column(name = "network_alias_ref_id", nullable = false)
     private String networkAliasRefId;

     @Column(name = "type_cd", nullable = false)
     private String typeCd;

     @Column(name = "handle", nullable = false)
     private String handle;

     @Column(name = "expires_on", length = 29)
     private LocalDateTime expiresOn;

     @Column(name = "created_on", nullable = false, length = 29)
     private LocalDateTime createdOn;

     @Column(name = "updated_on", length = 29)
     private LocalDateTime updatedOn;

     @Column(name = "account_number", length = 29)
     private String accountNumber;

     @Column(name = "active", nullable = false)
     private boolean active;

}


