package com.peoples.banking.persistence.customer.entity;
// Generated May 5, 2020, 11:57:53 a.m. by Hibernate Tools 5.4.12.Final


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Enrollments generated by hbm2java
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="enrollments"
    ,schema="relationship"
    , uniqueConstraints = @UniqueConstraint(columnNames="network_enrollment_id")
)
public class Enrollments  implements java.io.Serializable {

     @Id
     @Column(name="id", unique=true, nullable=false)
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     private int id;

     @ManyToOne(fetch=FetchType.EAGER)
     @JoinColumn(name="customer_id", nullable=false)
     private Customers customers;

     @ManyToOne(fetch=FetchType.EAGER)
     @JoinColumn(name="product_id", nullable=false)
     private Products products;

     @Column(name="network_enrollment_id", unique=true, nullable=false)
     private String networkEnrollmentId;

     @Column(name="active", nullable=false)
     private boolean active;

     @Column(name="created_on", nullable=false, length=29)
     private LocalDateTime createdOn;

     @Column(name="updated_on", length=29)
     private LocalDateTime updatedOn;
}


