package com.peoples.banking.adapter.pb.serviceaccount.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.config.AdapterConstant;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.config.ServiceAccountAdapterConstant;
import com.peoples.banking.adapter.pb.serviceaccount.config.ServiceAccountAdapterProperty;
import com.peoples.banking.domain.serviceaccount.model.ErrorResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificateByRefResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.domain.serviceaccount.model.TransactionEligibilityResponse;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

@Log4j2
@Service
public class ServiceAccountAdapterImpl implements ServiceAccountAdapter {

  @Autowired
  private ServiceAccountAdapterProperty serviceAccountAdapterProperty;

  @Autowired
  @Qualifier("serviceAccountManagementRestTemplate")
  private RestTemplate restTemplate;

  @Autowired
  protected ObjectMapper objectMapper;

  /**
   * service account endpoint URL.
   */
  private String retrieveServiceAccountConfigurationByKeyEndpoint = null;
  private String retrieveServiceAccountConfigurationsEndpoint = null;
  private String retrieveServiceAccountEndpointByApiToken = null;
  private String retrieveServiceAccountEndpointByRefId = null;
  private String retrieveValidateServiceAccountTransactionEligibility = null;
  private String validateServiceAccountFeatureEndpoint = null;
  private String retrieveServiceAccountCertificateByRefId = null;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ServiceAccountResponse retrieveServiceAccountByApiToken(String apiToken)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (apiToken == null || apiToken.isBlank()) {
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getServiceAccountByApiTokenEndpoint();

    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(ServiceAccountAdapterConstant.API_TOKEN, apiToken);

    ServiceAccountResponse serviceAccountResponse = retrieveServiceAccount(endpointUrl, params);

    return serviceAccountResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public ServiceAccountResponse retrieveServiceAccountByRefId(String refId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check
    if (refId == null || refId.isBlank()) {
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getServiceAccountByRefIdEndpoint();

    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(ServiceAccountAdapterConstant.REF_ID, refId);

    ServiceAccountResponse serviceAccountResponse = retrieveServiceAccount(endpointUrl, params);

    return serviceAccountResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public RetrieveServiceAccountConfigurationsResponse retrieveServiceAccountConfigurations(String refId, String interactionId)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    String endpointUrl = getRetrieveServiceAccountConfigurationsEndpoint();

    Map<String, String> params = new HashMap<>();
    params.put(ServiceAccountAdapterConstant.REF_ID, refId);

    RetrieveServiceAccountConfigurationsResponse retrieveServiceAccountConfigurationsResponse = null;
    try {
      // populate HTTP headers
      log.debug("populating HTTP headers");
      // default HTTP httpHeaders
      HttpHeaders headers = buildHeaders();

      HttpEntity<ServiceAccountTransaction> httpEntity = new HttpEntity<>(headers);

      ResponseEntity<RetrieveServiceAccountConfigurationsResponse> response =
          restTemplate.exchange(endpointUrl, HttpMethod.GET, httpEntity, RetrieveServiceAccountConfigurationsResponse.class, params);

      retrieveServiceAccountConfigurationsResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return retrieveServiceAccountConfigurationsResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public TransactionEligibilityResponse validateServiceAccountTransactionElgty(String refId, ServiceAccountTransaction serviceAccountTransaction)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    if (refId == null || refId.isBlank()) {
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // endpoint URL
    String endpointUrl = getValidateServiceAccountTransactionElgtyEndpoint();

    // build path parameters
    Map<String, String> params = new HashMap<>();
    params.put(ServiceAccountAdapterConstant.REF_ID, refId);

    TransactionEligibilityResponse transactionEligibilityResult = null;
    try {
      // populate HTTP headers
      log.debug("populating HTTP headers");
      // default HTTP httpHeaders
      HttpHeaders headers = buildHeaders();
      HttpEntity<ServiceAccountTransaction> httpEntity = new HttpEntity<>(serviceAccountTransaction, headers);

      ResponseEntity<TransactionEligibilityResponse> response =
          restTemplate.exchange(endpointUrl, HttpMethod.POST, httpEntity, TransactionEligibilityResponse.class, params);

      log.info("responseBody={}", objectMapper.writeValueAsString(response.getBody()));

      transactionEligibilityResult = response.getBody();
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)

      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return transactionEligibilityResult;
  }

  /**
   * @param endpointUrl
   * @param params
   * @return
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  private ServiceAccountResponse retrieveServiceAccount(String endpointUrl, Map<String, String> params)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    ServiceAccountResponse serviceAccountResponse = null;
    try {
      // populate HTTP headers
      log.debug("populating HTTP headers");
      // default HTTP httpHeaders
      HttpHeaders headers = buildHeaders();

      ResponseEntity<ServiceAccountResponse> response =
          restTemplate.exchange(endpointUrl, HttpMethod.GET, new HttpEntity<>(headers), ServiceAccountResponse.class, params);

      log.debug("responseBody={}", response.getBody());
      serviceAccountResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side-effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return serviceAccountResponse;
  }

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public boolean validateServiceAccountFeature(String refId, String featureName)
      throws AdapterException, ResponseException, TBDException, TimeoutException {

    boolean validateServiceAccountResponse = false;
    try {
      if (StringUtils.isEmpty(refId)) {
        log.info("Reference id is empty for validateServiceAccountFeature ");
        return true;
      }
      // populate HTTP headers
      log.debug("populating HTTP headers");
      // default HTTP httpHeaders
      HttpHeaders headers = buildHeaders();

      // build path parameters
      Map<String, String> params = new HashMap<>();
      params.put(ServiceAccountAdapterConstant.REF_ID, refId);
      params.put(ServiceAccountAdapterConstant.FEATURE_NAME, featureName);

      String endpointUrl = getValidateServiceAccountFeatureEndpoint();

      ResponseEntity<Boolean> response =
          restTemplate.exchange(endpointUrl, HttpMethod.GET, new HttpEntity<>(headers), Boolean.class, params);

      validateServiceAccountResponse = response.getBody();
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());

      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side-effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return validateServiceAccountResponse;
  }

  @Override
  public RetrieveServiceAccountCertificateByRefResponse retrieveServiceAccountCertificateById(String keyId, String serviceAccountToken)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    RetrieveServiceAccountCertificateByRefResponse result = null;
    try {
      String endpointUrl = getRetrieveServiceAccountCertificateByRefIdEndpoint();

      // build path parameters
      Map<String, String> params = new HashMap<>();
      params.put(ServiceAccountAdapterConstant.CERTIFICATE_KEY_ID, keyId);
      params.put(ServiceAccountAdapterConstant.API_TOKEN, serviceAccountToken);
      // populate HTTP headers
      log.debug("populating HTTP headers");
      // default HTTP httpHeaders
      HttpHeaders headers = buildHeaders();

      ResponseEntity<RetrieveServiceAccountCertificateByRefResponse> response =
          restTemplate.exchange(endpointUrl, HttpMethod.GET, new HttpEntity<>(headers), RetrieveServiceAccountCertificateByRefResponse.class, params);

      result = response.getBody();
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete) {@code includes
       * HttpClientErrorException (all 4xx series HTTP status codes)} {@code includes
       * HttpServerErrorException (all 5xx series HTTP status codes)} {@code includes
       * UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)

      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // result with potential side effects (transaction MAY HAVE completed)
      // {@code includes SocketTimeoutException (may have updated record, undetermined)}
      // {@code includes ConnectionPoolTimeoutException (no record updated)}
      // {@code includes ConnectTimeoutException (undefined HTTP status codes)}

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return result;
  }

  /**
   * @inheritDoc
   */
  // TODO clean this up, not elegant; make part of abstract class
  @Override
  public void throwResponseException(String response, Integer httpStatusCode) throws ResponseException {

    // sanity check
    if (response != null && !response.isBlank()) {
      ErrorResponse responseModel;
      try {
        responseModel = this.convertResponseToDomain(response, ErrorResponse.class);
      } catch (TBDException e) {
        log.warn("cannot convert Customer Management error response into something meaningful");
        throw new ResponseException(httpStatusCode, null, null);
      }

      throw new ResponseException(httpStatusCode,
          responseModel.getError().get(0).getCode(),
          responseModel.getError().get(0).getAdditionalInformation());
    } else {
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>       the generic object type that the response is converted to
   * @param payload   payload to be converted
   * @param classType the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String payload, Class<T> classType) throws TBDException {
    T response = null;

    // sanity check
    if (payload != null && !payload.isBlank()) {
      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }

  /**
   * Returns the <i>system</i> domain endpoint URL.
   */
  protected String getBaseUrl() {
    return serviceAccountAdapterProperty.getServiceAccountUrlBase();
  }

  /**
   * Helper utility to construct <i>AccountByApiToken</i> endpoint URL.
   *
   * @return
   */
  public String getServiceAccountByApiTokenEndpoint() {
    if (retrieveServiceAccountEndpointByApiToken == null) {
      retrieveServiceAccountEndpointByApiToken = this.getBaseUrl() + ServiceAccountAdapterConstant.RETRIEVE_SERVICE_ACCOUNT_BY_API_TOKEN;
    }
    return retrieveServiceAccountEndpointByApiToken;
  }

  public String getRetrieveServiceAccountConfigurationsEndpoint() {
    if (retrieveServiceAccountConfigurationsEndpoint == null) {
      retrieveServiceAccountConfigurationsEndpoint = this.getBaseUrl() + ServiceAccountAdapterConstant.RETRIEVE_SERVICE_ACCOUNT_CONFIGS;
    }
    return retrieveServiceAccountConfigurationsEndpoint;
  }

  /**
   * Helper utility to construct <i>AccountByRefId</i> endpoint URL.
   *
   * @return
   */
  public String getServiceAccountByRefIdEndpoint() {
    if (retrieveServiceAccountEndpointByRefId == null) {
      retrieveServiceAccountEndpointByRefId = this.getBaseUrl() + ServiceAccountAdapterConstant.RETRIEVE_SERVICE_ACCOUNT_BY_REF_ID;
    }
    return retrieveServiceAccountEndpointByRefId;
  }

  public String getValidateServiceAccountTransactionElgtyEndpoint() {
    if (retrieveValidateServiceAccountTransactionEligibility == null) {
      retrieveValidateServiceAccountTransactionEligibility =
          this.getBaseUrl() + ServiceAccountAdapterConstant.VALIDATE_SERVICE_ACCOUNT_TRANSACTION_ELGTY;
    }
    return retrieveValidateServiceAccountTransactionEligibility;
  }

  public String getValidateServiceAccountFeatureEndpoint() {
    if (validateServiceAccountFeatureEndpoint == null) {
      validateServiceAccountFeatureEndpoint = this.getBaseUrl() + ServiceAccountAdapterConstant.VALIDATE_SERVICE_ACCOUNT_FEATURE;
    }
    return validateServiceAccountFeatureEndpoint;
  }

  public String getRetrieveServiceAccountCertificateByRefIdEndpoint() {
    if (retrieveServiceAccountCertificateByRefId == null) {
      retrieveServiceAccountCertificateByRefId = this.getBaseUrl() + ServiceAccountAdapterConstant.RETRIEVE_SERVICE_ACCOUNT_CERTIFICATE_BY_KEY;
    }
    return retrieveServiceAccountCertificateByRefId;
  }

  /**
   * Builds default headers for Service Account API requests.
   *
   * @return Default HttpHeaders.
   */
  public HttpHeaders buildHeaders() {
    HttpHeaders httpHeaders = new HttpHeaders();
    final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
    if (requestAttributes != null) {
      String loggingGuid = (String) requestAttributes
              .getAttribute(AdapterConstant.LOGGING_FIELD_GUIID, RequestAttributes.SCOPE_REQUEST);
      httpHeaders.add(AdapterConstant.HEADER_INTERACTION_ID, loggingGuid);
    }

    // default HTTP httpHeaders
    httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    httpHeaders.add(AdapterConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());

    return httpHeaders;
  }

}
