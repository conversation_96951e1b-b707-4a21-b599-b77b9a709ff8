package com.peoples.banking.adapter.pb.serviceaccount;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountCertificateByRefResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationResponse;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.domain.serviceaccount.model.TransactionEligibilityResponse;

public interface ServiceAccountAdapter {

  /**
   * retrieve service account by api token
   *
   * @param apiToken - service account api token
   * @return - service account mapped to ServiceAccountResponse
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  ServiceAccountResponse retrieveServiceAccountByApiToken(String apiToken)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * retrieve service account by reference id
   *
   * @param refId - service account reference id
   * @return - service account mapped to ServiceAccountResponse
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  ServiceAccountResponse retrieveServiceAccountByRefId(String refId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * retrieve service account config by reference id
   *
   * @param refId - service account reference id
   * @param interactionId - interactionId
   * @return - service account mapped to ServiceAccountResponse
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  RetrieveServiceAccountConfigurationsResponse retrieveServiceAccountConfigurations(String refId, String interactionId)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  boolean validateServiceAccountFeature(String refId, String featureName)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * validate service account transaction eligibility according to current limits
   *
   * @param refId                     - service account reference id
   * @param serviceAccountTransaction - service account transaction to validate
   * @return - eligibility for operation result
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  TransactionEligibilityResponse validateServiceAccountTransactionElgty(String refId, ServiceAccountTransaction serviceAccountTransaction)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

  /**
   * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
   *
   * @param response       response body from <i>Interac</i>
   * @param httpStatusCode HTTP status code from downstream system
   * @throws ResponseException response exception from Interac
   */
  void throwResponseException(String response, Integer httpStatusCode) throws ResponseException;

  /**
   * validate service account transaction eligibility according to current limits
   *
   * @param keyId               - service account certificate key id
   * @param serviceAccountToken - service account inbound api token
   * @return - eligibility for operation result
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  RetrieveServiceAccountCertificateByRefResponse retrieveServiceAccountCertificateById(String keyId, String serviceAccountToken)
      throws AdapterException, ResponseException, TBDException, TimeoutException;

}
