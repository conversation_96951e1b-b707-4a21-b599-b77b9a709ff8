package com.peoples.banking.adapter.pb.serviceaccount.config;

public final class ServiceAccountAdapterConstant {

  private ServiceAccountAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

  private static final String SERVICE_ACCOUNT_OPERATION = "/v1/internal/service-account";
  public static final String API_TOKEN = "apiToken";
  public static final String TOKEN = "/token";
  public static final String REF_ID = "refId";
  public static final String CERTIFICATE_KEY_ID = "certificateKeyId";
  public static final String KEY_NAME = "key_name";
  public static final String FEATURE_NAME = "featureName";
  public static final String FEATURE = "feature";
  public static final String TRANSACTION_ELIGIBILITY = "/transaction-eligibility";
  private static final String CERTIFICATE = "/certificate";
  public static final String RETRIEVE_SERVICE_ACCOUNT_BY_API_TOKEN = SERVICE_ACCOUNT_OPERATION + TOKEN + "/{" + API_TOKEN + "}";

  public static final String RETRIEVE_SERVICE_ACCOUNT_CONFIGS = SERVICE_ACCOUNT_OPERATION + "/{" + REF_ID + "}/config";
  public static final String RETRIEVE_SERVICE_ACCOUNT_BY_REF_ID = SERVICE_ACCOUNT_OPERATION + "/{" + REF_ID + "}";
  public static final String VALIDATE_SERVICE_ACCOUNT_TRANSACTION_ELGTY =
      SERVICE_ACCOUNT_OPERATION + "/{" + REF_ID + "}" + TRANSACTION_ELIGIBILITY;
  public static final String VALIDATE_SERVICE_ACCOUNT_FEATURE =
      SERVICE_ACCOUNT_OPERATION + "/{" + REF_ID + "}/" + FEATURE + "/{" + FEATURE_NAME + "}";
  public static final String RETRIEVE_SERVICE_ACCOUNT_CERTIFICATE_BY_KEY =
      SERVICE_ACCOUNT_OPERATION + "/{" + API_TOKEN + "}" + CERTIFICATE + "/{" + CERTIFICATE_KEY_ID + "}";

  public static final String GL_SERVICE_ENROLLMENT = "GL_SERVICE_ENROLLMENT";
  public static final String GL_ACCOUNT_ID = "GL_ACCOUNT_ID";
  public static final String GL_PROFILE_ID = "GL_PROFILE_ID";

}
