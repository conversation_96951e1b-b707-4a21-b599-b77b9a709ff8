package com.peoples.banking.api.ptc.payment.queue.listener.v1.util;

import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.RandomStringUtils;

@UtilityClass
public class PtcPaymentTestUtil {

  public static final String SERVICE_ACCOUNT_REF_ID = "ABC123";
  public static final String TEST_ACCOUNT_NUMBER = "621-16001-************";
  public static final String NETWORK_TRANSACTION_ID = "CAeu8o23jf3829";
  public static final String AMOUNT = "100.25";


  public static Payments createPayments() {
    Payments payments = new Payments();
    payments.setServiceAccountRefId(SERVICE_ACCOUNT_REF_ID);
    payments.setEndToEndId(UUID.randomUUID().toString());
    payments.setCustomerRefId("CUST1235");
    payments.setExternalRefId(RandomStringUtils.randomAlphanumeric(20));
    payments.setTypeCd(PaymentCdType.OUTBOUND);

    short version = 1;

    payments.setContactJson(
        "{\"name\": \"Alize Bogisich\", \"aliasReferenceId\": \"wztmhtsf210304095922292tem35s4il\", \"contactMethodDto\": {\"email\": \"<EMAIL>\"}}");

    payments.setContactJsonVersion(version);

    payments.setAmount(new BigDecimal(AMOUNT));
    payments.setAccountName("testing acct-123");
    payments.setAccountNumber("621-00525-*********");

    payments.setOptionJsonVersion((short) 1);
    payments.setOptionJson("{\"expiredAfterDays\": 30}");
    payments.setRemittanceJsonVersion(version);
    payments.setRemittanceJson("{\"unstructured\": {\"memo\": \"some simple memo\"}}");

    payments.setNetworkRefId("INTERAC_EMT");
    payments.setNetworkPaymentRefId("CA" + RandomStringUtils.randomAlphanumeric(8));
    payments.setNetworkPaymentUrl("https://gateway-web.beta.interac.ca/reqPayment/CA1MR6jDK7tA");
    payments.setNetworkPaymentType(NetworkPaymentType.REGULAR_PAYMENT.name());
    payments.setNetworkEnrollmentId("2LO7MqgRE");
    payments.setNetworkTransactionId(NETWORK_TRANSACTION_ID);
    payments.setStatus(PaymentStatus.AVAILABLE.getValue());

    payments.setStatusReasonJson("{\"reasonCode\": \"CUSTOMER_INITIATED\", \"description\": \"tesing\"}");
    payments.setStatusReasonJsonVersion(version);

    payments.setNetworkFraudResultJson("{\"score\":0 , \"action\": \"ALLOW\"}");
    payments.setNetworkFraudResultJsonVersion(version);

    payments.setExpiryDate(LocalDateTime.now());
    payments.setNetworkSettled(false);
    payments.setSaSettled(false);

    payments.setCreatedOn(LocalDateTime.now());
    payments.setUpdatedOn(LocalDateTime.now());
    payments.setExternalFiAccountInfoJson("{\"creditor\" : {\"accountNumber\": \"" + TEST_ACCOUNT_NUMBER + "\"}}");

    return payments;
  }
}
