package com.peoples.banking.api.ptc.payment.queue.listener.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.*;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.config.PtcPaymentKafkaListenerProperty;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.util.PtcPaymentTestUtil;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.dto.PtcPaymentKafkaDto;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import java.util.Optional;

import com.peoples.banking.util.api.common.type.PaymentStatus;
import org.hibernate.JDBCException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
class PtcPaymentKafkaListenerServiceTest {

  public static final String PAYMENT_REF_ID = "payment_ref_id";
  @Mock
  private PaymentsRepository paymentsRepository;

  @Mock
  private PtcPaymentKafkaListenerProperty ptcPaymentKafkaListenerProperty;
  @Mock
  private JsonConverter jsonConverter;

  @InjectMocks
  private PtcPaymentKafkaListenerService service;

  @Test
  void consumeMessageThrowsRetryableException() {
    KafkaListenerRetryableException exception = assertThrows(KafkaListenerRetryableException.class, () -> {
      when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(PAYMENT_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(Optional.of(
              PtcPaymentTestUtil.createPayments()));
      service.consumeMessage(new PtcPaymentKafkaDto());
    });
    verify(paymentsRepository, never()).save(isA(Payments.class));
    assertNotNull(exception);
    assertEquals("Error while processing payment kafka message", exception.getMessage());
  }

  @Test
  void consumeMessage_inbound_payment_not_found() {
    KafkaListenerRetryableException exception = assertThrows(KafkaListenerRetryableException.class, () -> {
      PtcPaymentKafkaDto message = new PtcPaymentKafkaDto();
      String networkPaymentType = "network_payment_type";
      message.setPaymentRefId(PAYMENT_REF_ID);
      message.setNetworkPaymentType(networkPaymentType);
      when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(PAYMENT_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(Optional.of(
          PtcPaymentTestUtil.createPayments()));
      service.consumeMessage(new PtcPaymentKafkaDto());
    });
    verify(paymentsRepository, never()).save(isA(Payments.class));
    assertNotNull(exception);
    assertEquals("Error while processing payment kafka message", exception.getMessage());
  }

  @Test
  void consumeMessage_success() {
    PtcPaymentKafkaDto message = new PtcPaymentKafkaDto();
    String networkPaymentType = "network_payment_type";
    message.setPaymentRefId(PAYMENT_REF_ID);
    message.setNetworkPaymentType(networkPaymentType);
    when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(PAYMENT_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(Optional.of(
        PtcPaymentTestUtil.createPayments()));
    when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCdAndStatus(PAYMENT_REF_ID, PaymentCdType.INBOUND, PaymentStatus.COMPLETE.getValue())).thenReturn(Optional.of(
        PtcPaymentTestUtil.createPayments()));
    service.consumeMessage(message);
    verify(paymentsRepository).save(isA(Payments.class));
  }

  @Test
  void consumeMessage_paymentRepositorySaveThrowsException() {
    PtcPaymentKafkaDto message = new PtcPaymentKafkaDto();
    String networkPaymentType = "network_payment_type";
    message.setPaymentRefId(PAYMENT_REF_ID);
    message.setNetworkPaymentType(networkPaymentType);
    when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(PAYMENT_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(Optional.of(
        PtcPaymentTestUtil.createPayments()));
    when(paymentsRepository.findByNetworkPaymentRefIdAndTypeCdAndStatus(PAYMENT_REF_ID, PaymentCdType.INBOUND, PaymentStatus.COMPLETE.getValue())).thenReturn(Optional.of(
        PtcPaymentTestUtil.createPayments()));
    when(paymentsRepository.save(any())).thenThrow(JDBCException.class);
    KafkaListenerRetryableException exception = assertThrows(KafkaListenerRetryableException.class, () -> {
      service.consumeMessage(message);
    });
    assertNotNull(exception);
    assertEquals("Error while processing payment kafka message", exception.getMessage());
  }
}