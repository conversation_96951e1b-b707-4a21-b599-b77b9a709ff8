package com.peoples.banking.api.ptc.payment.queue.listener.v1.service;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerNotRetryableException;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.config.PtcPaymentKafkaListenerProperty;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.dto.ExternalFiAccountInfoJsonDto;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.dto.FiAccountDto;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.dto.PtcPaymentKafkaDto;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import java.util.Optional;

import com.peoples.banking.util.api.common.type.PaymentStatus;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class PtcPaymentKafkaListenerService {

  @Autowired
  private PtcPaymentKafkaListenerProperty ptcPaymentKafkaListenerProperty;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private JsonConverter jsonConverter;

  @Transactional
  public void consumeMessage(PtcPaymentKafkaDto message) {
    try {
      log.info("Processing Ptc Payment Kafka message:{}", message);
      processPayment(message.getPaymentRefId());
      log.debug("Processed Ptc Payment Kafka Message:{}", message);
    } catch (Exception e) {
      throw new KafkaListenerRetryableException("Error while processing payment kafka message", e);
    }
  }

  private void processPayment(String paymentRefId) throws Exception {
    Optional<Payments> optionalPayments = paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(paymentRefId, PaymentCdType.OUTBOUND);
    if (optionalPayments.isEmpty()) {
      log.info("OUTBOUND paymentRefId {} is not found, process is not continue", paymentRefId);
      return;
    }
    Payments payments = optionalPayments.get();

    fullFillPayment(paymentRefId, payments.getAccountNumber(), payments.getAccountName());
  }

  private void fullFillPayment(String paymentRefId,
      String accountNumber, String accountName) throws Exception {
    Optional<Payments> optionalPayments = paymentsRepository.findByNetworkPaymentRefIdAndTypeCdAndStatus(paymentRefId,
        PaymentCdType.INBOUND, PaymentStatus.COMPLETE.getValue());
    if (optionalPayments.isEmpty()) {
      log.warn("Failed to found inbound payment with paymentRefId {}", paymentRefId);
      throw new KafkaListenerNotRetryableException("Failed to found payment with paymentRefId=" + paymentRefId);
    }
    Payments payments = optionalPayments.get();
    ExternalFiAccountInfoJsonDto externalFiAccountInfoJsonDto = new ExternalFiAccountInfoJsonDto();

    FiAccountDto debtor = new FiAccountDto();
    debtor.setFiId(ptcPaymentKafkaListenerProperty.getFiId());
    debtor.setAccountName(accountName);
    debtor.setAccountNumber(accountNumber);
    externalFiAccountInfoJsonDto.setDebtor(debtor);
    payments.setExternalFiAccountInfoJson(jsonConverter.toJsonString(externalFiAccountInfoJsonDto));
    paymentsRepository.save(payments);
    log.info("payment record was updated {}", paymentRefId);
  }

}
