package com.peoples.banking.api.ptc.payment.queue.listener.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class PtcPaymentKafkaListenerProperty {

  @Value("${spring.kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;
  @Value("${kafka.ptcPayment.topic}")
  private String kafkaPtcPaymentTopic;
  @Value("${kafka.ssl.enabled}")
  private boolean sslEnabled;
  @Value("${kafka.ssl.truststore.location}")
  private String sslTruststoreLocation;
  @Value("${kafka.ssl.truststore.password}")
  private String sslTruststorePassword;
  @Value("${kafka.ssl.keystore.location}")
  private String sslKeystoreLocation;
  @Value("${kafka.ssl.keystore.password}")
  private String sslKeystorePassword;
  @Value("${kafka.ssl.key.password}")
  private String sslKeyPassword;
  @Value("${sys.pn.interac.peoples.fiid}")
  private String fiId;
}
