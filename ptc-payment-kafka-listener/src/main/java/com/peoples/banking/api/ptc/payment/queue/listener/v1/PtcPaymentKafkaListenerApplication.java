package com.peoples.banking.api.ptc.payment.queue.listener.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {
        "com.peoples.banking.api.ptc.payment.queue.listener",
        "com.peoples.banking.persistence.payment",
        "com.peoples.banking.util"
})
public class PtcPaymentKafkaListenerApplication {

  public static void main(String[] args) {
    SpringApplication.run(PtcPaymentKafkaListenerApplication.class, args);
  }
}