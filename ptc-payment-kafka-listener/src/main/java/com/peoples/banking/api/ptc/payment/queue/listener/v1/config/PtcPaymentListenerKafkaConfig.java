package com.peoples.banking.api.ptc.payment.queue.listener.v1.config;

import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.dto.PtcPaymentKafkaDto;
import com.peoples.banking.api.ptc.payment.queue.listener.v1.service.PtcPaymentKafkaListenerService;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

@Configuration
@EnableKafka
@Log4j2
public class PtcPaymentListenerKafkaConfig {

    private static final String SSL = "SSL";

    @Autowired
    private PtcPaymentKafkaListenerProperty ptcPaymentKafkaListenerProperty;
    @Autowired
    private PtcPaymentKafkaListenerService ptcPaymentKafkaListenerService;

    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = buildConfigProps();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, ptcPaymentKafkaListenerProperty.getKafkaBootstrapAddress());
        return new DefaultKafkaConsumerFactory<>(props, new StringDeserializer(),  new StringDeserializer());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }

    @KafkaListener(topics = "${kafka.ptcPayment.topic}", groupId = "${kafka.ptcPayment.group.id}")
    public void processKafkaPtcPayment(String message) throws Exception {
        ThreadContext.put(APICommonUtilConstant.LOGGING_FIELD_GUIID, IdGeneratorUtil.generateRequestId());
        PtcPaymentKafkaDto ptcPaymentKafkaDto = JsonUtil.toObject(message, PtcPaymentKafkaDto.class);
        ptcPaymentKafkaListenerService.consumeMessage(ptcPaymentKafkaDto);
        ThreadContext.remove(APICommonUtilConstant.LOGGING_FIELD_GUIID);
    }

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        //Admin Kafka is registered by Spring Boot automatically as we are creating a new producer configuration, so
        //we need to set Producer's bootstrap urls with Admin's Kafka's boostrap
        Map<String, Object> configProps = buildConfigProps();
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        //do not pass headers in order to be able to deserialize the message successfully into different class at the consumer side
        configProps.put(JsonSerializer.ADD_TYPE_INFO_HEADERS, false);
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    private Map<String, Object> buildConfigProps() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, ptcPaymentKafkaListenerProperty.getKafkaBootstrapAddress());
        if (ptcPaymentKafkaListenerProperty.isSslEnabled()) {
            configProps.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, SSL);
            configProps.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, ptcPaymentKafkaListenerProperty.getSslTruststoreLocation());
            configProps.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, ptcPaymentKafkaListenerProperty.getSslTruststorePassword());
            configProps.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, ptcPaymentKafkaListenerProperty.getSslKeyPassword());
            configProps.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, ptcPaymentKafkaListenerProperty.getSslKeystoreLocation());
            configProps.put(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG, ptcPaymentKafkaListenerProperty.getSslKeystorePassword());
        }
        return configProps;
    }

    @Bean
    public KafkaAdmin kafkaAdminClient() {
        return new KafkaAdmin(buildConfigProps());
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
