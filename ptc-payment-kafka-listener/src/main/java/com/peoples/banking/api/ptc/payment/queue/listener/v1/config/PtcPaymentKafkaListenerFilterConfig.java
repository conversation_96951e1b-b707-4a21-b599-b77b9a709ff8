package com.peoples.banking.api.ptc.payment.queue.listener.v1.config;


import static com.peoples.banking.api.ptc.payment.queue.listener.v1.config.PtcPaymentConstant.SYSTEM_NAME;

import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

@Configuration
public class PtcPaymentKafkaListenerFilterConfig {
  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> ptcPaymentKafkaListenerLoggingFilter() {
    LoggingFilter ptcPaymentLoggingFilter = new LoggingFilter();
    ptcPaymentLoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    ptcPaymentLoggingFilter.setSystemName(SYSTEM_NAME);
    registrationBean.setFilter(ptcPaymentLoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);
    return registrationBean;
  }

  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
