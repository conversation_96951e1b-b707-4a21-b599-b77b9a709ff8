package com.peoples.banking.persistence.calendar.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="holidays"
    ,schema="configuration"
)
public class Holidays implements Serializable {

     @Id
     @Column(name="id", unique=true, nullable=false)
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     private short id;

     @Column(name="description", nullable=false)
     private String description;

     @Column(name="holiday_date", nullable=false, length=29)
     private LocalDate holidayDate;

     @Column(name="created_on", nullable=false, length=29)
     private LocalDateTime createdOn;

     @Column(name="updated_on", length=29)
     private LocalDateTime updatedOn;

}


