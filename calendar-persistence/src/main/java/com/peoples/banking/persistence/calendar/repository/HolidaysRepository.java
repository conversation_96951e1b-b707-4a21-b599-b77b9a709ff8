package com.peoples.banking.persistence.calendar.repository;

import com.peoples.banking.persistence.calendar.entity.Holidays;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface HolidaysRepository extends JpaRepository<Holidays, Integer> {

    @Override
    List<Holidays> findAll();

    Optional<Holidays> findHolidayByHolidayDate(LocalDate localDate);

}
