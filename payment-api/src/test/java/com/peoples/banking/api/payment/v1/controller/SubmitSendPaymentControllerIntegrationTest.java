package com.peoples.banking.api.payment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.payment.v1.PaymentTestUtil;
import com.peoples.banking.api.payment.v1.config.PaymentConstant;
import com.peoples.banking.api.payment.v1.dto.ContactJsonDto;
import com.peoples.banking.api.payment.v1.dto.FraudResultJsonDto;
import com.peoples.banking.api.payment.v1.dto.OptionJsonDto;
import com.peoples.banking.api.payment.v1.dto.RemittanceJsonDto;
import com.peoples.banking.api.payment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.payment.model.*;
import com.peoples.banking.domain.serviceaccount.model.RetrieveServiceAccountConfigurationsResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.payment.PaymentAdapter;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalPaymentTransactionStatus1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentType;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentResponse;
import com.peoples.banking.persistence.payment.entity.DeviceInfo;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.DeviceInfoRepository;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.type.TransactionType;
import java.net.URL;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class SubmitSendPaymentControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @Autowired
  private PaymentsRepository paymentsRepository;

  @Autowired
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @Autowired
  private DeviceInfoRepository deviceInfoRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private PaymentAdapter paymentAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private SystemAdapter systemAdapter;

  private SubmitSendPaymentRequest request;
  private ServiceAccountResponse serviceAccountResponse;
  private Payments payment;
  private List<PaymentStatusHistory> submitPaymentStatusHistoryList;
  private DeviceInfo deviceInfo;
  private DateTimeFormatter dateTimeFormatter;

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port);
    serviceAccountResponse = PaymentTestUtil
        .createServiceAccountResponse(PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, PaymentTestUtil.SERVICE_ACCOUNT_REF_ID, false);
    dateTimeFormatter = DateTimeFormatter.ofPattern(PaymentTestUtil.DATE_FORMAT);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    // clear test data in DB
    if (submitPaymentStatusHistoryList != null) {
      paymentStatusHistoryRepository.deleteInBatch(submitPaymentStatusHistoryList);
    }

    if (deviceInfo != null) {
      deviceInfoRepository.delete(deviceInfo);
    }

    if (payment != null) {
      paymentsRepository.delete(payment);
    }

    request = null;
    serviceAccountResponse = null;
    payment = null;
    submitPaymentStatusHistoryList = null;
    deviceInfo = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN2);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  @Test
  public void submitSendPayment_success() throws Exception {
    payment = PaymentTestUtil.createPayments();
    String initPaymentEndToEndId = payment.getEndToEndId();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
        .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
            payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
            ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitSendPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, SubmitSendPaymentResponse.class);

    // verify response status
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(responseEntity.getBody().getPaymentRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    deviceInfo = deviceInfoOptional.get();

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    //verify payment info in DB
    SubmitSendPaymentResponse response = responseEntity.getBody();
    assertEquals(PaymentStatus.AVAILABLE.toString(), payment.getStatus());
    assertEquals(serviceAccountResponse.getRefId(), payment.getServiceAccountRefId());
    //Submit Payment will not overwrite end to end Id in DB
    assertEquals(initPaymentEndToEndId, payment.getEndToEndId());
    assertEquals(request.getCustomerId(), payment.getCustomerRefId());
    assertEquals(responseEntity.getBody().getPaymentRefId(), payment.getExternalRefId());
    assertEquals(TransactionType.OUTBOUND.name(), payment.getTypeCd().name());
    //verify response and DB record are matched
    ContactJsonDto contactJsonDto = JsonUtil.toObject(payment.getContactJson(), ContactJsonDto.class);
    assertEquals(contactJsonDto.getName(), response.getContact().getName());
    assertEquals(contactJsonDto.getContactMethodDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(contactJsonDto.getContactMethodDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(contactJsonDto.getAliasReferenceId(), response.getContact().getAccount().getAliasReferenceId());
    assertEquals(payment.getAmount(), response.getAmount());
    assertEquals(payment.getAccountName(), response.getAccountName());
    assertEquals(payment.getAccountNumber(), response.getAccountNumber());
    assertEquals(payment.getAccountTransactionRefId(), response.getAccountTransactionRefId());
    OptionJsonDto optionJsonDto = JsonUtil.toObject(payment.getOptionJson(), OptionJsonDto.class);
    assertEquals(optionJsonDto.getExpiredAfterDays(), response.getOptions().getExpireAfterDays());
    RemittanceJsonDto remittanceJsonDto = JsonUtil.toObject(payment.getRemittanceJson(), RemittanceJsonDto.class);
    assertEquals(remittanceJsonDto.getUnstructured().getMemo(), response.getMemo());
    assertEquals(NetworkChannelType.EMT.getNetworkRefId(), payment.getNetworkRefId());
    assertEquals(
        submitPaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
        payment.getNetworkPaymentRefId());
    assertEquals(PaymentTestUtil.NETWORK_TRANSACTION_ID, payment.getNetworkTransactionId());
    assertEquals(PaymentType.REGULAR_PAYMENT.getValue(), payment.getNetworkPaymentType());
    assertEquals(retrieveCustomerProductResponse.getEnrollmentRefId(), payment.getNetworkEnrollmentId());
    FraudResultJsonDto fraudResultJsonDto = JsonUtil.toObject(payment.getNetworkFraudResultJson(), FraudResultJsonDto.class);
    assertEquals(0, fraudResultJsonDto.getScore());
    assertEquals("ALLOW", fraudResultJsonDto.getAction());
    assertEquals(dateTimeFormatter.format(submitPaymentResponse.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime()),
        dateTimeFormatter.format(DateUtil.toOffsetDateTime(payment.getNetworkCreatedDate())));
    assertEquals(PaymentStatus.AVAILABLE.getValue(), payment.getStatus());
    assertEquals(dateTimeFormatter.format(payment.getExpiryDate()),
        dateTimeFormatter.format(responseEntity.getBody().getExpiryDate()));

    //verify device info in DB
    assertEquals("**********", deviceInfo.getIpAddress());
    assertEquals("UIDdeviceInfoFingerprint", deviceInfo.getFingerprint());
    assertEquals(AuthenticationType.PASSWORD.getValue(), deviceInfo.getAuthenticationMethod());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.AVAILABLE.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  @Test
  public void submitSendPayment_requestFulfillment_success() throws Exception {
    payment = PaymentTestUtil.createPayments();
    String initPaymentEndToEndId = payment.getEndToEndId();
    payment.setNetworkPaymentType(NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name());
    payment.setNetworkInstructionId(RandomStringUtils.randomAlphabetic(20));

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
            .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
                    isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
            .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
                    payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
                    ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
            .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitSendPaymentResponse> responseEntity = template
            .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, SubmitSendPaymentResponse.class);

    // verify response status
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
            .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(responseEntity.getBody().getPaymentRefId(),
                    NetworkPaymentType.FULFILL_REQUEST_FOR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    //verify payment info in DB
    SubmitSendPaymentResponse response = responseEntity.getBody();
    assertEquals(PaymentStatus.AVAILABLE.toString(), payment.getStatus());
    assertEquals(serviceAccountResponse.getRefId(), payment.getServiceAccountRefId());
    //Submit Payment will not overwrite end to end Id in DB
    assertEquals(initPaymentEndToEndId, payment.getEndToEndId());
    assertEquals(request.getCustomerId(), payment.getCustomerRefId());
    assertEquals(responseEntity.getBody().getPaymentRefId(), payment.getExternalRefId());
    assertEquals(TransactionType.OUTBOUND.name(), payment.getTypeCd().name());
    //verify response and DB record are matched
    ContactJsonDto contactJsonDto = JsonUtil.toObject(payment.getContactJson(), ContactJsonDto.class);
    assertEquals(contactJsonDto.getName(), response.getContact().getName());
    assertEquals(contactJsonDto.getContactMethodDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(contactJsonDto.getContactMethodDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(contactJsonDto.getAliasReferenceId(), response.getContact().getAccount().getAliasReferenceId());
    assertEquals(payment.getAmount(), response.getAmount());
    assertEquals(payment.getAccountName(), response.getAccountName());
    assertEquals(payment.getAccountNumber(), response.getAccountNumber());
    assertEquals(payment.getAccountTransactionRefId(), response.getAccountTransactionRefId());
    OptionJsonDto optionJsonDto = JsonUtil.toObject(payment.getOptionJson(), OptionJsonDto.class);
    assertEquals(optionJsonDto.getExpiredAfterDays(), response.getOptions().getExpireAfterDays());
    RemittanceJsonDto remittanceJsonDto = JsonUtil.toObject(payment.getRemittanceJson(), RemittanceJsonDto.class);
    assertEquals(remittanceJsonDto.getUnstructured().getMemo(), response.getMemo());
    assertEquals(NetworkChannelType.EMT.getNetworkRefId(), payment.getNetworkRefId());
    assertEquals(
            submitPaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
            payment.getNetworkPaymentRefId());
    assertEquals(PaymentTestUtil.NETWORK_TRANSACTION_ID, payment.getNetworkTransactionId());
    assertEquals(SendPaymentType.REQUEST_FULFILLMENT, responseEntity.getBody().getPaymentType());
    assertEquals(retrieveCustomerProductResponse.getEnrollmentRefId(), payment.getNetworkEnrollmentId());
    FraudResultJsonDto fraudResultJsonDto = JsonUtil.toObject(payment.getNetworkFraudResultJson(), FraudResultJsonDto.class);
    assertEquals(0, fraudResultJsonDto.getScore());
    assertEquals("ALLOW", fraudResultJsonDto.getAction());
    assertEquals(dateTimeFormatter.format(submitPaymentResponse.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime()),
            dateTimeFormatter.format(DateUtil.toOffsetDateTime(payment.getNetworkCreatedDate())));
    assertEquals(PaymentStatus.AVAILABLE.getValue(), payment.getStatus());
    assertEquals(dateTimeFormatter.format(payment.getExpiryDate()),
            dateTimeFormatter.format(responseEntity.getBody().getExpiryDate()));


    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.AVAILABLE.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  @Test
  public void submitSendPayment_withGL_success() throws Exception {
    payment = PaymentTestUtil.createPayments();
    String initPaymentEndToEndId = payment.getEndToEndId();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    RetrieveServiceAccountConfigurationsResponse retrieveServiceAccountConfigurationsResponse = PaymentTestUtil
        .getRetrieveServiceAccountConfigurationsResponse();

    doReturn(retrieveServiceAccountConfigurationsResponse).when(serviceAccountAdapter)
        .retrieveServiceAccountConfigurations(isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
        .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
            payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
            ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitSendPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, SubmitSendPaymentResponse.class);

    // verify response status
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(responseEntity.getBody().getPaymentRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    deviceInfo = deviceInfoOptional.get();

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    //verify payment info in DB
    SubmitSendPaymentResponse response = responseEntity.getBody();
    assertEquals(PaymentStatus.AVAILABLE.toString(), payment.getStatus());
    assertEquals(serviceAccountResponse.getRefId(), payment.getServiceAccountRefId());
    //Submit Payment will not overwrite end to end Id in DB
    assertEquals(initPaymentEndToEndId, payment.getEndToEndId());
    assertEquals(request.getCustomerId(), payment.getCustomerRefId());
    assertEquals(responseEntity.getBody().getPaymentRefId(), payment.getExternalRefId());
    assertEquals(TransactionType.OUTBOUND.name(), payment.getTypeCd().name());
    //verify response and DB record are matched
    ContactJsonDto contactJsonDto = JsonUtil.toObject(payment.getContactJson(), ContactJsonDto.class);
    assertEquals(contactJsonDto.getName(), response.getContact().getName());
    assertEquals(contactJsonDto.getContactMethodDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(contactJsonDto.getContactMethodDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(contactJsonDto.getAliasReferenceId(), response.getContact().getAccount().getAliasReferenceId());
    assertEquals(payment.getAmount(), response.getAmount());
    assertEquals(payment.getAccountName(), response.getAccountName());
    assertEquals(payment.getAccountNumber(), response.getAccountNumber());
    assertEquals(payment.getAccountTransactionRefId(), response.getAccountTransactionRefId());
    OptionJsonDto optionJsonDto = JsonUtil.toObject(payment.getOptionJson(), OptionJsonDto.class);
    assertEquals(optionJsonDto.getExpiredAfterDays(), response.getOptions().getExpireAfterDays());
    RemittanceJsonDto remittanceJsonDto = JsonUtil.toObject(payment.getRemittanceJson(), RemittanceJsonDto.class);
    assertEquals(remittanceJsonDto.getUnstructured().getMemo(), response.getMemo());
    assertEquals(NetworkChannelType.EMT.getNetworkRefId(), payment.getNetworkRefId());
    assertEquals(
        submitPaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
        payment.getNetworkPaymentRefId());
    assertEquals(PaymentTestUtil.NETWORK_TRANSACTION_ID, payment.getNetworkTransactionId());
    assertEquals(PaymentType.REGULAR_PAYMENT.getValue(), payment.getNetworkPaymentType());
    assertEquals(retrieveCustomerProductResponse.getEnrollmentRefId(), payment.getNetworkEnrollmentId());
    FraudResultJsonDto fraudResultJsonDto = JsonUtil.toObject(payment.getNetworkFraudResultJson(), FraudResultJsonDto.class);
    assertEquals(0, fraudResultJsonDto.getScore());
    assertEquals("ALLOW", fraudResultJsonDto.getAction());
    assertEquals(dateTimeFormatter.format(submitPaymentResponse.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime()),
        dateTimeFormatter.format(DateUtil.toOffsetDateTime(payment.getNetworkCreatedDate())));
    assertEquals(PaymentStatus.AVAILABLE.getValue(), payment.getStatus());
    assertEquals(dateTimeFormatter.format(payment.getExpiryDate()),
        dateTimeFormatter.format(responseEntity.getBody().getExpiryDate()));

    //verify device info in DB
    assertEquals("**********", deviceInfo.getIpAddress());
    assertEquals("UIDdeviceInfoFingerprint", deviceInfo.getFingerprint());
    assertEquals(AuthenticationType.PASSWORD.getValue(), deviceInfo.getAuthenticationMethod());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.AVAILABLE.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  @Test
  public void submitSendPayment_serviceAccountDisabled_success() throws Exception {
    payment = PaymentTestUtil.createPayments();
    String initPaymentEndToEndId = payment.getEndToEndId();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    //Disable service account
    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
        .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
            payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
            ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitSendPaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, SubmitSendPaymentResponse.class);

    // verify response status
    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(responseEntity.getBody().getPaymentRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    deviceInfo = deviceInfoOptional.get();

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    //verify payment info in DB
    SubmitSendPaymentResponse response = responseEntity.getBody();
    assertEquals(PaymentStatus.AVAILABLE.toString(), payment.getStatus());
    assertEquals(serviceAccountResponse.getRefId(), payment.getServiceAccountRefId());
    //Submit Payment will not overwrite end to end Id in DB
    assertEquals(initPaymentEndToEndId, payment.getEndToEndId());
    assertEquals(request.getCustomerId(), payment.getCustomerRefId());
    assertEquals(responseEntity.getBody().getPaymentRefId(), payment.getExternalRefId());
    assertEquals(TransactionType.OUTBOUND.name(), payment.getTypeCd().name());
    //verify response and DB record are matched
    ContactJsonDto contactJsonDto = JsonUtil.toObject(payment.getContactJson(), ContactJsonDto.class);
    assertEquals(contactJsonDto.getName(), response.getContact().getName());
    assertEquals(contactJsonDto.getContactMethodDto().getEmail(), response.getContact().getEmailAddress());
    assertEquals(contactJsonDto.getContactMethodDto().getMobile(), response.getContact().getMobileNumber());
    assertEquals(contactJsonDto.getAliasReferenceId(), response.getContact().getAccount().getAliasReferenceId());
    assertEquals(payment.getAmount(), response.getAmount());
    assertEquals(payment.getAccountName(), response.getAccountName());
    assertEquals(payment.getAccountNumber(), response.getAccountNumber());
    assertEquals(payment.getAccountTransactionRefId(), response.getAccountTransactionRefId());
    OptionJsonDto optionJsonDto = JsonUtil.toObject(payment.getOptionJson(), OptionJsonDto.class);
    assertEquals(optionJsonDto.getExpiredAfterDays(), response.getOptions().getExpireAfterDays());
    RemittanceJsonDto remittanceJsonDto = JsonUtil.toObject(payment.getRemittanceJson(), RemittanceJsonDto.class);
    assertEquals(remittanceJsonDto.getUnstructured().getMemo(), response.getMemo());
    assertEquals(NetworkChannelType.EMT.getNetworkRefId(), payment.getNetworkRefId());
    assertEquals(
        submitPaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
        payment.getNetworkPaymentRefId());
    assertEquals(PaymentTestUtil.NETWORK_TRANSACTION_ID, payment.getNetworkTransactionId());
    assertEquals(PaymentType.REGULAR_PAYMENT.getValue(), payment.getNetworkPaymentType());
    assertEquals(retrieveCustomerProductResponse.getEnrollmentRefId(), payment.getNetworkEnrollmentId());
    FraudResultJsonDto fraudResultJsonDto = JsonUtil.toObject(payment.getNetworkFraudResultJson(), FraudResultJsonDto.class);
    assertEquals(0, fraudResultJsonDto.getScore());
    assertEquals("ALLOW", fraudResultJsonDto.getAction());
    assertEquals(dateTimeFormatter.format(submitPaymentResponse.getFiToFiPaymentStatusReport().getGroupHeader().getCreationDatetime()),
        dateTimeFormatter.format(DateUtil.toOffsetDateTime(payment.getNetworkCreatedDate())));
    assertEquals(PaymentStatus.AVAILABLE.getValue(), payment.getStatus());
    assertEquals(dateTimeFormatter.format(payment.getExpiryDate()),
        dateTimeFormatter.format(responseEntity.getBody().getExpiryDate()));

    //verify device info in DB
    assertEquals("**********", deviceInfo.getIpAddress());
    assertEquals("UIDdeviceInfoFingerprint", deviceInfo.getFingerprint());
    assertEquals(AuthenticationType.PASSWORD.getValue(), deviceInfo.getAuthenticationMethod());

    //verify payment status history
    assertFalse(submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.AVAILABLE.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  @Test
  public void submitPayment_paymentNotExist_fail() throws Exception {
    payment = PaymentTestUtil.createPayments();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, responseEntity.getStatusCode());

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, responseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void submitPayment_customerId_notMatch_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest("test1234");

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
        .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
            payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
            ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(PaymentUtil.INVALID_CUSTOMER, responseEntity.getBody().getError().get(0).getCode());
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "QUEUED",
      "CANCELLED",
      "FAILED",
      "FAILED_PENDING",
      "EXPIRED",
      "DECLINED",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "COMPLETE"
  })
  public void submitPayment_invalid_paymentStatus_fail(String paymentStatus) throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(paymentStatus);
    paymentsRepository.save(payment);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    SubmitPaymentResponse submitPaymentResponse = PaymentTestUtil
        .createSubmitPaymentResponse(com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus.INITIATED,
            payment.getNetworkPaymentRefId(), payment.getNetworkTransactionId(),
            ExternalPaymentTransactionStatus1Code.ACTC);

    doReturn(submitPaymentResponse).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

    assertEquals(PaymentUtil.INVALID_PAYMENT_STATUS, responseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test submitPayment interac throw exception 301	Debtor (customer) does not exist
   */
  @Test
  public void submitPayment_interac_customerNotExist_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("301");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 305 Clearing system reference cannot be found in the system
   */
  @Test
  public void submitPayment_interac_clearingSystemReferenceNotFound_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("305");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 373	clrSysBgnTxId cannot be found in the system
   */
  @Test
  public void submitPayment_interac_clrSysBgnTxIdNotFound_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("373");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.NOT_FOUND, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }


  /**
   * The test submitPayment interac throw exception 398	Duplicate participant reference number
   */
  @Test
  public void submitPayment_interac_duplicate_participant_reference_number_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("398");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.DUPLICATE_REQUEST, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 372	Participant transaction date is too early or too late
   */
  @Test
  public void submitPayment_interac_participant_transaction_date_wrong_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("372");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 205	Payment return transaction already submitted (committed)
   */
  @Test
  public void submitPayment_payment_already_submitted_success() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("205");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    assertEquals(APICommonUtilConstant.ALREADY_PROCESSED,
        responseEntity.getHeaders().get(APICommonUtilConstant.HEADER_WARNING_CODE).get(0));

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.AVAILABLE.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.AVAILABLE.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 206	Payment return transaction already reversed (rolled back)
   */
  @Test
  public void submitPayment_participantTransactionDateWrong_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("206");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment interac throw exception 375	Original Transaction ID/Original Instruction ID/Original EndToEndId/Original Clearing System
   * Reference do not match the value(s) in corresponding Initiate Payment Return request
   */
  @Test
  public void submitPayment_EndToEndId_NotMatch_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("375");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(!submitPaymentStatusHistoryList.isEmpty());
    assertEquals(1, submitPaymentStatusHistoryList.size());
    assertEquals(PaymentStatus.FAILED_PENDING.getValue(), submitPaymentStatusHistoryList.get(0).getStatus());
    assertEquals(PaymentStatus.ACCEPTED.getValue(), submitPaymentStatusHistoryList.get(0).getPreviousStatus());
  }

  /**
   * The test submitPayment when serviceAccountAdapter throws IllegalArgumentException
   */
  @Test
  public void submitPayment_illegalArgumentException_fail() throws Exception {
    //create initiate Payment in DB
    payment = PaymentTestUtil.createPayments();
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);
    deviceInfoRepository.save(deviceInfo);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("375");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    doThrow(IllegalArgumentException.class).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.ACCEPTED.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(submitPaymentStatusHistoryList.isEmpty());
  }

  /**
   * The test submitPayment when paymentAdapter throws 28	Disabled Creditor/Recipient
   */
  @Test
  public void submitPayment_RECIPIENT_DISABLED_fail() throws Exception {
    payment = PaymentTestUtil.createPayments();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("28");
    responseException.setHttpStatusCode(400);
    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.RECIPIENT_DISABLED, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(submitPaymentStatusHistoryList.size() > 0);
  }

  /**
   * The test submitPayment when paymentAdapter throws 32 Invalid transaction authorization token
   */
  @Test
  public void submitPayment_invalid_transaction_authorization_token_fail() throws Exception {
    payment = PaymentTestUtil.createPayments();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("32");
    responseException.setHttpStatusCode(400);
    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.RECEIVING_FI_UNEXPECTED_ERROR, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    Optional<DeviceInfo> deviceInfoOptional = deviceInfoRepository.findByPayments(payment);
    assertTrue(deviceInfoOptional.isPresent());
    DeviceInfo dbDeviceInfo = deviceInfoOptional.get();
    //verify device info in DB
    assertEquals(deviceInfo.getIpAddress(), dbDeviceInfo.getIpAddress());
    assertEquals(deviceInfo.getFingerprint(), dbDeviceInfo.getFingerprint());
    assertEquals(deviceInfo.getAuthenticationMethod(), dbDeviceInfo.getAuthenticationMethod());
    assertEquals(dateTimeFormatter.format(deviceInfo.getCreatedOn()), dateTimeFormatter.format(dbDeviceInfo.getCreatedOn()));

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED_PENDING.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(submitPaymentStatusHistoryList.size() > 0);
  }

  /**
   * The test submitPayment when Interac throws Error 446
   */
  @Test
  public void submitPayment_PAYMENT_ROLLED_BACK_fail() throws Exception {
    payment = PaymentTestUtil.createPayments();
    deviceInfo = PaymentTestUtil.createDeviceInfo(payment);

    //create initiate Payment in DB
    payment.setStatus(PaymentStatus.ACCEPTED.getValue());
    paymentsRepository.save(payment);
    deviceInfoRepository.save(deviceInfo);

    RetrieveCustomerProductResponse retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    retrieveCustomerProductResponse.setEnrollmentRefId(payment.getNetworkEnrollmentId());

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    request = PaymentTestUtil.createSubmitSendPaymentRequest(payment.getCustomerRefId());

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("446");
    responseException.setHttpStatusCode(400);
    doThrow(responseException).when(paymentAdapter)
        .submitPayment(isA(String.class), isA(String.class), isA(SubmitPaymentRequest.class), isA(String.class));

    String finalUrl = base + PaymentConstant.ROOT_SERVICE_URL + "/" + payment.getExternalRefId();

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitSendPayment = new HttpEntity<>(request, headers);

    ResponseEntity<ErrorResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitSendPayment, ErrorResponse.class);

    assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
    assertEquals(PaymentUtil.PAYMENT_ROLLED_BACK, responseEntity.getBody().getError().get(0).getCode());

    //Get Payment record
    Optional<Payments> paymentsOptional = paymentsRepository
        .findByExternalRefIdAndNetworkPaymentTypeAndTypeCd(payment.getExternalRefId(),
            NetworkPaymentType.REGULAR_PAYMENT.name(), PaymentCdType.OUTBOUND);
    assertTrue(paymentsOptional.isPresent());
    payment = paymentsOptional.get();

    assertEquals("{\"reasonCode\": \"PAYMENT_ROLLED_BACK\"}", payment.getStatusReasonJson());

    submitPaymentStatusHistoryList = paymentStatusHistoryRepository.findByPaymentsOrderById(payment);

    assertEquals(PaymentStatus.FAILED.toString(), payment.getStatus());

    //verify payment status history
    assertTrue(submitPaymentStatusHistoryList.size() > 0);
  }
}
