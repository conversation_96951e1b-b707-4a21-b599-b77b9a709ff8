package com.peoples.banking.api.payment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.payment.v1.PaymentTestUtil;
import com.peoples.banking.api.payment.v1.config.PaymentConstant;
import com.peoples.banking.api.payment.v1.util.PaymentUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.payment.model.CancelPaymentRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.payment.PaymentAdapter;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentsCancelPostRequestModel;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class CancelPaymentControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private PaymentAdapter paymentAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private SystemAdapter systemAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private CancelPaymentRequest request;

  private ServiceAccountResponse serviceAccountResponse;

  static {
    System.setProperty("SPRING_CONFIG_LOCATION", "classpath:/");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port);
    request = PaymentTestUtil.createCancelPaymentRequest();
    serviceAccountResponse = PaymentTestUtil
        .createServiceAccountResponse(PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, PaymentTestUtil.SERVICE_ACCOUNT_REF_ID, false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test cancelPayment happy path
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "A1",
      "ABC",
      "ABC 123",
      "ABC- Hello",
      "ABC-   Hello"
  })
  public void cancelPayment_success(String reasonDescription) throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    request.setReasonDescription(reasonDescription);
    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());
  }

  @Test
  public void cancelPayment_serviceAccountSuspended_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());
  }

  /**
   * The test cancelPayment happy path when service account disabled
   */
  @Test
  public void cancelPayment_serviceAccountDisabled_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    //Set service account disabled
    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());
  }

  /**
   * The test cancelPayment happy path
   */
  @ParameterizedTest
  @ValueSource(strings = {
      " ABC",
      "   ABC",
      "ABC ",
      "ABC   ",
      "    ABC    "
  })
  public void cancelPayment_bad_reason_fail(String reasonDescription) throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));
    request.setReasonDescription(reasonDescription);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_INPUT, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment missing customer ID to trigger error
   */
  @Test
  public void cancelPayment_missingCustomerID_failed() throws Exception {
    //Empty customerID to trigger error
    request.setCustomerId(null);
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment missing reason code to trigger error
   */
  @Test
  public void cancelPayment_missingReasonCode_failed() throws Exception {
    //Empty customerID to trigger error
    request.setReasonCode(null);
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.MISSING_FIELD, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment payment not exist to trigger error
   */
  @Test
  public void cancelPayment_paymentNotExist_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

  }

  /**
   * The test cancelPayment missing reason code to trigger error
   */
  @Test
  public void cancelPayment_customerId_notMatch_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_CUSTOMER, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment payment status is not available to trigger error
   */
  @Test
  public void cancelPayment_paymentStatus_failed() throws Exception {
    //Empty customerID to trigger error
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("FAILED");
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_PAYMENT_STATUS, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception customer did not exist to trigger error
   */
  @Test
  public void cancelPayment_interac_customerNotExist_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    payments.setStatus("AVAILABLE");
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("301");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

  }

  /**
   * The test cancelPayment interac throw exception customer did not exist to trigger error
   */
  @Test
  public void cancelPayment_interac_transferNotFound_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("305");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

    assertEquals(PaymentUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception payment reference num did not exist to trigger error
   */
  @Test
  public void cancelPayment_interac_senderIsDisabled_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("410");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.CUSTOMER_DISABLED_AT_NETWORK, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception Transfer Reference Number is on block list trigger error
   */
  @Test
  public void cancelPayment_interac_transferReferenceNumberIsOnBlockList_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("412");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.TRANSFER_BLOCKED, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception 320	Operation cannot be performed due to current transfer status
   */
  @Test
  public void cancelPayment_interac_invalidTransferStatus_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("320");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.INVALID_STATUS, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception 374	Previous transaction between this Sender and Recipient has not been completed
   */
  @Test
  public void cancelPayment_previousTransactionStatusNotCompleted_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("374");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.PREVIOUS_TRANSACTION_INCOMPLETE, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception 372	Participant Transaction Date is too early or too late
   */
  @Test
  public void cancelPayment_participantTransactionDateWrong_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("372");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception 398	Duplicate Participant Transaction Reference Number
   */
  @Test
  public void cancelPayment_duplicateParticipantTransactionReferenceNumber_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("398");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(PaymentUtil.DUPLICATE_REQUEST, errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test cancelPayment interac throw exception 215	This payment transaction has already been completed, but we will treat it as success
   */
  @Test
  public void cancelPayment_paymentCompleted_interac_already_complete_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("215");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());

    assertEquals(APICommonUtilConstant.ALREADY_PROCESSED,
        responseEntity.getHeaders().get(APICommonUtilConstant.HEADER_WARNING_CODE).get(0));

  }

  /**
   * The test cancelPayment interac throw exception 215	This payment transaction has already been completed, but we will treat it as success
   */
  @Test
  public void cancelPayment_paymentCompleted_interac_ptc_complete_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("CANCELLED");
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assertEquals(HttpStatus.NO_CONTENT, responseEntity.getStatusCode());

    assertEquals(APICommonUtilConstant.ALREADY_PROCESSED,
        responseEntity.getHeaders().get(APICommonUtilConstant.HEADER_WARNING_CODE).get(0));

  }


  /**
   * The test cancelPayment interac throw IllegalArgumentException when call interac
   */
  @Test
  public void cancelPayment_illegalArgumentException_failed() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doThrow(IllegalArgumentException.class).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);

    assertEquals(PaymentUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());
  }


  @Test
  public void cancelPayment_reason_description_success() throws Exception {
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    request.setReasonDescription("àâäèéêëîïôœùûüÿÀÂÄÈÉÊËÎÏÔŒÙÛÜŸ");

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  /**
   * The test cancelPayment with max 12 customerId happy path
   */
  @Test
  public void cancelPayment_12CustomerId_success() throws Exception {
    request.setCustomerId("cid123456789");
    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCd((isA(String.class)),
            (isA(String.class)), (isA(PaymentCdType.class)));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.CANCEL_PAYMENT_URL.replace("{paymentRefId}", "ABC1231593101413847");

    HttpHeaders headers = getHttpHeader();

    HttpEntity requestCancel = new HttpEntity<>(request, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.POST, requestCancel, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

}
