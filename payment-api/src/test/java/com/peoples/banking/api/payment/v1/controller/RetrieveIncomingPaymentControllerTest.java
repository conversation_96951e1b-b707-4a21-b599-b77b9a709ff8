package com.peoples.banking.api.payment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.payment.v1.PaymentTestUtil;
import com.peoples.banking.api.payment.v1.config.PaymentConstant;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.payment.model.RetrieveIncomingPaymentRequest;
import com.peoples.banking.domain.payment.model.RetrieveIncomingPaymentResponse;
import com.peoples.banking.domain.payment.model.Status;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.payment.PaymentAdapter;
import com.peoples.banking.partner.domain.interac.deposit.model.IncomingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentStatus;
import com.peoples.banking.persistence.payment.entity.DeviceInfo;
import com.peoples.banking.persistence.payment.entity.PaymentAuthentications;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentAuthenticationsRepository;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.config.NetworkErrorProperty;
import java.net.URL;
import java.time.Instant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class RetrieveIncomingPaymentControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentAuthenticationsRepository paymentAuthenticationsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private PaymentAdapter paymentAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private SystemAdapter systemAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private ServiceAccountResponse serviceAccountResponse;

  private RetrieveIncomingPaymentRequest request;

  private RetrieveCustomerProductResponse retrieveCustomerProductResponse;

  static {
    System.setProperty("SPRING_CONFIG_LOCATION", "classpath:/");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port);
    retrieveCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    serviceAccountResponse = PaymentTestUtil
        .createServiceAccountResponse(PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, PaymentTestUtil.SERVICE_ACCOUNT_REF_ID, false);
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    template = null;
    retrieveCustomerProductResponse = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test RetrieveIncomingPayment happy path test sync status with interac side not ACCEPTED, but we have DB record exist
   */
  @Test
  public void retrieveIncomingPayment_has_PTC_DB_success() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class),isA(String.class),isA(String.class));

    IncomingPayment incomingPayment = PaymentTestUtil.createIncomingPayment();
    doReturn(incomingPayment).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    doReturn(new PaymentAuthentications()).when(paymentAuthenticationsRepository).save(isA(PaymentAuthentications.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    ResponseEntity<RetrieveIncomingPaymentResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, RetrieveIncomingPaymentResponse.class);

    assert (responseEntity != null);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
    RetrieveIncomingPaymentResponse entityBody = responseEntity.getBody();
    assertNotNull(entityBody);

  }

  /**
   * The test RetrieveIncomingPayment happy path test sync status with interac side not ACCEPTED, but we have DB record exist
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_success() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    IncomingPayment incomingPayment = PaymentTestUtil.createIncomingPayment();
    doReturn(incomingPayment).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));
    doReturn(new PaymentAuthentications()).when(paymentAuthenticationsRepository).save(isA(PaymentAuthentications.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    ResponseEntity<RetrieveIncomingPaymentResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, RetrieveIncomingPaymentResponse.class);

    assert (responseEntity != null);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
    RetrieveIncomingPaymentResponse entityBody = responseEntity.getBody();
    assertNotNull(entityBody);

  }

  /**
   * The test RetrieveIncomingPayment happy path test sync status with interac side not ACCEPTED, but we have DB record exist
   * GetIncoming Payment - when search a payment record in DB, we should add customer id as part of the searching
   * criteria and the payment status is not FAILED.
   * When NO payment record is found and Interac’s transfer_status = AUTHENTICATION_SUCCESSFUL,
   * set the status = AVAILABLE and generate a new PTC reference id
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "AUTHENTICATION_SUCCESSFUL"
  })
  public void retrieveIncomingPayment_status_AUTHENTICATION_no_PTC_DB_success(String interacTransferStatus) throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    IncomingPayment incomingPayment = PaymentTestUtil.createIncomingPayment();
    incomingPayment.setTransferStatus(PaymentStatus.fromValue(interacTransferStatus));
    doReturn(incomingPayment).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));
    doReturn(new PaymentAuthentications()).when(paymentAuthenticationsRepository).save(isA(PaymentAuthentications.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    ResponseEntity<RetrieveIncomingPaymentResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, RetrieveIncomingPaymentResponse.class);

    assert (responseEntity != null);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
    RetrieveIncomingPaymentResponse entityBody = responseEntity.getBody();
    assertEquals(Status.AVAILABLE,entityBody.getStatus());
    assertNotNull(entityBody);
  }

  /**
   * The test RetrieveIncomingPayment happy path test sync status with interac side not ACCEPTED, but we have DB record exist
   * GetIncoming Payment - when search a payment record in DB, we should add customer id as part of the searching
   * criteria and the payment status is not FAILED.
   * When NO payment record is found and Interac’s transfer_status = AUTHENTICATION_SUCCESSFUL,
   * set the status = AVAILABLE and generate a new PTC reference id
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "AVAILABLE"
  })
  public void retrieveIncomingPayment_status_AVAILABLE_no_PTC_DB_success(String interacTransferStatus) throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    IncomingPayment incomingPayment = PaymentTestUtil.createIncomingPayment();
    incomingPayment.setTransferStatus(PaymentStatus.fromValue(interacTransferStatus));
    doReturn(incomingPayment).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));
    doReturn(new PaymentAuthentications()).when(paymentAuthenticationsRepository).save(isA(PaymentAuthentications.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    ResponseEntity<RetrieveIncomingPaymentResponse> responseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, RetrieveIncomingPaymentResponse.class);

    assert (responseEntity != null);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
    RetrieveIncomingPaymentResponse entityBody = responseEntity.getBody();
    assertEquals(Status.ACCEPTED,entityBody.getStatus());
    assertNotNull(entityBody);
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side is not ACCEPTED, and we have DB record exist
   */
  @ParameterizedTest
  @ValueSource(strings = {
      "CANCELLED",
      "COMPLETE",
      "DECLINED",
      "DEPOSIT_INITIATED",
      "DEPOSIT_PENDING",
      "DEPOSIT_FAILED",
      "EXPIRED",
      "FAILED",
      "FAILED_PENDING",
      "SECURITY_ANSWER_FAILURE",
      "QUEUED"
  })
  public void retrieveIncomingPayment_has_PTC_DB_failed(String paymentStatus) throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus(paymentStatus);
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByNetworkPaymentRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class),isA(String.class),isA(String.class));

    IncomingPayment incomingPayment = PaymentTestUtil.createIncomingPayment();
    doReturn(incomingPayment).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 301	Customer does not exist
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_customer_not_exist_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("301");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    assertEquals(ErrorProperty.UNEXPECTED_ERROR.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test service account disabled
   */
  @Test
  public void retrieveIncomingPayment_serviceAccountDisabled_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("301");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    //Disable service account
    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
  }

   /*
   * The test RetrieveIncomingPayment unHappy path when interac ref id has length 7
   */
  @Test
  public void retrieveIncomingPayment_incorrect_interac_ref_id_failed() {

    Payments payments = PaymentTestUtil.createPayments();

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    request.setInteracRefId("A123456");

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(ErrorProperty.INVALID_FIELD_LENGTH.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 305	Payment does not exist
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_payment_not_exist_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("305");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);
    assertEquals(ErrorProperty.RESOURCE_NOT_FOUND.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 411	Recipient is disabled
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_Recipient_disabled_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("411");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(ErrorProperty.CUSTOMER_DISABLED_AT_NETWORK.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 410	Sender is disabled
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_Sender_disabled_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("410");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(ErrorProperty.CONTACT_DISABLED.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }


  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 321	Sender and Recipient cannot be the same User ID at
   * the same Participant
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_Sender_Recipient_cannot_same_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("321");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(NetworkErrorProperty.SENDER_RECIPIENT_CANNOT_BE_THE_SAME.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 320	Operation cannot be performed due to current payment
   * status
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_Operation_cannot_be_performed_due_to_current_payment_status_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("320");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(ErrorProperty.INVALID_STATUS.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 323	Payment was previously authenticated with a different
   * User ID
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_Payment_was_previously_authenticated_with_different_user_ID_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("323");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingPayment = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingPayment, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(NetworkErrorProperty.AUTHENTICATED_BY_DIFFERENT_CUSTOMER.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

  /**
   * The test RetrieveIncomingPayment unHappy path test sync status with interac side return 461	Product Code, Currency Code combination not
   * supported
   */
  @Test
  public void retrieveIncomingPayment_no_PTC_DB_ProductCode_CurrencyCode_combination_not_supported_failed() throws Exception {

    Payments payments = PaymentTestUtil.createPayments();
    payments.setStatus("ACCEPTED");
    DeviceInfo deviceInfo = PaymentTestUtil.createDeviceInfo(payments);
    payments.setDeviceInfo(deviceInfo);

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("461");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(paymentAdapter)
        .getIncomingPayment(isA(String.class), isA(String.class), isA(String.class));

    doReturn(retrieveCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class),
            isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.RETRIEVE_INCOMING_PAYMENT_URL;

    request = PaymentTestUtil
        .createRetrieveIncomingPaymentRequest(payments.getNetworkPaymentRefId(), retrieveCustomerProductResponse.getEnrollmentRefId());

    HttpHeaders headers = getHttpHeader();

    HttpEntity retrieveIncomingTransfer = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.POST,
        retrieveIncomingTransfer, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    assertEquals(ErrorProperty.UNEXPECTED_ERROR.name(), errResponseEntity.getBody().getError().get(0).getCode());
  }

}
