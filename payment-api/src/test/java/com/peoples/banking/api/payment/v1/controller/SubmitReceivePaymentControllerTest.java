package com.peoples.banking.api.payment.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;

import com.peoples.banking.adapter.pb.customer.CustomerAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.payment.v1.PaymentTestUtil;
import com.peoples.banking.api.payment.v1.config.PaymentConstant;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.payment.model.SubmitReceivePaymentRequest;
import com.peoples.banking.domain.payment.model.SubmitReceivePaymentResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.payment.PaymentAdapter;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentsCancelPostRequestModel;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@DirtiesContext
@EmbeddedKafka(partitions = 1, brokerProperties = { "listeners=PLAINTEXT://localhost:9092", "port=9092" })
public class SubmitReceivePaymentControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private PaymentsRepository paymentsRepository;

  @MockBean
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  private PaymentAdapter paymentAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  @MockBean
  private SystemAdapter systemAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private SubmitReceivePaymentRequest request;

  private ServiceAccountResponse serviceAccountResponse;

  static {
    System.setProperty("SPRING_CONFIG_LOCATION", "classpath:/");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port);
    request = PaymentTestUtil.createSubmitReceivePaymentRequest();
    serviceAccountResponse = PaymentTestUtil
        .createServiceAccountResponse(PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN, PaymentTestUtil.SERVICE_ACCOUNT_REF_ID, false);
    doReturn(true).when(serviceAccountAdapter).validateServiceAccountFeature(isA(String.class), isA(String.class));
    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
  }

  @AfterEach
  public void tearDown() {
    template = null;
    customerAdapter = null;
    paymentsRepository = null;
    base = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, PaymentTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  /**
   * The test submitReceivePayment happy path
   */
  @Test
  public void submitReceivePayment_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.SUBMIT_RECEIVE_PAYMENT_URL.replace("{paymentRefId}", RandomStringUtils.randomAlphanumeric(20));

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitReceivePayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitReceivePaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitReceivePayment, SubmitReceivePaymentResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
  }

  @Test
  public void submitReceivePayment_serviceAccountSuspended_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.SUBMIT_RECEIVE_PAYMENT_URL.replace("{paymentRefId}", RandomStringUtils.randomAlphanumeric(20));

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitReceivePayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitReceivePaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitReceivePayment, SubmitReceivePaymentResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
  }

  @Test
  public void submitReceivePayment_serviceAccountDisabled_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    serviceAccountResponse.setStatus(StatusEnum.DISABLED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.SUBMIT_RECEIVE_PAYMENT_URL.replace("{paymentRefId}", RandomStringUtils.randomAlphanumeric(20));

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitReceivePayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitReceivePaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitReceivePayment, SubmitReceivePaymentResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
  }

  @Test
  public void submitReceivePayment_doubleSubmitGetAlreadyProcessed_success() throws Exception {

    RetrieveCustomerProductResponse getCustomerProductResponse = PaymentTestUtil.createRetrieveCustomerProductResponse();
    doReturn(getCustomerProductResponse).when(customerAdapter)
        .retrieveCustomerProduct(isA(String.class), isA(String.class), isA(String.class), isA(String.class));

    Payments payments = PaymentTestUtil.createPayments();
    //Sync the customer ID for payments and cancelPayment Request
    payments.setCustomerRefId(request.getCustomerId());
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class), isA(String.class), isA(String.class));

    doReturn(true).when(paymentAdapter).cancelPayment(isA(String.class), isA(String.class),
        isA(PaymentsCancelPostRequestModel.class), isA(Boolean.class), isA(String.class));

    doReturn(payments).when(paymentsRepository).save(isA(Payments.class));

    PaymentStatusHistory paymentStatusHistory = new PaymentStatusHistory();
    doReturn(paymentStatusHistory).when(paymentStatusHistoryRepository).save(isA(PaymentStatusHistory.class));

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    String finalUrl = base + PaymentConstant.SUBMIT_RECEIVE_PAYMENT_URL.replace("{paymentRefId}", RandomStringUtils.randomAlphanumeric(20));

    HttpHeaders headers = getHttpHeader();

    HttpEntity submitReceivePayment = new HttpEntity<>(request, headers);

    ResponseEntity<SubmitReceivePaymentResponse> responseEntity = template
        .exchange(finalUrl, HttpMethod.PUT, submitReceivePayment, SubmitReceivePaymentResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());
    //First time we should not have it
    assertNull(responseEntity.getHeaders().get(APICommonUtilConstant.HEADER_WARNING_CODE));

    //Since first time complete we need set this status to Mock
    payments.setStatus("COMPLETE");
    doReturn(java.util.Optional.of(payments)).when(paymentsRepository)
        .findByExternalRefIdAndNetworkPaymentTypeNotAndTypeCdAndCustomerRefIdAndStatusNot(isA(String.class),
            isA(String.class), isA(PaymentCdType.class), isA(String.class), isA(String.class));

    responseEntity = template.exchange(finalUrl, HttpMethod.PUT, submitReceivePayment, SubmitReceivePaymentResponse.class);

    assertEquals(HttpStatus.CREATED, responseEntity.getStatusCode());

    //Second time we should have it
    assertEquals(APICommonUtilConstant.ALREADY_PROCESSED,
        responseEntity.getHeaders().get(APICommonUtilConstant.HEADER_WARNING_CODE).get(0));
  }
}
