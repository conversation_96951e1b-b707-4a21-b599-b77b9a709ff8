package com.peoples.banking.api.payment.v1.mapper;

import com.peoples.banking.api.payment.v1.config.PaymentConstant;
import com.peoples.banking.api.payment.v1.dto.ContactJsonDto;
import com.peoples.banking.api.payment.v1.dto.ContactMethodDto;
import com.peoples.banking.api.payment.v1.dto.FraudResultDto;
import com.peoples.banking.api.payment.v1.dto.PaymentAuthenticationDto;
import com.peoples.banking.api.payment.v1.dto.PaymentDto;
import com.peoples.banking.api.payment.v1.type.CustomerAuthenticationMethod;
import com.peoples.banking.api.payment.v1.type.PaymentAuthenticationType;
import com.peoples.banking.api.payment.v1.util.HashUtil;
import com.peoples.banking.domain.payment.model.DeviceInfo;
import com.peoples.banking.domain.payment.model.FraudResultType;
import com.peoples.banking.domain.payment.model.InitiateSendPaymentRequest;
import com.peoples.banking.domain.payment.model.InitiateSendPaymentResponse;
import com.peoples.banking.domain.payment.model.Options;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccount;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountTransaction;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountIdentification4Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountSchemeName1Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyCode;
import com.peoples.banking.partner.domain.interac.deposit.model.AuthenticationType;
import com.peoples.banking.partner.domain.interac.deposit.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.deposit.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.CreditTransferTransaction39;
import com.peoples.banking.partner.domain.interac.deposit.model.ExternalPaymentTransactionStatus1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.FraudCheckResult;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericAccountIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericOrganisationIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.HashType;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentAuthentication;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction110;
import com.peoples.banking.partner.domain.interac.deposit.model.ProductCode;
import com.peoples.banking.partner.domain.interac.deposit.model.ProxyAccountIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.ProxyAccountType1Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ProxyAccountType1Choice.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementMethod1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.SupplementaryInfo;
import com.peoples.banking.partner.domain.interac.deposit.model.SupplementaryInfo.CustomerAuthenticationMethodEnum;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.mapper.converter.JsonConverter;
import com.peoples.banking.util.api.common.type.NetworkChannelType;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import com.peoples.banking.util.api.common.type.TransactionType;
import java.security.NoSuchAlgorithmException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;

@Mapper(componentModel = "spring",
    imports = {
        DateUtil.class,
        IdGeneratorUtil.class,
        TransactionType.class,
        HashType.class,
        SettlementMethod1Code.class,
        ClearingSystemIdentification3Choice.ProprietaryEnum.class,
        ActiveCurrencyCode.class,
        ChargeBearerType1Code.class,
        ProductCode.class},
    uses = {
        JsonConverter.class,
        DateConverter.class
    })
public abstract class InitiateSendPaymentMapper extends CommonMapper {

  /**
   * To map from InitiateSendPaymentRequest to PaymentDto
   *
   * @param initiateSendPaymentRequest the InitiateSendPaymentRequest
   * @param fiId                       PTC FIID
   * @param serviceAccountRefId        business partner's service account reference ice
   * @param paymentRefId               PTC generated payment reference id
   * @param networkEnrollmentId        a customer's enrollment id registered at network side
   * @param networkChannelType         network channel
   * @param paymentAuthenticationType  the payment's authentication type
   * @param hashType                   hash type for the payment authentication answer
   * @param hashSalt                   hash salt for the payment authentication answer
   * @return PaymentDto the payment Dto
   */
  @Mapping(source = "paymentRefId",
      target = "externalRefId")
  @Mapping(source = "serviceAccountRefId",
      target = "serviceAccountRefId")
  @Mapping(source = "fiId",
      target = "fiId")
  @Mapping(source = "initiateSendPaymentRequest.customerId",
      target = "customerDto.customerExternalId")
  @Mapping(source = "customerCreatedDate",
      target = "customerDto.createdDate")
  @Mapping(source = "initiateSendPaymentRequest.endToEndId",
      target = "endToEndId", defaultValue = NOT_PROVIDED)
  @Mapping(source = "initiateSendPaymentRequest.contact.name",
      target = "contactDto.contactJsonDto.name")
  @Mapping(source = "initiateSendPaymentRequest.contact.account.aliasReferenceId",
      target = "contactDto.contactJsonDto.aliasReferenceId")
  @Mapping(source = "initiateSendPaymentRequest.contact.account.accountNumber",
      target = "contactDto.contactJsonDto.accountNumber")
  @Mapping(source = "initiateSendPaymentRequest.contact.emailAddress",
      target = "contactDto.contactJsonDto.contactMethodDto.email")
  @Mapping(source = "initiateSendPaymentRequest.contact.mobileNumber",
      target = "contactDto.contactJsonDto.contactMethodDto.mobile")
  @Mapping(source = "initiateSendPaymentRequest.amount",
      target = "amount")
  @Mapping(source = "initiateSendPaymentRequest.accountName",
      target = "accountDto.name")
  @Mapping(source = "initiateSendPaymentRequest.accountNumber",
      target = "accountDto.number")
  @Mapping(source = "initiateSendPaymentRequest.options",
      target = "expiryDate",
      qualifiedByName = "optionsExpiryDateMapper")
  @Mapping(source = "initiateSendPaymentRequest.options.expireAfterDays",
      target = "optionDto.optionJsonDto.expiredAfterDays")
  @Mapping(expression = "java(TransactionType.OUTBOUND)",
      target = "transactionType")
  @Mapping(expression = "java(IdGeneratorUtil.generateRequestId().replace(\"-\", \"\"))",
      target = "networkDto.networkMessageId")
  @Mapping(source = "networkEnrollmentId",
      target = "networkDto.networkEnrollmentId")
  @Mapping(source = "networkChannelType",
      target = "networkDto.networkRefId")
  @Mapping(source = "initiateSendPaymentRequest.paymentType",
      target = "networkDto.networkPaymentType")
  @Mapping(source = "initiateSendPaymentRequest.networkRequestRefId",
      target = "networkDto.networkRequestPaymentRefId")
  @Mapping(expression = "java(paymentAuthenticationToPaymentAuthenticationDto(initiateSendPaymentRequest.getPaymentAuthentication(), paymentAuthenticationType, hashType, hashSalt))",
      target = "paymentAuthenticationDto")
  @Mapping(source = "initiateSendPaymentRequest.deviceInfo.authenticationMethod",
      target = "deviceInfoDto.customerAuthenticationMethod")
  @Mapping(source = "initiateSendPaymentRequest.deviceInfo.ipAddress",
      target = "deviceInfoDto.ipAddress")
  @Mapping(source = "initiateSendPaymentRequest.deviceInfo",
      target = "deviceInfoDto.fingerprint", qualifiedByName = "deviceFingerprintMapper")
  @Mapping(expression = "java(DateUtil.getCurrentUTCDateTime())", target = "deviceInfoDto.createdDate")
  @Mapping(source = "initiateSendPaymentRequest.memo",
      target = "remittanceDto.remittanceJsonDto.unstructured.memo")
  @Mapping(source = "initiateSendPaymentRequest.language", target = "language")
  @Mapping(expression = "java(DateUtil.getCurrentUTCDateTime())",
      target = "createdDate")
  @Mapping(source = "connectorType", target = "connectorType")
  @Mapping(source = "indirectConnectorId", target = "indirectConnectorId")
  public abstract PaymentDto initiateSendPaymentRequestToPaymentDto(InitiateSendPaymentRequest initiateSendPaymentRequest, String fiId,
      String serviceAccountRefId, OffsetDateTime customerCreatedDate, String paymentRefId, @Context int defaultExpiryDays,
      String networkEnrollmentId,
      NetworkChannelType networkChannelType,
      PaymentAuthenticationType paymentAuthenticationType, String hashType, String hashSalt, String connectorType, String indirectConnectorId);

  /**
   * Map from PaymentDto to Interac's InitiatePaymentRequest
   *
   * @param paymentDto payment Dto
   * @return InitiatePaymentRequest Interac's InitiatePaymentRequest
   */
  @Mapping(source = "networkDto.networkMessageId", target = "fiToFiCustomerCreditTransfer.groupHeader.messageIdentification")
  @Mapping(target = "fiToFiCustomerCreditTransfer.groupHeader.creationDatetime", expression = "java(DateUtil.getCurrentUTCDateTime())")
  @Mapping(target = "fiToFiCustomerCreditTransfer.groupHeader.numberOfTransactions", expression = "java(\"1\")")
  @Mapping(target = "fiToFiCustomerCreditTransfer.groupHeader.settlementInformation.settlementMethod", expression = "java(SettlementMethod1Code.CLRG)")
  @Mapping(target = "fiToFiCustomerCreditTransfer.groupHeader.settlementInformation.clearingSystem.proprietary", expression = "java(ClearingSystemIdentification3Choice.ProprietaryEnum.ETR)")
  @Mapping(source = "paymentDto.fiId", target = "fiToFiCustomerCreditTransfer.groupHeader.instructingAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification")
  @Mapping(target = "fiToFiCustomerCreditTransfer.groupHeader.instructedAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification", expression = "java(NOT_PROVIDED)")
  @Mapping(source = "paymentDto", target = "fiToFiCustomerCreditTransfer.creditTransferTransactionInformation")
  @Mapping(target = "productCode", expression = "java(ProductCode.DOMESTIC)")
  @Mapping(source = "expiryDate", target = "expiryDate")
  @Mapping(source = "accountDto.number", target = "fiAccountId")
  @Mapping(source = "accountDto.name", target = "accountHolderName")
  @Mapping(source = "paymentDto", target = "fraudSupplementaryInfo")
  @Mapping(source = "language", target = "language")
  @Mapping(source = "paymentAuthenticationDto", target = "paymentAuthentication")
  public abstract InitiatePaymentRequest paymentDtoToInitiatePaymentRequest(PaymentDto paymentDto);

  /**
   * The custom implementation of mapping from Interac's InitiatePaymentResponse to existing PaymentDto
   *
   * @param paymentDto              payment Dto
   * @param initiatePaymentResponse Interac's InitiatePaymentResponse
   */
  public void initiatePaymentResponseToPaymentDto(PaymentDto paymentDto, InitiatePaymentResponse initiatePaymentResponse) {
    if (initiatePaymentResponse == null) {
      return;
    }

    List<PaymentTransaction110> transactionInformationAndStatusList = initiatePaymentResponse.getFiToFiPaymentStatusReport()
        .getTransactionInformationAndStatus();
    if (transactionInformationAndStatusList != null && !transactionInformationAndStatusList.isEmpty()) {
      PaymentTransaction110 transactionInformationAndStatus = transactionInformationAndStatusList.get(0);
      paymentDto.getNetworkDto().setNetworkPaymentRefId(transactionInformationAndStatus.getClearingSystemReference());
      ExternalPaymentTransactionStatus1Code transactionStatus = transactionInformationAndStatus.getTransactionStatus();
      paymentDto.setStatus(externalPaymentTransactionStatus1CodeToPaymentStatus(transactionStatus));
    }

    if (initiatePaymentResponse.getPaymentTransactionToken() != null) {
      paymentDto.getNetworkDto().setNetworkTransactionId(initiatePaymentResponse.getPaymentTransactionToken());
    }

    if (initiatePaymentResponse.getFraudCheckResult() != null) {
      paymentDto.setFraudResultDto(fraudCheckResultToFraudReasonDto(initiatePaymentResponse.getFraudCheckResult()));
    }
  }


  @Mapping(source = "customerDto.customerExternalId", target = "customerId")
  @Mapping(source = "endToEndId", target = "endToEndId")
  @Mapping(source = "contactDto.contactJsonDto.name", target = "contact.name")
  @Mapping(source = "contactDto.contactJsonDto", target = "contact.account", qualifiedByName = "contactJsonDtoToAccountMapper")
  @Mapping(source = "contactDto.contactJsonDto.contactMethodDto.email", target = "contact.emailAddress")
  @Mapping(source = "contactDto.contactJsonDto.contactMethodDto.mobile", target = "contact.mobileNumber")
  @Mapping(source = "amount", target = "amount")
  @Mapping(source = "accountDto.name", target = "accountName")
  @Mapping(source = "accountDto.number", target = "accountNumber")
  @Mapping(source = "optionDto.optionJsonDto.expiredAfterDays", target = "options.expireAfterDays")
  @Mapping(source = "networkDto.networkPaymentType", target = "paymentType")
  @Mapping(source = "paymentAuthenticationDto", target = "paymentAuthentication")
  @Mapping(source = "deviceInfoDto.customerAuthenticationMethod", target = "deviceInfo.authenticationMethod")
  @Mapping(source = "deviceInfoDto.ipAddress", target = "deviceInfo.ipAddress")
  @Mapping(source = "deviceInfoDto.fingerprint", target = "deviceInfo.deviceFingerprint", qualifiedByName = "extractDeviceFingerprintMapper")
  @Mapping(source = "deviceInfoDto.fingerprint", target = "deviceInfo.deviceFingerprintType", qualifiedByName = "extractDeviceFingerprintTypeMapper")
  @Mapping(source = "remittanceDto.remittanceJsonDto.unstructured.memo", target = "memo")
  @Mapping(source = "externalRefId", target = "paymentRefId")
  @Mapping(source = "status", target = "status")
  @Mapping(source = "expiryDate", target = "expiryDate")
  @Mapping(source = "createdDate", target = "createdDate")
  @Mapping(source = "fraudResultDto.fraudResultJsonDto.action", target = "fraudResult", qualifiedByName = "fraudResultTypeMapper")
  @Mapping(source = "networkDto.networkRequestPaymentRefId", target = "networkRequestRefId")
  @Mapping(source = "networkDto.networkPaymentRefId", target = "networkPaymentRefId")
  public abstract InitiateSendPaymentResponse paymentDtoToInitiateSendPaymentResponse(PaymentDto paymentDto);

  @Named("fraudResultTypeMapper")
  protected com.peoples.banking.domain.payment.model.FraudResultType extractFraudResultType(String fraudType) {
    if (StringUtils.isBlank(fraudType)) {
      return null;
    }
    FraudResultType fraudResultType = FraudResultType.fromValue(fraudType);
    return fraudResultType;
  }

  @Named("deviceFingerprintMapper")
  protected String deviceFingerprint(DeviceInfo deviceInfo, @Context int defaultExpiryDays) {
    if (deviceInfo == null || StringUtils.isBlank(deviceInfo.getDeviceFingerprint()) || deviceInfo.getDeviceFingerprintType() == null) {
      return null;
    }

    return joinDeviceFingerprintFingerprintType(deviceInfo);
  }

  @Named("optionsExpiryDateMapper")
  protected OffsetDateTime optionsExpiryDate(Options options, @Context int defaultExpiryDays) {

    if (options == null || options.getExpireAfterDays() == null) {
      return DateUtil.getCurrentUTCDateTime().plusDays(defaultExpiryDays);
    } else {
      return DateUtil.getCurrentUTCDateTime().plusDays(options.getExpireAfterDays());
    }
  }

  /**
   * the custom mapping for mapping from InitiateSendPaymentRequest's PaymentAuthentication to PaymentAuthenticationDto
   *
   * @param paymentAuthentication     PaymentAuthentication from InitiateSendPaymentRequest
   * @param paymentAuthenticationType the payment authentication type and default to Contact level
   * @param hashType                  the hash type for payment authentication answer, default to SHA2
   * @param hashSalt                  the hash salt for payment authentication answer
   * @return PaymentAuthenticationDto the payment authentication Dto
   */
  protected PaymentAuthenticationDto paymentAuthenticationToPaymentAuthenticationDto(
      com.peoples.banking.domain.payment.model.PaymentAuthentication paymentAuthentication,
      PaymentAuthenticationType paymentAuthenticationType, String hashType, String hashSalt) {

    if (paymentAuthentication == null) {
      return null;
    }

    PaymentAuthenticationDto paymentAuthenticationDto = new PaymentAuthenticationDto();

    paymentAuthenticationDto.setQuestion(paymentAuthentication.getQuestion());
    paymentAuthenticationDto.setAnswer(paymentAuthentication.getAnswer());

    if (paymentAuthenticationType != null) {
      paymentAuthenticationDto.setPaymentAuthenticationType(paymentAuthenticationType);
    }
    if (hashType != null) {
      paymentAuthenticationDto.setHashType(hashType);
    }
    if (hashSalt != null) {
      paymentAuthenticationDto.setHashSalt(hashSalt);
    }

    return paymentAuthenticationDto;
  }


  /**
   * Map PaymentDto to CreditTransferTransactionInformation List
   *
   * @param paymentDto payment Dto
   * @return List Interac's CreditTransferTransactionInformation List
   */
  protected List<CreditTransferTransaction39> paymentDtoToCreditTransferTransactionInformationList(PaymentDto paymentDto) {
    if (paymentDto == null) {
      return null;
    }

    List<CreditTransferTransaction39> creditTransferTransaction39List = new ArrayList<>();
    creditTransferTransaction39List.add(paymentDtoToCreditTransferTransactionInformation(paymentDto));

    CashAccount38 creditorAccount = creditTransferTransaction39List.get(0).getCreditorAccount();
    if (creditorAccount == null) {
      creditorAccount = new CashAccount38();
      creditTransferTransaction39List.get(0).setCreditorAccount(creditorAccount);
    }

    //if payment type is ACCOUNT_ALIAS_PAYMENT  or REALTIME_ACCOUNT_ALIAS_PAYMENT then proxy is mandatory
    if (paymentDto.getNetworkDto().getNetworkPaymentType() == NetworkPaymentType.ACCOUNT_ALIAS_PAYMENT ||
        paymentDto.getNetworkDto().getNetworkPaymentType() == NetworkPaymentType.REALTIME_ACCOUNT_ALIAS_PAYMENT) {
      if (paymentDto.getContactDto().getContactJsonDto().getContactMethodDto() != null) {
        creditorAccount
            .setProxy(paymentDtoToProxyAccountIdentification1(paymentDto.getContactDto().getContactJsonDto().getContactMethodDto()));
      }
    }

    if (paymentDto.getNetworkDto().getNetworkPaymentType() == NetworkPaymentType.ACCOUNT_DEPOSIT_PAYMENT ||
        paymentDto.getNetworkDto().getNetworkPaymentType() == NetworkPaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT) {
      if (paymentDto.getContactDto().getContactJsonDto().getContactMethodDto() != null) {
        if (creditorAccount.getIdentification() == null) {
          creditorAccount.setIdentification(new AccountIdentification4Choice());
        }
        creditTransferTransaction39List.get(0).getCreditorAccount()
            .getIdentification().setOther(paymentDtoToGenericAccountIdentification1(paymentDto.getContactDto().getContactJsonDto()));
      }
    }

    return creditTransferTransaction39List;
  }

  /**
   * Map PaymentDto to CreditTransferTransactionInformation
   *
   * @param paymentDto payment Dto
   * @return CreditTransferTransactionInformation
   */
  @Mapping(source = "endToEndId", target = "paymentIdentification.endToEndIdentification", qualifiedByName = "truncateEndToEndId")
  @Mapping(source = "networkDto.networkRequestPaymentRefId", target = "paymentIdentification.instructionIdentification")
  @Mapping(source = "externalRefId", target = "paymentIdentification.transactionIdentification")
  @Mapping(source = "networkDto.networkPaymentType", target = "paymentTypeInformation.localInstrument.proprietary")
  @Mapping(source = "amount", target = "interbankSettlementAmount.amount")
  @Mapping(target = "interbankSettlementAmount.currency", expression = "java(ActiveCurrencyCode.CAD)")
  @Mapping(target = "interbankSettlementDate", expression = "java(DateUtil.getCurrentDateAsUTC())")
  @Mapping(target = "chargeBearer", expression = "java(ChargeBearerType1Code.SLEV)")
  @Mapping(source = "networkDto.networkEnrollmentId", target = "debtor.identification.organisationIdentification.other")
  @Mapping(source = "accountDto.number", target = "debtorAccount.identification.other.identification")
  @Mapping(source = "paymentDto", target = "debtorAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification",
      qualifiedByName = "debtorAgentMemberIdentification")
  @Mapping(target = "creditorAgent.financialInstitutionIdentification.clearingSystemMemberIdentification.memberIdentification", expression = "java(NOT_PROVIDED)")
  @Mapping(source = "contactDto.contactJsonDto.name", target = "creditor.name")
  @Mapping(source = "contactDto.contactJsonDto.contactMethodDto.email", target = "creditor.contactDetails.emailAddress")
  @Mapping(target = "creditor.contactDetails.mobileNumber",
      expression = "java(contactJsonDto.getContactMethodDto().getMobile() == null? "
          + "null : contactJsonDto.getContactMethodDto().getMobile().replaceFirst(\"(\\\\d{3})(\\\\d{3})(\\\\d+)\", \"+1-$1-$2-$3\"))")
  @Mapping(source = "contactDto.contactJsonDto.aliasReferenceId", target = "creditorAccount")
  @Mapping(source = "remittanceDto.remittanceJsonDto.unstructured.memo", target = "remittanceInformation")
  protected abstract CreditTransferTransaction39 paymentDtoToCreditTransferTransactionInformation(PaymentDto paymentDto);

  /**
   * Map NetworkPaymentType in PaymentDto to Interac's Payment type
   *
   * @param networkPaymentType network payment type from network Dto
   * @return LocalInstrument2Choice.ProprietaryEnum Interac's payment type
   */
  @ValueMapping(source = "REQUEST_FOR_PAYMENT", target = MappingConstants.NULL)
  protected abstract LocalInstrument2Choice.ProprietaryEnum networkPaymentTypeToProprietaryEnum(NetworkPaymentType networkPaymentType);

  /**
   * Map networkEnrollmentId to Interac's debtor identification List
   *
   * @param networkEnrollmentId the network enrollment id
   * @return List Interac's GenericOrganisationIdentification1 list
   */
  protected List<GenericOrganisationIdentification1> networkEnrollmentIdToGenericOrganisationIdentification1List(
      String networkEnrollmentId) {
    if (networkEnrollmentId == null) {
      return null;
    }

    List<GenericOrganisationIdentification1> debtorIdentificationList = new ArrayList<>();
    debtorIdentificationList.add(networkEnrollmentIdToGenericOrganisationIdentification1(networkEnrollmentId));

    return debtorIdentificationList;
  }

  /**
   * Map networkEnrollmentId to Interac's debtor's identification
   *
   * @param networkEnrollmentId networkEnrollmentId
   * @return GenericOrganisationIdentification1 Interac's debtor's identification
   */
  @Mapping(source = "networkEnrollmentId", target = "identification")
  protected abstract GenericOrganisationIdentification1 networkEnrollmentIdToGenericOrganisationIdentification1(String networkEnrollmentId);


  /**
   * Map contact's alias reference id to Interac's creditor account
   *
   * @param aliasReferenceId
   * @return
   */
  @Mapping(source = "aliasReferenceId", target = "identification.other.identification")
  protected abstract CashAccount38 aliasReferenceIdToCreditorAccount(String aliasReferenceId);

  /**
   * Map CustomerAuthenticationMethod in DeviceInfoDto to Interac's CustomerAuthenticationMethodEnum
   *
   * @param customerAuthenticationMethod customer authentication method from DeviceInfoDto
   * @return CustomerAuthenticationMethodEnum Interac's CustomerAuthenticationMethodEnum
   */
  @ValueMappings({@ValueMapping(source = "PERSONAL_VERIFICATION_QUESTION", target = "PVQ"),
      @ValueMapping(source = "BIOMETRICS", target = "BIO_METRICS"),
      @ValueMapping(source = "ONE_TIME_PASSWORD", target = "OTP")
  })
  protected abstract CustomerAuthenticationMethodEnum customerAuthenticationMethodToCustomerAuthenticationMethodEnum(
      CustomerAuthenticationMethod customerAuthenticationMethod);


  /**
   * Map PaymentDto to Interac's SupplementaryInfo
   *
   * @param paymentDto payment Dto
   * @return SupplementaryInfo Interac's SupplementaryInfo
   */
  @Mapping(source = "deviceInfoDto.ipAddress", target = "customerIpAddress")
  @Mapping(target = "accountCreationDate", expression = "java(DateUtil.offsetDateTimeToLocalDate(paymentDto.getCustomerDto().getCreatedDate()))")
  @Mapping(source = "deviceInfoDto.fingerprint", target = "customerDeviceFingerPrint")
  @Mapping(source = "deviceInfoDto.customerAuthenticationMethod", target = "customerAuthenticationMethod")
  protected abstract SupplementaryInfo paymentDtoToSupplementaryInfo(PaymentDto paymentDto);

  /**
   * Map PaymentAuthenticationDto to Interac's PaymentAuthentication
   *
   * @param paymentAuthenticationDto payment authentication Dto
   * @return PaymentAuthentication Interac's PaymentAuthentication
   * @throws NoSuchAlgorithmException
   */
  protected PaymentAuthentication paymentAuthenticationDtoToInteracPaymentAuthentication(PaymentAuthenticationDto paymentAuthenticationDto)
      throws NoSuchAlgorithmException {
    if (paymentAuthenticationDto == null) {
      return null;
    }

    PaymentAuthentication paymentAuthentication = new PaymentAuthentication();
    paymentAuthentication
        .setAuthenticationType(paymentAuthenticationTypeToAuthenticationType(paymentAuthenticationDto.getPaymentAuthenticationType()));
    paymentAuthentication.setHashType(HashType.fromValue(paymentAuthenticationDto.getHashType()));
    paymentAuthentication.setHashSalt(paymentAuthenticationDto.getHashSalt());
    paymentAuthentication.setSecurityQuestion(paymentAuthenticationDto.getQuestion());

    String securityAnswer = paymentAuthenticationDto.getAnswer();
    String hashedSecurityAnswer = HashUtil.toHashedString(paymentAuthentication.getHashSalt(), securityAnswer,
        com.peoples.banking.api.payment.v1.type.HashType.SHA2.getValue());
    paymentAuthentication.setSecurityAnswer(hashedSecurityAnswer);

    return paymentAuthentication;
  }

  /**
   * To Map the paymentAuthenticationType in the PaymentAuthenticationDto to Interac's AuthenticationType
   *
   * @param paymentAuthenticationType payment authentication type in payment authentication Dto
   * @return AuthenticationType Interac's AuthenticationType
   */
  @ValueMappings({@ValueMapping(source = "CONTACT", target = "CONTACT_LEVEL"),
      @ValueMapping(source = "PAYMENT", target = "PAYMENT_LEVEL"),
      @ValueMapping(source = "NOT_REQUIRED", target = "NOT_REQUIRED")
  })
  protected abstract AuthenticationType paymentAuthenticationTypeToAuthenticationType(PaymentAuthenticationType paymentAuthenticationType);

  /**
   * map FraudCheckResult to FraudResultDto
   *
   * @param fraudCheckResult the Interac's fraud check result
   * @return FraudResultDto the fraudResultDto
   */
  @Mapping(source = "fraudCheckResult", target = "fraudResultJsonDto")
  protected abstract FraudResultDto fraudCheckResultToFraudReasonDto(FraudCheckResult fraudCheckResult);

  protected abstract com.peoples.banking.domain.payment.model.PaymentAuthentication paymentAuthenticationDtoToPaymentAuthentication(
      PaymentAuthenticationDto paymentAuthenticationDto);


  /**
   * The custom implementation of mapping ExternalPaymentTransactionStatus1Code to PaymentStatus. ACTC and PDNG are equivalent to PTC's AVAILABLE
   * status; RJCT and others are equivalent to PTC's FAILED status
   *
   * @param paymentDto payment dto objecti
   * @return ServiceAccountTransaction service account transaction mapped
   */
  @Mapping(source = "externalRefId", target = "paymentRefId")
  @Mapping(source = "amount", target = "amount")
  @Mapping(source = "createdDate", target = "transactionDate")
  public abstract ServiceAccountTransaction paymentDtoToServiceAccountTransaction(PaymentDto paymentDto);

  /**
   * The custom implementation of mapping ExternalPaymentTransactionStatus1Code to PaymentStatus. ACTC and PDNG are equivalent to PTC's AVAILABLE
   * status; RJCT and others are equivalent to PTC's FAILED status
   *
   * @param transactionStatus the external payment transaction status
   * @return PaymentStatus the PTC payment status
   */
  @ValueMappings({@ValueMapping(source = "ACTC", target = "ACCEPTED"),
      @ValueMapping(source = "PDNG", target = "ACCEPTED"),
      @ValueMapping(source = "RJCT", target = "FAILED"),
      @ValueMapping(source = "ACSP", target = "ACCEPTED")
  })
  protected abstract PaymentStatus externalPaymentTransactionStatus1CodeToPaymentStatus(
      ExternalPaymentTransactionStatus1Code transactionStatus);

  /**
   * To map Remittance memo from PaymentDto to RemittanceInformation16
   *
   * @param unstructuredMemo the unstructuredMemo
   * @return RemittanceInformation16 the RemittanceInformation16
   */
  protected RemittanceInformation16 paymentDtoToRemittanceInformation16(String unstructuredMemo) {
    if (unstructuredMemo == null || unstructuredMemo.isEmpty()) {
      return null;
    }
    RemittanceInformation16 remittanceInformation16 = new RemittanceInformation16();
    List<String> memoLst = splitEqually(unstructuredMemo,
        PaymentConstant.INTERAC_REMITTANCE_MEMO_LENGTH);
    if (memoLst != null) {
      for (String memo : memoLst) {
        remittanceInformation16.addUnstructuredItem(memo);
      }
    }

    return remittanceInformation16;
  }

  /**
   * To map proxy info from PaymentDto to ProxyAccountIdentification1 If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_ALIAS_PAYMENT
   * or REALTIME_ACCOUNT_ALIAS_PAYMENT , then this block is mandatory.
   *
   * @param contactMethodDto the ContactMethodDto
   * @return ProxyAccountIdentification1 the ProxyAccountIdentification1
   */
  protected ProxyAccountIdentification1 paymentDtoToProxyAccountIdentification1(ContactMethodDto contactMethodDto) {
    if (contactMethodDto == null) {
      return null;
    }

    ProxyAccountIdentification1 proxyAccountIdentification1 = new ProxyAccountIdentification1();

    if (contactMethodDto.getEmail() != null && !contactMethodDto.getEmail().isEmpty()) {
      proxyAccountIdentification1.setIdentification(contactMethodDto.getEmail());
      ProxyAccountType1Choice proxyAccountType1Choice = new ProxyAccountType1Choice();
      proxyAccountType1Choice.setProprietary(ProprietaryEnum.EMAIL);
      proxyAccountIdentification1.setType(proxyAccountType1Choice);
    } else if (contactMethodDto.getMobile() != null && !contactMethodDto.getMobile().isEmpty()) {
      proxyAccountIdentification1.setIdentification(contactMethodDto.getMobile());
      ProxyAccountType1Choice proxyAccountType1Choice = new ProxyAccountType1Choice();
      proxyAccountType1Choice.setProprietary(ProprietaryEnum.PHONE);
      proxyAccountIdentification1.setType(proxyAccountType1Choice);
    }
    return proxyAccountIdentification1;

  }

  /**
   * To map proxy info from PaymentDto to GenericAccountIdentification1 if scheme name is ALIAS_ACCT_NO then this element must contain the Account
   * Alias Reference Number (i.e. Autodeposit reference number) generated by Interac if scheme name is BANK_ACCT_NO then this element must contain the
   * actual creditor’s bank account number. Valid format: aaa-bbbbb-cccccccccccccccccccccccc where
   * <p>
   * aaa is the Institution Id (fixed length 3 digits) bbbbb is the Transit Number (fixed length 5 digits) cccccccccccccccccccccccc is the bank
   * account number (up to max 24 digits)
   *
   * @param contactJsonDto the ContactJsonDto
   * @return GenericAccountIdentification1 the GenericAccountIdentification1
   */
  protected GenericAccountIdentification1 paymentDtoToGenericAccountIdentification1(ContactJsonDto contactJsonDto) {
    if (contactJsonDto == null) {
      return null;
    }

    GenericAccountIdentification1 genericAccountIdentification1 = new GenericAccountIdentification1();

    if (contactJsonDto.getAliasReferenceId() != null && !contactJsonDto.getAliasReferenceId().isEmpty()) {
      genericAccountIdentification1.setIdentification(contactJsonDto.getAliasReferenceId());
      AccountSchemeName1Choice accountSchemeName1Choice = new AccountSchemeName1Choice();
      accountSchemeName1Choice.setProprietary(AccountSchemeName1Choice.ProprietaryEnum.ALIAS_ACCT_NO);
      genericAccountIdentification1.setSchemeName(accountSchemeName1Choice);
    } else if (contactJsonDto.getAccountNumber() != null && !contactJsonDto.getAccountNumber().isEmpty()) {
      genericAccountIdentification1.setIdentification(contactJsonDto.getAccountNumber());
      AccountSchemeName1Choice accountSchemeName1Choice = new AccountSchemeName1Choice();
      accountSchemeName1Choice.setProprietary(AccountSchemeName1Choice.ProprietaryEnum.BANK_ACCT_NO);
      genericAccountIdentification1.setSchemeName(accountSchemeName1Choice);
    }
    return genericAccountIdentification1;
  }

  @Named("debtorAgentMemberIdentification")
  protected String getDebtorAgentMemberIdentification(PaymentDto paymentDto) {
    if (ServiceAccount.ConnectorTypeEnum.INDIRECT.getValue().equals(paymentDto.getConnectorType()) && paymentDto.getIndirectConnectorId() != null) {
      return paymentDto.getIndirectConnectorId();
    } else {
      return paymentDto.getFiId();
    }
  }
  
}
