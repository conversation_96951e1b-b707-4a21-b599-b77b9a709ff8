package com.peoples.banking.api.serviceaccountcredential.v1.model;

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@DynamoDBTable(tableName = "service-account-local")
public class ServiceAccountDynamo {

  private String token;
  private List<String> authMethod;

  private List<Map<String,String>> credentials;

  @DynamoDBHashKey(attributeName = "token")
  @DynamoDBAttribute(attributeName = "token")
  public String getToken() {
    return token;
  }

  @DynamoDBAttribute(attributeName = "auth_method")
  public List<String> getAuthMethod() {
    return authMethod;
  }


  @DynamoDBAttribute(attributeName = "credential")
  public List<Map<String,String>> getCredentials() {
    return credentials;
  }
}
