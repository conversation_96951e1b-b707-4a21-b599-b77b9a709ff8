package com.peoples.banking.api.serviceaccountcredential.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class ServiceAccountCredentialProperty {

  @Value("${serviceaccountcredential.api.timetolive:30000}")
  private int timeToLive;
}
