package com.peoples.banking.api.serviceaccountcredential.v1.service;

import com.peoples.banking.api.serviceaccountcredential.v1.mapper.CreateServiceAccountCredentialRequestMapper;
import com.peoples.banking.api.serviceaccountcredential.v1.model.ServiceAccountDynamo;
import com.peoples.banking.api.serviceaccountcredential.v1.repositories.ServiceAccountInfoRepository;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class ServiceAccountCredentialService {

  @Autowired
  private ServiceAccountInfoRepository serviceAccountInfoRepository;

  /**
   * Mapper to handle CreateServiceAccountCredentialRequest mapping
   */
  @Autowired
  private CreateServiceAccountCredentialRequestMapper createServiceAccountCredentialRequestMapper;


  public void createServiceAccountCredential(CreateServiceAccountCredentialRequest request) {
    try {
      ServiceAccountDynamo serviceAccountDynamo = createServiceAccountCredentialRequestMapper.createServiceAccountCredentialRequestToServiceAccountDynamo(
          request);

      serviceAccountInfoRepository.save(Objects.requireNonNull(serviceAccountDynamo));

      log.debug("serviceAccountCredentialDynamoDB '{}' saved successfully", serviceAccountDynamo);
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }
  }
}