package com.peoples.banking.api.serviceaccountcredential.v1.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperConfig;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTypeConverterFactory;
import lombok.Data;
import org.socialsignin.spring.data.dynamodb.repository.config.EnableDynamoDBRepositories;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@EnableDynamoDBRepositories(basePackages = "com.peoples.banking.api.serviceaccountcredential.v1")
public class AwsDynamoDbConfig {

  @Value("${aws.region}")
  private String region;
  @Value("${aws.dynamodb.url}")
  private String dynamoDbEndpointUrl;
  @Value("${aws.dynamodb.access-key}")
  private String accessKey;
  @Value("${aws.dynamodb.secret-key}")
  private String secretKey;

  @Value("${aws.dynamodb.table}")
  private String tableName;

  @Value("${aws.dynamodb.pass.encrypt.length}")
  private int passEncryptLength;

  @Bean
  public DynamoDBMapperConfig dynamoDBMapperConfig() {
    //Check this ticket for DynamoDB prefix and Database name override
    //https://github.com/derjust/spring-data-dynamodb/issues/73
    DynamoDBMapperConfig.Builder builder = new DynamoDBMapperConfig.Builder();
    builder.setTypeConverterFactory(
        DynamoDBTypeConverterFactory.standard());
    builder.setTableNameOverride(DynamoDBMapperConfig.TableNameOverride.withTableNameReplacement(tableName));
    return builder.build();
  }

  @Bean(name = "amazonDynamoDB")
  public AmazonDynamoDB amazonDynamoDB() {
    return AmazonDynamoDBClientBuilder.standard()
        .withCredentials(getCredentialsProvider())
        .withEndpointConfiguration(getEndpointConfiguration(dynamoDbEndpointUrl))
        .build();
  }

  private EndpointConfiguration getEndpointConfiguration(String url) {
    return new EndpointConfiguration(url, region);
  }

  private AWSStaticCredentialsProvider getCredentialsProvider() {
    return new AWSStaticCredentialsProvider(getBasicAWSCredentials());
  }

  private BasicAWSCredentials getBasicAWSCredentials() {
    return new BasicAWSCredentials(accessKey, secretKey);
  }
}
