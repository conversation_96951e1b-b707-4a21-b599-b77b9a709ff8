package com.peoples.banking.api.serviceaccountcredential.v1.controller;

import com.peoples.banking.api.serviceaccountcredential.v1.config.ServiceAccountCredentialAPIConstant;
import com.peoples.banking.api.serviceaccountcredential.v1.config.ServiceAccountCredentialProperty;
import com.peoples.banking.api.serviceaccountcredential.v1.service.ServiceAccountCredentialService;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.controller.InternalAPIController;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ServiceAccountCredentialController extends InternalAPIController {

  @Autowired
  private ServiceAccountCredentialService serviceAccountCredentialService;

  @Autowired
  private ServiceAccountCredentialProperty serviceAccountCredentialProperty;

  /**
   * ServiceAccountCredential controller for create dynamo DB entry
   */
  @PerfLogger
  @PostMapping(value = ServiceAccountCredentialAPIConstant.SERVICE_ACCOUNT_CREDENTIAL, produces = {
      APICommonUtilConstant.DEFAULT_CONTENT_TYPE})
  public ResponseEntity<?> createServiceAccountCredential(
      @Valid @RequestBody CreateServiceAccountCredentialRequest request) {
    serviceAccountCredentialService.createServiceAccountCredential(request);
    return new ResponseEntity<>(null, null, HttpStatus.CREATED);
  }

  @Override
  protected int getApiTimeToLiveValue() {
    return serviceAccountCredentialProperty.getTimeToLive();
  }
}
