package com.peoples.banking.api.serviceaccountcredential.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(basePackages = {
    "com.peoples.banking.api.serviceaccountcredential",
    "com.peoples.banking.util"})
@EnableScheduling
@EnableAutoConfiguration(exclude={DataSourceAutoConfiguration.class})
public class ServiceAccountCredentialApiApplication {

  public static void main(String[] args) {
    SpringApplication.run(ServiceAccountCredentialApiApplication.class, args);
  }

}
