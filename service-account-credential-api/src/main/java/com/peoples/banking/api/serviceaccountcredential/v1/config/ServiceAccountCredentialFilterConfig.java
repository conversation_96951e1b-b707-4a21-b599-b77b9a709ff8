package com.peoples.banking.api.serviceaccountcredential.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Service Account Credential API filter configuration
 */
@Configuration
public class ServiceAccountCredentialFilterConfig {

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> serviceAccountAPILoggingFilter() {
    LoggingFilter serviceAccountCredentialAPILoggingFilter = new LoggingFilter();
    serviceAccountCredentialAPILoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    serviceAccountCredentialAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    serviceAccountCredentialAPILoggingFilter.setSystemName(ServiceAccountCredentialAPIConstant.SERVICE_ACCOUNT_CREDENTIAL_API_SYSTEM_NAME);
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(serviceAccountCredentialAPILoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);

    return registrationBean;
  }


  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
