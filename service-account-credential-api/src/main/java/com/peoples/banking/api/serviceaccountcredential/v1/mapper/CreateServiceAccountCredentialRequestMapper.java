package com.peoples.banking.api.serviceaccountcredential.v1.mapper;

import com.peoples.banking.api.serviceaccountcredential.v1.config.AwsDynamoDbConfig;
import com.peoples.banking.api.serviceaccountcredential.v1.model.ServiceAccountDynamo;
import com.peoples.banking.domain.serviceaccountcredential.model.CreateServiceAccountCredentialRequest;
import com.peoples.banking.domain.serviceaccountcredential.model.ServiceAccountCredential;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@Mapper(componentModel = "spring")
public abstract class CreateServiceAccountCredentialRequestMapper {

  @Autowired
  AwsDynamoDbConfig awsDynamoDbConfig;

  @Mapping(source = "token", target = "token")
  @Mapping(source = "authMethod", target = "authMethod")
  @Mapping(source = "credential", target = "credentials")
  public abstract ServiceAccountDynamo createServiceAccountCredentialRequestToServiceAccountDynamo(
      CreateServiceAccountCredentialRequest createServiceAccountCredentialRequest);

  protected Map<String, String> populateServiceAccountCredential(ServiceAccountCredential serviceAccountCredential) {
    Map<String, String> map = new HashMap<>();
    map.put("user_name", serviceAccountCredential.getUserName());
    BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder(awsDynamoDbConfig.getPassEncryptLength(), new SecureRandom());
    map.put("password", bCryptPasswordEncoder.encode(serviceAccountCredential.getPassword()));
    return map;
  }
}
