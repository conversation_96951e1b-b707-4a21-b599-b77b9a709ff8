version: "3.6"
services:
  partnernotificationkafkalistener:
    build: .
    environment:
      - DATABASE_HOST=interac-db-qa.cluster-cpeag2bxo5g6.ca-central-1.rds.amazonaws.com
      - DATABASE_NAME=postgres
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=wXVTqTWk0Hu3LGBZRxit
      - KAFKA_SERVER=localhost:9092
      - KAFKA_PARTNER_NOTIFICATION_TOPIC=partner-notifications
      - KAFKA_PARTNER_NOTIFICATIONS_CONSUMER_GROUP=partner-notifications-group
      - KAFKA_PARTNER_NOTIFICATIONS_FRAUD_GROUP=partner-fraud-payment-group
      - KAFKA_PARTNER_NOTIFICATIONS_PAYMENT_GROUP=partner-payment-group
      - KAFKA_PARTNER_NOTIFICATIONS_REQUEST_FOR_PAYMENT_GROUP=partner-request-for-payment-group
      - KAFKA_PARTNER_NOTIFICATIONS_FRAUD_REGISTRATION_GROUP=partner-fraud-registration-group
      - KAFKA_PARTNER_NOTIFICATIONS_FRAUD_REQUEST_FOR_PAYMENT_GROUP=partner-fraud-request-for-payment-group
      - KAFKA_PARTNER_NOTIFICATIONS_REGISTRATION_GROUP=partner-registration-group
      - KAFKA_PARTNER_NOTIFICATIONS_PAYMENT_SCHEDULE_GROUP=partner-payment-schedule-group
      - KAFKA_PARTNER_NOTIFICATIONS_REQUEST_FOR_PAYMENT_SCHEDULE_GROUP=partner-request-for-payment-schedule-group
      - KAFKA_PARTNER_NOTIFICATIONS_REQUEST_FOR_PAYMENT_RETURN_GROUP=partner-request-for-payment-return-group
      - KAFKA_PARTNER_NOTIFICATIONS_CUSTOMER_REQUEST_FOR_PAYMENT_GROUP=partner-customer-request-for-payment-group
      - KAFKA_SSL_ENABLED=false
      - KAFKA_SSL_TRUSTSTORE_LOCATION=
      - KAFKA_SSL_TRUSTSTORE_PASSWORD=
      - KAFKA_SSL_KEYSTORE_LOCATION=
      - KAFKA_SSL_KEYSTORE_PASSWORD=
      - KAFKA_SSL_KEY=
      - CRM_SUBSCRIPTION_KEY=defaultCrmKey
      - CRM_HOST=https://pttestapp-apim.azure-api.net/api/qa/fascases