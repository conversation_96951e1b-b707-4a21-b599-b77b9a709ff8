package com.peoples.banking.api.partner.notification.listener.v1.mapper;


import com.peoples.banking.domain.push.notification.model.*;
import com.peoples.banking.partner.domain.interac.notification.model.*;
import com.peoples.banking.persistence.customer.entity.CustomerCodeType;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import com.peoples.banking.util.api.common.type.NetworkPaymentType;
import com.peoples.banking.util.api.common.type.PaymentStatus;
import java.text.DecimalFormat;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {RequestForPaymentFraudMapperImpl.class, DateConverter.class})
public class RequestForPaymentFraudMapperTest {
    public static final String CUSTOMER_REF_ID = "CUSTOMER_REF_ID";
    public static final BigDecimal AMOUNT = BigDecimal.TEN;
    public static final String FIRST_NAME = "John";
    public static final String LAST_NAME = "Wick";
    private static final String FIID = "CA000621";
    private static final String LEGAL_NAME = "Company legal name";
    private static final String EXTERNAL_REF_ID = "externalRefId";
    private static final LocalDateTime EXPIRY_DATE = LocalDateTime.now();
    private static final LocalDateTime CREATED_DATE = LocalDateTime.now().minusDays(10);
    private static final LocalDateTime UPDATED_DATE = LocalDateTime.now().minusDays(5);
    private static final String REQUEST_REFERENCE = "request_reference";
    private static final String ADDITIONAL_NOTES = "some test additional notes";

    @Autowired
    private RequestForPaymentFraudMapper mapper;

    @ParameterizedTest
    @ValueSource(strings = {
        "10.00",
        "10.0",
        "10",
        "10.001523",
        "10.000000",
        "0.01",
        "*********.12"
    })
    public void requestBlockedForFraud(String amount) {
        NotificationDetails notificationDetails = createNotificationDetails();
        Payments requestPayments = createPaymentsWithValues(amount);
        Customers customers = createCustomers();
        assertNotNull(mapper);

        BigDecimal decimalValue = new BigDecimal(amount);
        DecimalFormat decimalFormat = new DecimalFormat("#0.00");

        PushNotificationRequest result = mapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(FIID, requestPayments, notificationDetails, requestPayments.getStatus(), customers);
        assertNotNull(result);
        assertEquals(EventType.FRAUD, result.getEventType());
        assertEquals(EventSubType.REQUEST, result.getEventSubType());
        assertEquals(EventReason.BLOCKED, result.getEventReason());
        assertEquals(EXTERNAL_REF_ID, result.getEvent().getTransaction().getRefId());
        assertEquals(REQUEST_REFERENCE, result.getEvent().getTransaction().getNetworkRefId());
        assertEquals(Status.CANCELLED, result.getEvent().getTransaction().getStatus());
        assertEquals(decimalFormat.format(decimalValue), result.getEvent().getTransaction().getAmount());
        assertEquals(FIID, result.getEvent().getTransaction().getCreditorAgent().getRefId());
        assertEquals(CUSTOMER_REF_ID, result.getEvent().getTransaction().getCreditor().getRefId());
        assertEquals(FIRST_NAME + " " + LAST_NAME, result.getEvent().getTransaction().getCreditor().getName());
        assertEquals(TransactionType.REQUEST_PAYMENT, result.getEvent().getTransaction().getTransactionType());
        assertEquals(DateUtil.toOffsetDateTime(EXPIRY_DATE), result.getEvent().getTransaction().getExpiryDate());
        assertEquals(DateUtil.toOffsetDateTime(UPDATED_DATE), result.getEvent().getTransaction().getUpdatedDate());
        assertEquals(DateUtil.toOffsetDateTime(CREATED_DATE), result.getEvent().getTransaction().getCreatedDate());
        assertEquals(DateUtil.getCurrentUTCDateTime().getYear(), result.getEventDate().getYear());
        assertEquals(DateUtil.getCurrentUTCDateTime().getMonth(), result.getEventDate().getMonth());
        assertEquals(DateUtil.getCurrentUTCDateTime().getDayOfMonth(), result.getEventDate().getDayOfMonth());
        assertEquals(DateUtil.getCurrentUTCDateTime().getHour(), result.getEventDate().getHour());
        assertEquals(ADDITIONAL_NOTES, result.getEvent().getTransaction().getFraud().getNote());
    }

    @Test
    public void requestBlockedForFraud_null_values_success() {
        NotificationDetails notificationDetails = createNotificationDetails();
        Payments requestPayments = createPaymentsWithValues("10.0");
        Customers customers = createCustomers();
        requestPayments.setExternalRefId(null);
        requestPayments.setCustomerRefId(null);
        notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getRequestForPaymentFraudDetails().setAdditionalNotes(null);
        notificationDetails.getEventPayload().getRequestForPayments().setRequestReference(null);
        notificationDetails.getEventPayload().getRequestForPayments().setStatus(null);
        customers.setNameJson(null);

        PushNotificationRequest result = mapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(null, requestPayments, notificationDetails, requestPayments.getStatus(), customers);
        assertNotNull(result);
        assertEquals(EventType.FRAUD, result.getEventType());
        assertEquals(EventSubType.REQUEST, result.getEventSubType());
        assertEquals(EventReason.BLOCKED, result.getEventReason());
        assertNull(result.getEvent().getTransaction().getRefId());
        assertNull(result.getEvent().getTransaction().getNetworkRefId());
        assertNull(result.getEvent().getTransaction().getStatus());
        assertNull(result.getEvent().getTransaction().getCreditorAgent());
        assertNull(result.getEvent().getTransaction().getCreditor().getRefId());
        assertNull(result.getEvent().getTransaction().getCreditor().getName());
        assertNotNull(result.getEvent().getTransaction().getTransactionType());
        assertNotNull(result.getEvent().getTransaction().getExpiryDate());
        assertNotNull(result.getEvent().getTransaction().getUpdatedDate());
        assertNotNull(result.getEvent().getTransaction().getCreatedDate());
        assertEquals(DateUtil.getCurrentUTCDateTime().getYear(), result.getEventDate().getYear());
        assertEquals(DateUtil.getCurrentUTCDateTime().getMonth(), result.getEventDate().getMonth());
        assertEquals(DateUtil.getCurrentUTCDateTime().getDayOfMonth(), result.getEventDate().getDayOfMonth());
        assertEquals(DateUtil.getCurrentUTCDateTime().getHour(), result.getEventDate().getHour());
        assertNull(result.getEvent().getTransaction().getFraud().getNote());
    }

    @Test
    public void requestBlockedForFraudBusinessCustomer() {
        NotificationDetails notificationDetails = createNotificationDetails();
        Payments requestPayments = createRequestPayments();
        Customers customers = createCustomers();
        customers.setTypeCd(CustomerCodeType.SMALL_BUSINESS);
        assertNotNull(mapper);

        PushNotificationRequest result = mapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(FIID, requestPayments, notificationDetails, requestPayments.getStatus(), customers);
        assertNotNull(result);
        assertEquals(LEGAL_NAME, result.getEvent().getTransaction().getCreditor().getName());
    }

    @Test
    public void requestBlockedForFraudFulfilledStatus() {
        NotificationDetails notificationDetails = createNotificationDetails();
        notificationDetails.getEventPayload().getRequestForPayments().setStatus(RequestForPaymentStatus.AVAILABLE);
        Payments requestPayments = createRequestPayments();
        Customers customers = createCustomers();
        assertNotNull(mapper);

        PushNotificationRequest result = mapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(FIID, requestPayments, notificationDetails, requestPayments.getStatus(), customers);
        assertNotNull(result);
        assertEquals(Status.AVAILABLE, result.getEvent().getTransaction().getStatus());
    }

    @Test
    public void requestUnblocked() {
        NotificationDetails notificationDetails = createNotificationDetails();
        notificationDetails.setEvent(NotificationDetails.EventEnum.PAYMENT_FRAUD_UNBLOCKED);
        notificationDetails.getEventPayload().getRequestForPayments().setStatus(RequestForPaymentStatus.AVAILABLE);
        Payments requestPayments = createRequestPayments();
        Customers customers = createCustomers();
        assertNotNull(mapper);

        PushNotificationRequest result = mapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(FIID, requestPayments, notificationDetails, requestPayments.getStatus(), customers);
        assertNotNull(result);
        assertEquals(EventReason.UNBLOCKED, result.getEventReason());
    }


    @SneakyThrows
    private Customers createCustomers() {
        Customers res = new Customers();
        res.setTypeCd(CustomerCodeType.INDIVIDUAL);
        res.setNameJson(JsonUtil.toString(Map.of("individualNameDetailDto", Map.of("firstName", FIRST_NAME, "lastName", LAST_NAME),
                "businessNameDetailDto", Map.of("legalName", LEGAL_NAME))));
        return res;
    }

    private NotificationDetails createNotificationDetails() {
        NotificationDetails res = new NotificationDetails();
        res.setEvent(NotificationDetails.EventEnum.REQUEST_FOR_PAYMENT_FRAUD_BLOCKED);
        EventDetails eventPayload = new EventDetails();
        RequestForPaymentDetails requestForPayments = new RequestForPaymentDetails();
        requestForPayments.setStatus(RequestForPaymentStatus.CANCELLED);
        requestForPayments.setRequestReference(REQUEST_REFERENCE);
        eventPayload.setRequestForPayments(requestForPayments);
        FraudDetails fraudDetails = new FraudDetails();
        FraudInformation fraudInfoPayload = new FraudInformation();
        RequestforPaymentFraudDetails requestForPaymentFraudDetails = new RequestforPaymentFraudDetails();
        requestForPaymentFraudDetails.setAdditionalNotes(ADDITIONAL_NOTES);
        fraudInfoPayload.setRequestForPaymentFraudDetails(requestForPaymentFraudDetails);
        fraudDetails.setFraudInfoPayload(fraudInfoPayload);
        eventPayload.setFraud(fraudDetails);
        res.setEventPayload(eventPayload);
        return res;
    }

    private Payments createPaymentsWithValues(String amount) {
        BigDecimal value = new BigDecimal(amount);
        Payments res = new Payments();
        res.setExternalRefId(EXTERNAL_REF_ID);
        res.setAmount(value);
        res.setStatus("DEPOSIT_INITIATED");
        res.setCustomerRefId(CUSTOMER_REF_ID);
        res.setExpiryDate(EXPIRY_DATE);
        res.setUpdatedOn(UPDATED_DATE);
        res.setCreatedOn(CREATED_DATE);
        return res;
    }


    private Payments createRequestPayments() {
        Payments res = new Payments();
        res.setExternalRefId(EXTERNAL_REF_ID);
        res.setAmount(AMOUNT);
        res.setCustomerRefId(CUSTOMER_REF_ID);
        res.setExpiryDate(EXPIRY_DATE);
        res.setUpdatedOn(UPDATED_DATE);
        res.setCreatedOn(CREATED_DATE);
        res.setStatus(PaymentStatus.AVAILABLE.getValue());
        res.setNetworkPaymentType(NetworkPaymentType.REQUEST_FOR_PAYMENT.name());
        res.setTypeCd(PaymentCdType.REQUEST);

        return res;
    }
}