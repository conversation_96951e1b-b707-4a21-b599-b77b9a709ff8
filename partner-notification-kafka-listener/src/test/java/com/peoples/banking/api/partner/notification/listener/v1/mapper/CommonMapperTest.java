package com.peoples.banking.api.partner.notification.listener.v1.mapper;


import com.peoples.banking.domain.push.notification.model.EventReason;
import com.peoples.banking.domain.push.notification.model.EventSubType;
import com.peoples.banking.domain.push.notification.model.EventType;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.domain.push.notification.model.PaymentEvent;
import com.peoples.banking.domain.push.notification.model.Status;
import com.peoples.banking.partner.domain.interac.notification.model.RequestForPaymentStatus;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {CommonMapperImpl.class})
public class CommonMapperTest {

    @Autowired
    private CommonMapper mapper;

    @CsvSource({"REGISTRATION_ACCOUNT_ALIAS_COMPLETE,STATUS_UPDATED",
            "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED,STATUS_UPDATED",
            "REGISTRATION_ACCOUNT_ALIAS_UPDATED,STATUS_UPDATED",
            "PAYMENT_CREATED,STATUS_UPDATED",
            "PAYMENT_COMPLETED,STATUS_UPDATED",
            "PAYMENT_FRAUD_DELAY,DELAY",
            "PAYMENT_FRAUD_BLOCKED,BLOCKED",
            "PAYMENT_FRAUD_UNBLOCKED,UNBLOCKED",
            "PAYMENT_DEPOSIT_INTERRUPTED,INTERRUPTED",
            "PAYMENT_CANCELLED,STATUS_UPDATED",
            "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK,ROLLEDBACK",
            "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED,BLOCKED",
            "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED,UNBLOCKED",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER,RESPONDER_BLOCKED_REQUESTER",
            "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER,RESPONDER_UNBLOCKED_REQUESTER",
            "REQUEST_FOR_PAYMENT_CANCELLED,STATUS_UPDATED",
            "FRAUD_PAYMENT_REPORTED,REPORTED",
            "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED,STATUS_UPDATED",
            "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST,FUNDS_RECOVERY_REQUEST",
            "FRAUD_PAYMENT_FUNDS_RECOVERED,FUNDS_RECOVERED",
            "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED,REPORTED",
            "FRAUD_REQUEST_FOR_PAYMENT_REPORTED,REPORTED",
            "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED,STATUS_UPDATED",
            "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED,LINKED_EXPIRED",
            "REGISTRATION_ACCOUNT_ALIAS_EXPIRED,STATUS_UPDATED",
            "REGISTRATION_ACCOUNT_ALIAS_BLOCKED,BLOCKED",
            "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED,UNBLOCKED",
            "PAYMENT_AVAILABLE_FOR_PICKUP,STATUS_UPDATED",
            "PAYMENT_NOTIFICATION_FAILURE,NOTIFICATION_FAILED",
            "PAYMENT_DECLINED,STATUS_UPDATED",
            "PAYMENT_EXPIRY_REMINDER,REMINDER",
            "PAYMENT_EXPIRED,STATUS_UPDATED",
            "PAYMENT_AUTHENTICATION_FAILURE,STATUS_UPDATED",
            "PAYMENT_DEPOSIT_FAILURE,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE,NOTIFICATION_FAILED",
            "REQUEST_FOR_PAYMENT_AVAILABLE,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_DECLINED,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_FULFILLED,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER,REMINDER",
            "REQUEST_FOR_PAYMENT_EXPIRED,STATUS_UPDATED",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER,RESPONDER_UNBLOCKED_REQUESTER"})
    @ParameterizedTest
    public void eventEnumToEventReason_Success(NotificationDetails.EventEnum eventEnum, EventReason eventReason) {
        assertEquals(eventReason, mapper.eventEnumToEventReason(eventEnum));
    }

    @CsvSource({ "REGISTRATION_ACCOUNT_ALIAS_COMPLETE,REGISTRATION",
            "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED,REGISTRATION",
            "REGISTRATION_ACCOUNT_ALIAS_UPDATED,REGISTRATION",
            "PAYMENT_CREATED,TRANSACTION",
            "PAYMENT_COMPLETED,TRANSACTION",
            "PAYMENT_FRAUD_DELAY,FRAUD",
            "PAYMENT_FRAUD_BLOCKED,FRAUD",
            "PAYMENT_FRAUD_UNBLOCKED,FRAUD",
            "PAYMENT_DEPOSIT_INTERRUPTED,TRANSACTION",
            "PAYMENT_CANCELLED,TRANSACTION",
            "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK,TRANSACTION",
            "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED,FRAUD",
            "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED,FRAUD",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER,TRANSACTION",
            "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER,TRANSACTION",
            "REQUEST_FOR_PAYMENT_CANCELLED,TRANSACTION",
            "FRAUD_PAYMENT_REPORTED,FRAUD",
            "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED,FRAUD",
            "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST,FRAUD",
            "FRAUD_PAYMENT_FUNDS_RECOVERED,FRAUD",
            "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED,FRAUD",
            "FRAUD_REQUEST_FOR_PAYMENT_REPORTED,FRAUD",
            "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED,FRAUD",
            "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED,REGISTRATION",
            "REGISTRATION_ACCOUNT_ALIAS_EXPIRED,REGISTRATION",
            "REGISTRATION_ACCOUNT_ALIAS_BLOCKED,FRAUD",
            "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED,FRAUD",
            "PAYMENT_AVAILABLE_FOR_PICKUP,TRANSACTION",
            "PAYMENT_NOTIFICATION_FAILURE,TRANSACTION",
            "PAYMENT_DECLINED,TRANSACTION",
            "PAYMENT_EXPIRY_REMINDER,TRANSACTION",
            "PAYMENT_EXPIRED,TRANSACTION",
            "PAYMENT_AUTHENTICATION_FAILURE,TRANSACTION",
            "PAYMENT_DEPOSIT_FAILURE,TRANSACTION",
            "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE,TRANSACTION",
            "REQUEST_FOR_PAYMENT_AVAILABLE,TRANSACTION",
            "REQUEST_FOR_PAYMENT_DECLINED,TRANSACTION",
            "REQUEST_FOR_PAYMENT_FULFILLED,TRANSACTION",
            "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE,TRANSACTION",
            "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER,TRANSACTION",
            "REQUEST_FOR_PAYMENT_EXPIRED,TRANSACTION",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER,TRANSACTION"})
    @ParameterizedTest
    public void eventEnumToEventType_Success(NotificationDetails.EventEnum eventEnum, EventType eventType) {
        assertEquals(eventType, mapper.eventEnumToEventType(eventEnum));
    }

    @CsvSource({"REGISTRATION_ACCOUNT_ALIAS_COMPLETE,ALIAS",
            "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED,ALIAS",
            "REGISTRATION_ACCOUNT_ALIAS_UPDATED,ALIAS",
            "PAYMENT_CREATED,PAYMENT",
            "PAYMENT_COMPLETED,PAYMENT",
            "PAYMENT_FRAUD_DELAY,PAYMENT",
            "PAYMENT_FRAUD_BLOCKED,PAYMENT",
            "PAYMENT_FRAUD_UNBLOCKED,PAYMENT",
            "PAYMENT_DEPOSIT_INTERRUPTED,PAYMENT",
            "PAYMENT_CANCELLED,PAYMENT",
            "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK,PAYMENT",
            "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED,REQUEST",
            "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED,REQUEST",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER,REQUEST",
            "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER,REQUEST",
            "REQUEST_FOR_PAYMENT_CANCELLED,REQUEST",
            "FRAUD_PAYMENT_REPORTED,PAYMENT",
            "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED,PAYMENT",
            "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST,PAYMENT",
            "FRAUD_PAYMENT_FUNDS_RECOVERED,PAYMENT",
            "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED,ALIAS",
            "FRAUD_REQUEST_FOR_PAYMENT_REPORTED,REQUEST",
            "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED,REQUEST",
            "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED,ALIAS",
            "REGISTRATION_ACCOUNT_ALIAS_EXPIRED,ALIAS",
            "REGISTRATION_ACCOUNT_ALIAS_BLOCKED,ALIAS",
            "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED,ALIAS",
            "PAYMENT_AVAILABLE_FOR_PICKUP,PAYMENT",
            "PAYMENT_NOTIFICATION_FAILURE,PAYMENT",
            "PAYMENT_DECLINED,PAYMENT",
            "PAYMENT_EXPIRY_REMINDER,PAYMENT",
            "PAYMENT_EXPIRED,PAYMENT",
            "PAYMENT_AUTHENTICATION_FAILURE,PAYMENT",
            "PAYMENT_DEPOSIT_FAILURE,PAYMENT",
            "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE,REQUEST",
            "REQUEST_FOR_PAYMENT_AVAILABLE,REQUEST",
            "REQUEST_FOR_PAYMENT_DECLINED,REQUEST",
            "REQUEST_FOR_PAYMENT_FULFILLED,REQUEST",
            "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE,REQUEST",
            "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER,REQUEST",
            "REQUEST_FOR_PAYMENT_EXPIRED,REQUEST",
            "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER,REQUEST"})
    @ParameterizedTest
    public void eventEnumToEventSubType_Success(NotificationDetails.EventEnum eventEnum, EventSubType eventSubType) {
        assertEquals(eventSubType, mapper.eventEnumToEventSubType(eventEnum));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "AVAILABLE",
            "DEPOSIT_INITIATED",
            "DEPOSIT_PENDING",
            "DEPOSIT_COMPLETE",
            "DEPOSIT_FAILED",
            "DECLINED",
            "CANCELLED",
            "EXPIRED"
    })
    public void requestForPaymentStatusMapper_Success(String status) {
        RequestForPaymentStatus requestStatus;
        if (status.equals("DEPOSIT_INITIATED") || status.equals("DEPOSIT_PENDING")) {
            requestStatus = RequestForPaymentStatus.FULFILLED;
        } else {
            requestStatus = RequestForPaymentStatus.fromValue(status);
        }
        PaymentEvent paymentEvent = new PaymentEvent();
        paymentEvent.setStatus(Status.fromValue(status));
        assertEquals(Status.fromValue(status), mapper.interacPaymentStatusToPtcPaymentStatus(requestStatus, paymentEvent));
    }
}
