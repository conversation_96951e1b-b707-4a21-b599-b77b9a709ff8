package com.peoples.banking.api.partner.notification.listener.v1.dto;

import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.payment.entity.Payments;
import lombok.Data;

@Data
public class RequestDataDto {
    private NotificationDetails message;
    private ServiceAccountResponse serviceAccountResponse;
    private Payments payments;
    private Customers customers;

    public RequestDataDto(NotificationDetails message) {
        this.message = message;
    }

    public String getSaRef() {
        return serviceAccountResponse != null ? serviceAccountResponse.getRefId() : null;
    }
}
