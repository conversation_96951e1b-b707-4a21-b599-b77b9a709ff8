package com.peoples.banking.api.partner.notification.listener.v1.mapper;

import com.peoples.banking.domain.push.notification.model.*;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.RequestForPaymentStatus;
import com.peoples.banking.persistence.customer.entity.CustomerCodeType;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.util.api.common.JsonUtil;
import java.util.Map;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public abstract class CommonMapper {
    //account alias registrations
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_COMPLETE", target = "STATUS_UPDATED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UPDATED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED", target = "LINKED_EXPIRED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_EXPIRED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_BLOCKED", target = "BLOCKED")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED", target = "UNBLOCKED")
    //payments
    @ValueMapping(source = "PAYMENT_CREATED", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_COMPLETED", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_FRAUD_DELAY", target = "DELAY")
    @ValueMapping(source = "PAYMENT_FRAUD_BLOCKED", target = "BLOCKED")
    @ValueMapping(source = "PAYMENT_FRAUD_UNBLOCKED", target = "UNBLOCKED")
    @ValueMapping(source = "PAYMENT_DEPOSIT_INTERRUPTED", target = "INTERRUPTED")
    @ValueMapping(source = "PAYMENT_CANCELLED", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK", target = "ROLLEDBACK")
    @ValueMapping(source = "PAYMENT_AVAILABLE_FOR_PICKUP", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_NOTIFICATION_FAILURE", target = "NOTIFICATION_FAILED")
    @ValueMapping(source = "PAYMENT_DECLINED", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_EXPIRY_REMINDER", target = "REMINDER")
    @ValueMapping(source = "PAYMENT_EXPIRED", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_AUTHENTICATION_FAILURE", target = "STATUS_UPDATED")
    @ValueMapping(source = "PAYMENT_DEPOSIT_FAILURE", target = "STATUS_UPDATED")
    //requests for payment
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED", target = "BLOCKED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED", target = "UNBLOCKED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER", target = "RESPONDER_BLOCKED_REQUESTER")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER", target = "RESPONDER_UNBLOCKED_REQUESTER")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_CANCELLED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE", target = "NOTIFICATION_FAILED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_AVAILABLE", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DECLINED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FULFILLED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER", target = "REMINDER")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_EXPIRED", target = "STATUS_UPDATED")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER", target = "RESPONDER_UNBLOCKED_REQUESTER")
    //fraud
    @ValueMapping(source = "FRAUD_PAYMENT_REPORTED", target = "REPORTED")
    @ValueMapping(source = "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED", target = "STATUS_UPDATED")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST", target = "FUNDS_RECOVERY_REQUEST")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERED", target = "FUNDS_RECOVERED")
    @ValueMapping(source = "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED", target = "REPORTED")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_REPORTED", target = "REPORTED")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED", target = "STATUS_UPDATED")
    @ValueMapping( source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL )
    @Named("eventEnumToEventReason")
    protected abstract EventReason eventEnumToEventReason(NotificationDetails.EventEnum event);

    //account alias registrations
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_COMPLETE", target = "REGISTRATION")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED", target = "REGISTRATION")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UPDATED", target = "REGISTRATION")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED", target = "REGISTRATION")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_EXPIRED", target = "REGISTRATION")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_BLOCKED", target = "FRAUD")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED", target = "FRAUD")
    //payments
    @ValueMapping(source = "PAYMENT_CREATED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_COMPLETED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_FRAUD_DELAY", target = "FRAUD")
    @ValueMapping(source = "PAYMENT_FRAUD_BLOCKED", target = "FRAUD")
    @ValueMapping(source = "PAYMENT_FRAUD_UNBLOCKED", target = "FRAUD")
    @ValueMapping(source = "PAYMENT_DEPOSIT_INTERRUPTED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_CANCELLED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_AVAILABLE_FOR_PICKUP", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_NOTIFICATION_FAILURE", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_DECLINED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_EXPIRY_REMINDER", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_EXPIRED", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_AUTHENTICATION_FAILURE", target = "TRANSACTION")
    @ValueMapping(source = "PAYMENT_DEPOSIT_FAILURE", target = "TRANSACTION")
    //requests for payment
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED", target = "FRAUD")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED", target = "FRAUD")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_CANCELLED", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_AVAILABLE", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DECLINED", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FULFILLED", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_EXPIRED", target = "TRANSACTION")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER", target = "TRANSACTION")
    //fraud
    @ValueMapping(source = "FRAUD_PAYMENT_REPORTED", target = "FRAUD")
    @ValueMapping(source = "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED", target = "FRAUD")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST", target = "FRAUD")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERED", target = "FRAUD")
    @ValueMapping(source = "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED", target = "FRAUD")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_REPORTED", target = "FRAUD")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED", target = "FRAUD")
    @ValueMapping( source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL )
    @Named("eventEnumToEventType")
    protected abstract EventType eventEnumToEventType(NotificationDetails.EventEnum event);

    //account alias registrations
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_COMPLETE", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UPDATED", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_EXPIRED", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_BLOCKED", target = "ALIAS")
    @ValueMapping(source = "REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED", target = "ALIAS")
    //payments
    @ValueMapping(source = "PAYMENT_CREATED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_COMPLETED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_FRAUD_DELAY", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_FRAUD_BLOCKED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_FRAUD_UNBLOCKED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_DEPOSIT_INTERRUPTED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_CANCELLED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_AVAILABLE_FOR_PICKUP", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_NOTIFICATION_FAILURE", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_DECLINED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_EXPIRY_REMINDER", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_EXPIRED", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_AUTHENTICATION_FAILURE", target = "PAYMENT")
    @ValueMapping(source = "PAYMENT_DEPOSIT_FAILURE", target = "PAYMENT")
    //requests for payment
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_CANCELLED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_AVAILABLE", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DECLINED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FULFILLED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_EXPIRED", target = "REQUEST")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER", target = "REQUEST")
    //fraud
    @ValueMapping(source = "FRAUD_PAYMENT_REPORTED", target = "PAYMENT")
    @ValueMapping(source = "FRAUD_PAYMENT_FRAUD_STATUS_UPDATED", target = "PAYMENT")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST", target = "PAYMENT")
    @ValueMapping(source = "FRAUD_PAYMENT_FUNDS_RECOVERED", target = "PAYMENT")
    @ValueMapping(source = "FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED", target = "ALIAS")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_REPORTED", target = "REQUEST")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED", target = "REQUEST")
    @ValueMapping( source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL )
    @Named("eventEnumToEventSubType")
    protected abstract EventSubType eventEnumToEventSubType(NotificationDetails.EventEnum event);


    // Request Status
    @ValueMapping(source = "AVAILABLE", target = "AVAILABLE")
    //TODO - remove or fix
//    @ValueMapping(source = "DEPOSIT_INITIATED", target = "DEPOSIT_INITIATED")
//    @ValueMapping(source = "DEPOSIT_PENDING", target = "DEPOSIT_PENDING")
    @ValueMapping(source = "DEPOSIT_COMPLETE", target = "DEPOSIT_COMPLETE")
    @ValueMapping(source = "DEPOSIT_FAILED", target = "DEPOSIT_FAILED")
    @ValueMapping(source = "DECLINED", target = "DECLINED")
    @ValueMapping(source = "CANCELLED", target = "CANCELLED")
    @ValueMapping(source = "EXPIRED", target = "EXPIRED")
    @ValueMapping( source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL )
    protected abstract Status interacPaymentStatusToPtcPaymentStatus(RequestForPaymentStatus status, @Context PaymentEvent paymentEvent);

    //    !!!CHECK THESE MAPPINGS!!!!!!!!!!!
    //requests for payment
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_BLOCKED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_CANCELLED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_REPORTED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_AVAILABLE", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DECLINED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_FULFILLED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_EXPIRED", target = "REQUEST_PAYMENT")
    @ValueMapping(source = "REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER", target = "REQUEST_PAYMENT")
    @ValueMapping( source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL )
    @Named("eventEnumToRequestPaymentType")
    protected abstract TransactionType eventEnumToRequestPaymentType(NotificationDetails.EventEnum event);

    @BeforeMapping
    protected Status processInteracStatus(RequestForPaymentStatus status, @Context PaymentEvent paymentEvent) {
        if (status.equals(RequestForPaymentStatus.FULFILLED)) {
            return paymentEvent.getStatus();
        }

        return Status.fromValue(status.getValue());
    }

    @Named("customersNameConverter")
    @SneakyThrows
    protected String customersNameConverter(Customers customers) {
        if (customers == null) {
            return null;
        }
        if (StringUtils.isNotBlank(customers.getNameJson())) {
            Map<String, Object> nameMap = JsonUtil.toObject(customers.getNameJson(), Map.class);
            if (customers.getTypeCd() == CustomerCodeType.INDIVIDUAL) {
                Map<String, Object> individualNameMap = (Map<String, Object>) nameMap.get("individualNameDetailDto");
                if (individualNameMap != null) {
                    return String.join(" ", (String) individualNameMap.get("firstName"), (String) individualNameMap.get("lastName"));
                }
            }
            if (customers.getTypeCd() == CustomerCodeType.CORPORATION || customers.getTypeCd() == CustomerCodeType.SMALL_BUSINESS) {
                Map<String, Object> businessNameMap = (Map<String, Object>) nameMap.get("businessNameDetailDto");
                if (businessNameMap != null) {
                    return (String) businessNameMap.get("legalName");
                }
            }
        }
        return null;
    }

}