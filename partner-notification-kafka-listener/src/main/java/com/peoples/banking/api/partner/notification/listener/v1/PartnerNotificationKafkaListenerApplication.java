package com.peoples.banking.api.partner.notification.listener.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = {KafkaAutoConfiguration.class})
@ComponentScan(basePackages = {
        "com.peoples.banking.api.common.kafka.listener",
        "com.peoples.banking.api.partner.notification.listener",
        "com.peoples.banking.util",
        "com.peoples.banking.adapter.pb.serviceaccount",
        "com.peoples.banking.adapter.pb.partnerpushnotification",
        "com.peoples.banking.persistence.customer.repository",
        "com.peoples.banking.persistence.payment.repository"
})
@EntityScan(basePackages= {"com.peoples.banking.persistence.payment","com.peoples.banking.persistence.customer"})
public class PartnerNotificationKafkaListenerApplication {

  public static void main(String[] args) {
    SpringApplication.run(PartnerNotificationKafkaListenerApplication.class, args);
  }

}