package com.peoples.banking.api.partner.notification.listener.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

@Configuration
public class PartnerNotificationKafkaListenerFilterConfig {

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;

  private static final String systemName = "partnerPushNotificationKafkaListener";

  /**
   * add logging filter
   *
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> partnerPushNotificationKafkaListenerLoggingFilter() {
    LoggingFilter pushNotificationLoggingFilter = new LoggingFilter();
    pushNotificationLoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    pushNotificationLoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    pushNotificationLoggingFilter.setSystemName(systemName);
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(pushNotificationLoggingFilter);
    registrationBean.addUrlPatterns(APICommonUtilConstant.ROOT_FILTER_API_URL);
    return registrationBean;
  }

  /**
   * create request context listener. It is required for Request Context Holder
   *
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }
}
