package com.peoples.banking.api.partner.notification.listener.v1.service;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.adapter.base.type.ServiceAccountEndpointType;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.partnerpushnotification.PartnerPushNotificationAdapter;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerNotRetryableException;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.partner.notification.listener.v1.dto.RequestDataDto;
import com.peoples.banking.api.partner.notification.listener.v1.mapper.RequestForPaymentFraudMapper;
import com.peoples.banking.domain.push.notification.model.PushNotificationRequest;
import com.peoples.banking.domain.serviceaccount.model.ApiEndpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountConfiguration;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.RequestForPaymentDetails;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.partner.domain.interac.notification.model.*;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Log4j2
public class PartnerNotificationKafkaListenerService {

    @Value("${sys.pn.interac.peoples.fiid}")
    private String fiid;
    @Autowired
    private PaymentsRepository paymentsRepository;
    @Autowired
    private ServiceAccountAdapter serviceAccountAdapter;
    @Autowired
    private PartnerPushNotificationAdapter partnerPushNotificationAdapter;
    @Autowired
    private AliasesRepository aliasesRepository;
    @Autowired
    private CustomersRepository customersRepository;
    @Autowired
    private RequestForPaymentFraudMapper requestForPaymentFraudMapper;

    @Transactional(readOnly = true, noRollbackFor = Exception.class)
    @PerfLogger
    public void consumeMessage(NotificationDetails message) {
        if (message.getEventPayload() == null || message.getEvent() == null) {
            log.warn("empty event details or event type");
            throw new KafkaListenerNotRetryableException("empty event payload or event type");
        }

        try {
            log.info("{} for {} received", extractReference(message), message.getEvent());
            if (shouldSkipProcess(message)) {
                log.info("Skip SA search");
                return;
            }
            RequestDataDto rd = populateRequestData(message);
            if (!isPushNotificationEnabled(rd)) {
                log.debug("{} is not delivered, {} Push Notification feature is DISABLED", message.getEvent(), rd.getSaRef());
            } else {
                process(rd);
            }
        } catch (KafkaListenerRetryableException | KafkaListenerNotRetryableException e) {
            throw e;
        } catch (Exception e) {
            throw new KafkaListenerNotRetryableException("error while processing push notification", e);
        }
    }

    private RequestDataDto populateRequestData(NotificationDetails message) {
        RequestDataDto requestDataDto = new RequestDataDto(message);
        requestDataDto.setCustomers(retrieveCustomer(message));
        requestDataDto.setPayments(retrievePayments(message));
        requestDataDto.setServiceAccountResponse(retrieveServiceAccount(requestDataDto));
        return requestDataDto;
    }

    private ServiceAccountResponse retrieveServiceAccount(RequestDataDto requestDataDto) {
        if (requestDataDto.getPayments() != null) {
            return getServiceAccount(requestDataDto.getPayments().getServiceAccountRefId());
        }
        if (requestDataDto.getCustomers() != null) {
            return getServiceAccount(requestDataDto.getCustomers().getServiceAccountRefId());
        }
        return null;
    }

    private Payments retrievePayments(NotificationDetails message) {
        return firstPresent(
                Optional.of(message.getEventPayload()).map(EventDetails::getRequestForPayments).map(RequestForPaymentDetails::getRequestReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getFraud).map(FraudDetails::getFraudInfoPayload)
                        .map(FraudInformation::getRequestForPaymentFraudDetails).map(RequestforPaymentFraudDetails::getRequestForPayment)
                        .map(RequestForPaymentDetails::getRequestReference)
        ).flatMap(ref -> paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(ref, PaymentCdType.REQUEST))
            .orElse(null);
    }

    private Customers retrieveCustomer(NotificationDetails message) {
        return Optional.of(message.getEventPayload()).map(EventDetails::getAccountAliasRegistrations)
                .flatMap(details -> aliasesRepository.findByNetworkAliasRefIdAndExternalRefId(details.getAccountAliasReference(), details.getParticipantAccountAliasReference()))
                .flatMap(alias -> customersRepository.findById(alias.getCustomerId()))
                .orElse(null);
    }

    private boolean shouldSkipProcess(NotificationDetails message) {
        switch (message.getEvent()) {
            case REGISTRATION_ROUTED_NOTIFICATION_COMPLETE:
            case REGISTRATION_ROUTED_NOTIFICATION_DEACTIVATED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRY_REMINDER:
            case PAYMENT_SCHEDULE_CANCELLED:
            case PAYMENT_SCHEDULE_CREATED:
            case PAYMENT_SCHEDULE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_CANCELLED:
            case PAYMENT_SCHEDULE_INSTANCE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case PAYMENT_SCHEDULE_RECIPIENT_DEREGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_RECIPIENT_REGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_UPDATE_FAILURE:
            case PAYMENT_SCHEDULE_UPDATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_CREATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case REQUEST_FOR_PAYMENT_SCHEDULE_UPDATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_CANCELLED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_FAILED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_FAILED:
            case REQUEST_FOR_PAYMENT_RETURN_AVAILABLE:
            case REQUEST_FOR_PAYMENT_RETURN_DECLINED:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE_MANUAL_RECLAIM:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_SUCCESSFUL:
            case REQUEST_FOR_PAYMENT_RETURN_EXPIRED:
                return true;
            default:
                return false;
        }
    }

    private String extractReference(NotificationDetails message) {
        return firstPresent(
                Optional.of(message.getEventPayload()).map(EventDetails::getRequestForPayments).map(RequestForPaymentDetails::getRequestReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getFraud).map(FraudDetails::getFraudInfoPayload)
                        .map(FraudInformation::getRequestForPaymentFraudDetails).map(RequestforPaymentFraudDetails::getRequestForPayment)
                        .map(RequestForPaymentDetails::getRequestReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getPayment).map(PaymentDetails::getPaymentReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getFraud).map(FraudDetails::getFraudInfoPayload)
                        .map(FraudInformation::getPaymentFraudDetails).map(PaymentFraudDetails::getPayment).map(PaymentDetails::getPaymentReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getFraud).map(FraudDetails::getFraudInfoPayload)
                        .map(FraudInformation::getAccountAliasFraudDetails).map(AccountAliasRegistrationFraudDetails::getAccountAliasRegistrations)
                        .map(AccountAliasRegistrationDetails::getAccountAliasReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getAccountAliasRegistrations).map(AccountAliasRegistrationDetails::getAccountAliasReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getRoutedNotificationRegistrations).map(RoutedNotificationRegistrationDetails::getRoutedNotificationReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getPaymentSchedule).map(PaymentScheduleDetails::getScheduleReference),
                Optional.of(message.getEventPayload()).map(EventDetails::getRequestToPaySchedule).map(RequestForPaymentScheduleDetails::getScheduleReference)
        ).orElse(null);
    }

    private void process(RequestDataDto requestDataDto) {
        switch (requestDataDto.getMessage().getEvent()) {
            case REQUEST_FOR_PAYMENT_FRAUD_BLOCKED:
            case REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED:
                processFraudRequestPaymentBlockedUnblockedMessage(requestDataDto);
                break;
            case PAYMENT_AUTHENTICATION_FAILURE:
            case PAYMENT_AUTO_RECLAIM_SUCCESSFUL:
            case PAYMENT_AVAILABLE_FOR_PICKUP:
            case PAYMENT_CANCELLED:
            case PAYMENT_COMPLETED:
            case PAYMENT_CREATED:
            case PAYMENT_DECLINED:
            case PAYMENT_DEPOSIT_FAILURE:
            case PAYMENT_DEPOSIT_INTERRUPTED:
            case PAYMENT_EXPIRED:
            case PAYMENT_EXPIRY_REMINDER:
            case PAYMENT_FRAUD_BLOCKED:
            case PAYMENT_FRAUD_UNBLOCKED:
            case PAYMENT_FRAUD_DELAY:
            case PAYMENT_MANUAL_RECLAIM:
            case PAYMENT_MANUAL_RECLAIM_REMINDER:
            case PAYMENT_NOTIFICATION_FAILURE:
            case PAYMENT_RESEND_AVAILABLE_FOR_PICKUP:
            case PAYMENT_INTERRUPTED_CANCELLATION_COMMITTED:
            case PAYMENT_INTERRUPTED_CANCELLATION_ROLLEDBACK:
            case PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK:
            case PAYMENT_INTERRUPTED_COMPLETION_ROLLEDBACK:
            case FRAUD_PAYMENT_FRAUD_STATUS_UPDATED:
            case FRAUD_PAYMENT_FUNDS_RECOVERED:
            case FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST:
            case FRAUD_PAYMENT_REPORTED:
            case FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED:
            case FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED:
            case FRAUD_REQUEST_FOR_PAYMENT_REPORTED:
            case REGISTRATION_ACCOUNT_ALIAS_BLOCKED:
            case REGISTRATION_ACCOUNT_ALIAS_COMPLETE:
            case REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED:
            case REGISTRATION_ACCOUNT_ALIAS_EXPIRED:
            case REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED:
            case REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED:
            case REGISTRATION_ACCOUNT_ALIAS_UPDATED:
            case REGISTRATION_ROUTED_NOTIFICATION_COMPLETE:
            case REGISTRATION_ROUTED_NOTIFICATION_DEACTIVATED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRY_REMINDER:
            case REQUEST_FOR_PAYMENT_AVAILABLE:
            case REQUEST_FOR_PAYMENT_CANCELLED:
            case REQUEST_FOR_PAYMENT_DECLINED:
            case REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE:
            case REQUEST_FOR_PAYMENT_EXPIRED:
            case REQUEST_FOR_PAYMENT_FULFILLED:
            case REQUEST_FOR_PAYMENT_MANUAL_REMINDER:
            case REQUEST_FOR_PAYMENT_MODIFIED:
            case REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE:
            case REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER:
            case REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER:
            case REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER:
            case REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER:
            case PAYMENT_SCHEDULE_CANCELLED:
            case PAYMENT_SCHEDULE_CREATED:
            case PAYMENT_SCHEDULE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_CANCELLED:
            case PAYMENT_SCHEDULE_INSTANCE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case PAYMENT_SCHEDULE_RECIPIENT_DEREGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_RECIPIENT_REGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_UPDATE_FAILURE:
            case PAYMENT_SCHEDULE_UPDATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_CREATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case REQUEST_FOR_PAYMENT_SCHEDULE_UPDATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_CANCELLED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_FAILED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_FAILED:
            case REQUEST_FOR_PAYMENT_RETURN_AVAILABLE:
            case REQUEST_FOR_PAYMENT_RETURN_DECLINED:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE_MANUAL_RECLAIM:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_SUCCESSFUL:
            case REQUEST_FOR_PAYMENT_RETURN_EXPIRED:
            case CUSTOMER_REQUEST_FOR_PAYMENT_UNSUBSCRIBE:
                logMessage(requestDataDto);
                break;
            default:
                log.warn("Invalid Push Notification Event {}", requestDataDto.getMessage().getEvent());
                throw new KafkaListenerNotRetryableException("payment reference is empty for notification details");
        }
    }

    private void processFraudRequestPaymentBlockedUnblockedMessage(RequestDataDto requestDataDto) {
        if (requestDataDto.getPayments() == null) {
            log.warn("payment {} not found for event {}", extractReference(requestDataDto.getMessage()), requestDataDto.getMessage().getEvent());
            throw new KafkaListenerNotRetryableException("payment not found");
        }
        try {
            Payments payments = requestDataDto.getPayments();
            if (!isPushNotificationEnabled(requestDataDto)) {
                log.debug("push notification disabled for SA {}, payment reference: {}", payments.getServiceAccountRefId(), requestDataDto.getPayments().getExternalRefId());
                return;
            }
            Optional<Customers> customerOpt = customersRepository.findByCustomerIdAndServiceAccountRefId(payments.getCustomerRefId(), payments.getServiceAccountRefId());
            if (customerOpt.isEmpty()) {
                log.warn("customer {} not found for event {}", payments.getCustomerRefId(), requestDataDto.getMessage().getEvent());
            }
            PushNotificationRequest notificationRequest = requestForPaymentFraudMapper.blockedUnblockedRequestForPaymentToPushNotificationRequest(fiid, payments, requestDataDto.getMessage(), payments.getStatus(), customerOpt.orElse(null));
            partnerPushNotificationAdapter.pushNotification(notificationRequest, createSaProfile(requestDataDto.getServiceAccountResponse()), requestDataDto.getPayments().getCustomerRefId());
        } catch (AdapterException e) {
            log.warn("Adapter error while sending push notification to SA: {} for payment {}", requestDataDto.getSaRef(), requestDataDto.getPayments().getExternalRefId());
            throw new KafkaListenerNotRetryableException(e);
        } catch (TimeoutException e) {
            log.warn("Timeout error while sending push notification to SA: {} for payment {}", requestDataDto.getSaRef(), requestDataDto.getPayments().getExternalRefId());
            throw new KafkaListenerRetryableException(e);
        } catch (TBDException e) {
            log.warn("TDB error while sending push notification to SA: {} for payment {}", requestDataDto.getSaRef(), requestDataDto.getPayments().getExternalRefId());
            throw new KafkaListenerRetryableException(e);
        } catch (ResponseException e) {
            log.warn("Response error while sending push notification to SA: {} for payment {}", requestDataDto.getSaRef(), requestDataDto.getPayments().getExternalRefId());
            throw new KafkaListenerNotRetryableException(e);
        }
    }

    private <T> Optional<T> firstPresent(Optional<T>... arr) {
        for (int i = 0; i < arr.length; i++) {
            Optional<T> opt = arr[i];
            if(opt.isPresent())
                return opt;
        }
        return Optional.empty();
    }

    private static ServiceAccountProfile createSaProfile(ServiceAccountResponse serviceAccount) {
        ServiceAccountProfile saProfile = new ServiceAccountProfile();
        saProfile.setApiToken(serviceAccount.getOutboundApiToken());
        saProfile.setAuthToken(serviceAccount.getOutboundApiAuthToken());
        saProfile.setEndpointUrl(Optional.ofNullable(serviceAccount.getApiEndpoints()).map(m->m.get(ServiceAccountEndpointType.PUSH_NOTIFICATION.toString())).map(ApiEndpoint::getUrl).orElse(null));
        saProfile.setAuthType(serviceAccount.getOutboundApiAuth() == ServiceAccountResponse.OutboundApiAuthEnum.BASIC_AUTH ? ServiceAccountAuthType.BASIC_AUTH : ServiceAccountAuthType.NO_AUTH);
        return saProfile;
    }

    private void logMessage(RequestDataDto requestDataDto) {
        log.debug("Implementation is not ready for event {}", requestDataDto.getMessage().getEvent());
    }
    private boolean isPushNotificationEnabled(RequestDataDto requestDataDto) {
        if (requestDataDto.getServiceAccountResponse() == null) {
            log.warn("SA is missing or implementation is not ready for event {}", requestDataDto.getMessage().getEvent());
            return false;
        }
        List<ServiceAccountConfiguration> configurations = requestDataDto.getServiceAccountResponse().getConfigurations();
        if (configurations == null) {
            return false;
        }
        return configurations.stream().anyMatch(c -> "PUSH_NOTIFICATION".equals(c.getKey()) && "ENABLED".equals(c.getValue()));
    }
    private ServiceAccountResponse getServiceAccount(String serviceAccountRefId) {
        try {
            return serviceAccountAdapter.retrieveServiceAccountByRefId(serviceAccountRefId);
        } catch (ResponseException e) {
            if (e.getHttpStatusCode() == HttpStatus.NOT_FOUND.value()) {
                log.warn("service account={} is invalid, will not retry anymore", serviceAccountRefId);
                throw new KafkaListenerNotRetryableException(e);
            } else {
                throw new KafkaListenerRetryableException(e);
            }
        } catch (TBDException | TimeoutException e) {
            throw new KafkaListenerRetryableException(e);
        } catch (Exception e) {
            log.warn("during processing we got exception we can't handle, skip processing this notification", e);
            throw new KafkaListenerNotRetryableException(e);
        }
    }


}
