package com.peoples.banking.api.partner.notification.listener.v1.config;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaConsumerGroupDetails;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerConfigProperty;
import com.peoples.banking.api.partner.notification.listener.v1.service.PartnerNotificationKafkaListenerService;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Collections;
import java.util.function.Consumer;

@Configuration
@EnableKafka
@Log4j2
public class PartnerNotificationKafkaListenerKafkaConfig {

    @Autowired
    private PartnerNotificationKafkaListenerProperty partnerNotificationKafkaListenerProperty;
    @Autowired
    private PartnerNotificationKafkaListenerService partnerNotificationKafkaListenerService;

    @Bean
    public KafkaListenerConfigProperty config() {
        return new KafkaListenerConfigProperty(Collections.singletonList(
                new KafkaConsumerGroupDetails(NotificationDetails.class,
                        partnerNotificationKafkaListenerProperty.getKafkaNotificationTopic(),
                        partnerNotificationKafkaListenerProperty.getPartnerNotificationConsumerGroup(),
                        (Consumer<NotificationDetails>) this::listenKafkaPartnerNotifications)));
    }

    @PerfLogger
    public void listenKafkaPartnerNotifications(NotificationDetails message) {
        partnerNotificationKafkaListenerService.consumeMessage(message);
    }

}
