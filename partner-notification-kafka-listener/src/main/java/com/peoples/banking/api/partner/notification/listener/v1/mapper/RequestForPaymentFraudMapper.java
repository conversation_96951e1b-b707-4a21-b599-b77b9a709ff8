package com.peoples.banking.api.partner.notification.listener.v1.mapper;


import com.peoples.banking.domain.push.notification.model.Event;
import com.peoples.banking.domain.push.notification.model.PushNotificationRequest;
import com.peoples.banking.domain.push.notification.model.Status;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.RequestForPaymentStatus;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;

import org.mapstruct.*;

import java.text.DecimalFormat;

import java.util.Optional;

@Mapper(componentModel = "spring", uses = {DateConverter.class}, nullValuePropertyMappingStrategy =  NullValuePropertyMappingStrategy.IGNORE)
public abstract class RequestForPaymentFraudMapper extends CommonMapper {

    public static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#0.00");

    @Mapping(source = "notificationDetails.event", target = "eventType", qualifiedByName = "eventEnumToEventType")
    @Mapping(source = "notificationDetails.event", target = "eventSubType", qualifiedByName = "eventEnumToEventSubType")
    @Mapping(source = "notificationDetails.event", target = "eventReason", qualifiedByName = "eventEnumToEventReason")
    @Mapping(source = "payments.externalRefId", target = "event.transaction.refId")
    @Mapping(source = "notificationDetails.eventPayload.requestForPayments.requestReference", target = "event.transaction.networkRefId")
    @Mapping(source = "notificationDetails.eventPayload.requestForPayments.status", target = "event.transaction.status", qualifiedByName = "requestForPaymentStatusConverter")
    @Mapping(target = "event.transaction.amount", ignore = true)
    @Mapping(source = "fiId", target = "event.transaction.creditorAgent.refId")
    @Mapping(source = "payments.customerRefId", target = "event.transaction.creditor.refId")
    @Mapping(source = "customers", target = "event.transaction.creditor.name", qualifiedByName = "customersNameConverter")
    @Mapping(source = "notificationDetails.event", target = "event.transaction.transactionType", qualifiedByName = "eventEnumToRequestPaymentType")
    @Mapping(target = "event.transaction.expiryDate", ignore = true)
    @Mapping(source = "payments.updatedOn", target = "event.transaction.updatedDate", qualifiedByName = "toOffsetDateTime")
    @Mapping(source = "payments.createdOn", target = "event.transaction.createdDate", qualifiedByName = "toOffsetDateTime")
    @Mapping(target = "eventDate", expression = "java(dateConverter.getCurrentUTCDateTime())")
    @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.requestForPaymentFraudDetails.additionalNotes", target = "event.transaction.fraud.note")
    public abstract PushNotificationRequest blockedUnblockedRequestForPaymentToPushNotificationRequest(String fiId, Payments payments, NotificationDetails notificationDetails, @Context String paymentsStatus, Customers customers);

    //TODO  some weird mapstruct issue: amount is not mapped as BigDecimal, as it should, but as AmountWithCurrency and code does not compile ..
    // same with the date... suspect this is because Payments and NotificationDetails have same field and even though they are specified in the mapper, code generator is still confused for some reason
    @AfterMapping
    public void afterMappingPushNotificationRequest(@MappingTarget PushNotificationRequest request, Payments payments) {
        Optional.of(request).map(PushNotificationRequest::getEvent)
                .map(Event::getTransaction).ifPresent(p -> p.setAmount(DECIMAL_FORMAT.format(payments.getAmount())));
        Optional.of(request).map(PushNotificationRequest::getEvent)
                .map(Event::getTransaction).ifPresent(p -> p.setExpiryDate(DateUtil.toOffsetDateTime(payments.getExpiryDate())));
    }
    @Named("requestForPaymentStatusConverter")
    protected Status requestForPaymentStatusConverter(RequestForPaymentStatus status, @Context String paymentsStatus) {
        if (status == null) {
            return null;
        }
        if (status == RequestForPaymentStatus.FULFILLED) {
            return Status.valueOf(paymentsStatus);
        } else {
            return Status.valueOf(status.getValue());
        }
    }


}