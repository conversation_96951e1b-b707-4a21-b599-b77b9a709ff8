package com.peoples.banking.api.partner.notification.listener.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class PartnerNotificationKafkaListenerProperty {

  @Value("${push.notification.api.timetolive}")
  private int timeToLive;
  @Value("${kafka.notificationTopic}")
  private String kafkaNotificationTopic;
  @Value("${kafka.partner.notification.group.id}")
  private String partnerNotificationConsumerGroup;
}
