---
# Generated-By: OA3 <PERSON><PERSON> Tool
# Input-File: v3.5/fraud-api-35.yaml
# Options: --encoding=utf-8; --log-level=ERROR; --oa3fix; --overwrite; --prune; --strip-vendor
# -----
openapi: 3.0.0
info:
  description: Fraud Services
  version: "3.5.0"
  title: Fraud Services
  contact:
    name: eTransfer Support
    url: https://www.interac.ca/en/contact-us-2.html
    email: <EMAIL>
  termsOfService: https://www.interac.ca/en/terms-and-conditions.html
  x-last-updated-date: "15-Jun-2023"
servers:
- description: Production Environment
  url: https://etransfer-services.interac.ca/fraud-api/v3.5.0
- description: FI Test Environment
  url: https://etransfer-services.fit.interac.ca/fraud-api/v3.5.0
- description: Beta Test Environment
  url: https://etransfer-services.beta.interac.ca/fraud-api/v3.5.0
tags:
- name: payments
  description: payment fraud services
- name: account alias registrations
  description: account alias registration fraud services
- name: request for payment
  description: request for payment fraud services
- name: fraud
  description: general fraud activities
paths:
  /report-fraud-activity:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    post:
      tags:
      - fraud
      summary: report fraud activity
      operationId: reportFraudActivity
      description: >-
        This service can be used by a Participant to report to Interac a fraudulent
        activity, before the payment is sent to Interac. (this is applicable for payments
        that are flagged/deems fraudulent payments, account alias or account numbers
        by the Sending Participant fraud detection system).
      requestBody:
        description: report fraudulent activity
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportFraudActivity'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Report Fraud activity response Payment information is not provided.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
        '200':
          description: Report Fraud activity response this applicable only if the
            fraud activity was reported for a payment (not for an account alias nor
            for an account)
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ReportFraudActivityResponse"
  /payments/score:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    post:
      tags:
      - payments
      summary: score payment
      operationId: preScorePayment
      description: >-
        This service can be used by a Participant to obtain from Interac the fraud
        score for a payment that Participant intends to send or r-score already scored
        payment.
      requestBody:
        description: Score Payment
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ScorePayment"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud score response
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScorePaymentResponse"
  /payments/{id}/block:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
      - payments
      summary: Block Payment
      operationId: blockPayment
      description: This is a service that a Participant FI can use to place a block
        on a specific Payment in order to prevent it from completing until it is confirmed
        legitimate.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FraudBlockRequest'
        description: Block Payment request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Payment blocked
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/unblock:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
      - payments
      summary: Unblock Payment
      operationId: unBlockPayment
      description: This service can be used by a Participant FI to unblock a payment
        that was previously blocked.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FraudUnblockRequest'
        description: Unblock transfer request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Payment unblocked
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/hold:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
      - payments
      summary: Hold Payment
      operationId: holdPayment
      description: A Participant FI may be in the process of investigating the legitimacy
        of a particular Payment and wishes to place a hold on that Payment in order
        to prevent it from completing until it is confirmed or released.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentHoldRequest'
        description: Hold Payment request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Payment On Hold
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/release:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
      - payments
      summary: Release Payment
      operationId: releasePayment
      description: A Participant FI can use this api to release the Payment previously
        put on hold.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentReleaseRequest'
        description: Unblock transfer request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Payment Released
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/score:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    get:
      tags:
      - payments
      summary: Get fraud score
      operationId: getPaymentFraudScore
      description: >-
        This function will return the fraud result provided by the Fraud Detection
        System as part of the Money Request or Payment to complete the fund Payment.
        Sender's FI moves the amount of Payment from the senders account into a suspense
        account and confirms to Interac.
        (If there are not sufficient funds, or other problem, FI sends an error response
        to Interac).
        Interac e-Transfer performs fraud scoring of the Payment, and may delay it
        or alert it for manual review.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud score response
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FraudCheckResult"
  /payments/{id}/fraud-status:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    patch:
      tags:
      - payments
      summary: Update fraud status
      operationId: updateRequestFraudStatus
      description: A payment fraud status update may be needed especially when a Participant
        FI has investigated a suspicious Payment and identified if it is legitimate,
        fraudulent or a scam. This may be accomplished by using the UPDATE Payment
        FRAUD STATUS message, or manually by way of the Payment Details page or the
        Customer Details page of the e-Transfer OAS.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePaymentFraudStatusRequest'
        description: Fraud status request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"

  /requests/{id}/score:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    get:
      tags:
      - request for payment
      summary: Get fraud score
      operationId: getRequestFraudScore
      description: >-
        This function will return the fraud result provided by the Fraud Detection
        System as part of the request for payment.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud score response
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FraudCheckResult"
  /requests/{id}/fraud-status:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    patch:
      tags:
      - request for payment
      summary: Update fraud status
      operationId: updateRequestForPaymentFraudStatus
      description: A request for payment fraud status update may be needed especially
        when a Participant FI has investigated a suspicious request for Payment and
        identified if it is legitimate, fraudulent or a scam. This may be accomplished
        by using the UPDATE request for Payment FRAUD STATUS message.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRequestForPaymentFraudStatusRequest'
        description: Fraud status request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /requests/{id}/block-requestor:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/ClearingSystemReferenceNumber"
    post:
      tags:
      - request for payment
      summary: Block Requestor
      operationId: block requestor
      description: This service can be used by a Participant allow their Customer
        to stop receiving requests for payment from a particular requestor or from
        all requestors.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockRequestorRequest'
        description: Block Requestor
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /requests/unblock-requestor:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    post:
      tags:
      - request for payment
      summary: Unblock Requestor
      operationId: Unblock requestor
      description: This service can be used by a Participant allow their Customer
        to stop receiving requests for payment from a particular requestor or from
        all requestors.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnblockRequestorRequest'
        description: Unblock Requestor
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /requests/blocked-requestors:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    get:
      tags:
      - request for payment
      summary: Get All Blocked Requestors
      operationId: getAllBlockedRequestors
      description: This service can be used by a Participant to obtain from Interac
        the list of blocked requestors for a Customer.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockedRequestors'


  /account-alias-registrations/{id}/fraud-status:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/AccountAliasReference"
    patch:
      tags:
      - account alias registrations
      summary: Update account alias fraud status
      operationId: updateAccountAliasFraudStatus
      description: A Account Alias fraud status update may be needed especially when
        a Participant FI has investigated a suspicious AccountAlias and identified
        if it is legitimate, fraudulent or a scam. This may be accomplished by using
        the UPDATE request for Payment FRAUD STATUS message.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountAliasFraudStatusRequest'
        description: Fraud status request
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Fraud status updated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /account-alias-registrations/{id}/score:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    - $ref: "#/components/parameters/AccountAliasReference"
    get:
      tags:
      - account alias registrations
      summary: Get account alias fraud score
      operationId: getAccountAliasFraudScoreById
      description: >-
        This function will return the fraud result provided by the Fraud Detection
        System as part of the account alias registration.
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud score response
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FraudCheckResult"
  /account-alias-registrations/score:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    - $ref: "#/components/parameters/ProductCode"
    get:
      tags:
      - account alias registrations
      summary: Get Account alias fraud score
      operationId: getAccountAliasFraudScore
      description: >-
        This function will return the fraud result provided by the Fraud Detection
        System as part of the account alias registration.
      parameters:
      - in: query
        name: service_type
        description: >-
          Filter based on this handle type used for the account alias registration.
          <br/>
          EMAIL - email based account alias registration <br/>
          UUID - UUID based account alias registration <br/>
          PHONE - phone based account alias registration <br/>
          This element is required if account_alias_handle element is present.
        schema:
          $ref: "#/components/schemas/ServiceType"
      - in: query
        name: account_alias_handle
        description: >-
          Filter based on a specific account alias registration handle. <br/>
          If service_type is EMAIL then this element must contain the customer's email
          address that is used to uniquely identify the account alias registration
          in the Interac e-Transfer system. <br/>
          If service_type is UUID then this element must contain the customer's UUID
          that is used to uniquely identify the account alias registration in the
          Interac e-Transfer system. <br/>
          If service_type is PHONE then this element must contain the customer's phone
          number that is used to uniquely identify the  account alias registration
          in the Interac e-Transfer system.
        schema:
          type: string
          minLength: 1
          maxLength: 256
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Fraud score response
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FraudCheckResult"

components:
  headers:
    x-et-rate-limit-ceiling:
      description: >
        The rate limit ceiling for that given endpoint in the given window.
      schema:
        type: integer
        format: int64
        example: 120
    x-et-rate-limit-remaining:
      description: >
        The number of requests left for the current window. Since the eTransfer is
        calculating the rate limit based on GMT in HOUR window, the timezones that
        have 30-minute shift should realize the remaining count is based on the clock
        in the hourly-shift zones.
      schema:
        type: integer
        format: int64
        example: 60
    x-et-rate-limit-window:
      description: >
        The rate limit gauging and resetting window. To simplify the complexity of
        the impact of timezone on the rate limiting, eTransfer will use GMT for rate
        limiting window. MINUTE is the default setting unless specified otherwise.
      schema:
        type: string
        enum: ['HOUR', 'MINUTE']
        x-example: MINUTE
    x-et-response-code:
      description: >
        A numeric response code specifying the outcome of the message. A successful
        call will
        return a response code of 0, along with any additional response data.
      schema:
        type: integer
        example: 0
  parameters:
    AccountAliasReference:
      description: The Interac-generated account alias reference.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    Authorization:
      in: header
      name: Authorization
      description: >-
        Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
      schema:
        type: string
      required: false
      example: 12345
    ChannelIndicator:
      in: header
      name: x-et-channel-indicator
      description: see components/schemas/ChannelIndicator
      schema:
        $ref: '#/components/schemas/ChannelIndicator'
      required: true
      example: ONLINE
    ClearingSystemReferenceNumber:
      description: The Interac-generated Clearing System Reference Number.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    IndirectConnectorId:
      in: header
      name: x-et-indirect-connector-id
      description: >-
        Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined
        in e-Transfer system.
      schema:
        type: string
        minLength: 1
        maxLength: 35
      required: false
      example: 1
    ParticipantId:
      in: header
      name: x-et-participant-id
      description: >-
        Direct Participant Identifier as defined in e-Transfer system. Participant
        must ensure conformity to the following pattern before transmitting this data
        - CA000xxx where xxx is the Financial Institution Identifier as defined by
        the Canadian Payment Association. If customer's FI connects indirectly through
        a Participant, the participant-id field identifies the direct connector.
      schema:
        type: string
        minLength: 8
        maxLength: 8
      required: true
      example: CA000001
    ParticipantUserId:
      description: Present for all API calls initiated on behalf of a customer. Customer
        ID provided as defined in the Participant system and Customer must be registered
        in the e-Transfer system.
      in: header
      name: x-et-participant-user-id
      schema:
        type: string
        minLength: 1
        maxLength: 35
      required: true
      example: CA000001-user-123
    ProductCode:
      in: header
      name: x-et-product-code
      description: see '#/components/schemas/ProductCode'
      schema:
        $ref: '#/components/schemas/ProductCode'
      required: true
      example: DOMESTIC

    RequestId:
      in: header
      name: x-et-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes. In
        case of a request retry use the same ID as in the original message.
      schema:
        type: string
        minLength: 1
        maxLength: 36
      required: true
      example: 12345
    Signature:
      in: header
      name: x-et-api-signature
      description: >-
        JWS detached signature of the payload (body only), required for all API calls.
      schema:
        type: string
      required: true
      example: 12345
    SignatureType:
      in: header
      name: x-et-api-signature-type
      description: >-
        The type of the JWT. Required. Allowed values are 'PAYLOAD_DIGEST_SHA256'
      schema:
        $ref: '#/components/schemas/SignatureType'
      required: true
      example: PAYLOAD_DIGEST_SHA256
    TransactionTime:
      in: header
      name: x-et-transaction-time
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      schema:
        $ref: '#/components/schemas/TransactionTime'
      required: true
      example: '2020-01-23T12:34:56.000Z'
  responses:
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    401-unauthorized:
      description: Unauthorized
    403-forbidden:
      description: Forbidden
    404-not-found:
      description: Resources Not Found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    429-too-many-requests:
      description: Too many requests; blocked due to rate limiting.
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    500-internal-server-error:
      description: Internal Server Error
    503-service-unavailable:
      description: Service Unavailable - The server cannot handle the request for
        a service due to temporary maintenance.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
  schemas:
    AccountAliasRegHandle:
      description: Email / SMS / UUID service type preferences
      discriminator:
        propertyName: type
        mapping:
          EMAIL: '#/components/schemas/EmailAccountAliasHandle'
          PHONE: '#/components/schemas/PhoneAccountAliasHandle'
          UUID: '#/components/schemas/UuidAccountAliasHandle'
      required:
      - type
      properties:
        type:
          $ref: '#/components/schemas/AccountAliasRegServiceType'
    AccountAliasRegServiceType:
      type: string
      description: Email <br/> PHONE <br/> UUID
      enum: ['EMAIL', 'PHONE', 'UUID']
      x-example: EMAIL
    AccountIdentification4Choice:
      type: object
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'
      required:
      - other
    AccountSchemeName1Choice:
      type: object
      oneOf:
      - type: object
        properties:
          code:
            $ref: '#/components/schemas/ExternalAccountIdentification1Code'
      - type: object
        properties:
          proprietary:
            description: >-
              Proprietary scheme name used in the identification of the account. Accepted
              values are
              'ALIAS_ACCT_NO' - Identification scheme used by Interac to identify
              the Account alias registration number of an e-Transfer customer
              'BANK_ACCT_NO' - Unique and unambiguous assignment made by a specific
              bank or similar financial institution to identify a relationship as
              defined between the bank and its client
            type: string
            enum: ['ALIAS_ACCT_NO', 'BANK_ACCT_NO']
            x-example: ALIAS_ACCT_NO
    ActiveCurrencyAndAmount:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveCurrencyCode'
    ActiveCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 44.44
    ActiveCurrencyCode:
      type: string
      enum: ['CAD', 'USD']
      x-example: CAD
    ActiveOrHistoricCurrencyAndAmount:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyCode'
    ActiveOrHistoricCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 55.55
    ActiveOrHistoricCurrencyCode:
      type: string
      enum: ['CAD', 'USD']
      x-example: CAD
    AddressType2Code:
      type: string
      enum: ['ADDR', 'PBOX', 'HOME', 'BIZZ', 'MLTO', 'DLVY']
      x-example: 'ADDR'
    AddressType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/AddressType2Code'
      required:
      - code
    AuthenticationType:
      description: >-
        'Flag indicating the type of authentication required to complete the Transfer.
        <br/>
          CONTACT_LEVEL - Security question and answer defined at contact level. <br/>
          PAYMENT_LEVEL - Security question and answer defined at payment level. <br/>
          NOT_REQUIRED - No authentication. </br> '
      type: string
      enum: ['CONTACT_LEVEL', 'PAYMENT_LEVEL', 'NOT_REQUIRED']
      x-example: CONTACT_LEVEL
    BICFIDec2014Identifier:
      type: string
      pattern: '[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}'
      example: AAAADEBBXXX
    BlockRequestorRequest:
      description: Element indicating if the customer wants to block one particular
        requestor or all requestors. The following values are currently supported;
      type: object
      required:
      - block_request_type
      properties:
        block_request_type:
          description: >-
            This service can be used by a Participant allow their Customer to stop
            receiving requests for payment from a particular requestor or from all
            requestors.
          type: string
          x-example: BLOCK_ONE_REQUESTOR
          enum: ['BLOCK_ONE_REQUESTOR', 'BLOCK_ALL_REQUESTORS']
        block_reason:
          $ref: '#/components/schemas/Reason'
    BlockedRequestors:
      description: List of all blocked requestors.
      type: object
      required:
      - responder_handle
      - block_date
      properties:
        requestor_name:
          $ref: "#/components/schemas/LegalName"
        requestor_email:
          description: >-
            The requester email handle that has been blocked.
          type: string
          minLength: 1
          maxLength: 256
        responder_handle:
          $ref: "#/components/schemas/NotificationHandle"
        block_date:
          $ref: "#/components/schemas/CustomDateTime"
    BranchAndFinancialInstitutionIdentification6:
      type: object
      required:
      - financial_institution_identification
      properties:
        financial_institution_identification:  # FinInstnId
          $ref: '#/components/schemas/FinancialInstitutionIdentification18'
    BusinessName:
      description: Business name. This is required for type 1 (small business) or
        2 (corporation)
      type: object
      properties:
        company_name:
          description: Business/company name, is required while creating customer
            profile.
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
        trade_name:
          description: Trade name
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
    CashAccount38:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'
        proxy: # Prxy
          $ref: '#/components/schemas/ProxyAccountIdentification1'
    CategoryPurpose1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalCategoryPurpose1Code'
      required:
      - code
    ChannelIndicator:
      description: |
        Method Sender accessed the service description. <br/>
        Identifies the channel that the customer is using when making the <br/>
        request if the request is initiated by the customer. For requests <br/>
        initiated by an e-Transfer system or a participants system component it <br/>
        identifies the system that makes the request. Values are: <br/>
        ONLINE = Customer online initiated transaction <br/>
        MOBILE = Customer mobile initiated transaction <br/>
        PARTICIPANT_BULK_PAYMENT = Participant payment system initiated Bulk file transaction (*) <br/>
        PARTICIPANT_ETRANSFER_SYSTEM = Participant payment system initiated transaction <br/>
        PARTICIPANT_FRAUD_SYSTEM = Participant fraud detection system initiated transaction <br/>
        ETRANSFER_SYSTEM = e-Transfer system initiated transaction (*) <br/>
        ETRANSFER_FRAUD = e-Transfer fraud detection system initiated transaction (*) <br/>
        EXTERNAL_APPS = External API initiated transaction (*) <br/>
        INTERAC_SDK = Interac Proximity SDK initiated transaction <br/>
        (*) - values not accepted through Participant initiated requests
      type: string
      enum: ['ONLINE', 'MOBILE', 'PARTICIPANT_BULK_PAYMENT', 'PARTICIPANT_ETRANSFER_SYSTEM',
        'PARTICIPANT_FRAUD_SYSTEM', 'ETRANSFER_SYSTEM', 'ETRANSFER_FRAUD', 'EXTERNAL_APPS',
        'INTERAC_SDK']
      x-example: ONLINE
    ChargeBearerType1Code:
      type: string
      description: This identifies which party(ies) will pay changer due for processing
        this transaction. Value is set to 'SLEV', which means Following Service Level
      enum: ['SLEV']
      x-example: SLEV
    ClearingSystemIdentification3Choice:
      type: object
      properties:
        proprietary:
          type: string
          enum: ['ETR']
          x-example: ETR
      required:
      - proprietary
    ClearingSystemMemberIdentification2:
      type: object
      required:
      - member_identification
      properties:
        member_identification: # MmbId
          description: >-
            Identification of the Instructed/Instructing/Debtor/Creditor Agent.
            The value 'NOTPROVIDED' in the MemberIdentification element can be used
            when this information is not provided or is not available.
          type: string
          minLength: 1
          maxLength: 35
    ClearingSystemReference:
      description: The Interac-generated payment or request for payment reference
        numbers, also known as the clearing system reference in ISO 20022.
      type: string
      minLength: 8
      maxLength: 35
      example: 12345678
    Contact4:
      type: object
      properties:
        phone_number: # PhneNb
          $ref: '#/components/schemas/PhoneNumber'
        mobile_number: # MobNb
          $ref: '#/components/schemas/PhoneNumber'
        fax_number: # FaxNb
          $ref: '#/components/schemas/PhoneNumber'
        email_address: # EmailAdr
          type: string
          minLength: 1
          maxLength: 256
          format: email
          example: <EMAIL>
        name: # Nm
          type: string
          minLength: 1
          maxLength: 140
    CountryCode:
      type: string
      description: Only ISO 3166 Alpha-2 codes are allowed.
      enum: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO', 'AQ',
        'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG',
        'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW',
        'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM',
        'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM',
        'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM',
        'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN',
        'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT',
        'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM',
        'JO', 'JP', 'K1', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY',
        'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA',
        'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ',
        'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF',
        'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG',
        'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'QM', 'QN',
        'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW', 'QX', 'QY', 'QZ', 'RE',
        'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI',
        'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ',
        'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TP', 'TR',
        'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE',
        'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF', 'XG', 'XN',
        'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE', 'YT', 'YU',
        'ZA', 'ZM', 'ZW']
      x-example: CA
    CreditDebitCode:
      type: string
      enum: ['CRDT', 'DBIT']
      x-example: 'CRDT'
    CreditTransferTransaction39:
      type: object
      properties:
        payment_identification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification7'
        payment_type_information: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation28'
        interbank_settlement_amount: # IntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbank_settlement_date: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        charge_bearer: # ChrgBr
          $ref: '#/components/schemas/ChargeBearerType1Code'
        previous_instructing_agent_1: # PrvsInstgAgt1
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/PartyIdentification135'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
        debtor: # Dbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/PartyIdentification135'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/PartyIdentification135'
        related_remittance_information: # RltdRmtInf
          description: >-
            Elements in this data block can be used to specify details related to
            the handling of the remittance information such as remittance location
            and/or
            the unique identification of the remittance information if it was sent
            separately from the payment instruction.
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocation7'
          maxItems: 1
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
      required:
      - payment_identification
      - payment_type_information
      - interbank_settlement_amount
      - interbank_settlement_date
      - charge_bearer
      - debtor
      - debtor_account
      - debtor_agent
      - creditor_agent
      - creditor
    CreditorReferenceInformation2:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/CreditorReferenceType2'
        reference: # Ref
          $ref: '#/components/schemas/Max35Text'
    CreditorReferenceType1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType3Code'
      required:
      - code
    CreditorReferenceType2:
      type: object
      required:
      - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/CreditorReferenceType1Choice'
    CustomDate:
      description: >-
        A particular point in the progression of time in a calendar year
        expressed in the YYYY-MM-DD format. This representation is defined in
        XML Schema Part 2 Datatypes Second Edition - W3C Recommendation 28
        October 2004 which is aligned with ISO 8601.
      type: string
      format: date
      example: '2020-01-23'
      x-example: '2020-01-23'
    CustomDateTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.000Z'
    DocumentAdjustment1:
      type: object
      required:
      - amount
      properties:
        amount:  # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        credit_debit_indicator: # CdtDbtInd
          $ref: '#/components/schemas/CreditDebitCode'
        reason: # Rsn
          $ref: '#/components/schemas/Max4Text'
        additional_information: # AddtlInf
          $ref: '#/components/schemas/Max140Text'
    DocumentType3Code:
      type: string
      enum: ['RADM', 'RPIN', 'FXDR', 'DISP', 'PUOR', 'SCOR']
      x-example: 'RADM'
    DocumentType6Code:
      type: string
      enum: ['MSIN', 'CNFA', 'DNFA', 'CINV', 'CREN', 'DEBN', 'HIRI', 'SBIN', 'CMCN',
        'SOAC', 'DISP', 'BOLD', 'VCHR', 'AROI', 'TSUT', 'PUOR']
      x-example: 'MSIN'
    EmailAccountAliasHandle:
      allOf:
      - $ref: '#/components/schemas/AccountAliasRegHandle'
      - type: object
        description: AccountAliasRegServiceType Email
        required:
        - email
        properties:
          email:
            type: string
            format: email
            minLength: 1
            maxLength: 256
            description: The email of the contact
            example: <EMAIL>
    EmailNotificationHandle:
      allOf:
      - $ref: '#/components/schemas/NotificationHandle'
      - type: object
        description: Notification via Email
        required:
        - email
        properties:
          email:
            type: string
            minLength: 1
            maxLength: 256
            format: email
            description: The email of the contact
            example: <EMAIL>
    ErrorModel:
      type: object
      required:
      - code
      properties:
        code:
          type: string
          description: |
            Error list for all the APIs, please refer to the example for API-specific error codes.
          maxLength: 80
          example: 999
        text:
          description: Short error explanation.
          type: string
          maxLength: 2000
          example: Unknown error
    ExternalAccountIdentification1Code:
      type: string
      enum: ['AIIN', 'BBAN', 'CUID', 'UPIC']
      x-example: 'AIIN'
    ExternalCategoryPurpose1Code:
      type: string
      description: This includes iso codes which are 4 letter characters and custom
        which are 3 digit numbers.
      enum: ['BONU', 'CASH', 'CBLK', 'CCRD', 'CORT', 'DCRD', 'DIVI', 'DVPM', 'EPAY',
        'FCOL', 'GOVT', 'HEDG', 'ICCP', 'IDCP', 'INTC', 'INTE', 'LOAN', 'MP2B', 'MP2P',
        'OTHR', 'PENS', 'RPRE', 'RRCT', 'RVPM', 'SALA', 'SECU', 'SSBE', 'SUPP', 'TAXS',
        'TRAD', 'TREA', 'VATX', 'WHLD', '240', '260', '330', '370', '400', '430',
        '460', '480', '452', '308', '311', '318']
      x-example: BONU
    ExternalFinancialInstitutionIdentification1Code:
      type: string
      minLength: 1
      maxLength: 4
      example: CUST
    ExternalOrganisationIdentification1Code:
      type: string
      enum: ['BANK', 'CBID', 'CHID', 'CINC', 'COID', 'CUST', 'DUNS', 'EMPL', 'GS1G',
        'SREN', 'SRET', 'TXID']
      x-example: CUST
    FIToFICustomerCreditTransferV08:
      type: object
      required:
      - group_header
      - credit_transfer_transaction_information
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader93'
        credit_transfer_transaction_information: # CdtTrfTxInf
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransaction39'
          minItems: 1
          description: >-
            Elements used to provide information about payments.
          maxItems: 1
    FinancialIdentificationSchemeName1Choice:
      type: object
      oneOf:
      - type: object
        properties:
          code:
            $ref: '#/components/schemas/ExternalFinancialInstitutionIdentification1Code'
      - type: object
        properties:
          proprietary:
            $ref: '#/components/schemas/Max35Text'
    FinancialInstitutionIdentification18:
      type: object
      properties:
        bicfi:  # BICFI
          $ref: '#/components/schemas/BICFIDec2014Identifier'
        clearing_system_member_identification: # ClrSysMmbId
          $ref: '#/components/schemas/ClearingSystemMemberIdentification2'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        other: # Othr
          $ref: '#/components/schemas/GenericFinancialIdentification1'
    FraudBlockRequest:
      description: Fraud block request
      type: object
      properties:
        block_reason:
          #description: Reason for blocking the Payment e.g. Suspected Fraud, Confirmed Fraud, Insufficient Funds
          $ref: '#/components/schemas/Reason'
    FraudCaseId:
      description: Unique Interac-generated reference number associated with this
        fraud operations.
      type: string
      minLength: 1
      maxLength: 35
      example: 12345678
    FraudCheckResult:
      description: Fraud check results
      type: object
      required:
      - action
      properties:
        score:
          description: Numeric value between 0 and 999 qualifying Fraud Detection
            System assessment of fraud risk.
          type: integer
          minimum: 0
          maximum: 999
          example: 10
        reason:
          description: Fraud Reason from Fraud Detection System
          type: string
          minLength: 1
          maxLength: 256
          example: Fraud reason
        action:
          description: Description of action to be performed to mitigate fraud.  (Allow
            Transfer, Block Transfer, Delay Transfer, User Input Required, No Check
            Performed)
          type: string
          enum: ['ALLOW', 'BLOCK', 'DELAY', 'USER_INPUT_REQUIRED', 'NO_CHECK_PERFORMED']
          x-example: ALLOW
    FraudStatus:
      description: >-
        Fraud status of the Payment <br/> CONFIRM_FRAUD - Confirmed Fraud <br/> CONFIRM_LEGITIMATE
        - Confirmed Legitimate <br/> SCAM - Scam  <br/> PRESUME_LEGITIMATE - Presumed
        Legitimate <br/> SUSPICIOUS - suspicious.
      type: string
      x-example: CONFIRM_LEGITIMATE
      enum: ['CONFIRM_FRAUD', 'CONFIRM_LEGITIMATE', 'SCAM', 'PRESUME_LEGITIMATE',
        'SUSPICIOUS']
    FraudStatusDetails:
      description: Fraud status request
      type: object
      required:
      - fraud_status
      properties:
        fraud_status:
          $ref: "#/components/schemas/FraudStatus"
        fraud_type:
          $ref: "#/components/schemas/FraudType"
        memo:
          $ref: "#/components/schemas/Memo"
    FraudType:
      description: >-
        Present only if Confirmed Fraud and Scam. Possible Values - <br/>
        ACCT_TAKEOVER         - Account Takeover <br/>
        INVESTMENT_LOAN_SCAM  - Investment/Loan Scam <br/>
        JOB_SCAM              - Job Scam <br/>
        BUYER_SELLER_SCAM     - Buyer/Seller Scam <br/>
        ROMANCE_SCAM          - Romance Scam <br/>
        THREAT_EMERGENCY_SCAM - Threat Emergency Scam <br/>
        INTERCEPTED_PAYMENT   - Intercepted Transfer <br/>
        BUS_EML_COMPR         - Business Email Compromise <br/>
        VNDR_EML_COMPR        - Vendor Email Compromise <br/>
        APP_FRAUD             - Application Fraud <br/>
        FRAUD_BUS             - Fraudulent Business <br/>
        OTHER                 - Other
      type: string
      enum: ['ACCT_TAKEOVER', 'INVESTMENT_LOAN_SCAM', 'JOB_SCAM', 'BUYER_SELLER_SCAM',
        'ROMANCE_SCAM', 'THREAT_EMERGENCY_SCAM', 'INTERCEPTED_PAYMENT', 'BUS_EML_COMPR',
        'VNDR_EML_COMPR', 'APP_FRAUD', 'FRAUD_BUS', 'OTHER']
      x-example: ACCT_TAKEOVER
    FraudUnblockRequest:
      description: Fraud unblock request
      type: object
      properties:
        unblock_reason:
          #description: Reason for unblocking the Payment
          $ref: '#/components/schemas/Reason'
    GenericAccountIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          description: >-
            if scheme name is ALIAS_ACCT_NO then this element must contain the Account
            Alias Reference Number (i.e. Autodeposit reference number) generated by
            Interac
            if scheme name is BANK_ACCT_NO then this element must contain the actual
            bank account number. Valid format aaa-bbbbb-cccccccccccccccccccccccc where
              aaa is the Institution Id (fixed length 3 digits)
              bbbbb is the Transit Number (fixed length 5 digits)
              cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-****************7890
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/AccountSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericFinancialIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/Max35Text'
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/FinancialIdentificationSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericOrganisationIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          description: participant user ID at their financial institution (creditor
            / debtor/ initiating party ).
          type: string
          minLength: 1
          maxLength: 35
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/OrganisationIdentificationSchemeName1Choice'
    GroupHeader93:
      type: object
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        number_of_transactions: # NbOfTxs
          $ref: '#/components/schemas/Max15NumericText'
        settlement_information: # SttlmInf
          $ref: '#/components/schemas/SettlementInstruction7'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
      required:
      - message_identification
      - creation_datetime
      - number_of_transactions
      - settlement_information
      - instructing_agent
      - instructed_agent
    HashType:
      description: Algorithm used to hash the security answers, passwords examples
        - SHA2.
      type: string
      enum: ['SHA2']
      x-example: SHA2
    ISODate:
      type: string
      format: date
      description: Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      example: '2020-01-23'
    ISODateTime:
      type: string
      description: >-
        "A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601. "
      format: date-time
      example: '2020-01-23T12:34:56.000Z'
    LegalName:
      description: Legal name
      type: object
      oneOf:
      - type: object
        properties:
          retail_name:
            $ref: '#/components/schemas/RetailName'
      - type: object
        properties:
          business_name:
            $ref: '#/components/schemas/BusinessName'
    LocalInstrument2Choice:
      type: object
      properties:
        proprietary:
          description: >-
            REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
            FULFILL_REQUEST_FOR_PAYMENT      - Fulfill Money Request payment </br>
            ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
            REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
            ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
            REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>
            RTR_PAYMENT                      - RTR cross-scheme Payment </br>
          type: string
          enum: ['REGULAR_PAYMENT', 'FULFILL_REQUEST_FOR_PAYMENT', 'ACCOUNT_ALIAS_PAYMENT',
            'REALTIME_ACCOUNT_ALIAS_PAYMENT', 'ACCOUNT_DEPOSIT_PAYMENT', 'REALTIME_ACCOUNT_DEPOSIT_PAYMENT',
            'RTR_PAYMENT']
          x-example: REGULAR_PAYMENT
      required:
      - proprietary
    Max140Text:
      type: string
      minLength: 1
      maxLength: 140
    Max15NumericText:
      type: string
      pattern: '[0-9]{1,15}'
    Max2048Text:
      type: string
      minLength: 1
      maxLength: 2048
    Max35Text:
      type: string
      minLength: 1
      maxLength: 35
    Max4Text:
      type: string
      minLength: 1
      maxLength: 4
    Max70Text:
      type: string
      minLength: 1
      maxLength: 70
    Memo:
      description: Reason for updating fraud status. Conditional required only if
        fraudStatus is SCAM and fraudType is OTHER
      type: string
      minLength: 1
      maxLength: 420
    NotificationHandle:
      description: Email or SMS notification preferences
      discriminator:
        propertyName: type
        mapping:
          EMAIL: '#/components/schemas/EmailNotificationHandle'
          SMS: '#/components/schemas/SMSNotificationHandle'
      required:
      - type
      properties:
        type:
          $ref: '#/components/schemas/NotificationHandleType'
    NotificationHandleType:
      type: string
      description: Email <br/> SMS
      enum: ['EMAIL', 'SMS']
      x-example: EMAIL
# AccountAliasHandle used in case of fraud apis #
    OrganisationIdentification29:
      type: object
      properties:
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
      required:
      - other
    OrganisationIdentificationSchemeName1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalOrganisationIdentification1Code'
      required:
      - code
    Party38Choice:
      type: object
      properties:
        organisation_identification:
          $ref: '#/components/schemas/OrganisationIdentification29'
      required:
      - organisation_identification
    PartyIdentification135:
      type: object
      properties:
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        country_of_residence: # CtryOfRes
          $ref: '#/components/schemas/CountryCode'
        contact_details: # CtctDtls
          $ref: '#/components/schemas/Contact4'
        name:  # Nm
          type: string
          minLength: 1
          maxLength: 140
    PaymentAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      required:
      - authentication_type
      properties:
        authentication_type:
          $ref: "#/components/schemas/AuthenticationType"
        security_question:
          description: Question text. Required if authenticationType is PAYMENT_LEVEL
          type: string
          minLength: 1
          maxLength: 40
        hash_type:
          $ref: "#/components/schemas/HashType"
        hash_salt:
          description: Required if authenticationType is PAYMENT_LEVEL
          type: string
          minLength: 1
          maxLength: 44
        security_answer:
          description: >-
            'Answer to the security question (as provided by the customer) with leading
            and trailing whitespace trimmed, uppercase, postfixed with hashSalt if
            present, hashed using the algorithm identified by hashType and then Base64
            encoded. ISO-8859-1encoding to be used when the hash is generated. Required
            if authenticationType is 0.'
          type: string
          minLength: 1
          maxLength: 64
    PaymentDetails:
      description: Payment details
      type: object
      required:
      - fi_to_fi_customer_credit_transfer
      - fraud_supplementary_info
      - account_holder_name
      properties:
        fi_to_fi_customer_credit_transfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'
        expiry_date:
          ## can be provided if fi_to_fi_customer_credit_transfer is present.
          $ref: "#/components/schemas/CustomDateTime"
        fi_account_id:
          description: Unique FI Identifier for the customer account (tokenized account),
            can be provided if fi_to_fi_customer_credit_transfer is present.
          type: string
          minLength: 1
          maxLength: 50
        sender_account_identifier:
          description: Account Holder Name, can be provided if fi_to_fi_customer_credit_transfer
            is present.
          type: string
          minLength: 1
          maxLength: 80
        account_holder_name:
          description: Account Holder Name, required be provided if fi_to_fi_customer_credit_transfer
            is present.
          type: string
          minLength: 1
          maxLength: 80
        fraud_supplementary_info:
          $ref: "#/components/schemas/SupplementaryInfo"
        payment_authentication:
          ## can be provided if fi_to_fi_customer_credit_transfer is present.
          $ref: '#/components/schemas/PaymentAuthentication'
    PaymentHoldRequest:
      description: Fraud hold request
      type: object
      properties:
        hold_reason:
          $ref: '#/components/schemas/Reason'
    PaymentIdentification7:
      type: object
      properties:
        instruction_identification:  # InstrId
          description: >-
            Reference identification as assigned by the instructing party. Usage -
            if the credit transfer is used to fulfill a request for payment (i.e.
            if PmtTpInf.LclInstrm.Prtry is FULFILL_REQUEST_FOR_PAYMENT ) then this
            element is mandatory and must be populated as follows;
            if the request for payment was originally submitted to Interac via a pain.13
            then this element must match the Interac generated reference identification
            of the request for payment (i.e. clearingSystemReference element from
            the request body of ISO 20022 - Get Incoming Request for Payment)
            if the request for payment was submitted via a custom non-ISO Money Request
            then this element must match the Interac generated reference identification
            of the request for payment (i.e. requestReferenceNumber)
            if the credit transfer is not used to fulfill a request for payment then
            this element is optional
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            Reference identification as assigned by the customer/debtor. This remains
            unchanged in the whole end to end flow between the end customers, Usage;
            if the credit transfer is used to fulfill a request for payment (i.e.
            if PmtTpInf.LclInstrm.Prtry is FULFILL_REQUEST_FOR_PAYMENT) then,
                if the request for payment was submitted via a pain.13 then this element
            must match the EndToEndId reference identification from that pain.013
            request for payment
                if the request for payment was submitted via a custom non-ISO Money
            Request then this element should be set to 'NOTPROVIDED'
            if the credit transfer is not used to fulfill a request for payment then,
                if a reference is provided by the Debtor then this element should
            contain that value
                if a reference is not provided by the Debtor then this element should
            be set to 'NOTPROVIDED'
          type: string
          minLength: 1
          maxLength: 35
        transaction_identification: # TxId
          description: >-
            Transaction identification set by the first instructing agent (typically
            the Debtor Agent). This remains unchanged in the end to end flow between
            various agents, Usage;
            Reference identification as assigned by Debtor FI. This element is mandatory
            and must be unique for that FI
          type: string
          minLength: 1
          maxLength: 35
        clearing_system_reference: # ClrSysRef
          $ref: '#/components/schemas/Max35Text'
      required:
      - transaction_identification
      - end_to_end_identification
    PaymentReleaseRequest:
      description: Fraud release request
      type: object
      properties:
        release_reason:
          $ref: '#/components/schemas/Reason'
    PaymentTypeInformation28:
      type: object
      properties:
        local_instrument: # LclInstrm
          $ref: '#/components/schemas/LocalInstrument2Choice'
        category_purpose: # CtgyPurp
          $ref: '#/components/schemas/CategoryPurpose1Choice'
      required:
      - local_instrument
    PhoneAccountAliasHandle:
      allOf:
      - $ref: '#/components/schemas/AccountAliasRegHandle'
      - type: object
        description: AccountAliasRegServiceType Phone
        required:
        - phone_number
        properties:
          phone_number:
            $ref: '#/components/schemas/PhoneNumber'
    PhoneNumber:
      description: >-
        The collection of information which identifies a specific phone or FAX number
        as defined by telecom services.
        It consists of a '+' followed by the country code (from 1 to 3 characters)
        then a '-' and finally, any combination of numbers, '(', ')', '+' and '-'
        (up to 30 characters).
      type: string
      minLength: 1
      maxLength: 30
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      example: ******-555-1212
    PostalAddress24:
      type: object
      properties:
        address_type:  # AdrTp
          $ref: '#/components/schemas/AddressType3Choice'
        department: # Dept
          $ref: '#/components/schemas/Max70Text'
        sub_department: # SubDept
          $ref: '#/components/schemas/Max70Text'
        street_name: # StrtNm
          type: string
          minLength: 1
          maxLength: 70
        building_number: # BldgNb
          type: string
          minLength: 1
          maxLength: 16
        post_code: # PstCd
          type: string
          minLength: 1
          maxLength: 16
        town_name: # TwnNm
          type: string
          minLength: 1
          maxLength: 35
        country_sub_division: # CtrySubDvsn
          $ref: '#/components/schemas/Max35Text'
        country: # Ctry
          $ref: '#/components/schemas/CountryCode'
        address_line: # AdrLine
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 70
          maxItems: 7
    ProductCode:
      description: >-
        Product for which the transaction is applicable. Product Code: <br/>
        DOMESTIC = e-Transfer domestic <br/>
        INTERNATIONAL = e-Transfer International Remittance
      type: string
      enum: ['DOMESTIC', 'INTERNATIONAL']
      x-example: DOMESTIC
    ProxyAccountIdentification1:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ProxyAccountType1Choice'
        identification: # Id
          description: >-
            Depending on the value indicated in the the Proxy.Type/Proprietary above,
            this element contains the actual email, UUID or mobile phone number respectively,
            that was used to register the account alias.
          type: string
          minLength: 1
          maxLength: 256
      description: >-
        The elements in this data block can be used to specify the handle type and
        the handle value that were used during the account alias registration.
        Conditional, If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_ALIAS_PAYMENT
        or REALTIME_ACCOUNT_ALIAS_PAYMENT , then this block is mandatory.
        In all other cases this block is not required
      required:
      - identification
      - type
    ProxyAccountType1Choice:
      type: object
      properties:
        proprietary:
          description: >-
            Proprietary proxy handle type used in the identification of the creditor
            account. The following values are currently supported and accepted - EMAIL,
            UUID or PHONE
          type: string
          enum: ['EMAIL', 'UUID', 'PHONE']
          x-example: EMAIL
      required:
      - proprietary
    Reason:
      description: Reason for action performed ( block, unblock, hold and release
        )
      type: string
      minLength: 1
      maxLength: 400
    ReferredDocumentInformation7:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ReferredDocumentType4'
        number: # Nb
          $ref: '#/components/schemas/Max35Text'
        related_date: # RltdDt
          $ref: '#/components/schemas/ISODate'
    ReferredDocumentType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType6Code'
      required:
      - code
    ReferredDocumentType4:
      type: object
      required:
      - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/ReferredDocumentType3Choice'
    RemittanceAmount2:
      type: object
      properties:
        due_payable_amount:  # DuePyblAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        adjustment_amount_and_reason: # AdjstmntAmtAndRsn
          type: array
          items:
            $ref: '#/components/schemas/DocumentAdjustment1'
          description: >-
            Wrapper element for detailed information on the adjusted amount and reason
            of the adjustment.
          maxItems: 1
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    RemittanceInformation16:
      type: object
      properties:
        unstructured:  # Ustrd
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
        structured: # Strd
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: Remittance information in a structured form. Up to 5 block
            allowed.
          maxItems: 5
    RemittanceLocation7:
      type: object
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        remittance_location_details: # RmtLctnDtls
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocationData1'
          description: Set of elements used to provide information on the location
            and/or delivery of the remittance information.
          maxItems: 1
    RemittanceLocationData1:
      type: object
      properties:
        method:  # Mtd
          $ref: '#/components/schemas/RemittanceLocationMethod2Code'
        electronic_address: # ElctrncAdr
          $ref: '#/components/schemas/Max2048Text'
      required:
      - method
      - electronic_address
    RemittanceLocationMethod2Code:
      type: string
      description: >-
        Method used to deliver the remittance advice information. Restriction - only
        value URID is accepted.
      enum: ['URID']
      x-example: URID
    ReportFraudActivity:
      description: Report Fraud activity Request
      type: object
      required:
      - fraud_status_details
      properties:
        fi_to_fi_customer_credit_transfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'
        fraud_case_id:
          ## Fraud Case Id that was obtained from Interac during the ScorePayment Conditional usage
          ## If the report fraud activity is for a payment then either FIToFICstmrCdtTrf or (exclusive or) fraudCaseId must be provided (not both)
          $ref: "#/components/schemas/FraudCaseId"
        account_alias:
          $ref: "#/components/schemas/AccountAliasRegHandle"
        account_number:
          description: >-
            Valid format aaa-bbbbb-cccccccccccccccccccccccc where
            aaa is the Institution Id (fixed length 3 digits)
            bbbbb is the Transit Number (fixed length 5 digits)
            cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-****************7890
        fraud_status_details:
          $ref: "#/components/schemas/FraudStatusDetails"
    ReportFraudActivityResponse:
      description: Report Fraud Activity Response
      type: object
      properties:
        fraud_case_id:
          $ref: "#/components/schemas/FraudCaseId"
    RetailName:
      description: Retail name. This is required for type 0 (retail)
      type: object
      required:
      - first_name
      - last_name
      properties:
        first_name:
          description: First name
          type: string
          minLength: 1
          maxLength: 100
          example: first_name
        middle_name:
          description: Middle name
          type: string
          minLength: 1
          maxLength: 100
          example: middle_name
        last_name:
          description: Last name
          type: string
          minLength: 1
          maxLength: 100
          example: last_name
    SMSNotificationHandle:
      allOf:
      - $ref: '#/components/schemas/NotificationHandle'
      - type: object
        description: Notification via SMS
        required:
        - phone_number
        properties:
          phone_number:
            $ref: '#/components/schemas/PhoneNumber'
    ScorePayment:
      description: Score Payment Request
      type: object
      oneOf:
      - type: object
        properties:
          fraud_case_id:
            $ref: "#/components/schemas/FraudCaseId"
      - type: object
        properties:
          payment_details:
            $ref: "#/components/schemas/PaymentDetails"
    ScorePaymentResponse:
      description: Score Payment Response
      type: object
      required:
      - fraud_check_result
      - fraud_case_id
      properties:
        fraud_check_result:
          $ref: "#/components/schemas/FraudCheckResult"
        fraud_case_id:
          $ref: "#/components/schemas/FraudCaseId"
    ServiceType:
      description: >-
        Flag indicating the type of the Account Alias  registration
        <br/> EMAIL - means Email based account alias registration
        <br/> UUID  - means UUID based account alias registration. Multiple types
        of unique identifiers are supported.
        <br/> PHONE - means Phone based account alias registrations
      type: string
      enum: ['EMAIL', 'UUID', 'PHONE']
      x-example: EMAIL
    SettlementInstruction7:
      type: object
      properties:
        settlement_method:  # SttlmMtd
          $ref: '#/components/schemas/SettlementMethod1Code'
        clearing_system: # ClrSys
          $ref: '#/components/schemas/ClearingSystemIdentification3Choice'
      required:
      - settlement_method
      - clearing_system
    SettlementMethod1Code:
      type: string
      enum: ['CLRG']
      x-example: CLRG
    SignatureType:
      description: >-
        The type of the JWT. Required. Allowed values are 'PAYLOAD_DIGEST_SHA256'
      type: string
      enum: ['PAYLOAD_DIGEST_SHA256']
      x-example: PAYLOAD_DIGEST_SHA256
    StructuredRemittanceInformation16:
      type: object
      properties:
        referred_document_information:  # RfrdDocInf
          type: array
          items:
            $ref: '#/components/schemas/ReferredDocumentInformation7'
          description: >-
            Provides detailed information on the cancellation status reason.
          maxItems: 1
        referred_document_amount: # RfrdDocAmt
          $ref: '#/components/schemas/RemittanceAmount2'
        creditor_reference_information: # CdtrRefInf
          $ref: '#/components/schemas/CreditorReferenceInformation2'
        invoicer: # Invcr
          $ref: '#/components/schemas/PartyIdentification135'
        invoicee: # Invcee
          $ref: '#/components/schemas/PartyIdentification135'
        additional_remittance_information: # AddtlRmtInf
          description: >-
            This element is used to provide additional information, in free text form.
            (e.g. invoice description. etc.).
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          maxItems: 1
    SupplementaryInfo:
      description: Additional data block required for Fraud or Compliance reasons.
        All these 5 elements are required. Any conditional exceptions must be discussed/established
        with the Interac Fraud team prior to implementation.
      type: object
      properties:
        customer_ip_address:
          description: Public IP Address used by the Customer during payment initiation.
          type: string
          minLength: 7
          maxLength: 64
          example: *******
        customer_card_number:
          description: >-
            Customer's card number associated with the account, either clear value
            or hashed value. Supported hash algorithm - SHA256
          type: string
          minLength: 1
          maxLength: 64
          example: ****************
        account_creation_date:
          $ref: '#/components/schemas/CustomDate'
        customer_device_finger_print:
          description: >-
            Unique device fingerprint. The following options/types are supported in
            this preferred priority. They must start with a prefix (ITM or FTM or
            UID or CID) followed by the value of the ************* session id/device
            id/cookie id
              Interac ThreatMetrix profiling session Id        - ITM*************
              FI ThreatMetrix profiling Session Id             - FTM*************
              Unique Device Identifier of device               - UID*************
              Cookie Id Placed at customers computer or device - CID**************
          type: string
          minLength: 1
          maxLength: 256
          example: ITM1234567890123
        customer_authentication_method:
          description: >-
            Authentication method option used to authenticate the customer (sender)
            prior to payment initiation.
            The following values are currently supported.
          type: string
          enum: ['PASSWORD', 'PVQ', 'FINGERPRINT', 'BIO_METRICS', 'OTP', 'TOKEN',
            'MAKER_CHECKER', 'NONE', 'OTHER']
          x-example: PASSWORD
    TransactionTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.ssssssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.000Z'
    UnblockRequestorRequest:
      description: This service can be used by a Participant to allow their Customer
        to unblock a requestor that was previously blocked by the customer. The unblock
        operation requires the Interac generated identifier value of an existing block
        record (i.e. blockId). This information can be obtained via the GET BLOCKED
        REQUESTORS service.
      type: object
      required:
      - responder_handle
      properties:
        requestor_email:
          description: >-
            The requester email handle that has been blocked.
          type: string
          minLength: 1
          maxLength: 256
        responder_handle:
          $ref: "#/components/schemas/NotificationHandle"
    UpdateAccountAliasFraudStatusRequest:
      description: Fraud status request
      type: object
      required:
      - fraud_status
      properties:
        fraud_status:
          $ref: "#/components/schemas/FraudStatus"
        fraud_type:
          $ref: '#/components/schemas/FraudType'
        memo:
          $ref: "#/components/schemas/Memo"
    UpdatePaymentFraudStatusRequest:
      description: Fraud status request
      type: object
      required:
      - fraud_status
      properties:
        fraud_status:
          $ref: "#/components/schemas/FraudStatus"
        fraud_type:
          $ref: '#/components/schemas/FraudType'
        memo:
          $ref: "#/components/schemas/Memo"
    UpdateRequestForPaymentFraudStatusRequest:
      description: Fraud status request
      type: object
      required:
      - fraud_status
      properties:
        fraud_status:
          $ref: "#/components/schemas/FraudStatus"
        fraud_type:
          $ref: "#/components/schemas/FraudType"
        memo:
          $ref: "#/components/schemas/Memo"
    UuidAccountAliasHandle:
      allOf:
      - $ref: '#/components/schemas/AccountAliasRegHandle'
      - type: object
        description: AccountAliasRegServiceType UUID
        required:
        - uuid
        properties:
          uuid:
            type: string
            minLength: 1
            maxLength: 35
            description: The uuid of the contact
            example: ip1234234
