---
swagger: "2.0"
info:
  description: "BaaS::Interac eTransfer"
  version: "2.0.0"
  title: "interac-etransfer-api-dev"
host: "965036ss4h.execute-api.ca-central-1.amazonaws.com"
basePath: "/DEV"
schemes:
- "https"
paths:
  /v1/customer:
    post:
      operationId: "createCustomer"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/customer/{customer_id}:
    get:
      operationId: "retrieveCustomer"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "GET"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
    put:
      operationId: "updateCustomer"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "PUT"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/customer/{customer_id}/alias:
    get:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "GET"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/alias"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
    post:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/alias"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/customer/{customer_id}/alias/{alias_id}:
    get:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "alias_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "GET"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/alias/{alias_id}"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.path.alias_id: "method.request.path.alias_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
    delete:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "alias_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "DELETE"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/alias/{alias_id}"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.path.alias_id: "method.request.path.alias_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/customer/{customer_id}/disable:
    patch:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/disable"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/customer/{customer_id}/enable:
    patch:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "customer_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.customerEndpointUrl}/v1/customer/{customer_id}/enable"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.customer_id: "method.request.path.customer_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.customerVpcLink}"
  /v1/payment:
    post:
      operationId: "initiateSendPayment"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
  /v1/payment/options:
    post:
      operationId: "retrievePaymentOptions"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment/options"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
  /v1/payment/{payment_id}:
    get:
      parameters:
      - name: "payment_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: false
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: false
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: false
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "GET"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment/{payment_id}"
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.payment_id: "method.request.path.payment_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
    put:
      parameters:
      - name: "payment_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: false
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: false
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: false
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-warning-code:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "PUT"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment/{payment_id}"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.x-pg-warning-code: "integration.response.header.x-pg-warning-code"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.payment_id: "method.request.path.payment_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
  /v1/payment/{payment_id}/cancel:
    post:
      operationId: "cancelPayment"
      parameters:
      - name: "payment_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-warning-code:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment/{payment_id}/cancel"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.x-pg-warning-code: "integration.response.header.x-pg-warning-code"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.payment_id: "method.request.path.payment_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
  /v1/payment/{payment_id}/reverse:
    post:
      parameters:
      - name: "payment_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-warning-code:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.paymentEndpointUrl}/v1/payment/{payment_id}/reverse"
        responses:
          "400":
            statusCode: "400"
          "500":
            statusCode: "500"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.x-pg-warning-code: "integration.response.header.x-pg-warning-code"
          "404":
            statusCode: "404"
          "503":
            statusCode: "503"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.payment_id: "method.request.path.payment_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.paymentVpcLink}"
  /v1/request:
    post:
      operationId: "createRequestPayment"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.requestEndpointUrl}/v1/request"
        responses:
          default:
            statusCode: "200"
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.requestVpcLink}"
  /v1/request/{request_id}:
    get:
      operationId: "retrieveRequestPayment"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "request_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "GET"
        uri: "http://${stageVariables.requestEndpointUrl}/v1/request/{request_id}"
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.request_id: "method.request.path.request_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.requestVpcLink}"
  /v1/request/{request_id}/cancel:
    post:
      operationId: "cancelRequestPayment"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-service-account"
        in: "header"
        required: true
        type: "string"
      - name: "request_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      x-amazon-apigateway-integration:
        type: "http"
        httpMethod: "POST"
        uri: "http://${stageVariables.requestEndpointUrl}/v1/request/{request_id}/cancel"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.path.request_id: "method.request.path.request_id"
          integration.request.header.x-pg-service-account: "method.request.header.x-pg-service-account"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        connectionId: "${stageVariables.requestVpcLink}"
x-amazon-apigateway-gateway-responses:
  ACCESS_DENIED:
    statusCode: 403
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"NOT_AUTHORIZED\" } ] }"
  AUTHORIZER_FAILURE:
    statusCode: 500
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  INTEGRATION_FAILURE:
    statusCode: 504
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  INTEGRATION_TIMEOUT:
    statusCode: 504
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  DEFAULT_4XX:
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  BAD_REQUEST_BODY:
    statusCode: 400
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  DEFAULT_5XX:
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  THROTTLED:
    statusCode: 429
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"TOO_MANY_REQUESTS\" } ] }"
  REQUEST_TOO_LARGE:
    statusCode: 413
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  RESOURCE_NOT_FOUND:
    statusCode: 404
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  UNSUPPORTED_MEDIA_TYPE:
    statusCode: 415
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"UNSUPPORTED_MEDIA_TYPE\" } ]\
        \ }"
  QUOTA_EXCEEDED:
    statusCode: 429
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  UNAUTHORIZED:
    statusCode: 401
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"NOT_AUTHORIZED\" } ] }"
  API_CONFIGURATION_ERROR:
    statusCode: 500
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
  AUTHORIZER_CONFIGURATION_ERROR:
    statusCode: 500
    responseParameters:
      gatewayresponse.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
    responseTemplates:
      application/json: "{ \"error\": [ { \"code\": \"SERVICE_UNAVAILABLE\" } ] }"
