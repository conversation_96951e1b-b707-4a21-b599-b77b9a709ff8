apiVersion: apps/v1
kind: Deployment
metadata:
  name: limit-notification-kafka-listener-deployment
  namespace: pg-interac-dev
  labels:
    app: limit-notification-kafka-listener-v1
    domain: etransfer
#    tags.datadoghq.com/env: dev
#    tags.datadoghq.com/service: etransfer-limit-notification-kafka-listener-v1
#    tags.datadoghq.com/version: "20250225132712"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: limit-notification-kafka-listener-v1
  template:
    metadata:
#      annotations:
#        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: limit-notification-kafka-listener-v1
        domain: etransfer
    #    tags.datadoghq.com/env: dev
    #    tags.datadoghq.com/service: etransfer-limit-notification-kafka-listener-v1
    #    tags.datadoghq.com/version: "20250225132712"
#        admission.datadoghq.com/enabled: "true"
    spec:
      containers:
      - name: limit-notification-kafka-listener-container
        image: 017519569814.dkr.ecr.ca-central-1.amazonaws.com/limit-notification-kafka-listener:20250225132712
        resources:
          requests:
            memory: "400Mi"
            cpu: "100m"
          limits:
            memory: "600Mi"
            cpu: "200m"
        ports:
        - containerPort: 8080
        lifecycle:
          preStop:
            exec:
              command: ['sleep', '30']
        volumeMounts:
          - name: secret-volume
            mountPath: /vol/secrets
        # The kubelet uses startup probes to know when a container application has started. If such a probe is configured, it disables liveness and readiness checks until it succeeds, making sure those probes don't interfere with the application startup.
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 45
          periodSeconds: 15
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 37 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
        # The kubelet uses readiness probes to know when a container is ready to start accepting traffic. A Pod is considered ready when all of its containers are ready. One use of this signal is to control which Pods are used as backends for Services. When a Pod is not ready, it is removed from Service load balancers.
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 3 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
        # The kubelet uses liveness probes to know when to restart a container. For example, liveness probes could catch a deadlock, where an application is running, but unable to make progress. Restarting a container in such a state can help to make the application more available despite bugs.
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 600
          periodSeconds: 300
          timeoutSeconds: 30
          successThreshold: 1
          failureThreshold: 3
        env:
          - name: TZ
            value: 'America/Toronto'
          - name: SERVICE_ACCOUNT_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.service-account-api.host
          - name: KAFKA_SERVER
            valueFrom:
              configMapKeyRef:
                name: kafka-server
                key: sys.pn.kafka.url
          - name: KAFKA_LIMIT_BREACH_TOPIC
            valueFrom:
              configMapKeyRef:
                name: kafka-server
                key: sys.pn.kafka.topic.limit.notification
          - name: KAFKA_SSL_ENABLED
            valueFrom:
              configMapKeyRef:
                name: kafka-server
                key: sys.pn.kafka.ssl.enabled
          - name: DEAD_LETTER_TOPIC
            valueFrom:
              configMapKeyRef:
                name: kafka-server
                key: sys.pn.kafka.topic.dead.letter
          - name: KAFKA_SSL_TRUSTSTORE_LOCATION
            value: '/vol/secrets/kafka.truststore.jks'
          - name: KAFKA_SSL_KEYSTORE_LOCATION
            value: '/vol/secrets/kafka.keystore.jks'
          - name: KAFKA_SSL_TRUSTSTORE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: kafka-secret
                key: kafka.truststore.password
          - name: KAFKA_SSL_KEYSTORE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: kafka-secret
                key: kafka.keystore.password
          - name: KAFKA_SSL_KEY
            valueFrom:
              secretKeyRef:
                name: kafka-secret
                key: kafka.key.password
          - name: KAFKA_LIMIT_BREACH_CONSUMER_GROUP
            valueFrom:
              configMapKeyRef:
                name: kafka-server
                key: sys.pn.kafka.group.id.limit.notification
          - name: MAIL_SENDGRID_API_KEY
            valueFrom:
              secretKeyRef:
                name: sendgrid
                key: sendgrid.api.key
          - name: MAIL_CC_CONTACTS
            valueFrom:
              configMapKeyRef:
                name: limit-notification-listener
                key: mail.cc.contacts
          - name: MAIL_FROM_EMAIL
            valueFrom:
              configMapKeyRef:
                name: limit-notification-listener
                key: mail.from.email
          - name: MAIL_PTC_CONTACTS_INFO
            valueFrom:
              configMapKeyRef:
                name: limit-notification-listener
                key: mail.ptc.contact.info
          - name: DATABASE_REDIS_HOST
            valueFrom:
              secretKeyRef:
                name: system-database
                key: sys.redis-database.host
          - name: PTC_FI_ID
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.sys.ptc.fi-id
      volumes:
        - name: secret-volume
          secret:
            secretName: kafka-secret

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: limit-notification-kafka-listener-hpa
  namespace: pg-interac-dev
  labels:
    app: limit-notification-kafka-listener-v1
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: limit-notification-kafka-listener-deployment
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
