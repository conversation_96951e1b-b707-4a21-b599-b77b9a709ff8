kind: CronJob
apiVersion: batch/v1
metadata:
  name: batchjob-settle-fi-manual
  namespace: pg-interac-qa
  labels:
    app: batchjob-settle-fi-manual
    domain: etransfer
#    tags.datadoghq.com/env: qa
#    tags.datadoghq.com/service: etransfer-batchjob-settle-fi-manual
#    tags.datadoghq.com/version: "20250307135924"
spec:
  schedule: "* * 31 2 *"  # schedule to run it Feb 31, which is a never working day
  concurrencyPolicy: Forbid
  suspend: true           # set the suspend to be true to make it not auto start
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        metadata:
    #      annotations:
    #        admission.datadoghq.com/java-lib.version: latest
          labels:
            app: batchjob-settle-fi-manual
            domain: etransfer
        #    tags.datadoghq.com/env: qa
        #    tags.datadoghq.com/service: etransfer-batchjob-settle-fi-manual
        #    tags.datadoghq.com/version: "20250307135924"
    #        admission.datadoghq.com/enabled: "true"
        spec:
          restartPolicy: Never
          containers:
            - name: batch-app-manual-container
              image: 017519569814.dkr.ecr.ca-central-1.amazonaws.com/batch-app:20250307135924
              imagePullPolicy: ""
              resources: { }
              env:
              - name: SPRING_PROFILES_ACTIVE
                value: 'settleFIJob'
              - name: TZ
                value: 'America/Toronto'
              - name: DATABASE_HOST
                valueFrom:
                  secretKeyRef:
                    name: transaction-database
                    key: sys.database.host
              - name: DATABASE_NAME
                valueFrom:
                  secretKeyRef:
                    name: transaction-database
                    key: sys.database.name
              - name: DATABASE_USER
                valueFrom:
                  secretKeyRef:
                    name: transaction-database
                    key: sys.database.username
              - name: DATABASE_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: transaction-database
                    key: sys.database.password
              - name: RECON_DATABASE_HOST
                valueFrom:
                  secretKeyRef:
                    name: batch-database
                    key: sys.database.host
              - name: RECON_DATABASE_NAME
                valueFrom:
                  secretKeyRef:
                    name: batch-database
                    key: sys.database.name
              - name: RECON_DATABASE_USER
                valueFrom:
                  secretKeyRef:
                    name: batch-database
                    key: sys.database.username
              - name: RECON_DATABASE_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: batch-database
                    key: sys.database.password
              - name: AWS_ENDPOINT_URL
                valueFrom:
                  secretKeyRef:
                    name: aws-s3-interac-bucket
                    key: aws.endpoint.url
              - name: AWS_ACCESS_KEY
                valueFrom:
                  secretKeyRef:
                    name: aws-s3-interac-bucket
                    key: aws.access.key
              - name: AWS_SECRET_KEY
                valueFrom:
                  secretKeyRef:
                    name: aws-s3-interac-bucket
                    key: aws.secret.key
              - name: AWS_BUCKET_NAME
                valueFrom:
                  secretKeyRef:
                    name: aws-s3-interac-bucket
                    key: aws.bucket.name
              - name: EXCEPTION_OUTPUT_LOCATION
                valueFrom:
                  configMapKeyRef:
                    name: batch-app-config
                    key: app.batch.exception.output.location
              - name: AWS_TEMP_LOCATION
                valueFrom:
                  configMapKeyRef:
                    name: batch-app-config
                    key: app.batch.aws.temp.folder
              - name: RECON_OUTPUT_LOCATION
                valueFrom:
                  configMapKeyRef:
                    name: batch-app-config
                    key: app.batch.recon.output.location
              - name: SETTLEMENT_OUTPUT_LOCATION
                valueFrom:
                  configMapKeyRef:
                    name: batch-app-config
                    key: app.batch.settlement.output.location
              - name: RECON_DATE  # this is the special field for manually job
                valueFrom:
                  configMapKeyRef:
                    name: batch-app-config-date
                    key: app.batch.recon.date
              - name: EFT_API_URL
                valueFrom:
                  secretKeyRef:
                    name: eft-api
                    key: sys.pn.eft.url
              - name: EFT_API_ID
                valueFrom:
                  secretKeyRef:
                    name: eft-api
                    key: sys.pn.eft.client.id
              - name: EFT_API_SECRET
                valueFrom:
                  secretKeyRef:
                    name: eft-api
                    key: sys.pn.eft.client.secret
              - name: TRANSACTION_CALENDAR_API_HOST
                valueFrom:
                  configMapKeyRef:
                    name: application-config
                    key: app.internal.transaction-calendar-api.host
              - name: PTC_FI_ID
                valueFrom:
                  configMapKeyRef:
                    name: application-config
                    key: app.sys.ptc.fi-id
