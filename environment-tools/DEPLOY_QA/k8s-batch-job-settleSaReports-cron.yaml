kind: CronJob
apiVersion: batch/v1
metadata:
  name: batchjob-settlesa-reports-cron
  namespace: pg-interac-qa
  labels:
    app: batchjob-settlesa-reports-cron
    domain: etransfer
#    tags.datadoghq.com/env: qa
#    tags.datadoghq.com/service: etransfer-batchjob-settlesa-reports-cron
#    tags.datadoghq.com/version: "20250307135924"
spec:
  schedule: "10 03 * * *"  # 03:10 ET
  timeZone: 'America/Toronto'
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        metadata:
    #      annotations:
    #        admission.datadoghq.com/java-lib.version: latest
          labels:
            app: batchjob-settlesa-reports-cron
            domain: etransfer
        #    tags.datadoghq.com/env: qa
        #    tags.datadoghq.com/service: etransfer-batchjob-settlesa-reports-cron
        #    tags.datadoghq.com/version: "20250307135924"
    #        admission.datadoghq.com/enabled: "true"
        spec:
          restartPolicy: OnFailure
          containers:
            - name: batch-app-cron-container
              image: 017519569814.dkr.ecr.ca-central-1.amazonaws.com/batch-app:20250307135924
              imagePullPolicy: ""
              resources: { }
              env:
                - name: SPRING_PROFILES_ACTIVE
                  value: 'settleSAReportsJob'
                - name: TZ
                  value: 'America/Toronto'
                - name: DATABASE_HOST
                  valueFrom:
                    secretKeyRef:
                      name: transaction-database
                      key: sys.database.host.ro
                - name: DATABASE_NAME
                  valueFrom:
                    secretKeyRef:
                      name: transaction-database
                      key: sys.database.name
                - name: DATABASE_USER
                  valueFrom:
                    secretKeyRef:
                      name: transaction-database
                      key: sys.database.username
                - name: DATABASE_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: transaction-database
                      key: sys.database.password
                - name: RECON_DATABASE_HOST
                  valueFrom:
                    secretKeyRef:
                      name: batch-database
                      key: sys.database.host
                - name: RECON_DATABASE_NAME
                  valueFrom:
                    secretKeyRef:
                      name: batch-database
                      key: sys.database.name
                - name: RECON_DATABASE_USER
                  valueFrom:
                    secretKeyRef:
                      name: batch-database
                      key: sys.database.username
                - name: RECON_DATABASE_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: batch-database
                      key: sys.database.password
                - name: AWS_ENDPOINT_URL
                  valueFrom:
                    secretKeyRef:
                      name: aws-s3-interac-bucket
                      key: aws.endpoint.url
                - name: AWS_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-s3-interac-bucket
                      key: aws.access.key
                - name: AWS_SECRET_KEY
                  valueFrom:
                    secretKeyRef:
                      name: aws-s3-interac-bucket
                      key: aws.secret.key
                - name: AWS_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: aws-s3-interac-bucket
                      key: aws.bucket.name
                - name: EXCEPTION_OUTPUT_LOCATION
                  valueFrom:
                    configMapKeyRef:
                      name: batch-app-config
                      key: app.batch.exception.output.location
                - name: AWS_TEMP_LOCATION
                  valueFrom:
                    configMapKeyRef:
                      name: batch-app-config
                      key: app.batch.aws.temp.folder
                - name: RECON_OUTPUT_LOCATION
                  valueFrom:
                    configMapKeyRef:
                      name: batch-app-config
                      key: app.batch.recon.output.location
                - name: SETTLEMENT_OUTPUT_LOCATION
                  valueFrom:
                    configMapKeyRef:
                      name: batch-app-config
                      key: app.batch.settlement.output.location
                - name: EXCEPTION_FILE_LOCATION
                  valueFrom:
                    configMapKeyRef:
                      name: batch-app-config
                      key: app.batch.exception.output.location
                - name: EFT_API_URL
                  valueFrom:
                    secretKeyRef:
                      name: eft-api
                      key: sys.pn.eft.url
                - name: EFT_API_ID
                  valueFrom:
                    secretKeyRef:
                      name: eft-api
                      key: sys.pn.eft.client.id
                - name: EFT_API_SECRET
                  valueFrom:
                    secretKeyRef:
                      name: eft-api
                      key: sys.pn.eft.client.secret
                - name: PTC_FI_ID
                  valueFrom:
                    configMapKeyRef:
                      name: application-config
                      key: app.sys.ptc.fi-id
