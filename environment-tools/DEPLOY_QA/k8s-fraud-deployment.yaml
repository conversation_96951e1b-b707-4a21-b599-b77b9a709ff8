apiVersion: apps/v1
kind: Deployment
metadata:
  name: fraud-deployment
  namespace: pg-interac-qa
  labels:
    app: fraud-v1
    domain: etransfer
#    tags.datadoghq.com/env: qa
#    tags.datadoghq.com/service: etransfer-fraud-v1
#    tags.datadoghq.com/version: "20250312104929"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fraud-v1
  template:
    metadata:
#      annotations:
#        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: fraud-v1
        domain: etransfer
    #    tags.datadoghq.com/env: qa
    #    tags.datadoghq.com/service: etransfer-fraud-v1
    #    tags.datadoghq.com/version: "20250312104929"
#        admission.datadoghq.com/enabled: "true"
    spec:
      containers:
      - name: fraud-container
        image: 017519569814.dkr.ecr.ca-central-1.amazonaws.com/fraud:20250312104929
        resources:
          requests:
            memory: "400Mi"
            cpu: "100m"
          limits:
            memory: "500Mi"
            cpu: "200m"
        ports:
        - containerPort: 8080
        # The kubelet uses startup probes to know when a container application has started. If such a probe is configured, it disables liveness and readiness checks until it succeeds, making sure those probes don't interfere with the application startup.
        lifecycle:
          preStop:
            exec:
              command: ['sleep', '30']
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 45
          periodSeconds: 15
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 37 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
        # The kubelet uses readiness probes to know when a container is ready to start accepting traffic. A Pod is considered ready when all of its containers are ready. One use of this signal is to control which Pods are used as backends for Services. When a Pod is not ready, it is removed from Service load balancers.
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 3 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
        # The kubelet uses liveness probes to know when to restart a container. For example, liveness probes could catch a deadlock, where an application is running, but unable to make progress. Restarting a container in such a state can help to make the application more available despite bugs.
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 600
          periodSeconds: 300
          timeoutSeconds: 30
          successThreshold: 1
          failureThreshold: 3
        env:
          - name: TZ
            value: 'America/Toronto'
          - name: DATABASE_HOST
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.host
          - name: DATABASE_NAME
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.name
          - name: DATABASE_USER
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.username
          - name: DATABASE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.password
          - name: CUSTOMER_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.customer-api.host
          - name: SYSTEM_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.system-api.host
          - name: SYSTEM_API_READ_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.system-api.read.timeout
          - name: SERVICE_ACCOUNT_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.service-account-api.host
          - name: INTERAC_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.external.interac-api.host
          - name: INTERAC_READ_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.external.interac-api.read.timeout
          - name: INTERAC_GET_READ_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.external.interac-api.get.read.timeout
          - name: INTERAC_CONNECTION_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.external.interac-api.connection.timeout
          - name: PEOPLES_JWT_KEY_ID
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.sys.jwt.peoples.key-id
          - name: INTERAC_JWT_KEY_ID
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.sys.jwt.interac.key-id
          - name: COMPRESS_HTTP_CONTENT
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.external.interac-api.disable-gzip
          - name: PTC_FI_ID
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.sys.ptc.fi-id
        volumeMounts:
        - name: jw-certificate-vol
          mountPath: /vol/security
      volumes:
      - name: jw-certificate-vol
        secret:
          secretName: jw-certificate
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fraud-hpa
  namespace: pg-interac-qa
  labels:
    app: fraud-v1
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fraud-deployment
  minReplicas: 1
  maxReplicas: 1
  metrics:
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
