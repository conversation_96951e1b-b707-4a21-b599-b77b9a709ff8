apiVersion: apps/v1
kind: Deployment
metadata:
  name: inquiry-deployment
  namespace: pg-interac-integration
  labels:
    app: inquiry-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inquiry-v2
  template:
    metadata:
      labels:
        app: inquiry-v2
    spec:
      containers:
      - name: inquiry-container
        image: 017519569814.dkr.ecr.ca-central-1.amazonaws.com/inquiry:20230814131919
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 3
          timeoutSeconds: 2
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 15
          timeoutSeconds: 10
          failureThreshold: 10
          successThreshold: 1
        env:
          - name: TZ
            value: 'America/Toronto'
          - name: DISABLE_COGNITO_JWT_VERIFICATION
            value: 'false'
          - name: DATABASE_HOST
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.host.ro
          - name: DATABASE_NAME
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.name
          - name: DATABASE_USER
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.username
          - name: DATABASE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: transaction-database
                key: sys.database.password
          - name: CUSTOMER_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.customer-api.host
          - name: SYSTEM_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.system-api.host
          - name: SERVICE_ACCOUNT_API_HOST
            valueFrom:
              configMapKeyRef:
                name: application-config
                key: app.internal.service-account-api.host
        volumeMounts:
        - name: jw-certificate-vol
          mountPath: /vol/security
        - name: secret-volume
          mountPath: /vol/secrets
      volumes:
      - name: jw-certificate-vol
        secret:
          secretName: jw-certificate
      - name: secret-volume
        secret:
          secretName: kafka-secret
