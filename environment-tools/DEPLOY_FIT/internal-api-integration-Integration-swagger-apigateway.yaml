---
swagger: "2.0"
info:
  version: "2021-02-02T17:52:58Z"
  title: "internal-api-integration"
host: "skvql799ll.execute-api.ca-central-1.amazonaws.com"
basePath: "/Integration"
schemes:
- "https"
paths:
  /v1/query/transaction/search:
    post:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "max_items"
        in: "query"
        required: false
        type: "string"
      - name: "offset"
        in: "query"
        required: false
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.queryVpcLink}"
        httpMethod: "POST"
        uri: "http://${stageVariables.queryEndpointUrl}/v1/query/transaction/search"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.querystring.max_items: "method.request.querystring.max_items"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.querystring.offset: "method.request.querystring.offset"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,POST'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/query/transaction/{payment_ref_id}:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "payment_ref_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "400":
          description: "400 response"
        "500":
          description: "500 response"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
        "503":
          description: "503 response"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.queryVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.queryEndpointUrl}/v1/query/transaction/{payment_ref_id}"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
          "500":
            statusCode: "500"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
          "503":
            statusCode: "503"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.payment_ref_id: "method.request.path.payment_ref_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "payment_ref_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    put:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PUT"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS,PUT'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/contact:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/contact"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    post:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "POST"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/contact"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    put:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "505":
          description: "505 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PUT"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/contact"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "505"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS,POST,PUT'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/disable:
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PATCH'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
    patch:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/disable"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
  /v1/service-account/{service_account_id}/enable:
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PATCH'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
    patch:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/enable"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
  /v1/service-account/{service_account_id}/events:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "category"
        in: "query"
        required: false
        type: "string"
      - name: "max_items"
        in: "query"
        required: false
        type: "string"
      - name: "offset"
        in: "query"
        required: false
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "to_date"
        in: "query"
        required: false
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "from_date"
        in: "query"
        required: false
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/events"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.querystring.max_items: "method.request.querystring.max_items"
          integration.request.querystring.from_date: "method.request.querystring.from_date"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.querystring.to_date: "method.request.querystring.to_date"
          integration.request.querystring.category: "method.request.querystring.category"
          integration.request.querystring.offset: "method.request.querystring.offset"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/feature:
    post:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "POST"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/feature"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "403":
            statusCode: "403"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,POST'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/feature/status:
    put:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PUT"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/feature/status"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PUT'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/feature/{feature_name}/disable:
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "feature_name"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PATCH'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
    patch:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "feature_name"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/feature/{feature_name}/disable"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.path.feature_name: "method.request.path.feature_name"
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
  /v1/service-account/{service_account_id}/feature/{feature_name}/enable:
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "feature_name"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PATCH'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
    patch:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "feature_name"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/feature/{feature_name}/enable"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.path.feature_name: "method.request.path.feature_name"
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
  /v1/service-account/{service_account_id}/limit:
    get:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Authorization: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "GET"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/limit"
        responses:
          "200":
            statusCode: "200"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    post:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "201":
          description: "201 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "400":
          description: "400 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "POST"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/limit"
        responses:
          "201":
            statusCode: "201"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    put:
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PUT"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/limit"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS,POST,PUT'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
  /v1/service-account/{service_account_id}/suspend:
    options:
      consumes:
      - "application/json"
      produces:
      - "application/json"
      parameters:
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      responses:
        "200":
          description: "200 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            Access-Control-Allow-Methods:
              type: "string"
            Access-Control-Allow-Headers:
              type: "string"
      x-amazon-apigateway-integration:
        responses:
          default:
            statusCode: "200"
            responseParameters:
              method.response.header.Access-Control-Allow-Methods: "'OPTIONS,PATCH'"
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,x-pg-service-account,x-pg-interaction-id,x-pg-interaction-timestamp'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestTemplates:
          application/json: "{\"statusCode\": 200}"
        passthroughBehavior: "when_no_match"
        type: "mock"
    patch:
      produces:
      - "application/json"
      parameters:
      - name: "x-pg-interaction-timestamp"
        in: "header"
        required: true
        type: "string"
      - name: "service_account_id"
        in: "path"
        required: true
        type: "string"
      - name: "x-pg-interaction-id"
        in: "header"
        required: true
        type: "string"
      - name: "Authorization"
        in: "header"
        required: true
        type: "string"
      responses:
        "400":
          description: "400 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "500":
          description: "500 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "401":
          description: "401 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "204":
          description: "204 response"
          schema:
            $ref: "#/definitions/Empty"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "403":
          description: "403 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
        "404":
          description: "404 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
        "503":
          description: "503 response"
          headers:
            Access-Control-Allow-Origin:
              type: "string"
            x-pg-correspondent-id:
              type: "string"
            x-pg-interaction-id:
              type: "string"
      security:
      - Admin-risk-auth-py: []
      x-amazon-apigateway-integration:
        connectionId: "${stageVariables.serviceAccountVpcLink}"
        httpMethod: "PATCH"
        uri: "http://${stageVariables.serviceAccountEndpointUrl}/v1/service-account/{service_account_id}/suspend"
        responses:
          "400":
            statusCode: "400"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "500":
            statusCode: "500"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "401":
            statusCode: "401"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "204":
            statusCode: "204"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "403":
            statusCode: "403"
            responseParameters:
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "404":
            statusCode: "404"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
          "503":
            statusCode: "503"
            responseParameters:
              method.response.header.x-pg-correspondent-id: "integration.response.header.x-pg-correspondent-id"
              method.response.header.x-pg-interaction-id: "integration.response.header.x-pg-interaction-id"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        requestParameters:
          integration.request.header.x-pg-interaction-id: "method.request.header.x-pg-interaction-id"
          integration.request.header.Authorization: "method.request.header.Authorization"
          integration.request.path.service_account_id: "method.request.path.service_account_id"
          integration.request.header.x-pg-interaction-timestamp: "method.request.header.x-pg-interaction-timestamp"
        passthroughBehavior: "when_no_match"
        connectionType: "VPC_LINK"
        type: "http"
securityDefinitions:
  Authorization:
    type: "apiKey"
    name: "Authorization"
    in: "header"
    x-amazon-apigateway-authtype: "cognito_user_pools"
    x-amazon-apigateway-authorizer:
      providerARNs:
      - "arn:aws:cognito-idp:us-east-2:************:userpool/us-east-2_oH2B5QIUo"
      type: "cognito_user_pools"
  Admin-auth-py:
    type: "apiKey"
    name: "Authorization"
    in: "header"
    x-amazon-apigateway-authtype: "custom"
    x-amazon-apigateway-authorizer:
      authorizerUri: "arn:aws:apigateway:ca-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ca-central-1:************:function:pct-adminui-admin-auth/invocations"
      authorizerResultTtlInSeconds: 300
      type: "token"
  Admin-risk-auth-py:
    type: "apiKey"
    name: "Authorization"
    in: "header"
    x-amazon-apigateway-authtype: "custom"
    x-amazon-apigateway-authorizer:
      authorizerUri: "arn:aws:apigateway:ca-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:ca-central-1:************:function:pct-adminui-admin-risk-auth/invocations"
      authorizerResultTtlInSeconds: 300
      type: "token"
definitions:
  Empty:
    type: "object"
    title: "Empty Schema"
x-amazon-apigateway-policy:
  Version: "2012-10-17"
  Statement:
  - Effect: "Allow"
    Principal: "*"
    Action: "execute-api:Invoke"
    Resource: "arn:aws:execute-api:ca-central-1:************:oik9ebu91a/*/*/*"
  - Effect: "Deny"
    Principal: "*"
    Action: "execute-api:Invoke"
    Resource: "arn:aws:execute-api:ca-central-1:************:oik9ebu91a/*/*/*"
    Condition:
      NotIpAddress:
        aws:SourceIp:
        - "**************"
        - "**************"
        - "*************"
        - "*************"
        - "************"
        - "**************"
        - "************"
