package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * Adding all actuator requests
 */
@Service
@Log4j2
public class ActuatorService {

  private final WireMockServer wireMockServer;

  public ActuatorService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  /**
   * initialization method, will setup all actuator responses
   */
  @PostConstruct
  public void setupRequestAdaptor() {

    //cancel requests - no content for any cancel
    wireMockServer.stubFor(get(urlMatching("/actuator"))
        .willReturn(WireMock.aResponse().withBodyFile("ActuatorResponse.json")));
    wireMockServer.stubFor(get(urlMatching("/actuator/health"))
        .willReturn(WireMock.aResponse().withBodyFile("ActuatorHealthResponse.json")));

  }

}
