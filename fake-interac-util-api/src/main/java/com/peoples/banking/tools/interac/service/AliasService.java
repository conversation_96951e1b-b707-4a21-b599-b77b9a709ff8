package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.http.RequestMethod.GET;
import static com.github.tomakehurst.wiremock.http.RequestMethod.POST;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.DATE_FORMAT;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.PARTICIPANT_ID;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.SERVICE_ACCOUNT_ID;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.TIMESTAMP_FORMAT;
import static com.peoples.banking.tools.interac.util.Constants.ACCOUNT_ALIAS_REGISTRATIONS;
import static com.peoples.banking.tools.interac.util.Constants.ALIAS_REGISTRATIONS_PATH;
import static com.peoples.banking.tools.interac.util.Constants.ANYMATCH_REGEXP;
import static com.peoples.banking.tools.interac.util.Constants.CACHE_SECONDS_DURATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Request;
import com.google.common.io.Resources;
import com.peoples.banking.tools.interac.extensions.VariablesTransformer;
import com.peoples.banking.tools.interac.extensions.WebHooksCustom;
import com.peoples.banking.tools.interac.util.JsonUtil;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;

/**
 * adding all alias related setup API's
 */
@Component
@Log4j2
public class AliasService {

  private final WireMockServer wireMockServer;

  private final static Cache<String, Map<String, String>> aliasesVariablesMap = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();
  private final static Cache<String, String> customerAliasReferenceMap = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();
  private final static Cache<String, List<Map<String, String>>> customerAliasesMap = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();

  private static String ALIASES_RESPONSE_START_TEMPLATE;
  private static String CUSTOMER_ALIASES_TEMPLATE;
  private static String ALIASES_RESPONSE_END_TEMPLATE;

  public AliasService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  /**
   * setting up rules for alias requests
   */
  @PostConstruct
  public void setupAliases() {
    readAliasTemplates();

    //register alias payment
    wireMockServer.stubFor(post(urlMatching(ALIAS_REGISTRATIONS_PATH))
        //successful RegisterAliasResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("alias/RegisterAliasResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //retrieve all aliases
    wireMockServer.stubFor(get(urlMatching(ALIAS_REGISTRATIONS_PATH + ".*"))
        //successful RetrieveAliasesResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("alias/RetrieveAliasesResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //retrieve specific alias
    wireMockServer.stubFor(get(urlMatching(ALIAS_REGISTRATIONS_PATH + ANYMATCH_REGEXP))
        //successful RetrieveSpecificAliasResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("alias/RetrieveSpecificAliasResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //update alias request - no content in response
    wireMockServer.stubFor(put(urlMatching(ALIAS_REGISTRATIONS_PATH + ANYMATCH_REGEXP))
        .willReturn(WireMock.noContent()));

    //delete alias request - no content in response
    wireMockServer.stubFor(delete(urlMatching(ALIAS_REGISTRATIONS_PATH + ANYMATCH_REGEXP))
        .willReturn(WireMock.noContent()));
  }

  /**
   * storing values from request to use in responses
   *
   * @param request - original request that come
   */
  public static void storeValuesFromRegisterRequests(Request request, String aliasIdentification) {
    //storing values for Create Account Alias Registration request
    if (POST.equals(request.getMethod())) {
      String serviceAccountId = request.getHeader(SERVICE_ACCOUNT_ID);
      saveValuesFromCreateAliasRegistrationRequest(request.getBodyAsString(), aliasIdentification, serviceAccountId);
      String participantId = request.getHeader(PARTICIPANT_ID);
      if (participantId != null) {
        customerAliasReferenceMap.put(participantId, aliasIdentification);
      }
    }
  }

  /**
   * saving values from CreateAccountAliasRegistrationResponse for future use
   *
   * @param createAliasRegistrationRequest - body of CreateAccountAliasRegistrationResponse
   */
  private static void saveValuesFromCreateAliasRegistrationRequest(String createAliasRegistrationRequest, String aliasIdentification,
      String serviceAccountId) {

    JsonNode jsonNode = JsonUtil.parseJsonNode(createAliasRegistrationRequest);

    Map<String, String> variablesMap = new HashMap<>();

    variablesMap.put("accountAliasReference", aliasIdentification);
    variablesMap.put("serviceType", WebHooksCustom.extract("/service_type", jsonNode));
    variablesMap.put("accountAliasHandle", WebHooksCustom.extract("/account_alias_handle", jsonNode));
    variablesMap.put("accountCreationDate", WebHooksCustom.extract("/account_creation_date", jsonNode));
    variablesMap.put("participantAccountAliasReference", WebHooksCustom.extract("/participant_account_alias_reference", jsonNode));
    variablesMap.put("accountHolderName", WebHooksCustom.extract("/customer_account/account_holder_name", jsonNode));
    variablesMap.put("accountNumber", WebHooksCustom.extract("/customer_account/bank_account_identifier/account", jsonNode));

    aliasesVariablesMap.put(aliasIdentification, variablesMap);
    List<Map<String, String>> mapList = serviceAccountId == null ? null : customerAliasesMap.getIfPresent(serviceAccountId);
    if (mapList == null) {
      mapList = new ArrayList<>();
    }
    mapList.add(variablesMap);
    if (serviceAccountId != null) {
      customerAliasesMap.put(serviceAccountId, mapList);
    }
  }


  /**
   * extract alias id from request and trying to get map with all variables for this request
   *
   * @param request - original request
   * @return - map with all needed variables to fulfill response template
   */
  public static Map<String, String> getAliasVariablesMap(Request request, String aliasId) {
    if (GET.equals(request.getMethod())) {
      String url = request.getUrl();
      //this is retrieve all aliases request
      if (url.endsWith(ACCOUNT_ALIAS_REGISTRATIONS) || url.contains("offset=") || url.contains("max-response-items")) {
        String participantId = request.getHeader(PARTICIPANT_ID);
        aliasId = participantId == null ? "emptyAliasId" :  customerAliasReferenceMap.getIfPresent(participantId);
      } else {
        aliasId = url.substring(url.lastIndexOf("/") + 1);
      }
    }

    Map<String, String> originalMap = aliasId == null ? null :  aliasesVariablesMap.getIfPresent(aliasId);
    Map<String, String> result;
    if (originalMap == null) {
      log.warn("Failed to found an existing map for alias id {} ", aliasId);
      result = new HashMap<>();
    } else {
      result = new HashMap<>(originalMap);
    }
    Date currentDate = new Date();
    Date expiredDate = DateUtils.addMonths(currentDate, 1);
    result.put("timestamp", TIMESTAMP_FORMAT.format(currentDate));
    result.put("datestamp", DATE_FORMAT.format(currentDate));

    result.put("expirationTimestamp", TIMESTAMP_FORMAT.format(expiredDate));

    return result;
  }

  private static List<Map<String, String>> getAllCustomerAliases(String participantId) {
    if (participantId == null) {
      return null;
    }
    return customerAliasesMap.getIfPresent(participantId);
  }

  /**
   * building aliases json with start/end json and list of customer aliases
   *
   * @param request - original request
   * @return - json with customer aliases
   */
  public static String buildAliasesResponseBody(Request request) {
    Date currentDate = new Date();
    Date expiredDate = DateUtils.addMonths(currentDate, 1);
    StringBuilder result = new StringBuilder(ALIASES_RESPONSE_START_TEMPLATE);
    String participantId = request.getHeader(SERVICE_ACCOUNT_ID);
    List<Map<String, String>> customerAliases = getAllCustomerAliases(participantId);
    if (customerAliases != null) {
      for (Map<String, String> customerAlias : customerAliases) {
        customerAlias.put("expirationTimestamp", TIMESTAMP_FORMAT.format(expiredDate));
        String customerAliasJson = VariablesTransformer.fillVariablesFromMap(CUSTOMER_ALIASES_TEMPLATE, customerAlias);
        result.append(customerAliasJson);
      }
      //removing the latest comma from template
      result = new StringBuilder(result.substring(0, result.lastIndexOf(",")));
    }

    result.append(ALIASES_RESPONSE_END_TEMPLATE);
    return result.toString();
  }

  /**
   * reading template files, then it will be populated with data
   */
  private void readAliasTemplates() {
    try {
      ALIASES_RESPONSE_START_TEMPLATE = Resources
          .toString(Resources.getResource("wiremock/__files/alias/RetrieveAliasesResponseBeginTemplate.json"), Charset.defaultCharset());
      CUSTOMER_ALIASES_TEMPLATE = Resources
          .toString(Resources.getResource("wiremock/__files/alias/CustomerAliasTemplate.json"), Charset.defaultCharset());
      ALIASES_RESPONSE_END_TEMPLATE = Resources
          .toString(Resources.getResource("wiremock/__files/alias/RetrieveAliasesResponseEndTemplate.json"), Charset.defaultCharset());
    } catch (IOException e) {
      log.error("Failed to read response templates ", e);
    }
  }

}