package com.peoples.banking.tools.interac.extensions;

import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.ACCOUNT_NUMBER;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.INTERAC_DEPOSIT_PAYMENT_ID;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.INTERAC_REQUEST_ID;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.MESSAGE_IDENTIFICATION;
import static com.peoples.banking.tools.interac.service.FraudService.FRAUD_STATUS;
import static com.peoples.banking.tools.interac.service.PaymentService.DETAILS;
import static com.peoples.banking.tools.interac.service.PaymentService.TRANSACTION;
import static com.peoples.banking.tools.interac.util.Constants.ACCOUNT_ALIAS_REGISTRATIONS;
import static com.peoples.banking.tools.interac.util.Constants.ALIAS_REGISTRATIONS_PATH;
import static com.peoples.banking.tools.interac.util.Constants.PAYMENTS;
import static com.peoples.banking.tools.interac.util.Constants.REPLACEMENT_BODY;
import static com.peoples.banking.tools.interac.util.Constants.TIMEOUT_PATH;

import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.common.FileSource;
import com.github.tomakehurst.wiremock.extension.Parameters;
import com.github.tomakehurst.wiremock.extension.responsetemplating.ResponseTemplateTransformer;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.ResponseDefinition;
import com.peoples.banking.tools.interac.service.AliasService;
import com.peoples.banking.tools.interac.service.ConfigDelayService;
import com.peoples.banking.tools.interac.service.FraudService;
import com.peoples.banking.tools.interac.service.PaymentService;
import java.util.Date;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;
import org.springframework.http.HttpStatus;

/**
 * Custom implementation of response template transformer filling variables from global cache to templates
 */
public class VariablesTransformer extends ResponseTemplateTransformer {

  public VariablesTransformer(boolean global) {
    super(global);
  }

  /**
   * transform wiremock response with variables from global cache
   *
   * @param request            - original response
   * @param responseDefinition - some response definition
   * @param files              - template files
   * @param parameters         - parameters that passed to method
   * @return - updated response definition
   */
  public ResponseDefinition transform(Request request, final ResponseDefinition responseDefinition, FileSource files,
      Parameters parameters) {
    ResponseDefinitionBuilder newResponseDefBuilder = ResponseDefinitionBuilder.like(responseDefinition);
    ResponseDefinition transform = super.transform(request, responseDefinition, files, parameters);

    HttpHeader header = request.header("x-et-participant-user-id");
    String enrollmentId = header != null && header.isPresent() ? header.firstValue() : "enrollmentId";
    if (transform.getHeaders() != null) {
      newResponseDefBuilder.withHeaders(transform.getHeaders());
    }
    String originalBody = transform.getBody();
    String requestUrl = request.getUrl();

    if (requestUrl.contains(TIMEOUT_PATH)) {
      ConfigDelayService.setGlobalDelay(request);
    }

    if (originalBody != null) {
      String aliasIdentification = null;
      if (requestUrl.endsWith(TRANSACTION)) {
        PaymentService.storeValuesFromPaymentRequests(request);
      } else if (requestUrl.contains(ACCOUNT_ALIAS_REGISTRATIONS)) {
        aliasIdentification = WebHooksCustom.findByKeyEnrollmentId("accountAliasReference", UUID.randomUUID().toString(), 16);
        AliasService.storeValuesFromRegisterRequests(request, aliasIdentification);
      } else if (requestUrl.endsWith(DETAILS)) {
        PaymentService.storeValuesFromPaymentDetails(request);
      }

      if (requestUrl.contains(PAYMENTS) && !requestUrl.contains("/options")) {
        Map<String, String> paymentVariablesMap = PaymentService.getPaymentVariablesMap(request);
        newResponseDefBuilder.withBody(fillVariablesFromMap(originalBody, paymentVariablesMap));
      } else if (requestUrl.contains(ALIAS_REGISTRATIONS_PATH)) {
        if (originalBody.contains(REPLACEMENT_BODY)) {
          newResponseDefBuilder.withBody(AliasService.buildAliasesResponseBody(request));
        } else {
          Map<String, String> aliasesVariablesMap = AliasService.getAliasVariablesMap(request, aliasIdentification);
          newResponseDefBuilder.withBody(fillVariablesFromMap(originalBody, aliasesVariablesMap));
        }
      } else {
        newResponseDefBuilder.withBody(fillBodyVariables(originalBody, enrollmentId));
      }

    }

    //case for fraud status update
    if (requestUrl.endsWith(FRAUD_STATUS)) {
      boolean sameUpdatedStatus = FraudService.storeValuesFromUpdateFraudStatus(request);
      if (!sameUpdatedStatus) {
        return newResponseDefBuilder.but().withBody("{\"code\":\"356\",\"text\":\"Invalid Fraud Status\"}")
            .withStatus(HttpStatus.BAD_REQUEST.value())
            .build();
      }
    }

    return newResponseDefBuilder.build();
  }

  /**
   * Unique name for transformer, should be registered and called by that name
   *
   * @return - unique name for registering transformer
   */
  @Override
  public String getName() {
    return "variables-transformer";
  }

  /**
   * Take original body template and all variables for this enrollment id from Webhook cache, then replace it with specific for this
   * enrollment id values
   *
   * @param originalBody - body of original template
   * @param enrollmentId - request enrollment id
   * @return - template with filled variables (standard ones will be processed by parent - and they are passed with double {{}}
   */
  public String fillBodyVariables(String originalBody, String enrollmentId) {
    String result = originalBody;
    result = replaceVariable(result, "amount", WebHooksCustom.findByKeyWithTypeByEnrollmentId("amount", enrollmentId, Double.class));
    result = replaceVariable(result, "expiryDate", WebHooksCustom.findByKeyWithTypeByEnrollmentId("expiryDate", enrollmentId, Date.class));
    result = replaceVariable(result, INTERAC_REQUEST_ID, WebHooksCustom.findByKeyEnrollmentId(INTERAC_REQUEST_ID, enrollmentId));
    result = replaceVariable(result, "requestId", WebHooksCustom.findByKeyEnrollmentId("requestId", enrollmentId));
    result = replaceVariable(result, ACCOUNT_NUMBER, WebHooksCustom.findByKeyEnrollmentId(ACCOUNT_NUMBER, enrollmentId));
    result = replaceVariable(result, INTERAC_DEPOSIT_PAYMENT_ID,
        WebHooksCustom.findByKeyEnrollmentId(INTERAC_DEPOSIT_PAYMENT_ID, enrollmentId));
    result = replaceVariable(result, MESSAGE_IDENTIFICATION, WebHooksCustom.findByKeyEnrollmentId(MESSAGE_IDENTIFICATION, enrollmentId, 8));

    return result;
  }

  /**
   * Take original body template and all variables for this transaction then replace it with specific for this transaction id values
   *
   * @param originalBody        - body of original template
   * @param paymentVariablesMap - map with mapping variable - value
   * @return - template with filled variables (standard ones will be processed by parent - and they are passed with double {{}}
   */
  public static String fillVariablesFromMap(String originalBody, Map<String, String> paymentVariablesMap) {
    String result = originalBody;
    for (Entry<String, String> entry : paymentVariablesMap.entrySet()) {
      result = replaceVariable(result, entry.getKey(), entry.getValue());
    }
    return result;
  }

  /**
   * replacing variable in template with specified variable - note in this template just one {}!
   *
   * @param requestBody - request template
   * @param key         - key to replace
   * @param value       - value to replace key
   * @return - updated request template
   */
  private static String replaceVariable(String requestBody, String key, String value) {
    return requestBody.replace("{" + key + "}", value);
  }


}