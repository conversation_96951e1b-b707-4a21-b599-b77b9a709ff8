package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.anyUrl;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.webhookCustom;
import static com.peoples.banking.tools.interac.util.Constants.REQUEST_PATH;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.RequestMethod;
import com.google.common.io.Resources;
import com.peoples.banking.tools.interac.extensions.WebHooksCustom;
import com.peoples.banking.tools.interac.extensions.WebHooksCustomSecond;
import java.io.IOException;
import java.nio.charset.Charset;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Adding all money request specific API's and callbacks to them based on requests
 */
@Service
@Log4j2
public class RequestService {

  private static final String REQUEST_FOR_PAYMENT_RESPONSE_JSON = "SendRequestForPaymentResponse.json";
  private static String DEPOSIT_BODY;
  private static String SUBMIT_BODY;
  private static String REVERSE_BODY;

  private final WireMockServer wireMockServer;

  //callback url for deposit request
  @Value("${deposit.callback.url}")
  private String depositCallbackUrl;
  //callback url for submit request
  @Value("${submit.callback.url}")
  private String submitCallbackUrl;
  //callback url for reverse request
  @Value("${reverse.callback.url}")
  private String reverseCallbackUrl;
  //keyword in body that will trigger deposit callback only
  @Value("${deposit.keyword}")
  private String depositOnlyKeyword;
  //keyword in body that will trigger deposit and then submit callbacks
  @Value("${deposit.submit.keyword}")
  private String depositSubmitKeyword;
  //keyword in body that will trigger deposit and then rollback callbacks
  @Value("${deposit.reverse.keyword}")
  private String depositReverseKeyword;

  public RequestService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  /**
   * initialization method, will setup all Request service callbacks
   */
  @PostConstruct
  public void setupRequestAdaptor() {

    readCallbackBodies();

    //default answer, no callbacks
    wireMockServer.stubFor(post(REQUEST_PATH)
        .willReturn(WireMock.aResponse()
            .withBodyFile(REQUEST_FOR_PAYMENT_RESPONSE_JSON)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //deposit only
    wireMockServer.stubFor(post(REQUEST_PATH)
        .withRequestBody(containing(depositOnlyKeyword))
        .willReturn(WireMock.aResponse()
            .withBodyFile(REQUEST_FOR_PAYMENT_RESPONSE_JSON)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()))
        .withPostServeAction(WebHooksCustom.NAME, webhookCustom()
            .withMethod(RequestMethod.POST)
            .withUrl(depositCallbackUrl)
            .withBody(DEPOSIT_BODY)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //deposit, then submit
    wireMockServer.stubFor(post(REQUEST_PATH)
        .withRequestBody(containing(depositSubmitKeyword))
        .willReturn(WireMock.aResponse()
            .withBodyFile(REQUEST_FOR_PAYMENT_RESPONSE_JSON)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()))

        .withPostServeAction(WebHooksCustom.NAME, webhookCustom()
            .withMethod(RequestMethod.POST)
            .withUrl(depositCallbackUrl)
            .withBody(DEPOSIT_BODY)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()))
        .withPostServeAction(WebHooksCustomSecond.NAME, webhookCustom()
            .withMethod(RequestMethod.PUT)
            .withUrl(submitCallbackUrl)
            .withBody(SUBMIT_BODY)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //deposit, then reverse
    wireMockServer.stubFor(post(REQUEST_PATH)
        .withRequestBody(containing(depositReverseKeyword))
        .willReturn(WireMock.aResponse()
            .withBodyFile(REQUEST_FOR_PAYMENT_RESPONSE_JSON)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()))
        .withPostServeAction(WebHooksCustom.NAME, webhookCustom()
            .withMethod(RequestMethod.POST)
            .withUrl(depositCallbackUrl)
            .withBody(DEPOSIT_BODY)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()))
        .withPostServeAction(WebHooksCustomSecond.NAME, webhookCustom()
            .withMethod(RequestMethod.POST)
            .withUrl(reverseCallbackUrl)
            .withBody(REVERSE_BODY)
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //get request for payment response
//        wireMockServer.stubFor(get(urlMatching(REQUEST_PATH + "/.*"))
//                .willReturn(WireMock.aResponse()
//                        .withBodyFile(REQUEST_FOR_PAYMENT_RESPONSE_JSON)
//                        .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //test requests
    wireMockServer.stubFor(post(urlMatching("/network/interac/v1/payments/.*/deposit"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("DepositPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //test requests
    wireMockServer.stubFor(post(urlMatching("/network/interac/v1/payments/.*/deposit/reverse"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("DepositPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //get current request
    wireMockServer.stubFor(get(urlMatching(REQUEST_PATH + "/.*"))
        .withHeader("x-et-participant-user-id", anyUrl().getPattern())
        .willReturn(WireMock.aResponse()
            .withBodyFile("GetMoneyRequestResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //cancel requests - no content for any cancel
    wireMockServer.stubFor(post(urlMatching(REQUEST_PATH + "/.*/cancel"))
        .willReturn(WireMock.noContent()));

  }

  /**
   * reading template files, then it will be populated with data
   */
  private void readCallbackBodies() {
    try {
      DEPOSIT_BODY = Resources.toString(Resources.getResource("wiremock/__files/DepositInboundRequest.json"), Charset.defaultCharset());
      REVERSE_BODY = Resources.toString(Resources.getResource("wiremock/__files/ReverseInboundRequest.json"), Charset.defaultCharset());
      SUBMIT_BODY = Resources.toString(Resources.getResource("wiremock/__files/SubmitInboundRequest.json"), Charset.defaultCharset());
    } catch (IOException e) {
      log.error("Failed to read response bodies ", e);
    }
  }
}
