package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.notMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.peoples.banking.tools.interac.util.Constants.ANYMATCH_REGEXP;
import static com.peoples.banking.tools.interac.util.Constants.CUSTOMER_PATH;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import javax.annotation.PostConstruct;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;

/**
 * adding all customer related setup API's
 */
@Component
public class CustomerService {

  public static final String SOME_BODY = "some body";
  public static final String BAD_RESPONSE_BODY_KEY = "RN";

  private final WireMockServer wireMockServer;

  public CustomerService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  @PostConstruct
  public void setupCustomers() {

    //customers not doing anything useful
    wireMockServer.stubFor(WireMock.post(CUSTOMER_PATH).
        willReturn(WireMock.aResponse()));

    wireMockServer.stubFor(put(urlMatching(CUSTOMER_PATH + ANYMATCH_REGEXP))
        //we have some integration test who expect this testcase to fail, maybe needs to be more precise
        .withRequestBody(containing(BAD_RESPONSE_BODY_KEY))
        .willReturn(WireMock.badRequest()
            .withBody(SOME_BODY)));

    wireMockServer.stubFor(put(urlMatching(CUSTOMER_PATH + ANYMATCH_REGEXP))
        //just success response for customer update if bad request key is not present
        .withRequestBody(notMatching(BAD_RESPONSE_BODY_KEY))
        .willReturn(WireMock.aResponse()
            .withBody(SOME_BODY)));

    //get specific customer
    wireMockServer.stubFor(get(urlMatching(CUSTOMER_PATH + ANYMATCH_REGEXP)).
        willReturn(WireMock.aResponse()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
            .withBodyFile("GetCustomerResponse.json")));
  }
}
