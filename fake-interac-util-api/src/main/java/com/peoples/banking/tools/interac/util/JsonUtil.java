package com.peoples.banking.tools.interac.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class JsonUtil {

  /**
   * Json object mapper for parsing string to JsonNode tree
   */
  private static final ObjectMapper objectMapper = new ObjectMapper();

  private JsonUtil() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

  public static JsonNode parseJsonNode(String json) {
    try {
      return objectMapper.readTree(json);
    } catch (Exception ex) {
      log.warn("failed to parse json {}", json, ex);
    }
    return new TextNode("empty");
  }

  public static <T> T readValue(String json, Class<T> valueType) throws JsonProcessingException {
    return objectMapper.readValue(json, valueType);
  }

}
