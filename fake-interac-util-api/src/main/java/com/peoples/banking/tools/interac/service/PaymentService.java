package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.patch;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.put;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.http.RequestMethod.DELETE;
import static com.github.tomakehurst.wiremock.http.RequestMethod.GET;
import static com.github.tomakehurst.wiremock.http.RequestMethod.POST;
import static com.github.tomakehurst.wiremock.http.RequestMethod.PUT;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.DATE_FORMAT;
import static com.peoples.banking.tools.interac.extensions.WebHooksCustom.TIMESTAMP_FORMAT;
import static com.peoples.banking.tools.interac.util.Constants.ANYMATCH_REGEXP;
import static com.peoples.banking.tools.interac.util.Constants.CACHE_SECONDS_DURATION;
import static com.peoples.banking.tools.interac.util.Constants.PAYMENT_PATH;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Request;
import com.peoples.banking.tools.interac.extensions.WebHooksCustom;
import com.peoples.banking.tools.interac.util.JsonUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;

/**
 * adding all payment related setup API's
 */
@Service
@Log4j2
public class PaymentService {

  public static final String TRANSACTION = "/transaction";
  public static final String COMPLETE_PAYMENT_URL = PAYMENT_PATH + "/.*/receive" + TRANSACTION;
  public static final String COMPLETE_PAYMENT_BEGIN_URL = PAYMENT_PATH + "/.*/receive";
  public static final String DECLINE_PAYMENT_URL = PAYMENT_PATH + "/.*/receive/decline";
  public static final String GET_INCOMING_PAYMENT_URL = PAYMENT_PATH + "/.*/receive";
  public static final String AUTHENTICATE_PAYMENT_URL = PAYMENT_PATH + "/.*/authenticate";
  public static final String DETAILS = "/details";
  public static final String BLOCK_FRAUD_PAYMENT_KEYWORD = "BLOCK";
  private final WireMockServer wireMockServer;

  private final static Cache<String, Map<String, String>> paymentsVariablesMap = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();
  private final static Cache<String, String> clearingSystemToTransactionMapping = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();

  public PaymentService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  /**
   * setting up rules for payment requests
   */
  @PostConstruct
  public void setupPayments() {

    //initiate payment
    wireMockServer.stubFor(post(urlMatching(PAYMENT_PATH + TRANSACTION))
        //successful InitiateSendPaymentResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/InitiateSendPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //initiate payment block fraud result
    wireMockServer.stubFor(post(urlMatching(PAYMENT_PATH + TRANSACTION))
        //block fraud payment InitiateSendPaymentResponse
        .withRequestBody(containing(BLOCK_FRAUD_PAYMENT_KEYWORD))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/InitiateSendPaymentBlockFraudResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //submit payment
    wireMockServer.stubFor(put(urlMatching(PAYMENT_PATH + TRANSACTION))
        //successful SubmitSendResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/SubmitSendPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //get specific payment
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + ANYMATCH_REGEXP))
        //successful GetPaymentResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //cancel requests - no content for cancel response
    wireMockServer.stubFor(post(urlMatching(PAYMENT_PATH + ANYMATCH_REGEXP + "/cancel"))
        .willReturn(WireMock.noContent()));

    //payment options request - response for all other requests - default
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsDefaultResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //both payment options request - account number starting with 621-
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=621\\-.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountBothOptionsResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - account number starting with 621-16
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=621\\-16.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountDepositPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - account number starting with 621-00
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=621\\-001.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountRealtimeResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - mobile number ends with 111
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*111"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountDepositPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - mobile number ends with 222
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*222"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountRealtimeResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - mobile number ends with 333
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*333"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsAccountBothOptionsResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - email ends with com
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*autoandrealtime.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - email ends with org
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*autodeposit.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsRegularResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //payment options request - email ends with net
    wireMockServer.stubFor(get(urlMatching(PAYMENT_PATH + "/options.*deposit_handle=.*autorealtime.*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentOptionsRealtimeResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));


    //reverse payment response - static response for any params
    wireMockServer.stubFor(delete(urlMatching(PAYMENT_PATH + TRANSACTION + ".*"))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/ReversePaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //get incoming payment
    wireMockServer.stubFor(get(urlMatching(GET_INCOMING_PAYMENT_URL))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/IncomingPaymentResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

   //decline payment
    wireMockServer.stubFor(post(urlMatching(DECLINE_PAYMENT_URL))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

   //authenticate payment
    wireMockServer.stubFor(patch(urlMatching(AUTHENTICATE_PAYMENT_URL))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //complete payment begin
    wireMockServer.stubFor(post(urlMatching(COMPLETE_PAYMENT_BEGIN_URL))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

   //complete payment begin
    wireMockServer.stubFor(post(urlMatching(COMPLETE_PAYMENT_URL))
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/CompletePaymentBeginResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //complete payment commit
    wireMockServer.stubFor(put(urlMatching(COMPLETE_PAYMENT_URL))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //complete payment rollback
    wireMockServer.stubFor(delete(urlMatching(COMPLETE_PAYMENT_URL + ".*"))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));
  }

  /**
   * setting up rules for payment details requests
   */
  @PostConstruct
  public void setupPaymentDetails() {

    //initiate payment
    wireMockServer.stubFor(post(urlMatching(PAYMENT_PATH + DETAILS))
        //successful GetPaymentResponse
        .willReturn(WireMock.aResponse()
            .withBodyFile("payment/GetPaymentDetailsResponse.json")
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

  }

  /**
   * storing values from request to use in responses
   *
   * @param request - original request that come
   */
  public static void storeValuesFromPaymentRequests(Request request) {
    //storing values for InitiateSendPaymentRequest
    if (POST.equals(request.getMethod())) {
      saveValuesFromInitiateSendPaymentRequest(request.getBodyAsString());
    } else if (PUT.equals(request.getMethod())) {
      //storing values for SubmitSendPaymentRequest
      saveValuesFromSubmitSendPaymentRequest(request.getBodyAsString());
    }
  }

  /**
   * storing values from payment details request to use in responses
   *
   * @param request - original request that come
   */
  public static void storeValuesFromPaymentDetails(Request request) {
    //storing values for GetPaymentDetailsRequest
    if (POST.equals(request.getMethod())) {
      saveValuesFromPaymentDetailsRequest(request.getBodyAsString());
    }
  }

  /**
   * saving values from submit send payment request for future use
   *
   * @param initiateSendPaymentRequestBody - body of initiate send payment request
   */
  private static void saveValuesFromInitiateSendPaymentRequest(String initiateSendPaymentRequestBody) {

    JsonNode jsonNode = JsonUtil.parseJsonNode(initiateSendPaymentRequestBody);
    String transactionIdentification = extractTransactionIdFromInitiatePayment(jsonNode);
    String clearingSystemReferenceKey = WebHooksCustom.findByKeyEnrollmentId("clearingSystemReferenceKey", transactionIdentification, 8);

    clearingSystemToTransactionMapping.put(clearingSystemReferenceKey, transactionIdentification);

    Map<String, String> variablesMap = new HashMap<>();

    variablesMap.put("clearingSystemReferenceKey", clearingSystemReferenceKey);
    variablesMap.put("paymentMessageIdentification",
        WebHooksCustom.extract("/fi_to_fi_customer_credit_transfer/group_header/message_identification", jsonNode));
    variablesMap.put("endToEndIdentification", WebHooksCustom.extract(
        "/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/payment_identification/end_to_end_identification",
        jsonNode));

    variablesMap.put("transactionIdentification", transactionIdentification);
    variablesMap.put("interbankSettlementAmount", WebHooksCustom
        .extract("/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/interbank_settlement_amount/amount",
            jsonNode));
    variablesMap.put("interbankSettlementAmountCurrency", WebHooksCustom
        .extract("/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/interbank_settlement_amount/currency",
            jsonNode));
    variablesMap.put("requestExpiryDate", WebHooksCustom.extract("/expiry_date", jsonNode));
    variablesMap.put("paymentCreditorName",
        WebHooksCustom.extract("/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/creditor/name", jsonNode));
    variablesMap.put("accountHolderName", WebHooksCustom.extract("/account_holder_name", jsonNode));
    variablesMap.put("senderMemo", WebHooksCustom
        .extract("/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/remittance_information/unstructured/0",
            jsonNode));
    variablesMap.put("paymentAccountIdentification", WebHooksCustom.extract(
        "/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/debtor_account/identification/other/identification",
        jsonNode));
    variablesMap.put("creditorEmailAddress", WebHooksCustom
        .extract("/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/creditor/contact_details/email_address",
            jsonNode));
    variablesMap.put("participantReference", transactionIdentification);

    paymentsVariablesMap.put(transactionIdentification, variablesMap);

  }

  /**
   * saving values from Get Payment Details request for future use
   *
   * @param paymentDetailsRequestBody - body of payment details request
   */
  private static void saveValuesFromPaymentDetailsRequest(String paymentDetailsRequestBody) {

    JsonNode jsonNode = JsonUtil.parseJsonNode(paymentDetailsRequestBody);
    String transactionIdentification = extractTransactionIdFromGetPaymentDetails(jsonNode);
    String clearingSystemReferenceKey = WebHooksCustom.findByKeyEnrollmentId("clearingSystemReferenceKey", transactionIdentification, 8);

    clearingSystemToTransactionMapping.put(clearingSystemReferenceKey, transactionIdentification);

    Map<String, String> variablesMap = new HashMap<>();

    variablesMap.put("clearingSystemReferenceKey", clearingSystemReferenceKey);
    variablesMap.put("paymentMessageIdentification",
        WebHooksCustom.extract("/fi_to_fi_payment_status_request/group_header/message_identification", jsonNode));
    variablesMap.put("transactionIdentification", transactionIdentification);
    variablesMap.put("interbankSettlementAmount", "10.00");
    variablesMap.put("interbankSettlementAmountCurrency", "CAD");
    variablesMap.put("accountHolderName", WebHooksCustom.findByKeyEnrollmentId("account_holder_name", transactionIdentification));
    variablesMap.put("paymentCreditorName", WebHooksCustom.findByKeyEnrollmentId("paymentCreditorName", transactionIdentification));
    variablesMap.put("creditorEmailAddress", WebHooksCustom.findByKeyEnrollmentId("creditorEmailAddress", transactionIdentification));
    variablesMap.put("paymentAccountIdentification", WebHooksCustom.extract(
        "/fi_to_fi_payment_status_request/transaction_information/0/original_transaction_identification",
        jsonNode));
    variablesMap.put("requestExpiryDate", TIMESTAMP_FORMAT.format(new Date()));

    paymentsVariablesMap.put(transactionIdentification, variablesMap);

  }

  /**
   * extracting transaction id from parsed initiate payment request
   *
   * @param jsonNode - parsed submit payment request json object
   * @return - extracted transaction id
   */
  private static String extractTransactionIdFromInitiatePayment(JsonNode jsonNode) {
    return WebHooksCustom.extract(
        "/fi_to_fi_customer_credit_transfer/credit_transfer_transaction_information/0/payment_identification/transaction_identification",
        jsonNode);
  }

  /**
   * extracting transaction id from get payment details
   *
   * @param jsonNode - parsed submit payment request json object
   * @return - extracted transaction id
   */
  private static String extractTransactionIdFromGetPaymentDetails(JsonNode jsonNode) {
    return WebHooksCustom.extract(
        "/fi_to_fi_payment_status_request/group_header/message_identification",
        jsonNode);
  }

  /**
   * extracting transaction id from parsed initiate payment request
   *
   * @param jsonNode - parsed submit payment request json object
   * @return - extracted transaction id
   */
  private static String extractTransactionIdFromGetPayment(JsonNode jsonNode) {
    return WebHooksCustom.extract(
        "/fi_to_fi_payment_status_request/transaction_information/0/original_transaction_identification",
        jsonNode);
  }

  /**
   * saving values from submit send payment request for future use
   *
   * @param submitSendPaymentRequestBody - body of submit send payment request
   */
  private static void saveValuesFromSubmitSendPaymentRequest(String submitSendPaymentRequestBody) {
    JsonNode jsonNode = JsonUtil.parseJsonNode(submitSendPaymentRequestBody);
    String transactionId = extractTransactionIdFromSubmitPayment(jsonNode);
    Map<String, String> variablesMap = transactionId == null ? null : paymentsVariablesMap.getIfPresent(transactionId);
    if (variablesMap != null) {
      variablesMap.put("submitClearingSystemReference", WebHooksCustom.extract("/clearing_system_reference", jsonNode));
    } else {
      log.info("empty variables map for transactionId {}", transactionId);
    }
  }

  /**
   * extracting transaction id from parsed submit payment request
   *
   * @param jsonNode - parsed submit payment request json object
   * @return - extracted transaction id
   */
  private static String extractTransactionIdFromSubmitPayment(JsonNode jsonNode) {
    return WebHooksCustom.extract("/transaction_id", jsonNode);
  }

  /**
   * extract transaction id from request and trying to get map with all variables for this request
   *
   * @param request - original request
   * @return - map with all needed variables to fulfill response template
   */
  public static Map<String, String> getPaymentVariablesMap(Request request) {
    String transactionId = "empty";
    String url = request.getUrl();
    if (GET.equals(request.getMethod())) {
      String clearingSystemReference = url.substring(url.lastIndexOf("/") + 1);
      transactionId = clearingSystemToTransactionMapping.getIfPresent(clearingSystemReference);
    } else {
      JsonNode jsonNode = JsonUtil.parseJsonNode(request.getBodyAsString());
      if (POST.equals(request.getMethod())) {
        transactionId = extractTransactionIdFromInitiatePayment(jsonNode);
      } else if (PUT.equals(request.getMethod())) {
        transactionId = extractTransactionIdFromSubmitPayment(jsonNode);
      } else if (DELETE.equals(request.getMethod()) && url.contains("=")) {
        transactionId = url.substring(url.lastIndexOf("=") + 1);
      }
      if (url.endsWith(DETAILS)) {
        transactionId = extractTransactionIdFromGetPaymentDetails(jsonNode);
      }
      if (StringUtils.isEmpty(transactionId)) {
        transactionId = extractTransactionIdFromGetPayment(jsonNode);
      }
    }

    Map<String, String> originalMap = transactionId == null ? null : paymentsVariablesMap.getIfPresent(transactionId);
    Map<String, String> result;
    if (originalMap == null) {
      log.warn("Failed to found an existing map for transaction id {} ", transactionId);
      result = new HashMap<>();
    } else {
      result = new HashMap<>(originalMap);
    }
    Date currentDate = new Date();
    result.put("timestamp", TIMESTAMP_FORMAT.format(currentDate));
    result.put("datestamp", DATE_FORMAT.format(currentDate));

    return result;
  }

}
