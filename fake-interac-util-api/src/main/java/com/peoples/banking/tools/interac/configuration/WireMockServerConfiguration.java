package com.peoples.banking.tools.interac.configuration;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.peoples.banking.tools.interac.extensions.VariablesTransformer;
import com.peoples.banking.tools.interac.extensions.WebHooksCustom;
import com.peoples.banking.tools.interac.extensions.WebHooksCustomSecond;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WireMockServerConfiguration {

  /**
   * server http port
   */
  @Value("${server.http.port:8080}")
  private int SERVER_PORT;
  /**
   * path for template files (wiremock adding __files folder to search templates)
   */
  public static final String RESOURCES_PATH = "wiremock/";

  /**
   * configured delay in seconds for first callback
   */
  @Value("${first.callback.delay}")
  private int firstCallbackDelay;

  /**
   * configured delay in seconds for second callback
   */
  @Value("${second.callback.delay}")
  private int secondCallbackDelay;

  @Bean
  public WireMockServer wireMockServer() {
    String resourcePath = this.getClass().getClassLoader().getResource("wiremock").getPath();

    //Heart of all application mocks, should be started before use
    WireMockServer wireMockServer = new WireMockServer(wireMockConfig()
        .extensions(new WebHooksCustom(firstCallbackDelay), new WebHooksCustomSecond(secondCallbackDelay),
            new VariablesTransformer(true)).port(SERVER_PORT).withRootDirectory(resourcePath));

    wireMockServer.start();
    return wireMockServer;
  }
}
