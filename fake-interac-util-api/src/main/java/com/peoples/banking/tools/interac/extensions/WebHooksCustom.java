package com.peoples.banking.tools.interac.extensions;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.tomakehurst.wiremock.common.Exceptions;
import com.github.tomakehurst.wiremock.core.Admin;
import com.github.tomakehurst.wiremock.extension.Parameters;
import com.github.tomakehurst.wiremock.http.HttpClientFactory;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.peoples.banking.tools.interac.util.JsonUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;
import org.wiremock.webhooks.WebhookDefinition;
import org.wiremock.webhooks.Webhooks;
import org.wiremock.webhooks.interceptors.WebhookTransformer;

/**
 * Custom webhook scheduler that is used to send messages after receiving of original request
 */
@Log4j2
public class WebHooksCustom extends Webhooks {

  public static final String NAME = "webhookscustom";
  public static final String PEOPLE_DEPOSIT_PAYMENT_ID = "peopleDepositPaymentId";
  public static final String INTERAC_DEPOSIT_PAYMENT_ID = "interacDepositPaymentId";
  public static final String ENROLLMENT_ID = "enrollmentId";
  public static final String MESSAGE_IDENTIFICATION = "messageIdentification";
  public static final String INTERAC_REQUEST_ID = "interacRequestId";
  public static final String ACCOUNT_NUMBER = "accountNumber";

  private static final String[] ACCOUNTS = {"621-16001-************",
      "621-16001-************",
      "621-16001-************"};
  public static final String PARTICIPANT_ID = "x-et-participant-id";
  public static final String SERVICE_ACCOUNT_ID = "x-et-participant-id";

  /**
   * scheduler executor service
   */
  private final ScheduledExecutorService scheduler;

  /**
   * synchronous http client
   */
  private final HttpClient httpClient;

  /**
   * additional transformers for request - not really used now
   */
  private final List<WebhookTransformer> transformers;

  /**
   * default delay in seconds for callback
   */
  private int delayInSeconds = 0;

  /**
   * just default date formatter for variables
   */
  public final static DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

  /**
   * date formatter with date and time
   */
  public static final DateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.S'Z'");

  /**
   * Global guava cache for storing all request specific variables, missing values are generated
   */
  public static LoadingCache<Key, String> cacheValuesMap = CacheBuilder.newBuilder()
      .maximumSize(10000)
      .expireAfterWrite(1, TimeUnit.HOURS)
      .build(
          new CacheLoader<>() {
            public String load(Key key) {
              return generateValue(key);
            }
          });

  private WebHooksCustom(ScheduledExecutorService scheduler, HttpClient httpClient, List<WebhookTransformer> transformers) {
    this.scheduler = scheduler;
    this.httpClient = httpClient;
    this.transformers = transformers;
  }

  /**
   * Constructor with delay in seconds for callback
   *
   * @param delayInSeconds - delay for callback in seconds
   */
  public WebHooksCustom(int delayInSeconds) {
    this(Executors.newScheduledThreadPool(10), HttpClientFactory.createClient(), new ArrayList<>());
    this.delayInSeconds = delayInSeconds;
  }

  /**
   * extracting specific node text from JsonNode
   *
   * @param path         - json path
   * @param rootJsonNode - root json node
   * @return node text if it exist, empty string otherwise
   */
  public static String extract(String path, JsonNode rootJsonNode) {
    JsonNode jsonNode = rootJsonNode.at(path);
    return jsonNode.asText();
  }

  /**
   * Replacing all variables from map in request body
   *
   * @param requestBody  - request body aka template
   * @param variablesMap - variables map (variable key->variable value)
   * @return updated body with all passed variables
   */
  private static String replaceVariables(String requestBody, Map<String, String> variablesMap) {
    for (Map.Entry<String, String> entry : variablesMap.entrySet()) {
      requestBody = replaceVariable(requestBody, entry.getKey(), entry.getValue());
    }
    return requestBody;
  }

  /**
   * replace specified variable in request body with its value
   *
   * @param requestBody - request body
   * @param key         - variable key to replace
   * @param value       - variable value
   * @return - updated body
   */
  private static String replaceVariable(String requestBody, String key, String value) {
    return requestBody.replace("{{" + key + "}}", value);
  }

  /**
   * construct new instance of webhook definition, no logic
   *
   * @return new instance f webhook definition
   */
  public static WebhookDefinition webhookCustom() {
    return new WebhookDefinition();
  }

  /**
   * return unique specific for webhook name, by which its registered
   *
   * @return - unique webhook name
   */
  @Override
  public String getName() {
    return NAME;
  }

  /**
   * Scheduling task to send http request in configured amount of seconds
   *
   * @param serveEvent - original event that initiated this task, contains original request/headers/etc
   * @param admin      - some user, internal wiremock stuff, not used here
   * @param parameters - request parameters to build new request
   */
  public void doAction(final ServeEvent serveEvent, Admin admin, final Parameters parameters) {
    this.scheduler.schedule(() -> {
      WebhookDefinition definition = parameters.as(WebhookDefinition.class);

      WebhookTransformer transformer;
      for (Iterator<WebhookTransformer> it = transformers.iterator(); it.hasNext();
          definition = transformer.transform(serveEvent, definition)) {
        transformer = it.next();
      }

      HttpUriRequest request = buildRequest(definition, serveEvent);

      try {
        HttpResponse response = httpClient.execute(request);
        String responseAsString = EntityUtils.toString(response.getEntity());

        log.info("Webhook {} request to {} returned status {}\n\n{}", definition.getMethod(), definition.getUrl(), response.getStatusLine(),
            responseAsString);
        JsonNode jsonNode = JsonUtil.parseJsonNode(responseAsString);
        String accountServiceReference = extract(
            "/fi_to_fi_payment_status_report/transaction_information_and_status/0/account_servicer_reference", jsonNode);
        String originalRequestBody = serveEvent.getRequest().getBodyAsString();
        JsonNode jsonNodeOriginal = JsonUtil.parseJsonNode(originalRequestBody);
        String enrollmentId = getEnrollmentId(jsonNodeOriginal);

        if (accountServiceReference != null) {
          cacheValuesMap.put(buildKey(PEOPLE_DEPOSIT_PAYMENT_ID, enrollmentId, 0), accountServiceReference);
        }
        //just hack so after submit we need to clear all maps, can create issues with simultaneous requests
        if (request.getMethod().equalsIgnoreCase("put")) {
          removeOldCacheRequests(enrollmentId);
        }

      } catch (IOException e) {
        log.warn("Exception happens during calling back peoplesoft", e);
        Exceptions.throwUnchecked(e);
      }

    }, delayInSeconds, TimeUnit.SECONDS);
  }

  private void removeOldCacheRequests(String enrollmentId) {
    cacheValuesMap.invalidate(buildKey(PEOPLE_DEPOSIT_PAYMENT_ID, enrollmentId, 0));
    cacheValuesMap.invalidate(buildKey(INTERAC_DEPOSIT_PAYMENT_ID, enrollmentId, 0));
    cacheValuesMap.invalidate(buildKey(INTERAC_REQUEST_ID, enrollmentId, 0));
    cacheValuesMap.invalidate(buildKey(MESSAGE_IDENTIFICATION, enrollmentId, 0));

  }

  /**
   * build httpUriRequest by its definition and original request event add interac specific headers and filling specific variables in
   * template
   *
   * @param definition - definition of request
   * @param serveEvent - original request event, that triggered this callback
   * @return - HttpUriRequest that should be called
   */
  @SneakyThrows
  private HttpUriRequest buildRequest(WebhookDefinition definition, ServeEvent serveEvent) {
    String url = definition.getUrl().toString();
    String originalRequestBody = serveEvent.getRequest().getBodyAsString();
    JsonNode jsonNodeOriginal = JsonUtil.parseJsonNode(originalRequestBody);
    url = url.replace(INTERAC_DEPOSIT_PAYMENT_ID, getStoredKey(INTERAC_DEPOSIT_PAYMENT_ID, getEnrollmentId(jsonNodeOriginal), 8));

    HttpUriRequest request = HttpClientFactory.getHttpRequestFor(definition.getMethod(), url);
    for (HttpHeader header : definition.getHeaders().all()) {
      request.addHeader(header.key(), header.firstValue());
    }
    //adding interac specific headers
    for (Map.Entry<String, String> header : buildHeaders(serveEvent).entrySet()) {
      request.addHeader(header.getKey(), header.getValue());
    }

    if (definition.getMethod().hasEntity()) {
      Map<String, String> variablesMap = buildVariablesMap(serveEvent);
      HttpEntityEnclosingRequestBase entityRequest = (HttpEntityEnclosingRequestBase) request;
      String requestBody = definition.getBody();
      requestBody = replaceVariables(requestBody, variablesMap);
      log.info("Webhook  request is {} returned status ", requestBody);
      entityRequest.setEntity(new ByteArrayEntity(requestBody.getBytes()));
    }

    return request;
  }

  /**
   * build all specific interac headers with stored (or generated values)
   *
   * @param event - original event that caused request
   * @return - headers map
   */
  @SneakyThrows
  private Map<String, String> buildHeaders(ServeEvent event) {
    String originalRequestBody = event.getRequest().getBodyAsString();
    JsonNode jsonNode = JsonUtil.parseJsonNode(originalRequestBody);
    Map<String, String> result = new HashMap<>();
    result.put(PARTICIPANT_ID, "CA000621");
    result.put("x-et-participant-user-id", getEnrollmentId(jsonNode));
    result.put("x-et-indirect-connector-id", null);
    String eventId = event.getId().toString();
    if (eventId.length() > 30) {
      eventId = eventId.substring(0, 30);
    }
    result.put("x-et-request-id", eventId);
    result.put("x-et-retry-indicator", "true");
    result.put("x-et-channel-indicator", "ETRANSFER_SYSTEM");
    result.put("x-et-api-signature",
        "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
    result.put("x-et-api-signature-type", "PAYLOAD_DIGEST_SHA256");
    result.put("x-et-transaction-time", TIMESTAMP_FORMAT.format(new Date()));

    return result;
  }

  /**
   * build all variables map based on original request event
   *
   * @param serveEvent - original request event, that initiated callbacks
   * @return - variables map
   */
  @SneakyThrows
  private Map<String, String> buildVariablesMap(ServeEvent serveEvent) {
    Date currentDate = new Date();
    String originalRequestBody = serveEvent.getRequest().getBodyAsString();
    JsonNode jsonNode = JsonUtil.parseJsonNode(originalRequestBody);
    String enrollmentId = getEnrollmentId(jsonNode);
    Map<String, String> variablesMap = new HashMap<>();
    variablesMap.put("timestamp", TIMESTAMP_FORMAT.format(currentDate));
    variablesMap.put("datestamp", DATE_FORMAT.format(currentDate));
    String extractedAmount = extract(
        "/creditor_payment_activation_request/payment_information/0/credit_transfer_transaction/0/amount/instructed_amount/amount",
        jsonNode);
    if (StringUtils.isEmpty(extractedAmount)) {
      extractedAmount = extract("/amount", jsonNode);
    }
    variablesMap.put("amount", extractedAmount);
    String accountNumber = extract(
        "/creditor_payment_activation_request/payment_information/0/credit_transfer_transaction/0/creditor_account/identification/other/identification",
        jsonNode);

    if (StringUtils.isEmpty(accountNumber)) {
      accountNumber = extract("/account_number", jsonNode);
    }
    variablesMap.put(ACCOUNT_NUMBER, accountNumber);

    variablesMap.put(INTERAC_DEPOSIT_PAYMENT_ID, getStoredKey(INTERAC_DEPOSIT_PAYMENT_ID, enrollmentId, 8));
    variablesMap.put(INTERAC_REQUEST_ID, getStoredKey(INTERAC_REQUEST_ID, enrollmentId));

    String requestId = extract(
        "/creditor_payment_activation_request/payment_information/0/credit_transfer_transaction/0/payment_identification/instruction_identification",
        jsonNode);
    if (StringUtils.isEmpty(requestId)) {
      requestId = extract("/end_to_end_id", jsonNode);
    }
    variablesMap.put("requestId", requestId);

    variablesMap.put(MESSAGE_IDENTIFICATION, getStoredKey(MESSAGE_IDENTIFICATION, enrollmentId, 8));
    variablesMap.put(ENROLLMENT_ID, enrollmentId);
    variablesMap.put(PEOPLE_DEPOSIT_PAYMENT_ID, getStoredKey(PEOPLE_DEPOSIT_PAYMENT_ID, enrollmentId));
    variablesMap.put("expiryDate", extract("/creditor_payment_activation_request/payment_information/0/expiry_date", jsonNode));

    variablesMap.put("randomStr4", serveEvent.getId().toString().substring(0, 4));

    for (Map.Entry<String, String> entry : variablesMap.entrySet()) {
      cacheValuesMap.put(buildKey(entry.getKey(), enrollmentId, 0), entry.getValue());
    }

    return variablesMap;
  }

  /**
   * Extract enrollment id from original request
   *
   * @param jsonNode - original request in JsonNode format
   * @return - enrollment id from request
   */
  private String getEnrollmentId(JsonNode jsonNode) {
    String result = extract(
            "/creditor_payment_activation_request/payment_information/0/credit_transfer_transaction/0/creditor/identification/organisation_identification/other/0/identification",
            jsonNode);
    if (StringUtils.isEmpty(result)) {
      result = extract("/customer_id", jsonNode);
    }
    return result;
  }

  /**
   * Return value from global cache (generate if it not exists)
   *
   * @param key          - key to found
   * @param enrollmentId - enrollment id
   * @return - stored or generated value
   */
  @SneakyThrows
  private String getStoredKey(String key, String enrollmentId) {
    return getStoredKey(key, enrollmentId, 0);
  }
  
  /**
   * Return value from global cache (generate if it not exists)
   *
   * @param key          - key to found
   * @param enrollmentId - enrollment id
   * @param length length of key to return                    
   * @return - stored or generated value
   */
  @SneakyThrows
  private String getStoredKey(String key, String enrollmentId, int length) {
    return cacheValuesMap.get(buildKey(key, enrollmentId, length));
  }

  /**
   * searching in global cache by specific String key and enrollment id, in case of missing values - it will be generated
   *
   * @param key          - String key to identify value
   * @param enrollmentId - user specific enrollment id
   * @return - value from cache or new generated one (also will be stored in cache)
   */

  @SneakyThrows
  public static String findByKeyEnrollmentId(String key, String enrollmentId, int desiredKeyLength) {
    return cacheValuesMap.get(buildKey(key, enrollmentId, desiredKeyLength));
  }
  @SneakyThrows
  public static String findByKeyEnrollmentId(String key, String enrollmentId) {
    return findByKeyEnrollmentId(key, enrollmentId, 0);
  }

  @SneakyThrows
  public static String findByKeyWithTypeByEnrollmentId(String key, String enrollmentId, Class type) {
    return cacheValuesMap.get(buildKey(key, enrollmentId, type));
  }


  /**
   * build cache key for key and enrollment id
   *
   * @param key          - string key
   * @param enrollmentId - enrollment id
   * @param length - key desired length                    
   * @return - Key object for guava cache
   */
  private static Key buildKey(String key, String enrollmentId, int length) {
    return new Key(key, enrollmentId, length, String.class);
  }

  private static Key buildKey(String key, String enrollmentId, Class type) {
    return new Key(key, enrollmentId, 0, type);
  }

  /**
   * generate value - by default with key length, but  max length is 32
   *
   * @param mapKey - key for which generate value
   * @return - generated random string
   */
  private static String generateValue(Key mapKey) {
    String key = mapKey.getKey();
    int desiredLength = mapKey.getLength();
    if (mapKey.getType() == Double.class) {
      return generateDouble();
    }

    if (mapKey.getType() == Date.class) {
      return generateDate();
    }

    if (ACCOUNT_NUMBER.equals(key)) {
      return generateAccountNumber();
    }

    String result = UUID.randomUUID().toString();
    if (desiredLength > 0 && result.length() > desiredLength) {
      return result.substring(0, desiredLength);
    }

    if (key.length() < result.length()) {
      return result.substring(0, key.length());
    }
    return result;
  }

  private static String generateAccountNumber() {
    int randomIdx = new Random().nextInt(ACCOUNTS.length);
    return ACCOUNTS[randomIdx];
  }

  private static String generateDate() {
    return TIMESTAMP_FORMAT.format(new Date());
  }

  private static String generateDouble() {
    BigDecimal result = BigDecimal.valueOf(ThreadLocalRandom.current().nextDouble(1,1000));
    result = result.setScale(2, RoundingMode.HALF_DOWN);
    return String.valueOf(result);
  }

  /**
   * Key for internal cache
   */
  @Getter
  @AllArgsConstructor
  @EqualsAndHashCode
  private static class Key {
    private final String key;
    private final String enrollmentId;
    @EqualsAndHashCode.Exclude
    private final int length;
    @EqualsAndHashCode.Exclude
    private final Class type;
    
  }

}
