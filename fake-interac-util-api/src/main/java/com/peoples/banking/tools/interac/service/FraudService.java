package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.patch;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.peoples.banking.tools.interac.util.Constants.CACHE_SECONDS_DURATION;
import static com.peoples.banking.tools.interac.util.Constants.FRAUD_PATH;
import static com.peoples.banking.tools.interac.util.Constants.PAYMENTS;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Request;
import com.peoples.banking.tools.interac.util.JsonUtil;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;

/**
 * Adding all fraud request specific API's
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class FraudService {

  public static final String FRAUD_STATUS = "/fraud-status";
  private final WireMockServer wireMockServer;

  private final static Cache<String, String> paymentStatusMap = Caffeine.newBuilder()
      .expireAfterWrite(CACHE_SECONDS_DURATION, TimeUnit.SECONDS)
      .build();

  public static boolean storeValuesFromUpdateFraudStatus(Request request) {
    String requestUrl = request.getUrl();
    String paymentRefId = requestUrl.substring(requestUrl.indexOf(PAYMENTS) + PAYMENTS.length() + 1, requestUrl.indexOf(FRAUD_STATUS));
    String requestBody = request.getBodyAsString();
    String paymentStatus;
    try {
      Map<String, String> objectMap = JsonUtil.readValue(requestBody, Map.class);
      paymentStatus = objectMap.get("fraud_status");
    } catch (JsonProcessingException e) {
      log.warn("Failed to process message {}", requestBody, e);
      return false;
    }
    String currentPaymentStatus = paymentStatusMap.getIfPresent(paymentRefId);
    if (StringUtils.equals(currentPaymentStatus, paymentStatus)) {
      return false;
    }
    paymentStatusMap.put(paymentRefId, paymentStatus);
    log.info("updated paymentStatus for paymentRefId {} , status {}" ,  paymentRefId, paymentStatus);
    return true;
  }

  /**
   * initialization method, will setup all Fraud service apis
   */
  @PostConstruct
  public void setupFraud() {

    //update payment fraud status
    wireMockServer.stubFor(patch(urlMatching(FRAUD_PATH + "/.*" + FRAUD_STATUS))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //blockPaymentFraud
    wireMockServer.stubFor(post(urlMatching(FRAUD_PATH + "/.*/block"))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

    //unblockPaymentFraud
    wireMockServer.stubFor(post(urlMatching(FRAUD_PATH + "/.*/unblock"))
        .willReturn(WireMock.noContent()
            .withHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())));

  }

}
