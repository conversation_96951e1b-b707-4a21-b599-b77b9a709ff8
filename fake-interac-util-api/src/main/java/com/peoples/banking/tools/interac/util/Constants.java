package com.peoples.banking.tools.interac.util;

public final class Constants {

  private Constants() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }

  public static final String INTERAC_API_VERSION = "v3.5.0";
  public final static String CUSTOMER_PATH = "/customer-api/" + INTERAC_API_VERSION + "/customers";
  public final static String REQUEST_PATH = "/request-api/" + INTERAC_API_VERSION + "/requests";
  public static final String PAYMENTS = "/payments";
  public final static String FRAUD_PATH = "/fraud-api/" + INTERAC_API_VERSION + PAYMENTS;
  public final static String PAYMENT_PATH = "/payment-api/" + INTERAC_API_VERSION + PAYMENTS;
  public static final String ACCOUNT_ALIAS_REGISTRATIONS = "/account-alias-registrations";
  public final static String ALIAS_REGISTRATIONS_PATH = "/registration-api/" + INTERAC_API_VERSION + ACCOUNT_ALIAS_REGISTRATIONS;
  public static final String REPLACEMENT_BODY = "REPLACEMENT_BODY";
  public final static String TIMEOUT_PATH = "/config-delay";

  public static final String ANYMATCH_REGEXP = "/.*";

  //7 days of cache duration
  public static final int CACHE_SECONDS_DURATION = 86_400 * 7;

}