package com.peoples.banking.tools.interac.extensions;

import lombok.extern.log4j.Log4j2;

/**
 * shouldn't contain logic, this class is exists just because wiremock officially not supporting multiple post actions (still in branch), so
 * it just for registering other name
 */
@Log4j2
public class WebHooksCustomSecond extends WebHooksCustom {

  public static final String NAME = "webhookscustomsecond";

  /**
   * Just constructor with delay in seconds for callback
   *
   * @param delayInSeconds - delay in seconds before callback
   */
  public WebHooksCustomSecond(int delayInSeconds) {
    super(delayInSeconds);
  }

  @Override
  public String getName() {
    return NAME;
  }

}
