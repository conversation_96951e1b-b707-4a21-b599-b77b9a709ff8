package com.peoples.banking.tools.interac.service;

import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.http.RequestMethod.POST;
import static com.peoples.banking.tools.interac.util.Constants.ANYMATCH_REGEXP;
import static com.peoples.banking.tools.interac.util.Constants.TIMEOUT_PATH;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Request;
import javax.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * adding all timeout related API's and managing them
 */
@Service
@Log4j2
public class ConfigDelayService {

  private static WireMockServer wireMockServer;

  public ConfigDelayService(WireMockServer wireMockServer) {
    this.wireMockServer = wireMockServer;
  }

  /**
   * setting up rules for timeout requests
   */
  @PostConstruct
  public void setupTimeouts() {

    //set timeout api
    wireMockServer.stubFor(post(urlMatching(TIMEOUT_PATH + ANYMATCH_REGEXP))
        //just no content
        .willReturn(WireMock.noContent()));
  }

  /**
   * set global delay for all responses. Note - request to set delay will be also delayed
   *
   * @param request - original request that come
   */
  public static void setGlobalDelay(Request request) {
    if (POST.equals(request.getMethod())) {
      String requestUrl = request.getUrl();
      int timeoutValue = Integer.parseInt(requestUrl.substring(requestUrl.lastIndexOf("/") + 1));
      if (timeoutValue > 0) {
        wireMockServer.setGlobalFixedDelay(timeoutValue);
        log.info("Set timeout to new value {}", timeoutValue);
      } else {
        log.error("trying to set negative timeout {}", timeoutValue);
      }
    }
  }


}