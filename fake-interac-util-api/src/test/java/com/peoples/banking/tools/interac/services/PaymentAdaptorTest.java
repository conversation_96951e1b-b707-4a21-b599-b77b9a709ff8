package com.peoples.banking.tools.interac.services;

import static com.peoples.banking.tools.interac.util.Constants.PAYMENT_PATH;
import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

public class PaymentAdaptorTest extends AbstractAdaptorTest {

  @Test
  public void testCreate() {
    HttpHeaders headers = new HttpHeaders();
    HttpEntity<String> requestEntity = new HttpEntity<>("test customer", headers);
    ResponseEntity<String> result = testRestTemplate
        .exchange(LOCAL_ADDRESS + LOCAL_PORT + PAYMENT_PATH + "/some/cancel", HttpMethod.POST, requestEntity, String.class);

    assertThat(result.getBody()).isNullOrEmpty();
  }
}
