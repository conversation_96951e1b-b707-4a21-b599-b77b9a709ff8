package com.peoples.banking.tools.interac.services;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource("classpath:application.properties")
public abstract class AbstractAdaptorTest {

  @Value("${server.host:http://localhost:}")
  protected String LOCAL_ADDRESS;

  @Value("${server.http.port}")
  protected int LOCAL_PORT;

  TestRestTemplate testRestTemplate = new TestRestTemplate();

}
