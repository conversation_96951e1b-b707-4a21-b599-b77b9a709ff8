package com.peoples.banking.tools.interac.services;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.http.*;

import static com.peoples.banking.tools.interac.service.FraudService.FRAUD_STATUS;
import static com.peoples.banking.tools.interac.util.Constants.FRAUD_PATH;
import static org.assertj.core.api.Assertions.assertThat;

public class FraudAdaptorTest extends AbstractAdaptorTest {

    public static final String PAYMENT_ID = "paymentId";

    @ParameterizedTest
    @ValueSource(strings = {"INVESTMENT_LOAN_SCAM", "JOB_SCAM", "BUYER_SELLER_SCAM", "ROMANCE_SCAM", "THREAT_EMERGENCY_SCAM"})
    public void testUpdatePaymentStatus_success(String fraudType) {
        HttpEntity<?> requestEntity = buildRequestEntity(fraudType);
        ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + FRAUD_PATH + "/"
                + PAYMENT_ID + FRAUD_STATUS, HttpMethod.PATCH, requestEntity, String.class);
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
        assertThat(result.getBody()).isBlank();
    }

    private static HttpEntity<?> buildRequestEntity(String fraudType) {
        HttpHeaders headers = new HttpHeaders();
        return new HttpEntity<>("{\"fraud_status\" : \"" + fraudType + "\"}\n", headers);
    }

    @ParameterizedTest
    @ValueSource(strings = {"INVESTMENT_LOAN_SCAM", "JOB_SCAM", "BUYER_SELLER_SCAM", "ROMANCE_SCAM", "THREAT_EMERGENCY_SCAM"})
    public void testUpdatePaymentStatus_duplicate_bad_request(String fraudType) {
        HttpEntity<?> requestEntity = buildRequestEntity(fraudType);
        ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + FRAUD_PATH + "/"
                + PAYMENT_ID + FRAUD_STATUS, HttpMethod.PATCH, requestEntity, String.class);
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
        assertThat(result.getBody()).isBlank();

        result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + FRAUD_PATH + "/" + PAYMENT_ID
                        + FRAUD_STATUS, HttpMethod.PATCH, requestEntity, String.class);
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(result.getBody()).isNotBlank();
    }

    @Test
    public void testBlockPayment() {
        ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + FRAUD_PATH + "/"
                + PAYMENT_ID + "/block", HttpMethod.POST, null, String.class);
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
        assertThat(result.getBody()).isBlank();
    }

    @Test
    public void testUnBlockPayment() {
        ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + FRAUD_PATH + "/"
                + PAYMENT_ID + "/unblock", HttpMethod.POST, null, String.class);
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.NO_CONTENT);
        assertThat(result.getBody()).isBlank();
    }
}
