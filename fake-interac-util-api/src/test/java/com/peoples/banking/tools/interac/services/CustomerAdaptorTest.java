package com.peoples.banking.tools.interac.services;

import static com.peoples.banking.tools.interac.util.Constants.CUSTOMER_PATH;
import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

public class CustomerAdaptorTest extends AbstractAdaptorTest {

  @Test
  public void testCreate() {
    HttpHeaders headers = new HttpHeaders();
    headers.add("test-header", "deposit");
    HttpEntity<String> requestEntity = new HttpEntity<>("test customer", headers);
    ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + CUSTOMER_PATH, HttpMethod.PUT, requestEntity, String.class);

    assertThat(result.getBody()).isNullOrEmpty();
  }
}
