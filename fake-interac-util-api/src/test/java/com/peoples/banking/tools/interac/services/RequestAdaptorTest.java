package com.peoples.banking.tools.interac.services;

import static com.peoples.banking.tools.interac.util.Constants.REQUEST_PATH;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.common.io.Resources;
import java.io.IOException;
import java.nio.charset.Charset;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

public class RequestAdaptorTest extends AbstractAdaptorTest {

    private static String REQUEST_BODY;

    @BeforeAll
    public static void setup() throws IOException {
        REQUEST_BODY = Resources
            .toString(Resources.getResource("wiremock/__files/SendRequestForPaymentRequest.json"), Charset.defaultCharset());
    }


    @Test
    public void testDepositSubmit() {
        String result = testRestTemplate.postForObject(LOCAL_ADDRESS + LOCAL_PORT + REQUEST_PATH, REQUEST_BODY, String.class);
        assertThat(result).isNotBlank();
        //assertThat(result).contains(MESSAGE_ID);
    }

    @Test
    public void testDeposit() {

        HttpHeaders headers = new HttpHeaders();
        headers.add("test-header", "deposit");
        HttpEntity<String> httpEntity = new HttpEntity<>(REQUEST_BODY, headers);
        ResponseEntity<String> result = testRestTemplate.exchange(LOCAL_ADDRESS + LOCAL_PORT + REQUEST_PATH , HttpMethod.POST, httpEntity, String.class);
        assertThat(result.getBody()).isNotBlank();
        //assertThat(result.getBody()).contains(MESSAGE_ID);
    }

}
