package com.peoples.banking.adapter.pb.partnerpushnotification.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.config.AdapterConstant;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.base.util.ValidateServiceAccountProfile;
import com.peoples.banking.adapter.pb.partnerpushnotification.PartnerPushNotificationAdapter;
import com.peoples.banking.adapter.pb.partnerpushnotification.config.PushNotificationAdapterConstant;
import com.peoples.banking.adapter.pb.partnerpushnotification.config.PushNotificationAdapterProperty;
import com.peoples.banking.domain.push.notification.model.ErrorResponse;
import com.peoples.banking.domain.push.notification.model.PushNotificationRequest;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;
import java.util.stream.Collectors;

import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

@Log4j2
@Service
@Setter
public class PartnerPushNotificationAdapterImpl implements PartnerPushNotificationAdapter {

  @Autowired
  protected ObjectMapper objectMapper;

  @Autowired
  private PushNotificationAdapterProperty pushNotificationProperty;

  @Autowired
  @Qualifier("partnerPushNotificationRestTemplate")
  private RestTemplate restTemplate;

  /**
   * @inheritDoc
   */
  @PerfLogger
  @Override
  public void pushNotification(PushNotificationRequest pushNotificationRequest, ServiceAccountProfile serviceAccountProfile,
      String customerId)
      throws AdapterException, TimeoutException, TBDException, ResponseException {

    if (pushNotificationRequest == null) {
      log.warn("required parameters: request={}", pushNotificationRequest);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    if (!ValidateServiceAccountProfile.isValidServiceAccountProfile(serviceAccountProfile)) {
      log.warn("required parameters: serviceAccountProfile={}", serviceAccountProfile);
      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    String payload = null;
    try {
      payload = objectMapper.writeValueAsString(pushNotificationRequest);
    } catch (JsonProcessingException e) {
      // should NOT be triggered, as we validate this during initialization
      log.error("failed on serializing request payload", e);
      throw new AdapterException("failed on serializing PushNotificationRequest payload", ErrorCode.UNEXPECTED_EXCEPTION);
    }
    log.info("HTTP payload (raw)={}", payload);

    HttpHeaders headers = this.buildHeaders(serviceAccountProfile.getApiToken(), customerId);

    try {

      HttpEntity<PushNotificationRequest> httpEntity = new HttpEntity<>(pushNotificationRequest, headers);

      log.info("initiating request to {}", serviceAccountProfile.getEndpointUrl());

      ResponseEntity<Void> response = restTemplate.exchange(serviceAccountProfile.getEndpointUrl(), HttpMethod.POST, httpEntity, Void.class);

      // shield expensive call with debug level check
      log.info("responseStatusCode={}", response.getStatusCode());

    } catch (RestClientResponseException e) {
      /**
       * result with no side-effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      log.warn("exception received, statusCode={}", e.getRawStatusCode(), e);

      // debug logging (shield these with a isDebugEnabled for efficiency)
      log.info("headers={}", e.getResponseHeaders());
      log.info("body={}", e.getResponseBodyAsString());


      if (e.getRawStatusCode() >= 500) {
        throw new AdapterException("Unexpected error", ErrorCode.UNEXPECTED_EXCEPTION);
      } else {
        // parse the exception payloads and status code
        String exceptionResponseBody = e.getResponseBodyAsString();
        Integer statusCode = e.getRawStatusCode();

        // throw appropriate exception
        this.throwResponseException(exceptionResponseBody, statusCode);
      }
    } catch (ResourceAccessException e) {
      // for any READ only operation, there are no side effects, so throw Timeout regardless of reason but do log reason for failure

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException
        // being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw TimeoutException, as no side effects (eg. nothing created as this is a READ).
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return;
  }

  /**
   * @inheritDoc
   */
  // TODO clean this up, not elegant; make part of abstract class
  @Override
  public void throwResponseException(String response, Integer httpStatusCode) throws ResponseException {

    // sanity check
    if (response != null && !response.isBlank()) {
      ErrorResponse responseModel = null;
      try {
        responseModel = this.convertResponseToDomain(response, ErrorResponse.class);
      } catch (TBDException e) {
        log.warn("cannot convert Customer Management error response into something meaningful");
        throw new ResponseException(httpStatusCode, null, null);
      }

      throw new ResponseException(httpStatusCode, responseModel.getError().get(0).getCode(),
          responseModel.getError().get(0).getAdditionalInformation());
    } else {
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>       the generic object type that the response is converted to
   * @param payload   payload to be converted
   * @param classType the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String payload, Class<T> classType) throws TBDException {
    T response = null;

    // sanity check
    if (payload != null && !payload.isBlank()) {
      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }

  /**
   *  Build HTTP reader for the request.
   * @param serviceAccountOutboundToken service account api token
   * @param customerId customerId
   * @return HttpHeaders
   */
  private HttpHeaders buildHeaders(String serviceAccountOutboundToken, String customerId) throws AdapterException {
    // initialize
    HttpHeaders httpHeaders = new HttpHeaders();

    // Added after to avoid logging
    if(serviceAccountOutboundToken == null || serviceAccountOutboundToken.isBlank()) {
      throw new AdapterException("invalid header arguments", ErrorCode.INVALID_INPUT);
    } else {
      httpHeaders.add(AdapterConstant.HEADER_API_TOKEN, serviceAccountOutboundToken);
    }

    // default HTTP httpHeaders
    httpHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);

    // customer id optional header
    if(customerId != null) {
      httpHeaders.add(PushNotificationAdapterConstant.CUSTOMER_ID_HEADER, customerId);
    }

    // InteractionId and Timestamp header
    httpHeaders.add(AdapterConstant.HEADER_INTERACTION_ID, UUID.randomUUID().toString());
    httpHeaders.add(AdapterConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    httpHeaders.add(AdapterConstant.HEADER_IDEMPOTENCY_ID, UUID.randomUUID().toString());


    if (log.isDebugEnabled()) {
      log.info("HTTP headers {}", httpHeaders.entrySet().stream()
          .map(Object::toString).collect(Collectors.joining(",")));
    } else {
      log.info("HTTP headers {}", httpHeaders.entrySet().stream()
          .filter(e -> !HttpHeaders.AUTHORIZATION.equals(e.getKey()) &&
              !AdapterConstant.HEADER_API_TOKEN.equals(e.getKey()))
          .map(Object::toString).collect(Collectors.joining(",")));
    }

    return httpHeaders;
  }
}
