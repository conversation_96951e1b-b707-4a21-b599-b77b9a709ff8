package com.peoples.banking.adapter.pb.partnerpushnotification.config;

import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.web.client.RestTemplate;
import com.peoples.banking.adapter.base.config.RestTemplateConfig;

/**
 * Partner Push Notification Management RestTemplate Configuration
 *
 */
@Configuration
public class PushNotificationRestTemplateConfig extends RestTemplateConfig{

  /**
   * HTTP client.
   */
  @Autowired
  @Qualifier("partnerPushNotificationHttpClient")
  CloseableHttpClient httpClient;

  /**
   * Instantiate new <i>RestTemplate</i> instance.
   *
   * @return instantiated object.
   */
  @Bean("partnerPushNotificationRestTemplate")
  public RestTemplate restTemplate() {
    return getRestTemplate();
  }

  /**
   * Instantiate new <i>ClientHttpRequestFactory</i> instance.
   *
   * @return instantiated object.
   */
  @Bean("partnerPushNotificationClientHttpRequestFactory")
  public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() {
    return getClientHttpRequestFactory();
  }

  /**
   * Instantiate new <i>TaskScheduler</i>, for reaping stale HTTP connections from the pool.
   *
   * @return instantiated object.
   */
  @Bean("partnerPushNotificationTaskScheduler")
  public TaskScheduler taskScheduler() {
    return getTaskScheduler();
  }

  /**
   * @inheritDoc
   */
  @Override
  public CloseableHttpClient getHttpClient() {
    return this.httpClient;
  }
}
