package com.peoples.banking.adapter.pb.partnerpushnotification;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.domain.push.notification.model.PushNotificationRequest;


public interface PartnerPushNotificationAdapter {

  /**
   * Populate the headers for a push notification response.
   *
   * @param pushNotificationRequest full push notification request info (event information and refid)
   * @param serviceAccountProfile   service account profile
   * @param customerId              customer id
   * @return push notification header response
   * @throws AdapterException
   * @throws ResponseException
   * @throws TBDException
   * @throws TimeoutException
   */
  void pushNotification(PushNotificationRequest pushNotificationRequest, ServiceAccountProfile serviceAccountProfile,
      String customerId)
      throws AdapterException, TimeoutException, TBDException, ResponseException;

  /**
   * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
   *
   * @param response       response body from <i>Interac</i>
   * @param httpStatusCode HTTP status code from downstream system
   * @throws ResponseException response exception from Interac
   */

  void throwResponseException(String response, Integer httpStatusCode) throws ResponseException;
}
