package com.peoples.banking.adapter.pb.parternpushnotification.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;
import com.peoples.banking.adapter.pb.partnerpushnotification.impl.PartnerPushNotificationAdapterImpl;
import com.peoples.banking.domain.push.notification.model.PushNotificationRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartnerPushNotificationAdapterImplTest {

    public static final String CUSTOMER_ID = "customer_id";
    public static final String ENDPOINT_URL = "endpoint_url";

    @InjectMocks
    private PartnerPushNotificationAdapterImpl adapter;
    @Mock
    private RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final ServiceAccountProfile serviceAccountProfile = buildServiceAccountProfile();

    @BeforeEach
    public void setup () {
        adapter.setObjectMapper(objectMapper);
    }


    private PushNotificationRequest buildPushNotificationRequest() {
        PushNotificationRequest accountTransactionRequest = new PushNotificationRequest();
        return accountTransactionRequest;
    }

    @Test
    void pushNotification_success() {
        PushNotificationRequest pushNotificationRequest = buildPushNotificationRequest();
        when(restTemplate.exchange(eq(ENDPOINT_URL), eq(HttpMethod.POST), any(), eq(Void.class)))
                .thenReturn(ResponseEntity.noContent().build());
        assertDoesNotThrow(() -> {
            adapter.pushNotification(pushNotificationRequest, serviceAccountProfile, CUSTOMER_ID);
        });
    }

    private ServiceAccountProfile buildServiceAccountProfile() {
        ServiceAccountProfile serviceAccountProfile = new ServiceAccountProfile();
        serviceAccountProfile.setAuthType(ServiceAccountAuthType.NO_AUTH);
        serviceAccountProfile.setApiToken("api_token");
        serviceAccountProfile.setEndpointUrl(ENDPOINT_URL);

        return serviceAccountProfile;
    }

    @Test
    void pushNotification_fail() {

        RestClientResponseException restClientResponseException = buildRestClientResponseException();
        when(restTemplate.exchange(eq(ENDPOINT_URL), eq(HttpMethod.POST), any(), eq(Void.class)))
                .thenThrow(restClientResponseException);
        PushNotificationRequest pushNotificationRequest = buildPushNotificationRequest();
        assertThrows(ResponseException.class, () -> {
            adapter.pushNotification(pushNotificationRequest, serviceAccountProfile, CUSTOMER_ID);
        });
    }

    private RestClientResponseException buildRestClientResponseException() {
        return new RestClientResponseException("bad exception", 400, null, null, new byte[]{}, null);
    }

}