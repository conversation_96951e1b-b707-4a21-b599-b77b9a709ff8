package com.peoples.banking.api.common.kafka.listener.v1.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import lombok.extern.log4j.Log4j2;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.kafka.listener.SeekToCurrentErrorHandler;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.util.backoff.ExponentialBackOff;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Configuration
@Log4j2
public class KafkaListenerConfig   {
    private static final String SSL = "SSL";
    private static final String CONSUMER_LISTENER_CONTAINER_SUFFIX = "_ConsumerListenerContainer";
    private static final String CONCURRENT_KAFKA_LISTENER_CONTAINER_FACTORY_SUFFIX = "_ConcurrentKafkaListenerContainerFactory";
    private static final String CONSUMER_FACTORY_SUFFIX = "_ConsumerFactory";

    @Autowired
    private KafkaListenerConfigProperty kafkaListenerConfigProperty;
    @Autowired
    private ConfigurableBeanFactory beanFactory;

    @Bean
    public ProducerFactory producerFactory() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        Map<String, Object> configProps = createGenericConfigProps();
        //do not pass headers in order to be able to deserialize the message successfully into different class at the consumer side
        configProps.put(JsonSerializer.ADD_TYPE_INFO_HEADERS, false);
        return new DefaultKafkaProducerFactory<>(configProps, new StringSerializer(), new JsonSerializer<>(objectMapper));
    }

    @Bean
    public KafkaAdmin kafkaAdminClient() {
        return new KafkaAdmin(createGenericConfigProps());
    }

    @Bean
    public KafkaTemplate kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    private Map<String, Object> createGenericConfigProps() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaListenerConfigProperty.getKafkaBootstrapAddress());
        if (kafkaListenerConfigProperty.isSslEnabled()) {
            configProps.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, SSL);
            configProps.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, kafkaListenerConfigProperty.getSslTruststoreLocation());
            configProps.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, kafkaListenerConfigProperty.getSslTruststorePassword());
            configProps.put(SslConfigs.SSL_KEY_PASSWORD_CONFIG, kafkaListenerConfigProperty.getSslKeyPassword());
            configProps.put(SslConfigs.SSL_KEYSTORE_LOCATION_CONFIG, kafkaListenerConfigProperty.getSslKeystoreLocation());
            configProps.put(SslConfigs.SSL_KEYSTORE_PASSWORD_CONFIG, kafkaListenerConfigProperty.getSslKeystorePassword());
        }
        return configProps;
    }

    @PostConstruct
    public void init() {
        kafkaListenerConfigProperty.getConsumerGroupDetails().forEach(this::createConsumerFactory);
        kafkaListenerConfigProperty.getConsumerGroupDetails().forEach(this::createConcurrentKafkaListenerContainerFactory);
        kafkaListenerConfigProperty.getConsumerGroupDetails().forEach(this::createListeners);
    }

    private <MESSAGE>void createListeners(final KafkaConsumerGroupDetails<MESSAGE> details) {
        String beanName = details.getQualifiedName() + CONSUMER_LISTENER_CONTAINER_SUFFIX;
        ConcurrentKafkaListenerContainerFactory<String, MESSAGE> listenerContainerFactory = (ConcurrentKafkaListenerContainerFactory<String, MESSAGE>) beanFactory.getBean(details.getTopic() + "_" + details.getGroupId() + "_ConcurrentKafkaListenerContainerFactory");
        ConcurrentMessageListenerContainer<String, MESSAGE> container = listenerContainerFactory.createContainer(details.getTopic());
        container.setupMessageListener((MessageListener<String, MESSAGE>) record ->{
            try {
                ThreadContext.put(APICommonUtilConstant.LOGGING_FIELD_GUIID, IdGeneratorUtil.generateRequestId());
                logMessageIfDebugEnabled(details, record);
                details.getConsumer().accept(record.value());
            } finally {
                ThreadContext.remove(APICommonUtilConstant.LOGGING_FIELD_GUIID);
            }
        } );
        beanFactory.registerSingleton(beanName, container);
    }

    private <MESSAGE> void logMessageIfDebugEnabled(final KafkaConsumerGroupDetails<MESSAGE> details, final ConsumerRecord<String, MESSAGE> record) {
        if (log.isDebugEnabled()) {
            try {
                log.debug("Received message '{}' to topic {}", JsonUtil.toString(record.value()), details.getTopic());
            } catch (JsonProcessingException e) {
                log.error("Error while marshalling message to string", e);
            }
        }
    }

    private <MESSAGE>void createConcurrentKafkaListenerContainerFactory(final KafkaConsumerGroupDetails<MESSAGE> details) {
        String beanName = details.getQualifiedName() + CONCURRENT_KAFKA_LISTENER_CONTAINER_FACTORY_SUFFIX;
        ConcurrentKafkaListenerContainerFactory<String, MESSAGE> factory = new ConcurrentKafkaListenerContainerFactory<>();
        SeekToCurrentErrorHandler errorHandler = new SeekToCurrentErrorHandler(new DeadLetterPublishingRecoverer(kafkaTemplate(), ((consumerRecord, e) -> new TopicPartition(kafkaListenerConfigProperty.getDeadLetterTopic(), 0))), new ExponentialBackOff());
        errorHandler.addNotRetryableExceptions(KafkaListenerNotRetryableException.class);
        factory.setErrorHandler(errorHandler);
        Object consumerFactory = beanFactory.getBean(details.getQualifiedName() + CONSUMER_FACTORY_SUFFIX);
        factory.setConsumerFactory((ConsumerFactory<String, MESSAGE>) consumerFactory);
        beanFactory.registerSingleton(beanName, factory);
    }

    private <MESSAGE>void createConsumerFactory(final KafkaConsumerGroupDetails<MESSAGE> details) {
        String beanName = details.getQualifiedName() + CONSUMER_FACTORY_SUFFIX;
        JsonDeserializer<MESSAGE> deserializer = new JsonDeserializer<>(details.getMessageClass());
        deserializer.setRemoveTypeHeaders(false);
        deserializer.addTrustedPackages("*");
        deserializer.setUseTypeMapperForKey(true);
        Map<String, Object> props = createGenericConfigProps();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, details.getGroupId());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, deserializer);
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, kafkaListenerConfigProperty.getMaxPollInterval());
        DefaultKafkaConsumerFactory<String, MESSAGE> consumerFactory = new DefaultKafkaConsumerFactory<>(props, new StringDeserializer(), deserializer);
        beanFactory.registerSingleton(beanName, consumerFactory);
    }

}
