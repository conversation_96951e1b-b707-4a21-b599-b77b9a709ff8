package com.peoples.banking.api.common.kafka.listener.v1.config;

public class KafkaListenerNotRetryableException extends RuntimeException {
    public KafkaListenerNotRetryableException() {
        super();
    }

    public KafkaListenerNotRetryableException(final String message) {
        super(message);
    }

    public KafkaListenerNotRetryableException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public KafkaListenerNotRetryableException(final Throwable cause) {
        super(cause);
    }

    protected KafkaListenerNotRetryableException(final String message, final Throwable cause, final boolean enableSuppression, final boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
