package com.peoples.banking.api.common.kafka.listener.v1.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

@Getter
@Setter
public class KafkaListenerConfigProperty {

  private List<KafkaConsumerGroupDetails> consumerGroupDetails;

  @Value("${spring.kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;
  @Value("${kafka.dead.letter.topic}")
  private String deadLetterTopic;
  @Value("${kafka.ssl.enabled}")
  private boolean sslEnabled;
  @Value("${kafka.ssl.truststore.location}")
  private String sslTruststoreLocation;
  @Value("${kafka.ssl.truststore.password}")
  private String sslTruststorePassword;
  @Value("${kafka.ssl.keystore.location}")
  private String sslKeystoreLocation;
  @Value("${kafka.ssl.keystore.password}")
  private String sslKeystorePassword;
  @Value("${kafka.ssl.key.password}")
  private String sslKeyPassword;
  @Value("${kafka.max.poll.interval.ms}")
  private int maxPollInterval;

  public KafkaListenerConfigProperty(final List<KafkaConsumerGroupDetails> consumerGroupDetails) {
    this.consumerGroupDetails = consumerGroupDetails;
  }
}
