package com.peoples.banking.api.common.kafka.listener.v1.config;

public class KafkaListenerRetryableException extends RuntimeException {
    public KafkaListenerRetryableException() {
        super();
    }

    public KafkaListenerRetryableException(final String message) {
        super(message);
    }

    public KafkaListenerRetryableException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public KafkaListenerRetryableException(final Throwable cause) {
        super(cause);
    }

    protected KafkaListenerRetryableException(final String message, final Throwable cause, final boolean enableSuppression, final boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
