package com.peoples.banking.api.common.kafka.listener.v1.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Consumer;

@Getter
@AllArgsConstructor
public class KafkaConsumerGroupDetails<T> {
    private Class<T> messageClass;
    private String topic;
    private String groupId;
    private Consumer<T> consumer;

    public String getQualifiedName() {
        return String.join("_", topic, groupId);
    }
}
