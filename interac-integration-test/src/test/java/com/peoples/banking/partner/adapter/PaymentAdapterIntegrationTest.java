package com.peoples.banking.partner.adapter;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.mock;

import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import java.lang.Thread;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracHttpClientConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestTemplateConfig;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.partner.adapter.interac.customer.impl.CustomerAdapterImpl;
import com.peoples.banking.partner.adapter.interac.payment.impl.PaymentAdapterImpl;
import com.peoples.banking.partner.adapter.interac.request.impl.RequestAdapterImpl;
import com.peoples.banking.partner.adapter.util.CustomerUtil;
import com.peoples.banking.partner.adapter.util.PaymentUtil;
import com.peoples.banking.partner.adapter.util.RequestPaymentUtil;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.AuthenticatePayment;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.IncomingPayment;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.*;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2Choice.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import java.security.NoSuchAlgorithmException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    InteracRestAdapterConfig.class, // initializes JsonWeb beans for sender & receiver
    JsonWebSignature.class, //
    JsonWebEncryption.class,
    InteracRestTemplateConfig.class,
    InteracHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
class PaymentAdapterIntegrationTest {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(PaymentAdapterIntegrationTest.class);

  /**
   * Absolute path to the sender's private key (loaded from TestConstant.VALID_SENDER_PRIVATE_KEY)
   */
  private static final String senderPrivateKey;
  /**
   * Absolute path to the receiver's public certificate (loaded from TestConstant.VALID_RECEIVER_PUBLIC_CERT)
   */
  private static final String receiverPublicCert;

  /**
   * Static initializer, to set certificate paths before any beans are initialized.
   */
  static {
    // Sender's private key (from test classpath)
    senderPrivateKey = PaymentAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.PEOPLES_TRUST_PRIVATE_KEY).getPath();
    System.setProperty("PEOPLES_JWT_PRIVATE_KEY", senderPrivateKey);

    // Receiver's public certificate (from test classpath)
    receiverPublicCert = PaymentAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.INTERAC_PUBLIC_CERT).getPath();
    System.setProperty("INTERAC_JWT_PUBLIC_CERT", receiverPublicCert);
  }

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebSignature jws;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebEncryption jwe;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;

  // Initialize as SpyBean, manually inject
  @SpyBean
  protected ObjectMapper objectMapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected RestTemplate restTemplate;

  /**
   * Mock REST API server.
   */
  MockRestServiceServer server;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  InteracAdapterProperty interacProperties;

  @InjectMocks
  CustomerAdapterImpl customerAdapter;

  @InjectMocks
  RequestAdapterImpl requestAdapter;

  // Initialzie and inject mocks into UUT
  @InjectMocks
  PaymentAdapterImpl uut;

  /**
   * Initialization before tests are run.
   *
   * @throws AdapterException
   */
  @BeforeAll
  public void prepare() throws AdapterException {

    LOGGER.info("Initializing test class");

    // initialize Mockito
    MockitoAnnotations.initMocks(this);

    /**
     * Force fields via Reflection
     * <pre>Note: issue given these are the same class mapped to different instances, need to manually wire these</pre>
     */
    ReflectionTestUtils.setField(customerAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(customerAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(requestAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(requestAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(uut, "jwSender", jwSender);
    ReflectionTestUtils.setField(uut, "jwReceiver", jwReceiver);

    // Initialize dependencies
    customerAdapter.init();
    requestAdapter.init();

    // Initialize UUT.
    uut.init();

    //
    // pre-requisite - CUSTOMER 01 - build add customer request
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME,
            TestConstant.CUSTOMER_ONE_EMAIL);

    // pre-requisite - CUSTOMER 01 - call API
    Boolean response = null;
    try {
      response = customerAdapter.addCustomer(TestConstant.CUSTOMER_ONE_ID, addCustomerRequest, null);
      LOGGER.info("Created new customer (id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (ResponseException e) {
      // expecting '{"code":"300","text":"Customer already exists [CA000621, JUNITCUST123]."}' if already registered

      Assertions.
          assertEquals(e.getResponseCode(), "300");
      Assertions
          .assertEquals(e.getResponseText(),
              "Customer already exists [" + TestConstant.PEOPLES_MEMBER_IDENT + ", " + TestConstant.CUSTOMER_ONE_ID + "].");

      LOGGER.info("Existing customer, reusing(id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    //
    // pre-requisite - CUSTOMER 02 - build add customer request
    addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_TWO_FIRST_NAME, TestConstant.CUSTOMER_TWO_LAST_NAME,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_EMAIL);

    // pre-requisite - CUSTOMER 02 - call API
    try {
      response = customerAdapter.addCustomer(TestConstant.CUSTOMER_TWO_ID, addCustomerRequest, null);
      LOGGER.info("Created new customer (id={}", TestConstant.CUSTOMER_TWO_ID);
    } catch (ResponseException e) {
      // expecting '{"code":"300","text":"Customer already exists [CA000621, JUNITCUST456]."}' if already registered

      Assertions.
          assertEquals(e.getResponseCode(), "300");
      Assertions
          .assertEquals(e.getResponseText(),
              "Customer already exists [" + TestConstant.PEOPLES_MEMBER_IDENT + ", " + TestConstant.CUSTOMER_TWO_ID + "].");

      LOGGER.info("Existing customer, reusing(id={}", TestConstant.CUSTOMER_TWO_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }
  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @ParameterizedTest
  @ValueSource(strings = {
      "************",
      "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
      "FE80:0000:0000:0000:0202:B3FF:FE1E:8329",
      "2001:db8:3333:4444:5555:6666:7777:8888",
      "2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF",
      "2001:db8::",
      "::1234:5678",
      "2001:1970:4918:b200::85a1",
      "2001:db8::1234:5678",
      "2001:0db8:0001:0000:0000:0ab9:C0A8:0102",
      "21DA:D3:0:2F3B:2AA:FF:FE28:9C5A"
  })
  public void sendPayment_regular_ipaddress_success(String ipAddress) throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    // (Armon Rouhani) user is been register under auto-deposit now

    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    //Give different client ip to test the IPV4 + IPV6
    initiatePayment.getFraudSupplementaryInfo().setCustomerIpAddress(ipAddress);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @ParameterizedTest
  @ValueSource(strings = {
      "test",
      "Je m’appelle Jessica",
      "La présentation",
      "C'est ma vie",
      "Les fêtes en France",
      "Lettre à ma meilleure amie",
      "La célébration de Pâques",
      "Les vacances d'été",
      "Noël en France",
      "àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ''’"
  })
  public void sendPayment_regular_memo_success(String memo) throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    // (Armon Rouhani) user is been register under auto-deposit now

    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    //Give different client ip to test the IPV4 + IPV6
    initiatePayment.getFraudSupplementaryInfo().setCustomerIpAddress("************");

    initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getRemittanceInformation()
        .getUnstructured().set(0, memo);
    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment
   * <pre>successful request for payment (sent by CUSTOMER_TWO)</pre>
   * <pre>successful fulfillment payment (sent by CUSTOMER_ONE)</pre>
   */
  @Order(1)
  @Test
  public void sendPayment_fulfillment_success() throws NoSuchAlgorithmException, JsonProcessingException {

    // pre-requisite - build request for payment from CUSTOMER_TWO to CUSTOMER_ONE
    SendRequestForPaymentRequest requestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(
            TestConstant.CUSTOMER_TWO_ID, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_ACCOUNT_ONE,
            TestConstant.CUSTOMER_ONE_ID, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // unit under test
    SendRequestForPaymentResponse requestPaymentResponse = assertDoesNotThrow(() -> {
      return requestAdapter.requestPayment(TestConstant.CUSTOMER_TWO_ID, requestPaymentRequest, null);
    });

    // assertions
    Assertions.assertNotNull(requestPaymentResponse);
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getMessageIdentification());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1,
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus());
    Assertions.assertEquals(
        1, requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference());

    // unit under test - step 1 - transfer values from request payment to initiate payment
    String requestClearingSystemRefId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus().get(0).getClearingSystemReference();
    String requestEndToEndId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getOriginalEndToEndIdentification();

    // unit under test - step 1 - build initiate payment
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_TWO_EMAIL, null, null,
            requestClearingSystemRefId, requestEndToEndId, ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions.assertNotNull(initiateResponse);
    Assertions.assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - build submit payment
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_fulfillment_success : addCustomer(name={}, role=fulfiller, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);
    LOGGER.info("sendPayment_fulfillment_success : addCustomer(name={}, role=payment_requestor, id={}",
        TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_ID);

    LOGGER.info("sendPayment_fulfillment_success : requestPayment(id={}, clearingSystemReferenceId={}, amount={}",
        requestPaymentRequest.getCreditorPaymentActivationRequest().getPaymentInformation()
            .get(0).getCreditTransferTransaction().get(0).getPaymentIdentification().getInstructionIdentification(),
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus()
            .get(0).getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
        requestPaymentRequest.getCreditorPaymentActivationRequest().getPaymentInformation()
            .get(0).getCreditTransferTransaction().get(0).getAmount().getInstructedAmount().getAmount());

    LOGGER.info("sendPayment_fulfillment_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_fulfillment_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }


  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment
   * <pre>successful request for payment (sent by CUSTOMER_TWO)</pre>
   * <pre>failed fulfillment payment (sent by CUSTOMER_ONE) because 361 error</pre>
   */
  @Order(1)
  @Test
  public void sendPayment_fulfillment_random_indirect_connector_failed() throws NoSuchAlgorithmException, JsonProcessingException {

    // pre-requisite - build request for payment from CUSTOMER_TWO to CUSTOMER_ONE
    SendRequestForPaymentRequest requestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(
            TestConstant.CUSTOMER_TWO_ID, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_ACCOUNT_ONE,
            TestConstant.CUSTOMER_ONE_ID, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // unit under test
    SendRequestForPaymentResponse requestPaymentResponse = assertDoesNotThrow(() -> {
      return requestAdapter.requestPayment(TestConstant.CUSTOMER_TWO_ID, requestPaymentRequest, null);
    });

    // assertions
    Assertions.assertNotNull(requestPaymentResponse);
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getMessageIdentification());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1,
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus());
    Assertions.assertEquals(
        1, requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference());

    // unit under test - step 1 - transfer values from request payment to initiate payment
    String requestClearingSystemRefId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus().get(0).getClearingSystemReference();
    String requestEndToEndId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getOriginalEndToEndIdentification();

    // unit under test - step 1 - build initiate payment
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_TWO_EMAIL, null, null,
            requestClearingSystemRefId, requestEndToEndId, ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = null;
    try {
      initiateResponse = uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, UUID.randomUUID().toString().substring(0, 20));

    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("361", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
    }
    // assertions
    Assertions.assertNull(initiateResponse);
  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & reversePayment
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @Test
  public void initiatePayment_reversePayment_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();

    // unit under test - step 2 - submit reverse - call API
    ReversePaymentResponse reversePaymentResponse = assertDoesNotThrow(() -> {
      return uut.reversePayment(TestConstant.CUSTOMER_ONE_ID, transactionId, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(reversePaymentResponse);
    Assertions.assertNotNull(reversePaymentResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(reversePaymentResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(reversePaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    //Compare and match the original transaction Identification matched
    Assertions.assertEquals(transactionId, reversePaymentResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0)
        .getOriginalTransactionIdentification());

    //
    // outcome of the tests
    LOGGER.info("initiatePayment_reversePayment_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("initiatePayment_reversePayment_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("initiatePayment_reversePayment_success : reversePayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }


  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment & cancelPayment
   * <pre>successful Q&A payment</pre>
   * Temporary comment it out due to the delay in Send Payment status change in Interac and causes Cancel failed
   */
  @Order(1)
  //@Test
  public void sendPayment_regular_cancel_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());

    // unit under test - step 3 - build cancel payment
    PaymentsCancelPostRequestModel paymentsCancelPostRequestModel = PaymentUtil
        .buildCancelPayment(clearingSystemRefId, transactionId, "PTC interac integration test");

    // unit under test - step 2 - cancel payment - call API
    Boolean cancelPaymentResult = assertDoesNotThrow(() -> {
      return uut.cancelPayment(TestConstant.CUSTOMER_ONE_ID,
          initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
              .get(0).getClearingSystemReference(),
          paymentsCancelPostRequestModel, false, null);
    });

    Assertions.assertEquals(true, cancelPaymentResult);

    LOGGER.info("cancelPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}, result={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        cancelPaymentResult);

  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment & getPayment
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @Test
  public void sendPayment_regular_getPayment_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());

    // unit under test - step 3 - build get payment

    // unit under test - step 2 - get payment - call API
    PaymentTransaction110 paymentTransaction110 = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0);
    GetPaymentStatusRequest getPaymentStatusRequest = PaymentUtil
        .buildGetPaymentStatusRequest(initiateResponse.getFiToFiPaymentStatusReport().getGroupHeader().getMessageIdentification(),
            paymentTransaction110.getOriginalEndToEndIdentification(),
            paymentTransaction110.getOriginalInstructionIdentification(),
            paymentTransaction110.getOriginalTransactionIdentification());

    GetPaymentStatusResponse getPaymentStatusResponse = assertDoesNotThrow(() -> {
      return uut.getPayment(TestConstant.CUSTOMER_ONE_ID,
          initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
              .get(0).getClearingSystemReference(), getPaymentStatusRequest, null);
    });

    Assertions.assertNotNull(getPaymentStatusResponse);
    Assertions.assertNotNull(getPaymentStatusResponse.getPaymentStatus());

    LOGGER.info("getPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        getPaymentStatusResponse.getPaymentStatus());

  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment & getOutgoingTransfers
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @Test
  public void sendPayment_regular_getOutGoingTransfers_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());

    // unit under test - step 3 - build get payment

    OffsetDateTime from_date = OffsetDateTime.now(ZoneOffset.UTC);

    // unit under test - step 2 - get payment - call API
    List<OutgoingPayment> outgoingPayments = assertDoesNotThrow(() -> {
      return uut.getOutgoingTransfers(TestConstant.CUSTOMER_ONE_ID, from_date.minusDays(1), from_date.plusDays(3), 0, 100, null);
    });

    Assertions.assertNotNull(outgoingPayments);
    Assertions.assertNotNull(outgoingPayments.get(0).getPaymentStatus());

    LOGGER.info("getPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        outgoingPayments.get(0).getPaymentStatus());

  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment & getReceivedTransfers
   * <pre>successful Q&A payment</pre>
   */
  @Order(1)
  @Test
  public void sendPayment_regular_getReceivedTransfers_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());

    // unit under test - step 3 - build get payment

    OffsetDateTime from_date = OffsetDateTime.now(ZoneOffset.UTC);

    // unit under test - step 2 - get payment - call API
    List<ReceivedPayment> receivedPayments = assertDoesNotThrow(() -> {
      return uut.getReceivedTransfers(TestConstant.CUSTOMER_ONE_ID, from_date.minusDays(1), from_date.plusDays(3), 0, 100, null);
    });

    if (receivedPayments.size() > 0) {
      Assertions.assertNotNull(receivedPayments.get(0));
      Assertions.assertNotNull(receivedPayments.get(0).getPaymentStatus());

      LOGGER.info("getPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
          initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
              .get(0).getPaymentIdentification().getTransactionIdentification(),
          initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
              .get(0).getClearingSystemReference(),
          initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
              .get(0).getInterbankSettlementAmount().getAmount(),
          receivedPayments.get(0).getPaymentStatus());
    }
  }

  /**
   * Unit under test is PaymentAdapter::initiatePayment & submitPayment & getIncomingPayment
   * <pre>successful Q&A payment</pre>
   * Temporary comment it out due to the delay in Send Payment status change in Interac and causes GetIncomingPayment failed
   */
  @Order(1)
  //@Test
  public void sendPayment_regular_getIncomingPayment_success() throws NoSuchAlgorithmException {

    // unit under test - step 1 - build initiate request
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions
        .assertNotNull(initiateResponse);
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions
        .assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions
        .assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions
        .assertNotNull(
            initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    // unit under test - step 2 - transfer values from initiate to submit
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();
    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    // unit under test - step 2 - build submit payment
    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    // need to give interac sometime to process the payment
    try {
      Thread.sleep(6000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    IncomingPayment incomingPayment = assertDoesNotThrow(() -> {
      return uut.getIncomingPayment(TestConstant.CUSTOMER_TWO_ID, clearingSystemRefId, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(incomingPayment);
    Assertions.assertNotNull(incomingPayment.getPaymentReference());
    Assertions.assertEquals(clearingSystemRefId, incomingPayment.getPaymentReference());
    Assertions.assertNotNull(incomingPayment.getPaymentType());
    Assertions.assertEquals(PaymentType.REGULAR_PAYMENT, incomingPayment.getPaymentType());
    Assertions.assertEquals(PaymentStatus.AVAILABLE, incomingPayment.getTransferStatus());
    Assertions.assertNotNull(incomingPayment.getTransferStatus());

    Assertions.assertNotNull(incomingPayment.getAmount());
    Assertions.assertEquals(
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation().get(0).getInterbankSettlementAmount()
            .getAmount().setScale(2), incomingPayment.getAmount().getAmount());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_regular_success : addCustomer(name={}, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);

    LOGGER.info("sendPayment_regular_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_regular_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }

  /**
   * Unit under test is PaymentAdapter::authenticatePayment
   * <pre>successful Q&A payment</pre>
   * Temporary comment it out due to the delay in Send Payment status change in Interac and causes Authentication failed
   */
  @Order(2)
  //@Test
  public void authenticatePayment_regular_RightSecurityAnswer() throws NoSuchAlgorithmException {
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(TestConstant.CUSTOMER_ONE_ID, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            25.00, "John Wick", null, "<EMAIL>", null, null,
            null, null, ProprietaryEnum.REGULAR_PAYMENT);

    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return uut.initiatePayment(TestConstant.CUSTOMER_ONE_ID, initiatePayment, false, null);
    });

    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();

    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();

    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return uut.submitPayment(TestConstant.CUSTOMER_ONE_ID, paymentTransactionToken, submitRequest, null);
    });

    //wait 6 seconds for interac to process
    try {
      Thread.sleep(6000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    IncomingPayment incomingPayment = assertDoesNotThrow(() -> {
      return uut.getIncomingPayment(TestConstant.CUSTOMER_TWO_ID, clearingSystemRefId, null);
    });

    AuthenticatePayment authenticatePayment = new AuthenticatePayment();
    authenticatePayment.setSecurityAnswer(initiatePayment.getPaymentAuthentication().getSecurityAnswer());

    Boolean initiateResponse1 = assertDoesNotThrow(() -> {
      return uut.authenticatePayment(TestConstant.CUSTOMER_TWO_ID, clearingSystemRefId, authenticatePayment, null);
    });
  }

  /**
   * Unit under test is PaymentAdapter::reverseCompletePayment
   * <pre>successful Q&A payment</pre>
   */
  @Order(3)
  @ParameterizedTest
  @CsvSource({"CAtQmuMu,BAeQsaMa"})
  public void reverseCompletePayment(String interacRefId, String ptcPaymentId) {
    try {
      boolean result = uut.reverseCompletePayment(TestConstant.CUSTOMER_ONE_ID, interacRefId, ptcPaymentId, null);
    } catch (TimeoutException e) {
      e.printStackTrace();
    } catch (TBDException e) {
      e.printStackTrace();
    } catch (AdapterException e) {
      e.printStackTrace();
    } catch (ResponseException e) {
      Assertions.assertEquals("305", e.getResponseCode());
      e.printStackTrace();
    }
  }

  /**
   * Configuration.
   */
  @Configuration
  public static class PaymentAdapterConfiguration {

    @Bean
    @Primary
    public InteracAdapterProperty interacAdapterPropertySpyBean() {
      return mock(InteracAdapterProperty.class);
    }
  }
}