package com.peoples.banking.partner.adapter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracHttpClientConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestTemplateConfig;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.partner.adapter.interac.customer.impl.CustomerAdapterImpl;
import com.peoples.banking.partner.adapter.util.CustomerUtil;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.partner.domain.interac.customer.model.GetCustomerRegistrationResponse;
import com.peoples.banking.partner.domain.interac.customer.model.GetCustomerRegistrationResponse.CustomerEnabledForMoneyRequestEnum;
import com.peoples.banking.partner.domain.interac.customer.model.GetCustomerRegistrationResponse.EnabledEnum;
import com.peoples.banking.partner.domain.interac.customer.model.NotificationType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    InteracRestAdapterConfig.class, // initializes JsonWeb beans for sender & receiver
    JsonWebSignature.class, //
    JsonWebEncryption.class,
    InteracRestTemplateConfig.class,
    InteracHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
class CustomerAdapterIntegrationTest {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(CustomerAdapterIntegrationTest.class);

  /**
   * Absolute path to the sender's private key (loaded from TestConstant.VALID_SENDER_PRIVATE_KEY)
   */
  private static final String senderPrivateKey;
  /**
   * Absolute path to the receiver's public certificate (loaded from TestConstant.VALID_RECEIVER_PUBLIC_CERT)
   */
  private static final String receiverPublicCert;
  public static final String TEST_CONNECTOR_ID = "test-connector-id";

  /**
   * Static initializer, to set certificate paths before any beans are initialized.
   */
  static {
    // Sender's private key (from test classpath)
    senderPrivateKey = CustomerAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.PEOPLES_TRUST_PRIVATE_KEY).getPath();
    System.setProperty("PEOPLES_JWT_PRIVATE_KEY", senderPrivateKey);

    // Receiver's public certificate (from test classpath)
    receiverPublicCert = CustomerAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.INTERAC_PUBLIC_CERT).getPath();
    System.setProperty("INTERAC_JWT_PUBLIC_CERT", receiverPublicCert);
  }

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebSignature jws;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebEncryption jwe;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;


  // Initialize as SpyBean, manually inject
  @SpyBean
  protected ObjectMapper objectMapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected RestTemplate restTemplate;

  /**
   * Mock REST API server.
   */
  MockRestServiceServer server;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  InteracAdapterProperty interacProperties;

  // Initialize and inject mocks into UUT
  @InjectMocks
  CustomerAdapterImpl uut;

  /**
   * Initialization before tests are run.
   *
   * @throws AdapterException
   */
  @BeforeAll
  public void prepare() throws AdapterException {

    LOGGER.info("Initializing test class");

    // initialize Mockito
    MockitoAnnotations.initMocks(this);

    /**
     * Force fields via Reflection
     * <pre>Note: issue given these are the same class mapped to different instances,  need to manually wire these</pre>
     */
    ReflectionTestUtils.setField(uut, "jwSender", jwSender);
    ReflectionTestUtils.setField(uut, "jwReceiver", jwReceiver);

    // Initialize UUT.
    uut.init();
  }

  /**
   * Unit under test is CustomerAdapter::addCustomer
   */

  /**
   * Unit under test is CustomerAdapter::addCustomer
   * <pre>successful request</pre>
   */
  @Order(1)
  @Test
  public void addCustomer_success() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(response);
    Assertions.assertTrue(response);
  }

 /**
   * Unit under test is CustomerAdapter::addCustomer
   * <pre>wrong indirect connector</pre>
   */
  @Order(6)
  @Test
  public void addCustomer_wrond_indirect_connector_id_failed() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request, TEST_CONNECTOR_ID);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("361", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
    }
  }

  /**
   * Unit under test is CustomerAdapter::addCustomer
   * <pre>failed request</pre>
   */
  @Order(2)
  @Test
  public void addCustomer_wrong_email_type_failed() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      request.getNotificationPreference().get(0).setType(NotificationType.RN);
      response = uut.addCustomer(enrolmentId, request,  null);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("388", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assertions.assertEquals("Missing mandatory body element- EMAIL MISSING",
      //    responseException.getResponseText());
    }
  }

  /**
   * Unit under test is CustomerAdapter::updateCustomer we need first add customer success
   * <pre>successful request</pre>
   */
  @Order(3)
  @Test
  public void updateCustomer_success() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request,  null);

      // assertions add customer result
      Assertions.assertNotNull(response);
      Assertions.assertTrue(response);

      //call the update customer to test if it works
      response = uut.updateCustomer(enrolmentId, CustomerUtil.createTestCustomer(), null);

      // assertions update customer result
      Assertions.assertNotNull(response);
      Assertions.assertTrue(response);

    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

  }

  /**
   * Unit under test is CustomerAdapter::updateCustomer we need first add customer success
   * <pre>successful request</pre>
   */
  @Order(4)
  @Test
  public void updateCustomer_wrong_email_type_failed() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request, null);

      // assertions add customer result
      Assertions.assertNotNull(response);
      Assertions.assertTrue(response);

      //call the update customer to test if it works
      Customer customer = CustomerUtil.createTestCustomer();
      //Set the NotificationType to wrong type to trigger error
      customer.getNotificationPreference().get(0).setType(NotificationType.RN);
      response = uut.updateCustomer(enrolmentId, customer, null);

    } catch (Exception e) {

      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("388", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assertions.assertEquals("Missing mandatory body element- EMAIL MISSING",
      //    responseException.getResponseText());

    }
  }

  /**
   * Unit under test is CustomerAdapter::addCustomer
   * <pre>successful request</pre>
   */
  @Order(5)
  @Test
  public void getCustomer_success() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request, null);
      // assertions
      Assertions.assertNotNull(response);
      Assertions.assertTrue(response);

      GetCustomerRegistrationResponse getCustomerRegistrationResponse = uut.getCustomer(enrolmentId, null);
      Assertions.assertEquals(request.getCustomerName().getLegalName(),
          getCustomerRegistrationResponse.getCustomerInformation().getCustomerName().getLegalName());

      Assertions.assertEquals(request.getCustomerName().getRegistrationName(),
          getCustomerRegistrationResponse.getCustomerInformation().getCustomerName().getRegistrationName());

      Assertions.assertEquals(request.getNotificationPreference().get(0).getType(),
          getCustomerRegistrationResponse.getCustomerInformation().getNotificationPreference().get(0).getType());

      Assertions.assertEquals(request.getNotificationPreference().get(0).getActive(),
          getCustomerRegistrationResponse.getCustomerInformation().getNotificationPreference().get(0).getActive());

      Assertions.assertEquals(EnabledEnum.ENABLED, getCustomerRegistrationResponse.getEnabled());

      Assertions
          .assertEquals(CustomerEnabledForMoneyRequestEnum.ENABLED, getCustomerRegistrationResponse.getCustomerEnabledForMoneyRequest());

    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

  }

  /**
   * Unit under test is CustomerAdapter::addCustomer
   * <pre>successful request</pre>
   */
  @Order(5)
  @Test
  public void getCustomer_wrong_enrollmentId_failed() {
    // initialize
    String enrolmentId = CustomerUtil.generateEnrollmentId();

    //create the customer request
    AddCustomerRequest request = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);

    // unit under test
    Boolean response = null;
    try {
      response = uut.addCustomer(enrolmentId, request, null);
      // assertions
      Assertions.assertNotNull(response);
      Assertions.assertTrue(response);

      GetCustomerRegistrationResponse getCustomerRegistrationResponse = uut.getCustomer(enrolmentId + " 112222",  null);
    } catch (Exception e) {
      e.printStackTrace();
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("301", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assertions.assertEquals("Customer not found",responseException.getResponseText());
    }
  }


  @Configuration
  public static class CustomerAdapterConfiguration {

    @Bean
    @Primary
    public InteracAdapterProperty interacAdapterPropertySpyBean() {
      return Mockito.mock(InteracAdapterProperty.class);
    }
  }
}