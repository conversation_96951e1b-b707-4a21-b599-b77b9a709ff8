package com.peoples.banking.partner.adapter;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracHttpClientConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestTemplateConfig;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.partner.adapter.interac.customer.impl.CustomerAdapterImpl;
import com.peoples.banking.partner.adapter.interac.registration.impl.RegistrationAdapterImpl;
import com.peoples.banking.partner.adapter.util.CustomerUtil;
import com.peoples.banking.partner.adapter.util.RegistrationUtil;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.CreateAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.GetAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.ServiceType;
import com.peoples.banking.partner.domain.interac.registration.model.UpdateAccountAliasRegistrationRequest;
import com.peoples.banking.partner.domain.interac.registration.model.ViewAccountAliasRegistration;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    InteracRestAdapterConfig.class, // initializes JsonWeb beans for sender & receiver
    JsonWebSignature.class, //
    JsonWebEncryption.class,
    InteracRestTemplateConfig.class,
    InteracHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
@TestMethodOrder(OrderAnnotation.class)
class RegistrationAdapterIntegrationTest {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(RegistrationAdapterIntegrationTest.class);

  /**
   * Absolute path to the sender's private key (loaded from TestConstant.VALID_SENDER_PRIVATE_KEY)
   */
  private static final String senderPrivateKey;
  /**
   * Absolute path to the receiver's public certificate (loaded from TestConstant.VALID_RECEIVER_PUBLIC_CERT)
   */
  private static final String receiverPublicCert;

  /**
   * Static initializer, to set certificate paths before any beans are initialized.
   */
  static {
    // Sender's private key (from test classpath)
    senderPrivateKey = RegistrationAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.PEOPLES_TRUST_PRIVATE_KEY).getPath();
    System.setProperty("PEOPLES_JWT_PRIVATE_KEY", senderPrivateKey);

    // Receiver's public certificate (from test classpath)
    receiverPublicCert = RegistrationAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.INTERAC_PUBLIC_CERT).getPath();
    System.setProperty("INTERAC_JWT_PUBLIC_CERT", receiverPublicCert);
  }

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebSignature jws;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebEncryption jwe;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;

  // Initialize as SpyBean, manually inject
  @SpyBean
  protected ObjectMapper objectMapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected RestTemplate restTemplate;

  /**
   * Mock REST API server.
   */
  MockRestServiceServer server;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  InteracAdapterProperty interacProperties;

  // Initialize and inject mocks into UUT
  @InjectMocks
  CustomerAdapterImpl customerAdapter;

  // Initialize and inject mocks into UUT
  @InjectMocks
  RegistrationAdapterImpl uut;

  private String registrationId = "CA1DDXjgK2AApHeg";

  /**
   * Initialization before tests are run.
   *
   * @throws AdapterException
   */
  @BeforeAll
  public void prepare() throws AdapterException {

    LOGGER.info("Initializing test class");

    // initialize Mockito
    MockitoAnnotations.initMocks(this);

    /**
     * Force fields via Reflection
     * <pre>Note: issue given these are the same class mapped to different instances,  need to manually wire these</pre>
     */
    ReflectionTestUtils.setField(customerAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(customerAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(uut, "jwSender", jwSender);
    ReflectionTestUtils.setField(uut, "jwReceiver", jwReceiver);

    // Initialize dependencies
    customerAdapter.init();

    // Initialize UUT.
    uut.init();

    //
    // pre-requisite - CUSTOMER 01 - build add customer request
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME,
            TestConstant.CUSTOMER_ONE_EMAIL);

    // pre-requisite - CUSTOMER 01 - call API
    Boolean response = null;
    try {
      response = customerAdapter.addCustomer(TestConstant.CUSTOMER_ONE_ID, addCustomerRequest, null);
      LOGGER.info("Created new customer (id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (ResponseException e) {
      // expecting '{"code":"300","text":"Customer already exists [CA000621, JUNITCUST123]."}' if already registered

      Assertions.
          assertEquals(e.getResponseCode(), "300");
      Assertions
          .assertEquals(e.getResponseText(),
              "Customer already exists [" + TestConstant.PEOPLES_MEMBER_IDENT + ", " + TestConstant.CUSTOMER_ONE_ID + "].");

      LOGGER.info("Existing customer, reusing(id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }
  }

  /**
   * Unit under test is RegistrationAdapter::createRegistration
   */
  @Order(1)
  @Test
  public void register_alias_email_success() {

    // unit under test - step 1 - build registration request
    AccountAliasRegistration aliasRegistration = RegistrationUtil
        .buildRegisterAlias(ServiceType.EMAIL, "frankl" + System.currentTimeMillis() + "@peoplespayments.com", "621-16001-************",
            "Frank (PTC)", "Lee");

    // unit under test - step 2 - register alias - call API
    CreateAccountAliasRegistrationResponse createAccountAliasRegistrationResponse = assertDoesNotThrow(() -> {
      return uut.registerAlias(TestConstant.CUSTOMER_ONE_ID, aliasRegistration, null);
    });
    registrationId = createAccountAliasRegistrationResponse.getAccountAliasReference();

  }

  /**
   * Unit under test is RegistrationAdapter::retrieveRegisteredAlias
   */
  @Order(2)
  @Test
  public void retrieve_aliases_success() {

    // unit under test - step 1 - retrieve all aliases for this enrollmentId - call API
    GetAccountAliasRegistrationResponse getAccountAliasRegistrationResponse = assertDoesNotThrow(() -> {
      return uut.retrieveRegisteredAlias(TestConstant.CUSTOMER_ONE_ID, 2, 1, null);
    });
    assertNotNull(getAccountAliasRegistrationResponse);
    assertNotNull(getAccountAliasRegistrationResponse.getAccountAliasRegistrations());
    assertFalse(getAccountAliasRegistrationResponse.getAccountAliasRegistrations().isEmpty());
    assertEquals(2, getAccountAliasRegistrationResponse.getAccountAliasRegistrations().size());
  }

  /**
   * Unit under test is RegistrationAdapter::retrieveRegisteredAlias
   */
  @Order(2)
  @Test
  public void retrieve_aliases_wrong_indirect_connector_fail() {

    // unit under test - step 1 - retrieve all aliases for this enrollmentId - call API
    GetAccountAliasRegistrationResponse getAccountAliasRegistrationResponse = null;
    try {
      getAccountAliasRegistrationResponse = uut.retrieveRegisteredAlias(TestConstant.CUSTOMER_ONE_ID, 2, 1, "test-wrong-connector");
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("361", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      return;
    }
    Assertions.fail("unexpected code block reached");
  }

  /**
   * Unit under test is RegistrationAdapter::retrieveRegisteredAlias
   */
  @Order(3)
  @Test
  public void retrieve_specific_alias_success() {

    // unit under test - step 1 - retrieveRegisteredAlias alias - call API
    ViewAccountAliasRegistration viewAccountAliasRegistration = assertDoesNotThrow(() -> {
      return uut.retrieveRegisteredAlias(TestConstant.CUSTOMER_ONE_ID, registrationId, null);
    });
    assertNotNull(viewAccountAliasRegistration);
    if (registrationId != null) {
      assertEquals(registrationId, viewAccountAliasRegistration.getAccountAliasReference());
    }
  }

  /**
   * Unit under test is RegistrationAdapter::updateRegisteredAlias
   */
  @Order(3)
  @Test
  public void update_registered_alias_success() {

    // unit under test - step 1 - build update account alias registration request
    UpdateAccountAliasRegistrationRequest updateAccountAliasRegistrationRequest = RegistrationUtil
        .buildUpdateAccountRegistrationAlias("621-16001-************", "Frank (PTC)", "Lee");

    // unit under test - step 1 - update account alias registration - call API
    Boolean updatedAccountAliasRegistration = assertDoesNotThrow(() -> {
      return uut.updateRegisteredAlias(TestConstant.CUSTOMER_ONE_ID, registrationId, updateAccountAliasRegistrationRequest, null);
    });
    assertTrue(updatedAccountAliasRegistration);
  }

  /**
   * Unit under test is RegistrationAdapter::updateRegisteredAlias
   */
  @Order(4)
  @Test
  public void delete_registered_alias_success() {

    // unit under test - step 1 - remove registered alias by registration id and enrollment id - call API
    Boolean removeAccountAliasRegistration = assertDoesNotThrow(() -> {
      return uut.removeRegisteredAlias(TestConstant.CUSTOMER_ONE_ID, registrationId, null);
    });
    assertTrue(removeAccountAliasRegistration);
  }

  /**
   * Configuration.
   */
  @Configuration
  public static class RegistrationAdapterConfiguration {

    @Bean
    @Primary
    public InteracAdapterProperty interacAdapterPropertySpyBean() {
      return Mockito.mock(InteracAdapterProperty.class);
    }
  }
}