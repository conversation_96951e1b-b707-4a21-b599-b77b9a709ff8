package com.peoples.banking.partner.adapter.util;

import com.peoples.banking.adapter.base.util.IdGeneratorUtil;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountIdentification4Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.AccountSchemeName1Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyAndAmount;
import com.peoples.banking.partner.domain.interac.deposit.model.ActiveCurrencyCode;
import com.peoples.banking.partner.domain.interac.deposit.model.AuthenticationType;
import com.peoples.banking.partner.domain.interac.deposit.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.deposit.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.deposit.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemIdentification3Choice.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.ClearingSystemMemberIdentification2;
import com.peoples.banking.partner.domain.interac.deposit.model.Contact4;
import com.peoples.banking.partner.domain.interac.deposit.model.CreditTransferTransaction39;
import com.peoples.banking.partner.domain.interac.deposit.model.FIToFICustomerCreditTransferV08;
import com.peoples.banking.partner.domain.interac.deposit.model.FIToFIPaymentStatusRequestV03;
import com.peoples.banking.partner.domain.interac.deposit.model.FinancialInstitutionIdentification18;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericAccountIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GenericOrganisationIdentification1;
import com.peoples.banking.partner.domain.interac.deposit.model.GetPaymentStatusRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader91;
import com.peoples.banking.partner.domain.interac.deposit.model.GroupHeader93;
import com.peoples.banking.partner.domain.interac.deposit.model.HashType;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.Language;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.OrganisationIdentification29;
import com.peoples.banking.partner.domain.interac.deposit.model.Party38Choice;
import com.peoples.banking.partner.domain.interac.deposit.model.PartyIdentification135;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentAuthentication;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentIdentification7;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTransaction113;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentTypeInformation28;
import com.peoples.banking.partner.domain.interac.deposit.model.PaymentsCancelPostRequestModel;
import com.peoples.banking.partner.domain.interac.deposit.model.ProductCode;
import com.peoples.banking.partner.domain.interac.deposit.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementInstruction7;
import com.peoples.banking.partner.domain.interac.deposit.model.SettlementMethod1Code;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SupplementaryInfo;
import com.peoples.banking.partner.domain.interac.deposit.model.SupplementaryInfo.CustomerAuthenticationMethodEnum;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.Month;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Random;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.lang.Nullable;

/**
 * Utility class to aid in the generation of <i>Interac</i> schema generation, for <i>Payment Processing</i> domain.
 */
public class PaymentUtil {

  /**
   * Constant value of "NOTPROVIDED".
   */
  private static final String NOT_PROVIDED = "NOTPROVIDED";
  public static final String SECURITY_ANSWER = "Blue";

  /**
   * Build base InitializePayment request for <i>Interac.</i>
   *
   * @param enrolmentId              customer's unique identifier on the <i>Interac eTranser</i> system
   * @param accountHolderName        customer's (sender/creditor) full legal name or business legal name
   * @param accountNumber            customer's (sender/creditor)) account number to receive the funds
   * @param amount                   amount to send
   * @param recipientName            recipient's (receiver/beneficiary/debtor) full legal name or business legal name
   * @param recipientMobile          recipient's (receiver/beneficiary/debtor) mobile phone number, for SMS notifications
   * @param recipientEmail           recipient's (receiver/beneficiary/debtor) email address, for email notifications
   * @param recipientAccountNumber   recipient's (receiver/beneficiary/debtor) account number, for deposit
   * @param recipientAccountAliasRef recipient's auto-teposit registration reference number at Interac
   * @param requestPaymentId         the Interac generated reference identification of the request for payment (i.e. clearingSystemReference
   *                                 element from the request body of ISO 20022 - Get Incoming Request for Payment),
   *                                 <b>Mandatory</b> if TYPE=FULFILL_REQUEST_FOR_PAYMENT.
   * @param requestPaymentEndToEndId the EndToEndId reference identification from that pain.013 request for payment.
   * @param paymentTransactionType   Categorizes the payment/credit transfer.
   * @return
   */
  public static InitiatePaymentRequest buildInitiatePayment(
      String enrolmentId, String accountHolderName, String accountNumber, Double amount,
      String recipientName, String recipientMobile, String recipientEmail, @Nullable String recipientAccountNumber,
      @Nullable String recipientAccountAliasRef,
      @Nullable String requestPaymentId, @Nullable String requestPaymentEndToEndId,
      LocalInstrument2Choice.ProprietaryEnum paymentTransactionType) throws NoSuchAlgorithmException {

    // BASE
    InitiatePaymentRequest request = new InitiatePaymentRequest();

    // CREDITOR PAYMENT ACTIVATION REQUEST (ISO 20022 PACS.008.001.08)
    FIToFICustomerCreditTransferV08 fiToFiCustomerCreditTransfer = buildFiToFiCustomerCreditTransfer(enrolmentId,
        accountNumber, amount,
        recipientName, recipientMobile, recipientEmail, recipientAccountNumber, recipientAccountAliasRef,
        requestPaymentId, requestPaymentEndToEndId, paymentTransactionType);

    request.setFiToFiCustomerCreditTransfer(fiToFiCustomerCreditTransfer);
    request.setProductCode(ProductCode.DOMESTIC);
    {
      // EXPIRY TIME
      OffsetDateTime expiryDate = OffsetDateTime.now().plusDays(15); // TODO expose as parameter?
      request.setExpiryDate(expiryDate);
    }
    request.setFiAccountId(accountNumber);
    request.setAccountHolderName(accountHolderName);
    request.setSenderAccountIdentifier("ABC123"); // TODO must be set conditionally, based on result of get transfer options
    {
      // SUPPLEMENTARY (FRAUD) INFO
      SupplementaryInfo supplementaryInfo = new SupplementaryInfo();
      supplementaryInfo.setCustomerIpAddress(generateIpAddress());
      supplementaryInfo.setCustomerCardNumber(null); // we dont have any card #s
      supplementaryInfo.setAccountCreationDate(LocalDate.of(2020, Month.JANUARY, 15));
      supplementaryInfo.setCustomerDeviceFingerPrint("UID" + IdGeneratorUtil.generateRequestId());
      supplementaryInfo.setCustomerAuthenticationMethod(CustomerAuthenticationMethodEnum.PASSWORD);

      request.setFraudSupplementaryInfo(supplementaryInfo);
    }
    {
      // TRANSFER AUTHENTICATION
      if (paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT) {
        PaymentAuthentication paymentAuth = new PaymentAuthentication();
        paymentAuth.setAuthenticationType(AuthenticationType.PAYMENT_LEVEL);
        paymentAuth.setSecurityQuestion("What colour is the sky?"); // TODO externalize this
        {
          // // TRANSFER AUTHENTICATION . SECURITY ANSWER

          String hashSalt = PaymentUtil.generateHashSalt();

          String securityAnswer = SECURITY_ANSWER; // TODO externalize this
          securityAnswer = securityAnswer.toUpperCase() + hashSalt;

          MessageDigest digest = MessageDigest.getInstance("SHA-256");
          byte[] encodedHash = digest.digest(securityAnswer.getBytes(StandardCharsets.UTF_8));

          String hashedSecurityAnswer = Base64.getEncoder().encodeToString(encodedHash);

          paymentAuth.setSecurityAnswer(hashedSecurityAnswer);
          paymentAuth.setHashSalt(hashSalt);
        }
        paymentAuth.setHashType(HashType.SHA2);

        // TODO incorporate authentication types besides not required
        request.setPaymentAuthentication(paymentAuth);
      }
    }
    request.setLanguage(Language.EN);

    return request;
  }

  /**
   * Utility function to generate the ISO20022 PACS.008.001.08 business request payload.
   *
   * @param enrolmentId              customer's unique identifier on the <i>Interac eTranser</i> system
   * @param accountNumber            customer's (sender/creditor)) account number to receive the funds
   * @param recipientName            recipient's (receiver/beneficiary/debtor) full legal name or business legal name
   * @param recipientMobile          recipient's (receiver/beneficiary/debtor) mobile phone number, for SMS notifications
   * @param recipientEmail           recipient's (receiver/beneficiary/debtor) email address, for email notifications
   * @param recipientAccountNumber   recipient's (receiver/beneficiary/debtor) account number, for deposit
   * @param recipientAccountAliasRef recipient's auto-teposit registration reference number at Interac
   * @param requestPaymentId         the Interac generated reference identification of the request for payment (i.e. clearingSystemReference
   *                                 element from the request body of ISO 20022 - Get Incoming Request for Payment)
   * @param requestPaymentEndToEndId the EndToEndId reference identification from that pain.013 request for payment
   * @param paymentTransactionType   Categorizes the payment/credit transfer.
   * @return
   */
  private static FIToFICustomerCreditTransferV08 buildFiToFiCustomerCreditTransfer(String enrolmentId,
      String accountNumber, Double amount,
      String recipientName, String recipientMobile, String recipientEmail, String recipientAccountNumber, String recipientAccountAliasRef,
      String requestPaymentId, String requestPaymentEndToEndId, LocalInstrument2Choice.ProprietaryEnum paymentTransactionType) {

    FIToFICustomerCreditTransferV08 request = new FIToFICustomerCreditTransferV08();

    // GROUP HEADER
    {
      GroupHeader93 groupHeader = new GroupHeader93();
      groupHeader.setMessageIdentification(generateId());
      groupHeader.setCreationDatetime(OffsetDateTime.now());
      groupHeader.setNumberOfTransactions("1"); // fixed as per specification

      // GROUP HEADER . SETTLEMENT INFORMATION
      {
        SettlementInstruction7 settlementInstruction = new SettlementInstruction7();
        settlementInstruction.setSettlementMethod(SettlementMethod1Code.CLRG);

        // GROUP HEADER . SETTLEMENT INFORMATION . CLEARING SYSTEM
        {
          ClearingSystemIdentification3Choice clearingSystem = new ClearingSystemIdentification3Choice();
          clearingSystem.setProprietary(ProprietaryEnum.ETR);
          settlementInstruction.setClearingSystem(clearingSystem);
        }
        groupHeader.setSettlementInformation(settlementInstruction);
      }

      // GROUP HEADER . INSTRUCTING AGENT
      {
        BranchAndFinancialInstitutionIdentification6 instructingAgent = new BranchAndFinancialInstitutionIdentification6();

        // GROUP HEADER . INSTRUCTING AGENT . FINANCIAL INSTITUTION IDENTIFICATION
        {
          FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

          // GROUP HEADER . INSTRUCTING AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM MEMBER ID
          {
            ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
            clearingSystemId.setMemberIdentification(TestConstant.PEOPLES_MEMBER_IDENT);
            financialInstitutionIdentification.setClearingSystemMemberIdentification(clearingSystemId);
          }
          instructingAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
        }
        groupHeader.setInstructingAgent(instructingAgent);
      }

      // GROUP HEADER . INSTRUCTED AGENT
      {
        BranchAndFinancialInstitutionIdentification6 instructedAgent = new BranchAndFinancialInstitutionIdentification6();

        // GROUP HEADER . INSTRUCTING AGENT . FINANCIAL INSTITUTION IDENTIFICATION
        {
          FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

          // GROUP HEADER . INSTRUCTING AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM MEMBER ID
          {
            ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
            clearingSystemId.setMemberIdentification(NOT_PROVIDED);
            financialInstitutionIdentification.setClearingSystemMemberIdentification(clearingSystemId);
          }
          instructedAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
        }
        groupHeader.setInstructedAgent(instructedAgent);
      }

      // populate GROUP HEADER
      request.setGroupHeader(groupHeader);
    }

    // CREDIT TRANSFER
    {
      CreditTransferTransaction39 creditTransfer = new CreditTransferTransaction39();

      // CREDIT TRANSFER . PAYMENT IDENTIFICATION
      {
        PaymentIdentification7 paymentIdentification = new PaymentIdentification7();
        paymentIdentification.setInstructionIdentification(requestPaymentId); // only if fulfillment
        if (requestPaymentEndToEndId == null) {
          requestPaymentEndToEndId = NOT_PROVIDED;
        }
        paymentIdentification.setEndToEndIdentification(requestPaymentEndToEndId);
        paymentIdentification.setTransactionIdentification(generateId());

        creditTransfer.setPaymentIdentification(paymentIdentification);
      }

      // CREDIT TRANSFER . PAYMENT TYPE INFORMATION
      {
        PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28();

        // CREDIT TRANSFER . PAYMENT TYPE INFORMATION . LOCAL INSTRUMENT
        {
          LocalInstrument2Choice localInstrument = new LocalInstrument2Choice();
          localInstrument.setProprietary(paymentTransactionType);
          paymentTypeInformation.setLocalInstrument(localInstrument);

          creditTransfer.setPaymentTypeInformation(paymentTypeInformation);
        }
        // CREDIT TRANSFER . PAYMENT TYPE INFORMATION . CATEGORY PURPOSE
        // optional
      }

      // CREDIT TRANSFER . INTERBANK SETTLEMENT AMOUNT
      {
        ActiveCurrencyAndAmount currencyAndAmount = new ActiveCurrencyAndAmount();
        currencyAndAmount.setAmount(BigDecimal.valueOf(amount));
        currencyAndAmount.setCurrency(ActiveCurrencyCode.CAD);

        creditTransfer.setInterbankSettlementAmount(currencyAndAmount);
      }

      // CREDIT TRANSFER . INTERBANK SETTLEMENT DATE
      {
        creditTransfer.setInterbankSettlementDate(LocalDate.now());
      }

      // CREDIT TRANSFER . ACCEPTANCE DATE
      // optional

      creditTransfer.chargeBearer(ChargeBearerType1Code.SLEV);

      // CREDIT TRANSFER . PREVIOUS INSTRUCTING AGENT
      // N/A for now ...

      // CREDIT TRANSFER . ULTIMATE DEBTOR

      // CREDIT TRANSFER . INITIATING PARTY

      // CREDIT TRANSFER . DEBTOR
      {
        PartyIdentification135 debtor = new PartyIdentification135();
        // NAME will be retrieved from customer profile

        // CREDIT TRANSFER . DEBTOR . IDENTIFICATION
        {
          Party38Choice identification = new Party38Choice();

          // CREDIT TRANSFER . DEBTOR . IDENTIFICATION . ORGANIZATION IDENTIFICATION
          {
            OrganisationIdentification29 orgIdentification = new OrganisationIdentification29();

            // CREDIT TRANSFER . DEBTOR . IDENTIFICATION . ORGANIZATION IDENTIFICATION . OTHER
            {
              GenericOrganisationIdentification1 other = new GenericOrganisationIdentification1();
              other.setIdentification(enrolmentId); // enrollment ID

              orgIdentification.addOtherItem(other); // creates list and adds other
            }
            identification.setOrganisationIdentification(orgIdentification);
          }
          debtor.setIdentification(identification);
        }
        creditTransfer.setDebtor(debtor);
      }

      // CREDIT TRANSFER . DEBTOR . DEBTOR ACCOUNT
      {
        CashAccount38 debtorAccount = new CashAccount38();

        // CREDIT TRANSFER . DEBTOR . DEBTOR ACCOUNT . IDENTIFICATION
        {
          AccountIdentification4Choice identification = new AccountIdentification4Choice();

          // CREDIT TRANSFER . DEBTOR . DEBTOR ACCOUNT . IDENTIFICATION . OTHER
          {
            GenericAccountIdentification1 other = new GenericAccountIdentification1();
            other.setIdentification(accountNumber);
            identification.setOther(other);
          }
          debtorAccount.setIdentification(identification);
        }
        creditTransfer.setDebtorAccount(debtorAccount);
      }

      // CREDIT TRANSFER . DEBTOR AGENT
      {
        BranchAndFinancialInstitutionIdentification6 debtorAgent = new BranchAndFinancialInstitutionIdentification6();

        // CREDIT TRANSFER . DEBTOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION
        {
          FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

          // CREDIT TRANSFER . DEBTOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM ID
          {
            ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
            clearingSystemId.setMemberIdentification(TestConstant.PEOPLES_MEMBER_IDENT);
            financialInstitutionIdentification.setClearingSystemMemberIdentification(clearingSystemId);
          }
          debtorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
        }
        creditTransfer.setDebtorAgent(debtorAgent);
      }

      // CREDIT TRANSFER . CREDITOR AGENT
      {
        BranchAndFinancialInstitutionIdentification6 creditorAgent = new BranchAndFinancialInstitutionIdentification6();

        // CREDIT TRANSFER . CREDITOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION
        {
          FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

          // CREDIT TRANSFER . CREDITOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM ID
          {
            ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
            clearingSystemId.setMemberIdentification(NOT_PROVIDED);
            financialInstitutionIdentification.setClearingSystemMemberIdentification(clearingSystemId);
          }
          creditorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
        }
        creditTransfer.setCreditorAgent(creditorAgent);
      }

      // CREDIT TRANSFER . CREDITOR
      {
        PartyIdentification135 creditor = new PartyIdentification135();
        if (paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT) {
          creditor.setName(NOT_PROVIDED);
        } else {
          creditor.setName(recipientName);

          // CREDIT TRANSFER . CREDITOR . CONTACT DETAILS
          {
            Contact4 contactDetails = new Contact4();
            contactDetails.setMobileNumber(recipientMobile);
            contactDetails.setEmailAddress(recipientEmail);
            creditor.setContactDetails(contactDetails);
          }
        }

        creditTransfer.setCreditor(creditor);
      }

      // CREDIT TRANSFER . CREDITOR ACCOUNT
      if (paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_ALIAS_PAYMENT ||
          paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT) {
        // Mandatory in this case, and MUST be an alias
        CashAccount38 creditorAccount = new CashAccount38();

        // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION
        {
          AccountIdentification4Choice identification = new AccountIdentification4Choice();

          // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION . OTHER
          {
            GenericAccountIdentification1 other = new GenericAccountIdentification1();
            other.setIdentification(recipientAccountAliasRef);
            // NOTE: if scheme name is ALIAS_ACCT_NO then this element must contain the Account Alias Reference Number (i.e. Autodeposit reference number) generated by Interac

            // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION . OTHER . SCHEME NAME
            {
              AccountSchemeName1Choice schemeName = new AccountSchemeName1Choice();
              schemeName.setProprietary(AccountSchemeName1Choice.ProprietaryEnum.ALIAS_ACCT_NO);

              other.setSchemeName(schemeName);
            }

            identification.setOther(other);
          }

          creditorAccount.setIdentification(identification);
        }
        creditTransfer.setCreditorAccount(creditorAccount);
      } else if (paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_DEPOSIT_PAYMENT ||
          paymentTransactionType == LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_DEPOSIT_PAYMENT) {
        // Mandatory in this case, and MUST be an account number
        CashAccount38 creditorAccount = new CashAccount38();

        // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION
        {
          AccountIdentification4Choice identification = new AccountIdentification4Choice();

          // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION . OTHER
          {
            GenericAccountIdentification1 other = new GenericAccountIdentification1();
            other.setIdentification(recipientAccountNumber);

            // CREDIT TRANSFER . CREDITOR ACCOUNT . IDENTIFICATION . OTHER . SCHEME NAME
            {
              AccountSchemeName1Choice schemeName = new AccountSchemeName1Choice();
              schemeName.setProprietary(AccountSchemeName1Choice.ProprietaryEnum.BANK_ACCT_NO);

              other.setSchemeName(schemeName);
            }

            identification.setOther(other);
          }

          creditorAccount.setIdentification(identification);
        }

        creditTransfer.setCreditorAccount(creditorAccount);
      }
      // ELSE -- not required

      // CREDIT TRANSFER . ULTIMATE CREDITOR
      // optional and N/A

      // CREDIT TRANSFER . RELATED REMITTANCE INFORMATION
      // TODO add remittance data

      // CREDIT TRANSFER . REMITTANCE INFORMATION
      {
        RemittanceInformation16 remittanceInformation = new RemittanceInformation16();

        // CREDIT TRANSFER . REMITTANCE INFORMATION . UNSTRUCTURED (0 - 3)
        {
          String line1 = "ABC 123";
          remittanceInformation.addUnstructuredItem(line1);

          String line2 = "JP 456";
          remittanceInformation.addUnstructuredItem(line2);

          String line3 = "XYZ 789";
          remittanceInformation.addUnstructuredItem(line3);
        }
        creditTransfer.setRemittanceInformation(remittanceInformation);
      }

      request.addCreditTransferTransactionInformationItem(creditTransfer);
    }

    return request;
  }

  /**
   * Build base SubmitPayment request for <i>Interac.</i>
   *
   * @param clearingSystemRefId
   * @param transactionId
   * @return
   */
  public static SubmitPaymentRequest buildSubmitPayment(String clearingSystemRefId, String transactionId) {

    // BASE
    SubmitPaymentRequest request = new SubmitPaymentRequest();

    request.setClearingSystemReference(clearingSystemRefId);
    request.setTransactionId(transactionId);
    request.setParticipantTransactionDate(OffsetDateTime.now());

    return request;
  }

  /**
   * Build base PaymentsCancelPostRequestModel request for <i>Interac.</i>
   *
   * @param clearingSystemRefId
   * @param transactionId
   * @return
   */
  public static PaymentsCancelPostRequestModel buildCancelPayment(String clearingSystemRefId, String transactionId, String reasonMemo) {

    // BASE
    PaymentsCancelPostRequestModel request = new PaymentsCancelPostRequestModel();

    request.setTxnId(clearingSystemRefId);
    request.setTxnDate(OffsetDateTime.now());
    request.setReasonMemo(reasonMemo);

    return request;
  }

  /**
   * Build base PaymentsCancelPostRequestModel request for <i>Interac.</i>
   *
   * @param endToEndId
   * @param transactionId
   * @param instructionId
   * @return
   */
  public static GetPaymentStatusRequest buildGetPaymentStatusRequest(String messageId, String endToEndId, String instructionId,
      String transactionId) {

    // BASE
    GetPaymentStatusRequest request = new GetPaymentStatusRequest();
    FIToFIPaymentStatusRequestV03 fiToFIPaymentStatusRequestV03 = new FIToFIPaymentStatusRequestV03();
    GroupHeader91 groupHeader91 = new GroupHeader91();
    groupHeader91.setMessageIdentification(messageId);
    groupHeader91.setCreationDatetime(OffsetDateTime.now());

    FinancialInstitutionIdentification18 financialInstitutionIdentification18 = new FinancialInstitutionIdentification18();
    ClearingSystemMemberIdentification2 clearingSystemMemberIdentification2 = new ClearingSystemMemberIdentification2();
    clearingSystemMemberIdentification2.setMemberIdentification("CA000621");
    financialInstitutionIdentification18.setClearingSystemMemberIdentification(clearingSystemMemberIdentification2);
    BranchAndFinancialInstitutionIdentification6 instructingAgent = new BranchAndFinancialInstitutionIdentification6();
    instructingAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification18);
    groupHeader91.setInstructingAgent(instructingAgent);

    financialInstitutionIdentification18 = new FinancialInstitutionIdentification18();
    clearingSystemMemberIdentification2 = new ClearingSystemMemberIdentification2();
    clearingSystemMemberIdentification2.setMemberIdentification("NOTPROVIDED");
    financialInstitutionIdentification18.setClearingSystemMemberIdentification(clearingSystemMemberIdentification2);
    BranchAndFinancialInstitutionIdentification6 instructedAgent = new BranchAndFinancialInstitutionIdentification6();
    instructedAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification18);
    groupHeader91.setInstructedAgent(instructedAgent);

    fiToFIPaymentStatusRequestV03.setGroupHeader(groupHeader91);

    List<PaymentTransaction113> paymentTransaction113s = new ArrayList<>();
    PaymentTransaction113 paymentTransaction113 = new PaymentTransaction113();
    paymentTransaction113.setOriginalEndToEndIdentification(endToEndId);
    paymentTransaction113.setOriginalTransactionIdentification(transactionId);
    paymentTransaction113.setOriginalInstructionIdentification(instructionId);
    paymentTransaction113s.add(paymentTransaction113);
    fiToFIPaymentStatusRequestV03.setTransactionInformation(paymentTransaction113s);

    request.setFiToFiPaymentStatusRequest(fiToFIPaymentStatusRequestV03);

    return request;
  }


  /**
   * Utility function to return UUID (without hyphens) to meet 35-character field limit.
   *
   * @return
   */
  public static String generateId() {
    return IdGeneratorUtil.generateRequestId().replace("-", "");
  }

  /**
   * Utility function to return random IP Address.
   *
   * @return
   */
  public static String generateIpAddress() {
    Random r = new Random();
    return r.nextInt(256) + "." + r.nextInt(256) + "." + r.nextInt(256) + "." + r.nextInt(256);
  }

  /**
   * Utility function to generate random hashSalt.
   */
  public static String generateHashSalt() {
    return RandomStringUtils.random(11, true, true);
  }
}