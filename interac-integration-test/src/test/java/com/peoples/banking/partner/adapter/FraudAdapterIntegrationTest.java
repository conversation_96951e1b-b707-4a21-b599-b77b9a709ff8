package com.peoples.banking.partner.adapter;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracHttpClientConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestTemplateConfig;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.partner.adapter.interac.customer.impl.CustomerAdapterImpl;
import com.peoples.banking.partner.adapter.interac.fraud.impl.FraudAdapterImpl;
import com.peoples.banking.partner.adapter.interac.payment.impl.PaymentAdapterImpl;
import com.peoples.banking.partner.adapter.interac.request.impl.RequestAdapterImpl;
import com.peoples.banking.partner.adapter.util.CustomerUtil;
import com.peoples.banking.partner.adapter.util.FraudUtil;
import com.peoples.banking.partner.adapter.util.PaymentUtil;
import com.peoples.banking.partner.adapter.util.RequestPaymentUtil;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.InitiatePaymentResponse;
import com.peoples.banking.partner.domain.interac.deposit.model.LocalInstrument2Choice.ProprietaryEnum;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentRequest;
import com.peoples.banking.partner.domain.interac.deposit.model.SubmitPaymentResponse;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdatePaymentFraudStatusRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import java.security.NoSuchAlgorithmException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    InteracRestAdapterConfig.class, // initializes JsonWeb beans for sender & receiver
    JsonWebSignature.class, //
    JsonWebEncryption.class,
    InteracRestTemplateConfig.class,
    InteracHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
public class FraudAdapterIntegrationTest {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(FraudAdapterIntegrationTest.class);

  /**
   * Absolute path to the sender's private key (loaded from TestConstant.VALID_SENDER_PRIVATE_KEY)
   */
  private static final String senderPrivateKey;
  /**
   * Absolute path to the receiver's public certificate (loaded from TestConstant.VALID_RECEIVER_PUBLIC_CERT)
   */
  private static final String receiverPublicCert;

  /**
   * Static initializer, to set certificate paths before any beans are initialized.
   */
  static {
    // Sender's private key (from test classpath)
    senderPrivateKey = PaymentAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.PEOPLES_TRUST_PRIVATE_KEY).getPath();
    System.setProperty("PEOPLES_JWT_PRIVATE_KEY", senderPrivateKey);

    // Receiver's public certificate (from test classpath)
    receiverPublicCert = PaymentAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.INTERAC_PUBLIC_CERT).getPath();
    System.setProperty("INTERAC_JWT_PUBLIC_CERT", receiverPublicCert);
  }

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebSignature jws;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebEncryption jwe;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;

  // Initialize as SpyBean, manually inject
  @SpyBean
  protected ObjectMapper objectMapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected RestTemplate restTemplate;

  /**
   * Mock REST API server.
   */
  MockRestServiceServer server;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  InteracAdapterProperty interacProperties;

  @InjectMocks
  CustomerAdapterImpl customerAdapter;

  @InjectMocks
  RequestAdapterImpl requestAdapter;

  // Initialzie and inject mocks into UUT
  @InjectMocks
  PaymentAdapterImpl paymentAdapter;

  @InjectMocks
  FraudAdapterImpl fraudAdapter;

  /**
   * Initialization before tests are run.
   *
   * @throws AdapterException
   */
  @BeforeAll
  public void prepare() throws AdapterException {

    LOGGER.info("Initializing test class");

    // initialize Mockito
    MockitoAnnotations.initMocks(this);

    /**
     * Force fields via Reflection
     * <pre>Note: issue given these are the same class mapped to different instances, need to manually wire these</pre>
     */
    ReflectionTestUtils.setField(customerAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(customerAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(requestAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(requestAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(paymentAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(paymentAdapter, "jwReceiver", jwReceiver);

    // Initialize dependencies
    customerAdapter.init();
    requestAdapter.init();

    // Initialize UUT.
    paymentAdapter.init();

    //Initialize fraudAdapter
    fraudAdapter.init();

    //
    // pre-requisite - CUSTOMER 01 - build add customer request
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME,
            TestConstant.CUSTOMER_ONE_EMAIL);

    // pre-requisite - CUSTOMER 01 - call API
    Boolean response = null;
    try {
      response = customerAdapter.addCustomer(TestConstant.CUSTOMER_ONE_ID, addCustomerRequest, null);
      LOGGER.info("Created new customer (id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (ResponseException e) {
      // expecting '{"code":"300","text":"Customer already exists [CA000621, JUNITCUST123]."}' if already registered

      Assertions.
          assertEquals(e.getResponseCode(), "300");
      Assertions
          .assertEquals(e.getResponseText(),
              "Customer already exists [" + TestConstant.PEOPLES_MEMBER_IDENT + ", " + TestConstant.CUSTOMER_ONE_ID + "].");

      LOGGER.info("Existing customer, reusing(id={}", TestConstant.CUSTOMER_ONE_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    //
    // pre-requisite - CUSTOMER 02 - build add customer request
    addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_TWO_FIRST_NAME, TestConstant.CUSTOMER_TWO_LAST_NAME,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_EMAIL);

    // pre-requisite - CUSTOMER 02 - call API
    try {
      response = customerAdapter.addCustomer(TestConstant.CUSTOMER_TWO_ID, addCustomerRequest, null);
      LOGGER.info("Created new customer (id={}", TestConstant.CUSTOMER_TWO_ID);
    } catch (ResponseException e) {
      // expecting '{"code":"300","text":"Customer already exists [CA000621, JUNITCUST456]."}' if already registered

      Assertions.assertEquals(e.getResponseCode(), "300");
      Assertions.assertEquals(e.getResponseText(),
          "Customer already exists [" + TestConstant.PEOPLES_MEMBER_IDENT + ", " + TestConstant.CUSTOMER_TWO_ID + "].");

      LOGGER.info("Existing customer, reusing(id={}", TestConstant.CUSTOMER_TWO_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }
  }

  @Order(1)
  @Test
  public void fraud_confirmFraud_fulfillment_success() throws NoSuchAlgorithmException, JsonProcessingException {

    // pre-requisite - build request for payment from CUSTOMER_TWO to CUSTOMER_ONE
    SendRequestForPaymentRequest requestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(
            TestConstant.CUSTOMER_TWO_ID, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_ACCOUNT_ONE,
            TestConstant.CUSTOMER_ONE_ID, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // unit under test
    SendRequestForPaymentResponse requestPaymentResponse = assertDoesNotThrow(() -> {
      return requestAdapter.requestPayment(TestConstant.CUSTOMER_TWO_ID, requestPaymentRequest, null);
    });

    // assertions
    Assertions.assertNotNull(requestPaymentResponse);
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getMessageIdentification());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1,
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus());
    Assertions.assertEquals(
        1, requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus().get(0)
            .getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference());

    // unit under test - step 1 - transfer values from request payment to initiate payment
    String requestClearingSystemRefId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus().get(0).getClearingSystemReference();
    String requestEndToEndId = requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getOriginalEndToEndIdentification();

    // unit under test - step 1 - build initiate payment
    String fulfillerId = TestConstant.CUSTOMER_ONE_ID;
    String requestId = requestClearingSystemRefId;
    Double amount = 1.11;
    InitiatePaymentRequest initiatePayment = PaymentUtil
        .buildInitiatePayment(fulfillerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            amount, TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_TWO_EMAIL, null, null,
            requestId, "NOTPROVIDED", ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);

    // unit under test - step 1 - initiate payment - call API
    InitiatePaymentResponse initiateResponse = assertDoesNotThrow(() -> {
      return paymentAdapter.initiatePayment(fulfillerId, initiatePayment, false, null);
    });

    // unit under test - step 1 - assertions
    Assertions.assertNotNull(initiateResponse);
    Assertions.assertNotNull(initiateResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertEquals(1, initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus().get(0).getClearingSystemReference());
    Assertions
        .assertNotNull(initiateResponse.getPaymentTransactionToken());

    String clearingSystemRefId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();

    //unit under test - step 2 -build fraud
    UpdatePaymentFraudStatusRequest updatePaymentFraudStatusRequest = FraudUtil.buildUpdatePaymentFraudStatusRequest_suspicious();
    Boolean response = null;
    response = assertDoesNotThrow(() -> {
      return fraudAdapter.updatePaymentFraudStatus(fulfillerId, clearingSystemRefId, updatePaymentFraudStatusRequest, null);
    });

    Assertions.assertNotNull(response);
    Assertions.assertTrue(response);

    // unit under test - step 3 - build submit payment
    String paymentTransactionToken = initiateResponse.getPaymentTransactionToken();
    String transactionId = initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
        .get(0).getOriginalTransactionIdentification();

    SubmitPaymentRequest submitRequest = PaymentUtil.buildSubmitPayment(clearingSystemRefId, transactionId);

    // unit under test - step 2 - submit payment - call API
    SubmitPaymentResponse submitResponse = assertDoesNotThrow(() -> {
      return paymentAdapter.submitPayment(fulfillerId, paymentTransactionToken, submitRequest, null);
    });

    // unit under test - step 2 - assertions
    Assertions.assertNotNull(submitResponse);
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getGroupHeader());
    Assertions.assertNotNull(submitResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus());
    Assertions.assertNotNull(submitResponse.getPaymentStatus());

    //
    // outcome of the tests
    LOGGER.info("sendPayment_fulfillment_success : addCustomer(name={}, role=fulfiller, id={}",
        TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ID);
    LOGGER.info("sendPayment_fulfillment_success : addCustomer(name={}, role=payment_requestor, id={}",
        TestConstant.CUSTOMER_TWO_DISPLAY_NAME, TestConstant.CUSTOMER_TWO_ID);

    LOGGER.info("sendPayment_fulfillment_success : requestPayment(id={}, clearingSystemReferenceId={}, amount={}",
        requestPaymentRequest.getCreditorPaymentActivationRequest().getPaymentInformation()
            .get(0).getCreditTransferTransaction().get(0).getPaymentIdentification().getInstructionIdentification(),
        requestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus()
            .get(0).getTransactionInformationAndStatus().get(0).getClearingSystemReference(),
        requestPaymentRequest.getCreditorPaymentActivationRequest().getPaymentInformation()
            .get(0).getCreditTransferTransaction().get(0).getAmount().getInstructedAmount().getAmount());

    LOGGER.info("sendPayment_fulfillment_success : initiatePayment(id={}, clearingSystemReferenceId={}, amount={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount());

    LOGGER.info("sendPayment_fulfillment_success : submitPayment(id={}, clearingSystemReferenceId={}, amount={}, paymentStatus={}",
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getPaymentIdentification().getTransactionIdentification(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getClearingSystemReference(),
        initiatePayment.getFiToFiCustomerCreditTransfer().getCreditTransferTransactionInformation()
            .get(0).getInterbankSettlementAmount().getAmount(),
        initiateResponse.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus()
            .get(0).getTransactionStatus().toString());
  }

}
