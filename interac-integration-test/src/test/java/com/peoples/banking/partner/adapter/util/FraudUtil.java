package com.peoples.banking.partner.adapter.util;

import com.peoples.banking.partner.domain.interac.fraud.model.FraudStatus;
import com.peoples.banking.partner.domain.interac.fraud.model.FraudType;
import com.peoples.banking.partner.domain.interac.fraud.model.UpdatePaymentFraudStatusRequest;

public class FraudUtil {

  private FraudUtil() {
  }

  public static UpdatePaymentFraudStatusRequest buildUpdatePaymentFraudStatusRequest_suspicious(){
    UpdatePaymentFraudStatusRequest updatePaymentFraudStatusRequest = new UpdatePaymentFraudStatusRequest();
    updatePaymentFraudStatusRequest.setFraudStatus(FraudStatus.SUSPICIOUS);

    return updatePaymentFraudStatusRequest;
  }
}
