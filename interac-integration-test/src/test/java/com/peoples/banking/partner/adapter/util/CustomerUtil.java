package com.peoples.banking.partner.adapter.util;

import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.partner.domain.interac.customer.model.CustomerName;
import com.peoples.banking.partner.domain.interac.customer.model.CustomerType;
import com.peoples.banking.partner.domain.interac.customer.model.EmailNotification;
import com.peoples.banking.partner.domain.interac.customer.model.Language;
import com.peoples.banking.partner.domain.interac.customer.model.LegalName;
import com.peoples.banking.partner.domain.interac.customer.model.NotificationPreference;
import com.peoples.banking.partner.domain.interac.customer.model.NotificationType;
import com.peoples.banking.partner.domain.interac.customer.model.ProductCode;
import com.peoples.banking.partner.domain.interac.customer.model.ProductRegistration;
import com.peoples.banking.partner.domain.interac.customer.model.ProductRegistration.CurrencyCodeEnum;
import com.peoples.banking.partner.domain.interac.customer.model.RetailName;

import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.text.CharacterPredicates;
import org.apache.commons.text.RandomStringGenerator;

/**
 * Utility class to aid in the generation of <i>Interac</i> schema generation, for <i>Customer Management</i> domain.
 */
public class CustomerUtil {

  /**
   * Unique ID generation for Customer.
   */
  private static RandomStringGenerator generator =
      new RandomStringGenerator.Builder().withinRange('0', 'z')
          .filteredBy(CharacterPredicates.DIGITS, CharacterPredicates.LETTERS).build();

  /**
   * Build base AddCustomerRequest for <i>Interac</i>.
   *
   * @param firstName   customers first name
   * @param lastName    customesr last name
   * @param displayName customers display name
   * @return
   */
  public static AddCustomerRequest buildAddCustomerRequest_retail(
      String firstName, String lastName, String displayName, String emailAddress) {

    // BASE
    AddCustomerRequest request = new AddCustomerRequest();

    {
      // PRODUCT REGISTRATION
      ProductRegistration productRegistration = new ProductRegistration();
      productRegistration.setProductCode(ProductCode.DOMESTIC);
      productRegistration.addCurrencyCodeItem(CurrencyCodeEnum.CAD);

      request.addProductRegistrationItem(productRegistration);
    }
    request.setCustomerType(CustomerType.RETAIL);
    {
      // CUSTOMER NAME
      CustomerName customerName = new CustomerName();
      customerName.setRegistrationName(displayName);

      // CUSTOMER NAME . LEGAL NAME
      {
        LegalName legalName = new LegalName();

        // CUSTOMER NAME . LEGAL NAME . RETAIL NAME
        {
          RetailName retailName = new RetailName();
          retailName.setFirstName(firstName);
          retailName.setLastName(lastName);

          legalName.setRetailName(retailName);
        }

        customerName.setLegalName(legalName);
      }
      request.setCustomerName(customerName);
    }
    request.setLanguage(Language.EN);
    {
      // NOTIFICATION PREFERENCE . EMAIL
      EmailNotification emailNotification = new EmailNotification();
      emailNotification.setType(NotificationType.EMAIL);
      emailNotification.setEmail(emailAddress);
      emailNotification.setActive(true);

      request.addNotificationPreferenceItem(emailNotification);
    }
    request.setCustomerCreationDate(OffsetDateTime.now().minus(5, ChronoUnit.DAYS));

    return request;
  }

  // create a full set customers with enrollment setup to eTransfer
  public static Customer createTestCustomer() {
    Customer customer=new Customer();

    ProductRegistration productRegistration=new ProductRegistration();
    productRegistration.setProductCode(ProductCode.DOMESTIC);
    List<CurrencyCodeEnum> currencyCodeEnumList=new ArrayList();
    currencyCodeEnumList.add(CurrencyCodeEnum.CAD);
    productRegistration.setCurrencyCode(currencyCodeEnumList);

    List<ProductRegistration> productRegistrationList=new ArrayList<>();
    productRegistrationList.add(productRegistration);
    customer.setProductRegistration(productRegistrationList);

    customer.setCustomerType(CustomerType.RETAIL);

    CustomerName customerName=new CustomerName();
    LegalName legalName=new LegalName();
    RetailName retailName=new RetailName();
    retailName.setFirstName("John");
    retailName.setLastName("Wick");
    retailName.setMiddleName("White");
    legalName.setRetailName(retailName);
    customerName.setLegalName(legalName);

    customerName.setRegistrationName("John Wick");
    customer.setCustomerName(customerName);

    customer.setLanguage(Language.EN);

    EmailNotification emailNotification=new EmailNotification();
    emailNotification.setEmail("<EMAIL>");
    emailNotification.setType(NotificationType.EMAIL);
    emailNotification.setActive(true);

    List<NotificationPreference> notificationPreferenceList=new ArrayList<>();
    notificationPreferenceList.add(emailNotification);
    customer.setNotificationPreference(notificationPreferenceList);

    return customer;
  }

  /**
   * To generate 12 alphanumeric character Id.
   *
   * @return the 12 alphanumeric characters Id.
   */
  public static String generateEnrollmentId() {
    return generator.generate(12, 12);
  }

}