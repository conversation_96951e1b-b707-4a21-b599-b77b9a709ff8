package com.peoples.banking.partner.adapter.util;

import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegNotificationPreference;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration.LanguageEnum;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration.PermissionsEnum;
import com.peoples.banking.partner.domain.interac.registration.model.BankAccountIdentifier;
import com.peoples.banking.partner.domain.interac.registration.model.BankAccountIdentifier.TypeEnum;
import com.peoples.banking.partner.domain.interac.registration.model.CustomerAccount;
import com.peoples.banking.partner.domain.interac.registration.model.LegalName;
import com.peoples.banking.partner.domain.interac.registration.model.RetailName;
import com.peoples.banking.partner.domain.interac.registration.model.ServiceType;
import com.peoples.banking.partner.domain.interac.registration.model.UpdateAccountAliasRegistrationRequest;
import java.time.OffsetDateTime;
import org.apache.commons.text.CharacterPredicates;
import org.apache.commons.text.RandomStringGenerator;

/**
 * Utility class to aid in the generation of <i>Interac</i> schema generation, for <i>Registration</i> domain.
 */
public class RegistrationUtil {

  /**
   * Unique ID generation for Registration.
   */
  private static RandomStringGenerator generator =
      new RandomStringGenerator.Builder().withinRange('0', 'z')
          .filteredBy(CharacterPredicates.DIGITS, CharacterPredicates.LETTERS).build();

  /**
   * Constant value of "NOTPROVIDED".
   */
  private static final String NOT_PROVIDED = "NOTPROVIDED";

  /**
   * Build base CreateRegistration request for <i>Interac.</i>
   *
   * @param serviceType        flag indicating type of account alias
   * @param accountAliasHandle account alias handle to register
   * @param accountNumber      customer account number
   * @param recipientFirstName customer's first name (legal name)
   * @param recipientLastName  customer's last name (legal name)
   * @return
   */
  public static AccountAliasRegistration buildRegisterAlias(
      ServiceType serviceType, String accountAliasHandle,
      String accountNumber, String recipientFirstName, String recipientLastName) {

    // BASE
    AccountAliasRegistration request = new AccountAliasRegistration();

    request.setServiceType(serviceType);
    request.setAccountAliasHandle(accountAliasHandle);

    request.setParticipantAccountAliasReference(generateRegistrationId());

    request.setAccountAliasNotificationPreference(AccountAliasRegNotificationPreference.ACCOUNT_ALIAS_REGISTRATION_LEVEL);

    request.setLanguage(LanguageEnum.EN);

    // LEGAL NAME
    LegalName legalName = new LegalName();

    // LEGAL NAME . RETAIL NAME
    RetailName retailName = new RetailName();
    retailName.setFirstName(recipientFirstName);
    retailName.setLastName(recipientLastName);

    legalName.setRetailName(retailName);

    // CUSTOMER ACCOUNT
    CustomerAccount customerAccount = new CustomerAccount();
    customerAccount.setAccountHolderName(recipientFirstName + " " + recipientLastName);

    // CUSTOMER ACCOUNT . BANK ACCOUNT IDENTIFIER
    BankAccountIdentifier bankAccountIdentifier = new BankAccountIdentifier();
    bankAccountIdentifier.setType(TypeEnum.CANADIAN);
    bankAccountIdentifier.setAccount(accountNumber);

    customerAccount.setBankAccountIdentifier(bankAccountIdentifier);
    request.setCustomerAccount(customerAccount);

    request.setAccountCreationDate(OffsetDateTime.now());
    request.setSenderAccountIdentifierRequired(false);
    request.setPermissions(PermissionsEnum.AUTO_DEPOSIT);

    return request;
  }

  /**
   * Build base CreateRegistration request for <i>Interac.</i>
   *
   * @param accountNumber      customer account number
   * @param recipientFirstName customer's first name (legal name)
   * @param recipientLastName  customer's last name (legal name)
   * @return
   */
  public static UpdateAccountAliasRegistrationRequest buildUpdateAccountRegistrationAlias(
      String accountNumber, String recipientFirstName, String recipientLastName) {

    // BASE
    UpdateAccountAliasRegistrationRequest request = new UpdateAccountAliasRegistrationRequest();

    request.setParticipantAccountAliasReference(generateRegistrationId());

    request.setAccountAliasNotificationPreference(AccountAliasRegNotificationPreference.ACCOUNT_ALIAS_REGISTRATION_LEVEL);

    // LEGAL NAME
    LegalName legalName = new LegalName();

    // LEGAL NAME . RETAIL NAME
    RetailName retailName = new RetailName();
    retailName.setFirstName(recipientFirstName);
    retailName.setLastName(recipientLastName);

    legalName.setRetailName(retailName);

    // CUSTOMER ACCOUNT
    CustomerAccount customerAccount = new CustomerAccount();
    customerAccount.setAccountHolderName(recipientFirstName + " " + recipientLastName);

    // CUSTOMER ACCOUNT . BANK ACCOUNT IDENTIFIER
    BankAccountIdentifier bankAccountIdentifier = new BankAccountIdentifier();
    bankAccountIdentifier.setType(TypeEnum.CANADIAN);
    bankAccountIdentifier.setAccount(accountNumber);

    customerAccount.setBankAccountIdentifier(bankAccountIdentifier);
    request.setCustomerAccount(customerAccount);

    request.setSenderAccountIdentifierRequired(false);

    return request;
  }


  /**
   * To generate 32-64 alphanumeric identifier for registration alias.
   *
   * @return the 32-64 alphanumeric ident.
   */
  public static String generateRegistrationId() {
    return generator.generate(32, 64);
  }
}