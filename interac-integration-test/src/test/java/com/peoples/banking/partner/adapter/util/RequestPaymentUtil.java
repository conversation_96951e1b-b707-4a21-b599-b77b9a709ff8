package com.peoples.banking.partner.adapter.util;

import com.peoples.banking.adapter.base.util.IdGeneratorUtil;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.domain.interac.request.model.AccountIdentification4Choice;
import com.peoples.banking.partner.domain.interac.request.model.ActiveOrHistoricCurrencyAndAmount;
import com.peoples.banking.partner.domain.interac.request.model.ActiveOrHistoricCurrencyCode;
import com.peoples.banking.partner.domain.interac.request.model.AmountType4Choice;
import com.peoples.banking.partner.domain.interac.request.model.BranchAndFinancialInstitutionIdentification6;
import com.peoples.banking.partner.domain.interac.request.model.CashAccount38;
import com.peoples.banking.partner.domain.interac.request.model.ChargeBearerType1Code;
import com.peoples.banking.partner.domain.interac.request.model.ClearingSystemMemberIdentification2;
import com.peoples.banking.partner.domain.interac.request.model.Contact4;
import com.peoples.banking.partner.domain.interac.request.model.CreditTransferTransaction35;
import com.peoples.banking.partner.domain.interac.request.model.CreditorPaymentActivationRequestV07;
import com.peoples.banking.partner.domain.interac.request.model.CreditorReferenceInformation2;
import com.peoples.banking.partner.domain.interac.request.model.CreditorReferenceType1Choice;
import com.peoples.banking.partner.domain.interac.request.model.CreditorReferenceType2;
import com.peoples.banking.partner.domain.interac.request.model.DocumentType3Code;
import com.peoples.banking.partner.domain.interac.request.model.FinancialInstitutionIdentification18;
import com.peoples.banking.partner.domain.interac.request.model.GenericAccountIdentification1;
import com.peoples.banking.partner.domain.interac.request.model.GenericOrganisationIdentification1;
import com.peoples.banking.partner.domain.interac.request.model.GroupHeader78;
import com.peoples.banking.partner.domain.interac.request.model.Language;
import com.peoples.banking.partner.domain.interac.request.model.OrganisationIdentification29;
import com.peoples.banking.partner.domain.interac.request.model.Party38Choice;
import com.peoples.banking.partner.domain.interac.request.model.PartyIdentification135;
import com.peoples.banking.partner.domain.interac.request.model.PaymentCondition1;
import com.peoples.banking.partner.domain.interac.request.model.PaymentIdentification6;
import com.peoples.banking.partner.domain.interac.request.model.PaymentInstruction31;
import com.peoples.banking.partner.domain.interac.request.model.PaymentMethod7Code;
import com.peoples.banking.partner.domain.interac.request.model.RemittanceInformation16;
import com.peoples.banking.partner.domain.interac.request.model.RemittanceLocation7;
import com.peoples.banking.partner.domain.interac.request.model.RemittanceLocationData1;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.StructuredRemittanceInformation16;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;

/**
 * Utility class to aid in the generation of <i>Interac</i> schema generation, for <i>Request
 * Management</i> domain.
 */
public class RequestPaymentUtil {

  private static final String NOT_PROVIDED = "NOTPROVIDED";
  private static final Integer REQUEST_AMOUNT_MAXIMUM = 100;

  /**
   * Build base SendRequestForPaymentRequest request for <i>Interac.</i>
   *
   * @param enrolmentId       customer's unique identifier on the <i>Interac eTranser</i> system
   * @param accountHolderName customer's (sender/creditor) full legal name or business legal name
   * @param accountNumber     customer's (sender/creditor)) account number to receive the funds
   * @param recipientName     recipient's (receiver/beneficiary/debtor) full legal name or business
   *                          legal name
   * @param recipientMobile   recipient's (receiver/beneficiary/debtor) mobile phone number, for SMS
   *                          notifications
   * @param recipientEmail    recipient's (receiver/beneficiary/debtor) email address, for email
   *                          notifications
   * @return
   */
  public static SendRequestForPaymentRequest buildSendRequestForPaymentResponse(
      String enrolmentId, String accountHolderName, String accountNumber,
      String recipientName, String recipientMobile, String recipientEmail) {

    // BASE
    SendRequestForPaymentRequest request = new SendRequestForPaymentRequest();

    // CREDITOR PAYMENT ACTIVATION REQUEST (ISO 20022 PAIN.013.001.07
    CreditorPaymentActivationRequestV07 creditorPaymentActivationRequest = buildCreditorPaymentActivationRequestV07(
        enrolmentId, accountHolderName, accountNumber,
        recipientName, recipientMobile, recipientEmail);
    request.setCreditorPaymentActivationRequest(creditorPaymentActivationRequest);
    request.setAccountHolderName(accountHolderName);
    request.setLanguage(Language.EN);
    request.setReturnUrl("https://www.slashdot.org"); // TBD -- might be useful to us

    return request;
  }

  /**
   * Utility function to generate the ISO20022 PAIN.013.001.07 business request payload.
   *
   * @param enrolmentId       customer's unique identifier on the <i>Interac eTranser</i> system
   * @param accountHolderName customer's (sender/creditor) full legal name or business legal name
   * @param accountNumber     customer's (sender/creditor)) account number to receive the funds
   * @param recipientName     recipient's (receiver/beneficiary/debtor) full legal name or business
   *                          legal name
   * @param recipientMobile   recipient's (receiver/beneficiary/debtor) mobile phone number, for SMS
   *                          notifications
   * @param recipientEmail    recipient's (receiver/beneficiary/debtor) email address, for email
   *                          notifications
   * @return
   */
  private static CreditorPaymentActivationRequestV07 buildCreditorPaymentActivationRequestV07(
      String enrolmentId, String accountHolderName, String accountNumber,
      String recipientName, String recipientMobile, String recipientEmail) {

    CreditorPaymentActivationRequestV07 request = new CreditorPaymentActivationRequestV07();

    // GROUP HEADER
    {
      GroupHeader78 groupHeader = new GroupHeader78();
      groupHeader.setMessageIdentification(generateId());
      groupHeader.setCreationDatetime(OffsetDateTime.now());
      groupHeader.setNumberOfTransactions("1"); // fixed as per specification

      // GROUP HEADER . INITIATING PARTY
      {
        // Party that initiates the transaction(s) on behalf of the creditor.
        PartyIdentification135 initiatingParty = new PartyIdentification135();
        initiatingParty.setName(NOT_PROVIDED);

        groupHeader.setInitiatingParty(initiatingParty);
      }

      // populate GROUP HEADER
      request.setGroupHeader(groupHeader);
    }

    // PAYMENT INFORMATION
    {
      PaymentInstruction31 paymentInformation = new PaymentInstruction31();
      paymentInformation.setPaymentInformationIdentification(generateId());
      paymentInformation.setPaymentMethod(PaymentMethod7Code.TRF); // fixed as per specification
      paymentInformation.setPaymentTypeInformation(null); // TBD -- can this be null?
      paymentInformation.setRequestedExecutionDate(OffsetDateTime.now());
      paymentInformation.setExpiryDate(OffsetDateTime.now().plus(30, ChronoUnit.DAYS)); // TBD

      // PAYMENT INFORMATION . PAYMENT CONDITION
      {
        PaymentCondition1 paymentCondition = new PaymentCondition1();
        paymentCondition.setAmountModificationAllowed(true); // TBD -- this we can set
        paymentCondition.setEarlyPaymentAllowed(true); // fixed as per specification
        paymentCondition.setGuaranteedPaymentRequested(true); // fixed as per specification;
        paymentInformation.setPaymentCondition(paymentCondition);
      }

      // PAYMENT INFORMATION . DEBTOR
      {
        PartyIdentification135 debtor = new PartyIdentification135();
        debtor.setName(recipientName);

        // PAYMENT INFORMATION . DEBTOR . CONTACT DETAILS
        {
          Contact4 contactDetails = new Contact4();
          contactDetails.setEmailAddress(recipientEmail);
          debtor.setContactDetails(contactDetails);
        }
        paymentInformation.setDebtor(debtor);
      }

      // PAYMENT INFORMATION . DEBTOR AGENT
      {
        BranchAndFinancialInstitutionIdentification6 debtorAgent = new BranchAndFinancialInstitutionIdentification6();

        // PAYMENT INFORMATION . DEBTOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION
        {
          FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

          // PAYMENT INFORMATION . DEBTOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM ID
          {
            ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
            clearingSystemId.setMemberIdentification(NOT_PROVIDED);
            financialInstitutionIdentification
                .setClearingSystemMemberIdentification(clearingSystemId);
          }
          debtorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
        }
        paymentInformation.setDebtorAgent(debtorAgent);
      }

      // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION
      {
        CreditTransferTransaction35 creditTransferTransaction = new CreditTransferTransaction35();
        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . PAYMENT IDENTIFICATION
        {
          PaymentIdentification6 paymentIdentification = new PaymentIdentification6();
          paymentIdentification.setInstructionIdentification(generateId());
          paymentIdentification.setEndToEndIdentification(NOT_PROVIDED);
          creditTransferTransaction.setPaymentIdentification(paymentIdentification);
        }
        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . AMOUNT
        {
          AmountType4Choice amount = new AmountType4Choice();
          // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . AMOUNT . INSTRUCTED AMOUNT
          {
            ActiveOrHistoricCurrencyAndAmount instructedAmount = new ActiveOrHistoricCurrencyAndAmount();
            instructedAmount
                .setAmount(new BigDecimal(
                    BigInteger.valueOf(new Random().nextInt(REQUEST_AMOUNT_MAXIMUM * 100)), 2));
            instructedAmount.setCurrency(ActiveOrHistoricCurrencyCode.CAD);
            amount.setInstructedAmount(instructedAmount);

          }
          creditTransferTransaction.setAmount(amount);
        }
        creditTransferTransaction.setChargeBearer(ChargeBearerType1Code.SLEV);
        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR AGENT
        {
          BranchAndFinancialInstitutionIdentification6 creditorAgent = new BranchAndFinancialInstitutionIdentification6();

          // // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION
          {
            FinancialInstitutionIdentification18 financialInstitutionIdentification = new FinancialInstitutionIdentification18();

            // // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR AGENT . FINANCIAL INSTITUTION IDENTIFICATION . CLEARING SYSTEM ID
            {
              ClearingSystemMemberIdentification2 clearingSystemId = new ClearingSystemMemberIdentification2();
              clearingSystemId.setMemberIdentification(TestConstant.PEOPLES_MEMBER_IDENT);
              financialInstitutionIdentification
                  .setClearingSystemMemberIdentification(clearingSystemId);
            }
            creditorAgent.setFinancialInstitutionIdentification(financialInstitutionIdentification);
          }
          creditTransferTransaction.setCreditorAgent(creditorAgent);
        }

        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR
        {
          PartyIdentification135 creditor = new PartyIdentification135();

          // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR . IDENTIFICATION
          {
            Party38Choice identification = new Party38Choice();

            // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR . IDENTIFICATION . ORGANIZATION IDENTIFICATION
            {
              OrganisationIdentification29 orgIdentification = new OrganisationIdentification29();

              // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR . IDENTIFICATION . ORGANIZATION IDENTIFICATION . OTHER
              {
                GenericOrganisationIdentification1 other = new GenericOrganisationIdentification1();
                other.setIdentification(enrolmentId);
                orgIdentification.addOtherItem(other); // creates list and adds other
              }
              identification.setOrganisationIdentification(orgIdentification);
            }
            creditor.setIdentification(identification);
          }
          creditTransferTransaction.setCreditor(creditor);
        }

        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR ACCOUNT
        {
          CashAccount38 creditorAccount = new CashAccount38();

          // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR ACCOUNT . IDENTIFICATION
          {
            AccountIdentification4Choice identification = new AccountIdentification4Choice();

            // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . CREDITOR ACCOUNT . IDENTIFICATION . OTHER
            {
              GenericAccountIdentification1 other = new GenericAccountIdentification1();
              other.setIdentification(accountNumber);
              identification.setOther(other);
            }
            creditorAccount.setIdentification(identification);
          }
          creditTransferTransaction.setCreditorAccount(creditorAccount);
        }

        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . TBD -- confirm if we need to set ultimate creditor
        creditTransferTransaction.setUltimateCreditor(null);
        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION .
        ArrayList<RemittanceLocation7> relatedRemittanceInformation = new ArrayList<>();
        RemittanceLocation7 remittanceLocation = new RemittanceLocation7();
        remittanceLocation.remittanceIdentification("remitIdentification");
        ArrayList<RemittanceLocationData1> remittanceLocationDetails = new ArrayList<>();
        RemittanceLocationData1 remittanceLocationData = new RemittanceLocationData1();
        remittanceLocationData.electronicAddress("<EMAIL>");

        remittanceLocation.remittanceLocationDetails(remittanceLocationDetails);
        relatedRemittanceInformation.add(remittanceLocation);
        creditTransferTransaction.setRelatedRemittanceInformation(relatedRemittanceInformation);

        // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . REMITTANCE INFORMATION
        {
          RemittanceInformation16 remittanceInformation = new RemittanceInformation16();

          // PAYMENT INFORMATION . CREDIT TRANSFER TRANSACTION . REMITTANCE INFORMATION . UNSTRUCTURED (0 - 3)
          {
            String line1 = "ABC 123";
            remittanceInformation.addUnstructuredItem(line1);

            String line2 = "JP 456";
            remittanceInformation.addUnstructuredItem(line2);

            String line3 = "XYZ 789";
            remittanceInformation.addUnstructuredItem(line3);

            ArrayList<StructuredRemittanceInformation16> structured = new ArrayList<>();
            StructuredRemittanceInformation16 structuredRemittanceInformation = new StructuredRemittanceInformation16();
            CreditorReferenceInformation2 creditorReferenceInformation = new CreditorReferenceInformation2();
            CreditorReferenceType2 creditorReferenceType2 = new CreditorReferenceType2();
            CreditorReferenceType1Choice codeOrProprietary = new CreditorReferenceType1Choice();
            codeOrProprietary.setCode(DocumentType3Code.DISP);
            creditorReferenceType2.setCodeOrProprietary(codeOrProprietary);
            creditorReferenceInformation.setType(creditorReferenceType2);
            creditorReferenceInformation.setReference("creditor_reference");
            structuredRemittanceInformation.creditorReferenceInformation(creditorReferenceInformation);
            structured.add(structuredRemittanceInformation);
            remittanceInformation.setStructured(structured);
          }
          creditTransferTransaction.setRemittanceInformation(remittanceInformation);
        }
        paymentInformation.addCreditTransferTransactionItem(creditTransferTransaction);
      }

      // populate PAYMENT INFORMATION
      request.addPaymentInformationItem(paymentInformation);
    }

    return request;
  }

  /**
   * Utility function to return UUID (without hypens) to meet 35-character field limit.
   *
   * @return
   */
  public static String generateId() {
    return IdGeneratorUtil.generateRequestId().replace("-", "");
  }
}