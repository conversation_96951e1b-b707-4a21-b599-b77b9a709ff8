package com.peoples.banking.partner.adapter;

import static org.mockito.Mockito.mock;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracHttpClientConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConfig;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestTemplateConfig;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.partner.adapter.interac.customer.impl.CustomerAdapterImpl;
import com.peoples.banking.partner.adapter.interac.request.impl.RequestAdapterImpl;
import com.peoples.banking.partner.adapter.util.CustomerUtil;
import com.peoples.banking.partner.adapter.util.RequestPaymentUtil;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.request.model.CancelRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.RequestForPaymentResponse;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentRequest;
import com.peoples.banking.partner.domain.interac.request.model.SendRequestForPaymentResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@ContextConfiguration(classes = {
    InteracRestAdapterConfig.class, // initializes JsonWeb beans for sender & receiver
    JsonWebSignature.class, //
    JsonWebEncryption.class,
    InteracRestTemplateConfig.class,
    InteracHttpClientConfig.class})
@TestPropertySource("classpath:application.properties")
@SpringJUnitConfig()
class RequestAdapterIntegrationTest {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(RequestAdapterIntegrationTest.class);

  /**
   * Absolute path to the sender's private key (loaded from TestConstant.VALID_SENDER_PRIVATE_KEY)
   */
  private static final String senderPrivateKey;
  /**
   * Absolute path to the receiver's public certificate (loaded from TestConstant.VALID_RECEIVER_PUBLIC_CERT)
   */
  private static final String receiverPublicCert;

  /**
   * COMPRESS_HTTP_CONTENT environment variable for enable/disable http content compression
   */
  private static final String compressHttpContent = "false";

  /**
   * Static initializer, to set certificate paths before any beans are initialized.
   */
  static {
    // Sender's private key (from test classpath)
    senderPrivateKey = RequestAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.PEOPLES_TRUST_PRIVATE_KEY).getPath();
    System.setProperty("PEOPLES_JWT_PRIVATE_KEY", senderPrivateKey);

    // Receiver's public certificate (from test classpath)
    receiverPublicCert = RequestAdapterIntegrationTest.class.getClassLoader().getResource(TestConstant.INTERAC_PUBLIC_CERT).getPath();
    System.setProperty("INTERAC_JWT_PUBLIC_CERT", receiverPublicCert);

    //add COMPRESS_HTTP_CONTENT environment variable and set it to false to disable the compression
    System.setProperty("COMPRESS_HTTP_CONTENT", compressHttpContent);
  }

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebSignature jws;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected JsonWebEncryption jwe;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  // Initialize as SpyBean, manually inject
  @SpyBean
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;


  // Initialize as SpyBean, manually inject
  @SpyBean
  protected ObjectMapper objectMapper;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  protected RestTemplate restTemplate;

  /**
   * Mock REST API server.
   */
  MockRestServiceServer server;

  // Initialize as SpyBean, and inject via @InjectMock into UUT
  @SpyBean
  InteracAdapterProperty interacProperties;

  @InjectMocks
  CustomerAdapterImpl customerAdapter;

  // Initialize and inject mocks into UUT
  @InjectMocks
  RequestAdapterImpl uut;

  /**
   * Initialization before tests are run.
   *
   * @throws AdapterException
   */
  @BeforeAll
  public void prepare() throws AdapterException {

    LOGGER.info("Initializing test class");

    // initialize Mockito
    MockitoAnnotations.initMocks(this);

    /**
     * Force fields via Reflection
     * <pre>Note: issue given these are the same class mapped to different instances, need to manually wire these</pre>
     */
    ReflectionTestUtils.setField(customerAdapter, "jwSender", jwSender);
    ReflectionTestUtils.setField(customerAdapter, "jwReceiver", jwReceiver);

    ReflectionTestUtils.setField(uut, "jwSender", jwSender);
    ReflectionTestUtils.setField(uut, "jwReceiver", jwReceiver);

    // Initialize dependencies
    customerAdapter.init();

    // Initialize UUT.
    uut.init();
  }

  /**
   * Unit under test is RequestAdapter::requestPayment
   */

  /**
   * Unit under test is RequestAdapter::requestPayment
   * <pre>successful request</pre>
   */
  @Order(1)
  @ParameterizedTest
  @ValueSource(strings = {
      "test",
      "Je m’appelle Jessica",
      "La présentation",
      "C'est ma vie",
      "Les fêtes en France",
      "Lettre à ma meilleure amie",
      "La célébration de Pâques",
      "Les vacances d'été",
      "Noël en France",
      "àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ''’"
  })
  public void requestPayment_success(String memo) {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest request = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    request.getCreditorPaymentActivationRequest().getPaymentInformation().get(0).getCreditTransferTransaction().get(0)
        .getRemittanceInformation().getUnstructured().set(0, memo);
    // sanity check
    Assertions.assertNotNull(request);
    Assertions.assertNotNull(request.getCreditorPaymentActivationRequest());

    // unit under test
    SendRequestForPaymentResponse response = null;
    try {
      response = uut.requestPayment(customerId, request, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(response);
    Assertions.assertNotNull(response.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(response.getCreditorPaymentActivationRequestStatusReport().getGroupHeader());
    Assertions.assertNotNull(response.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getMessageIdentification());
    Assertions.assertNotNull(response.getCreditorPaymentActivationRequestStatusReport().getGroupHeader().getCreationDatetime());
  }

  /**
   * Unit under test is RequestAdapter::requestPayment
   * <pre>successful request</pre>
   */
  @Order(1)
  @Test
  public void requestPayment_wrong_indirect_connector_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest request = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(request);
    Assertions.assertNotNull(request.getCreditorPaymentActivationRequest());

    // unit under test
    SendRequestForPaymentResponse response = null;
    try {
      response = uut.requestPayment(customerId, request, "wrong-connector-id");
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("361", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
    }

    // assertions
    Assertions.assertNull(response);
  }

  /**
   * Unit under test is RequestAdapter::requestPayment
   * <pre>successful request</pre>
   */
  @Order(2)
  @Test
  public void requestPayment_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request with wrong email address
    SendRequestForPaymentRequest request = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL.replace("@", ""));

    // sanity check
    Assertions.assertNotNull(request);
    Assertions.assertNotNull(request.getCreditorPaymentActivationRequest());

    // unit under test
    SendRequestForPaymentResponse response = null;
    try {
      response = uut.requestPayment(customerId, request, null);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      //Assertions.assertEquals("5000", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assert the text message return back contain Invalid Email
      //Assertions.assertEquals(true, responseException.getResponseText().contains(TestConstant.INVALID_EMAIL));
    }
  }

  /**
   * Unit under test is RequestAdapter::getRequestPayment
   * <pre>successful request</pre>
   */
  @Order(1)
  @Test
  public void getRequestPayment_success() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
      Assertions.assertEquals(true, addCustomerResult);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    RequestForPaymentResponse response = null;
    String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();
    try {
      response = uut.getRequestPayment(customerId, requestId, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(response);
  }

  /**
   * Unit under test is RequestAdapter::getRequestPayment
   * <pre>failed request</pre>
   */
  @Order(1)
  @Test
  public void getRequestPayment_invalid_requestId_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
      Assertions.assertEquals(true, addCustomerResult);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    RequestForPaymentResponse response = null;
    String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();
    try {
      //call get payment with a not exist requestId
      response = uut.getRequestPayment(customerId, requestId + "abc", null);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("305", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assert the text message return back contain not exist
      //Assertions.assertEquals(true, responseException.getResponseText().contains(TestConstant.NOT_EXIST));
    }
  }

  /**
   * Unit under test is RequestAdapter::getRequestPayment
   * <pre>failed request</pre>
   */
  @Order(1)
  @Test
  public void getRequestPayment_invalid_customerId_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
      Assertions.assertEquals(true, addCustomerResult);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    RequestForPaymentResponse response = null;
    String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport().getOriginalPaymentInformationAndStatus()
        .get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference();
    try {
      //call get payment with a not exist requestId
      response = uut.getRequestPayment(customerId + "abc", requestId, null);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("301", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assert the text message return back contain Customer
      //Assertions.assertEquals(true, responseException.getResponseText().contains(TestConstant.Customer));
    }
  }

  /**
   * Unit under test is RequestAdapter::cancelRequestPayment
   * <pre>successful request</pre>
   */
  @Order(1)
  @Test
  public void cancelRequestPayment_success() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    Boolean response = null;
    CancelRequestForPaymentRequest cancelRequestForPaymentRequest = new CancelRequestForPaymentRequest();

    try {
      //get the original requestId
      String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
          .getOriginalPaymentInformationAndStatus()
          .get(0).getTransactionInformationAndStatus()
          .get(0).getClearingSystemReference();

      cancelRequestForPaymentRequest.setCancelReason("Unit test from code");

      response = uut.cancelRequestPayment(customerId, requestId, cancelRequestForPaymentRequest, null);

      Assertions.assertEquals(true, response);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(response);
  }

  /**
   * Unit under test is RequestAdapter::cancelRequestPayment with invalid requestId
   * <pre>failed request</pre>
   */
  @Order(1)
  @Test
  public void cancelRequestPayment_invalid_requestId_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    Boolean response = null;
    CancelRequestForPaymentRequest cancelRequestForPaymentRequest = new CancelRequestForPaymentRequest();

    try {
      //get the original requestId
      String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
          .getOriginalPaymentInformationAndStatus()
          .get(0).getTransactionInformationAndStatus()
          .get(0).getClearingSystemReference();

      cancelRequestForPaymentRequest.setCancelReason("Unit test from code");

      response = uut.cancelRequestPayment(customerId, requestId + "abc", cancelRequestForPaymentRequest, null);

      Assertions.assertEquals(true, response);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("511", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assert the text message return back contain not exist
      //Assertions.assertEquals(true, responseException.getResponseText().contains(TestConstant.NOT_EXIST));
    }
  }

  /**
   * Unit under test is RequestAdapter::cancelRequestPayment with invalid customerId
   * <pre>failed request</pre>
   */
  @Order(1)
  @Test
  public void cancelRequestPayment_invalid_customerId_failed() {
    // initialize
    String customerId = CustomerUtil.generateEnrollmentId();

    // pre-requisite
    AddCustomerRequest addCustomerRequest = CustomerUtil
        .buildAddCustomerRequest_retail(TestConstant.CUSTOMER_ONE_FIRST_NAME, TestConstant.CUSTOMER_ONE_LAST_NAME,
            TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_EMAIL);
    try {
      Boolean addCustomerResult = customerAdapter.addCustomer(customerId, addCustomerRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(addCustomerRequest);

    // build request
    SendRequestForPaymentRequest sendRequestPaymentRequest = RequestPaymentUtil
        .buildSendRequestForPaymentResponse(customerId, TestConstant.CUSTOMER_ONE_DISPLAY_NAME, TestConstant.CUSTOMER_ONE_ACCOUNT_ONE,
            TestConstant.CUSTOMER_TWO_DISPLAY_NAME, null, TestConstant.CUSTOMER_ONE_EMAIL);

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentRequest);
    Assertions.assertNotNull(sendRequestPaymentRequest.getCreditorPaymentActivationRequest());

    // pre-requisite
    SendRequestForPaymentResponse sendRequestPaymentResponse = null;
    try {
      sendRequestPaymentResponse = uut.requestPayment(customerId, sendRequestPaymentRequest, null);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // sanity check
    Assertions.assertNotNull(sendRequestPaymentResponse);
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus());
    Assertions.assertEquals(1, sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus().size());
    Assertions.assertNotNull(sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
        .getOriginalPaymentInformationAndStatus().get(0).getTransactionInformationAndStatus()
        .get(0).getClearingSystemReference());

    // unit under test
    Boolean response = null;
    CancelRequestForPaymentRequest cancelRequestForPaymentRequest = new CancelRequestForPaymentRequest();

    try {
      //get the original requestId
      String requestId = sendRequestPaymentResponse.getCreditorPaymentActivationRequestStatusReport()
          .getOriginalPaymentInformationAndStatus()
          .get(0).getTransactionInformationAndStatus()
          .get(0).getClearingSystemReference();

      cancelRequestForPaymentRequest.setCancelReason("Unit test from code");

      response = uut.cancelRequestPayment(customerId + "abc", requestId , cancelRequestForPaymentRequest, null);

      Assertions.assertEquals(true, response);
    } catch (Exception e) {
      ResponseException responseException = (ResponseException) e;
      Assertions.assertEquals(400, responseException.getHttpStatusCode());
      Assertions.assertEquals("301", responseException.getResponseCode());
      Assertions.assertEquals(TestConstant.REQUEST_NOT_COMPLETED, responseException.getErrorCode().toString());
      //Assert the text message return back contain Customer
      //Assertions.assertEquals(true, responseException.getResponseText().contains(TestConstant.Customer));
    }
  }


  @Configuration
  public static class RequestAdapterConfiguration {

    @Bean
    @Primary
    public InteracAdapterProperty interacAdapterPropertySpyBean() {
      return mock(InteracAdapterProperty.class);
    }
  }
}