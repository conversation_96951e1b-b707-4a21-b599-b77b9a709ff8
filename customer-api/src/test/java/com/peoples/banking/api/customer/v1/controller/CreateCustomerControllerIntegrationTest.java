package com.peoples.banking.api.customer.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.adapter.pb.system.SystemAdapter;
import com.peoples.banking.api.customer.v1.CustomerTestUtil;
import com.peoples.banking.api.customer.v1.ProductTestUtil;
import com.peoples.banking.domain.customer.model.Address;
import com.peoples.banking.domain.customer.model.BusinessName;
import com.peoples.banking.domain.customer.model.CountryType;
import com.peoples.banking.domain.customer.model.CreateCustomerRequest;
import com.peoples.banking.domain.customer.model.CreateCustomerResponse;
import com.peoples.banking.domain.customer.model.CustomerEnum;
import com.peoples.banking.domain.customer.model.CustomerName;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.IndividualName;
import com.peoples.banking.domain.customer.model.PhoneNumberEnum;
import com.peoples.banking.domain.customer.model.SubdivisionEnum;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Products;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.customer.repository.ProductsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CreateCustomerControllerIntegrationTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @Autowired
  private CustomersRepository customersRepository;

  @Autowired
  private ProductsRepository productsRepository;

  @MockBean
  private SystemAdapter systemAdapter;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private CreateCustomerRequest request;

  private Customers customers;

  private List<Products> productList;

  private HttpHeaders headers;

  private HttpEntity<CreateCustomerRequest> requestCreate;

  private ResponseEntity<CreateCustomerResponse> responseEntity;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private ServiceAccountResponse serviceAccountResponse;

  private Customers dbCustomer = null;


  static {
    System.setProperty("SPRING_CONFIG_LOCATION", "classpath:/");
  }

  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1/customer/");
    customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    productList = new ArrayList<>();
    productList.add(ProductTestUtil.createTestProducts());
    headers = getHttpHeader();
    dbCustomer = null;
    serviceAccountResponse = CustomerTestUtil
        .createServiceAccountResponse(CustomerTestUtil.SERVICE_ACCOUNT_API_TOKEN, CustomerTestUtil.SERVICE_ACCOUNT_REF_ID, true);
  }

  @AfterEach
  public void tearDown() {
    template = null;
    if (dbCustomer != null) {
      customersRepository.delete(dbCustomer);
    }

    //rollback the DB change
    List<Products> productsList = productsRepository.findAll();
    for (Products product : productsList) {
      if (product.getExternalRefId().equals("nonETransfer")) {
        product.setExternalRefId("ETRANSFER");
        productsRepository.save(product);
        break;
      }
    }

    customersRepository = null;
    base = null;
    request = null;
    customers = null;
    productList = null;
    headers = null;
    requestCreate = null;
    responseEntity = null;
    errResponseEntity = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, CustomerTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "123456");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  @Test
  public void addCustomer_success() throws Exception {

    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);


    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());

    dbCustomer = customersOptional.get();
    assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());

  }

  @ParameterizedTest
  @ValueSource(strings = {
      "á",
      "à",
      "â",
      "ä",
      "è",
      "é",
      "ê",
      "ë",
      "î",
      "ï",
      "ô",
      "œ",
      "ù",
      "û",
      "ü",
      "ÿ",
      "ç",
      "À",
      "Â",
      "È",
      "É",
      "Ê",
      "Ë",
      "Î",
      "Ï",
      "Ô",
      "Œ",
      "Ù",
      "Û",
      "Ü",
      "Ÿ",
      "Ç",
      "Hello World",
      "Hello World Jr",
      "áàâäèéêëîïôœùûüúÿçÁÀÂÄÈÉÊËÎÏÔŒÙÛÜÚŸÇ"
  })
  public void addCustomer_specialChar_lastName_success(String specialName) throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);

    IndividualName individualName = request.getName().getIndividualName();
    individualName.setLastName(specialName);
    request.getName().setIndividualName(individualName);

    requestCreate = new HttpEntity<>(request, headers);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());

      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "testá",
      "testà",
      "testâ",
      "testä",
      "testè",
      "testé",
      "testê",
      "testë",
      "testî",
      "testï",
      "testô",
      "testœ",
      "testù",
      "testû",
      "testü",
      "testú",
      "testÿ",
      "testç",
      "testç",
      "testÁ",
      "testÂ",
      "testÈ",
      "testÉ",
      "testÊ",
      "testË",
      "testÎ",
      "testÏ",
      "testÔ",
      "testŒ",
      "testÙ",
      "testÛ",
      "testÜ",
      "testÚ",
      "testŸ",
      "testÇ"
  })
  public void addCustomer_specialChar_displayName_success(String specialName) throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);

    request.getName().setDisplayName(specialName);

    requestCreate = new HttpEntity<>(request, headers);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);


    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());

    dbCustomer = customersOptional.get();
    assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());

  }

  @ParameterizedTest
  @ValueSource(strings = {
      "Hello World",
      "Hello World Jr"
  })
  public void createCustomer_small_business_name_with_space_success(String specialName) throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.SMALL_BUSINESS);

    request.getName().setDisplayName(specialName);

    requestCreate = new HttpEntity<>(request, headers);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());

      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "Hello World",
      "Hello World Jr"
  })
  public void createCustomer_corporation_name_with_space_success(String specialName) throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.CORPORATION);

    request.getName().setDisplayName(specialName);

    requestCreate = new HttpEntity<>(request, headers);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());

      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
//      "test/<EMAIL>",
      "<EMAIL>",
//      "\"john..doe\"@example.org",
//      "mailhost!<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
  })
  public void addCustomer_email_RFC_5322_success(String email) throws Exception {

    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getEmail().get(0).setAddress(email);
    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @Test
  public void addCustomer_nullDisplayName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getName().setDisplayName(null);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);


    Optional<Customers> customersOptional = customersRepository
            .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());
  }

  @Test
  public void addCustomer_whitespaceDisplayName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getName().setDisplayName("   ");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());

  }

  @Test
  public void addCustomer_nullIndividualName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getName().setIndividualName(null);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
            .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());
  }

  @Test
  public void addCustomer_emptyBusinessNameLegalName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.SMALL_BUSINESS);
    request.getName().getBusinessName().setLegalName("");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_whitespaceBusinessNameLegalName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.SMALL_BUSINESS);
    request.getName().getBusinessName().setLegalName("     ");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_emptyBusinessNameDisplayName_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.SMALL_BUSINESS);
    request.getName().getBusinessName().setDisplayName("");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());

  }

  @Test
  public void addCustomer_whitespaceBusinessNameDisplayName_fail() {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.SMALL_BUSINESS);
    request.getName().getBusinessName().setDisplayName("     ");
    request.setDateOfBirth(null);

    requestCreate = new HttpEntity<>(request, headers);

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());
  }

  @Test
  public void addCustomer_mismatchNameType_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.setType(CustomerEnum.CORPORATION);
    request.setDateOfBirth(null);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());

  }

  /**
   * create an individual customer with future DOB to trigger INVALID_DATA_OF_BIRTH
   *
   * @throws Exception
   */
  @Test
  public void addCustomer_individual_dobFuture_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.setDateOfBirth(LocalDate.now().plusDays(100));

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);


    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());
  }

  /**
   * create an individual customer with Null DOB to success, since now it's optional, it should be success
   *
   * @throws Exception
   */
  @Test
  public void addCustomer_individual_dobNull_success() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.setDateOfBirth(null);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(false).when(systemAdapter).checkUniqueIdPerPartnerExist(isA(String.class), isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  /**
   * create an corporation customer with not empty DOB to trigger NOT_APPLICABLE
   *
   * @throws Exception
   */
  @Test
  public void addCustomer_corporation_dobNotEmpty_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    BusinessName bName = new BusinessName();
    bName.setLegalName("Marvel Corp.");
    bName.setDisplayName("Marvel");
    CustomerName custName = new CustomerName();
    custName.setBusinessName(bName);
    custName.setDisplayName("Marvel");
    request.setName(custName);
    request.setType(CustomerEnum.CORPORATION);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_smallBusiness_dobNotEmpty_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getPhone().get(0).setEnableNotification(true);
    request.getPhone().get(0).setType(PhoneNumberEnum.HOME);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertEquals(true, customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_duplicateAddressId_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getAddress().get(0).setId("id01");
    request.getAddress().get(1).setId("id01");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_duplicateEmailId_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getEmail().get(0).setId("id01");
    request.getEmail().get(1).setId("id01");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_duplicatePhoneId_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getPhone().get(0).setId("id01");
    request.getPhone().get(1).setId("id01");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_duplicateEmail_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getEmail().get(0).setAddress("<EMAIL>");
    request.getEmail().get(1).setAddress("<EMAIL>");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_duplicatePhone_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getPhone().get(0).setNumber("**********");
    request.getPhone().get(1).setNumber("**********");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }

  }

  @Test
  public void addCustomer_allEmailEnableNotificationTrue_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getEmail().get(0).setEnableNotification(true);
    request.getEmail().get(1).setEnableNotification(true);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_allEmailEnableNotificationFalse_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getPhone().get(0).setEnableNotification(true);
    request.getPhone().get(0).setType(PhoneNumberEnum.MOBILE);
    request.getPhone().get(1).setEnableNotification(true);
    request.getPhone().get(1).setType(PhoneNumberEnum.MOBILE);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_allPhoneEnableNotificationFalse_success() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.getPhone().get(0).setEnableNotification(false);
    request.getPhone().get(1).setEnableNotification(false);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @Test
  public void addCustomer_nullAddress_success() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    request.setAddress(null);
    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    responseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        CreateCustomerResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.CREATED);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      if (customersOptional.isPresent()) {
        dbCustomer = customersOptional.get();
        assertEquals(dbCustomer.getExternalRefId(), responseEntity.getBody().getCustomerId());
      }
    }
  }

  @Test
  public void addCustomer_nullLanguage_failed() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    //prepare the data
    List<Address> addressList = new ArrayList<>();
    Address address = new Address();
    address.setId("1222");
    address.setAddress1("6 Sheppard street");
    address.setRegion("Toronto");
    address.setSubdivision(SubdivisionEnum.ON);
    address.setPostalCode("C1C1C1");
    address.setCountry(CountryType.CAN);
    addressList.add(address);
    request.setAddress(addressList);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_nonEtransferProduct_failed() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);

    //change the DB to test
    List<Products> productsList = productsRepository.findAll();
    for (Products product : productsList) {
      if (product.getExternalRefId().equals("ETRANSFER")) {
        product.setExternalRefId("nonETransfer");
        productsRepository.save(product);
        break;
      }
    }

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_productDisabled_failed() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);

    //change the DB to test
    List<Products> productsList = productsRepository.findAll();
    Products eTransferProduct = productsList.get(0);
    eTransferProduct.setActive(false);
    productsRepository.save(eTransferProduct);

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }

    //rollback the DB change
    eTransferProduct.setActive(true);
    productsRepository.save(eTransferProduct);
  }

  @Test
  public void addCustomer_invalidPhoneNumberAreaCode_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    //give an invalid phone number
    request.getPhone().get(0).setNumber("**********");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_invalidPhoneNumber_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);
    //give an invalid phone number
    request.getPhone().get(0).setNumber("*********");

    requestCreate = new HttpEntity<>(request, headers);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    if (customersRepository != null) {
      Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
      //Make sure we have no DB records
      assertTrue(customersOptional.isEmpty());
    }
  }

  @Test
  public void addCustomer_invalidServiceAccountId_fail() throws Exception {
    String customerID = RandomStringUtils.randomAlphanumeric(12).toUpperCase();
    request = CustomerTestUtil.getCreateCustomerRequest(customerID, CustomerEnum.INDIVIDUAL);

    //create a fake service acct id
    headers = getHttpHeader();

    requestCreate = new HttpEntity<>(request, headers);

    ResponseException responseException = new ResponseException(HttpStatus.BAD_REQUEST.value(), "INVALID_SERVICE_ACCOUNT", null);
    doThrow(responseException).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));
    errResponseEntity = template.exchange(base.toString(), HttpMethod.POST, requestCreate,
        ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    Optional<Customers> customersOptional = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerID, customers.getServiceAccountRefId());
    //Make sure we have no DB records
    assertTrue(customersOptional.isEmpty());
  }

}