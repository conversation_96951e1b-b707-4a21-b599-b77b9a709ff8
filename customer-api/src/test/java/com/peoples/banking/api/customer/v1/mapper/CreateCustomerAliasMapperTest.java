package com.peoples.banking.api.customer.v1.mapper;

import static com.peoples.banking.api.customer.v1.CustomerAliasTestUtil.NETWORK_ENROLLMENT_ID;
import static com.peoples.banking.api.customer.v1.CustomerTestUtil.CUSTOMER_DISPLAY_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.peoples.banking.api.customer.v1.CustomerAliasTestUtil;
import com.peoples.banking.api.customer.v1.CustomerTestUtil;
import com.peoples.banking.api.customer.v1.dto.AliasDto;
import com.peoples.banking.api.customer.v1.dto.CustomerAliasDto;
import com.peoples.banking.api.customer.v1.type.AccountAliasRegNotificationPreference;
import com.peoples.banking.api.customer.v1.type.ServiceType;
import com.peoples.banking.domain.customer.model.RegisterAliasRequest;
import com.peoples.banking.domain.customer.model.RegisterAliasResponse;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration.LanguageEnum;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration.PermissionsEnum;
import com.peoples.banking.partner.domain.interac.registration.model.BankAccountIdentifier.TypeEnum;
import com.peoples.banking.partner.domain.interac.registration.model.CreateAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.CustomerAccount;
import com.peoples.banking.persistence.customer.entity.Customers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {CreateCustomerAliasMapperImpl.class})
public class CreateCustomerAliasMapperTest {
  @Autowired
  private CreateCustomerAliasMapper mapper;

  @Test
  public void createRegisterAliasResponse_Success() {
    RegisterAliasRequest request =
        CustomerTestUtil.getRegisterAliasRequest();
    CreateAccountAliasRegistrationResponse createAccountAliasRegistrationResponse = CustomerTestUtil
        .getCreateAccountAliasRegistrationResponse();
    Customers customers = CustomerTestUtil.createTestCustomer();
    CustomerAliasDto customerAliasDto = mapper
        .registerAliasRequestAndCustomersToCustomerAliasDto(request, customers, CustomerAliasTestUtil.EXTERNAL_REF_ID, 1,
            NETWORK_ENROLLMENT_ID, customers.getId(), customers.getServiceAccountRefId());
    customerAliasDto = mapper.createAccountAliasResponseToCustomerAliasDto(customerAliasDto, createAccountAliasRegistrationResponse);

    RegisterAliasResponse registerAliasResponse = mapper
        .customerAliasDtoToRegisterAliasResponse(customerAliasDto.getAliasDtoList().get(0));

    assertEquals(request.getAlias().getEmailAddress(), registerAliasResponse.getAlias().getEmailAddress());
    assertEquals(CustomerAliasTestUtil.EXTERNAL_REF_ID, registerAliasResponse.getAliasRefId());
    assertEquals(request.getAccountNumber(), registerAliasResponse.getAccountNumber());
    assertEquals(createAccountAliasRegistrationResponse.getAccountAliasRegistrationExpiryDate(), registerAliasResponse.getExpiryDate());
    assertNotNull(registerAliasResponse.getAccountOpenDate());

  }

  @Test
  public void createCustomerAliasDto_Success() {
    Customers customers = CustomerTestUtil.createTestCustomer();
    RegisterAliasRequest request =
        CustomerTestUtil.getRegisterAliasRequest();

    CustomerAliasDto customerAliasDto = mapper
        .registerAliasRequestAndCustomersToCustomerAliasDto(request, customers, CustomerAliasTestUtil.EXTERNAL_REF_ID, 1,
            NETWORK_ENROLLMENT_ID, customers.getId(), customers.getServiceAccountRefId());
    AliasDto aliasDto = customerAliasDto.getAliasDtoList().get(0);
    assertEquals(request.getAlias().getEmailAddress(), aliasDto.getContactEmailPhoneDto().getEmailAddress());
    assertEquals(request.getAlias().getMobileNumber(), aliasDto.getContactEmailPhoneDto().getMobileNumber());
    assertEquals(request.getAccountNumber(), aliasDto.getAccountNumber());
    assertEquals(request.getAccountOpenDate(), aliasDto.getAccountOpenDate());
    assertEquals(CUSTOMER_DISPLAY_NAME, aliasDto.getAccountHolderName());
    assertNotNull(aliasDto.getExternalId());
    assertEquals(request.getAccountOpenDate(), aliasDto.getAccountOpenDate());
    assertEquals(customers.getId(), aliasDto.getCustomerId());
    assertEquals(customers.getLanguageIso().name(), aliasDto.getLanguage());
    assertEquals(AccountAliasRegNotificationPreference.ACCOUNT_ALIAS_REGISTRATION_LEVEL,
        aliasDto.getAccountAliasNotificationPreference());

  }

  @Test
  public void createAccountAliasRegistration_Success() {
    Customers customers = CustomerTestUtil.createTestCustomer();
    RegisterAliasRequest request =
        CustomerTestUtil.getRegisterAliasRequest();

    CustomerAliasDto customerAliasDto = mapper
        .registerAliasRequestAndCustomersToCustomerAliasDto(request, customers, CustomerAliasTestUtil.EXTERNAL_REF_ID, 1,
            NETWORK_ENROLLMENT_ID, customers.getId(), customers.getServiceAccountRefId());

    AccountAliasRegistration accountAliasRegistration = mapper
        .customerAliasDtoToAccountAliasRegistration(customerAliasDto.getAliasDtoList().get(0));

    assertEquals(request.getAlias().getEmailAddress(), accountAliasRegistration.getAccountAliasHandle());
    assertNotNull(accountAliasRegistration.getParticipantAccountAliasReference());
    assertFalse(accountAliasRegistration.getParticipantAccountAliasReference().isEmpty());
    CustomerAccount customerAccount = accountAliasRegistration.getCustomerAccount();
    assertEquals(TypeEnum.CANADIAN, customerAccount.getBankAccountIdentifier().getType());
    assertEquals(request.getAccountNumber(), customerAccount.getBankAccountIdentifier().getAccount());
    assertEquals(CUSTOMER_DISPLAY_NAME, customerAccount.getAccountHolderName());

    assertEquals(request.getAccountOpenDate(), accountAliasRegistration.getAccountCreationDate());
    assertEquals(
        com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegNotificationPreference.ACCOUNT_ALIAS_REGISTRATION_LEVEL,
        accountAliasRegistration.getAccountAliasNotificationPreference());
    assertFalse(accountAliasRegistration.getSenderAccountIdentifierRequired());
    assertEquals(ServiceType.EMAIL.name(), accountAliasRegistration.getServiceType().name());
    assertEquals(PermissionsEnum.AUTO_DEPOSIT, accountAliasRegistration.getPermissions());
    assertEquals(LanguageEnum.EN, accountAliasRegistration.getLanguage());

    //checking for ServiceType - phone
    request.getAlias().setEmailAddress(null);
    request.getAlias().setMobileNumber(CustomerAliasTestUtil.MOBILE_NUMBER);
    customerAliasDto = mapper
        .registerAliasRequestAndCustomersToCustomerAliasDto(request, customers, CustomerAliasTestUtil.EXTERNAL_REF_ID, 1,
            NETWORK_ENROLLMENT_ID, customers.getId(), customers.getServiceAccountRefId());

    accountAliasRegistration = mapper.customerAliasDtoToAccountAliasRegistration(customerAliasDto.getAliasDtoList().get(0));
    assertEquals(ServiceType.PHONE.name(), accountAliasRegistration.getServiceType().name());
    assertEquals(CustomerAliasTestUtil.MOBILE_NUMBER, accountAliasRegistration.getAccountAliasHandle());

  }

}
