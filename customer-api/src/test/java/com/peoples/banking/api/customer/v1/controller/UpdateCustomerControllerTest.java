package com.peoples.banking.api.customer.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.customer.v1.CustomerTestUtil;
import com.peoples.banking.api.customer.v1.ProductTestUtil;
import com.peoples.banking.domain.customer.model.ErrorResponse;
import com.peoples.banking.domain.customer.model.UpdateCustomerRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.StatusEnum;
import com.peoples.banking.partner.adapter.interac.customer.CustomerAdapter;
import com.peoples.banking.partner.domain.interac.customer.model.Customer;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Enrollments;
import com.peoples.banking.persistence.customer.entity.Products;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.customer.repository.ProductsRepository;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import java.net.URL;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class UpdateCustomerControllerTest {

  @LocalServerPort
  private int port;

  private URL base;

  @Autowired
  private TestRestTemplate template;

  @MockBean
  private CustomerAdapter customerAdapter;

  @MockBean
  CustomersRepository customersRepository;

  @MockBean
  ProductsRepository productsRepository;

  @MockBean
  private ServiceAccountAdapter serviceAccountAdapter;

  private ResponseEntity<ErrorResponse> errResponseEntity;

  private List<Products> productList;

  private ServiceAccountResponse serviceAccountResponse;


  @BeforeEach
  public void setUp() throws Exception {
    this.base = new URL("http://localhost:" + port + "/v1/customer/");

    productList = new ArrayList<>();
    productList.add(ProductTestUtil.createTestProducts());
    serviceAccountResponse = CustomerTestUtil
        .createServiceAccountResponse(CustomerTestUtil.SERVICE_ACCOUNT_API_TOKEN, CustomerTestUtil.SERVICE_ACCOUNT_REF_ID, true);
  }

  @AfterEach
  public void tearDown() {
    template = null;
    customerAdapter = null;
    customersRepository = null;
    base = null;
    productList = null;
    serviceAccountResponse = null;
  }

  private HttpHeaders getHttpHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_SERVICE_ACCOUNT, CustomerTestUtil.SERVICE_ACCOUNT_API_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, "12345");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    return headers;
  }

  @Test
  public void updateCustomer_success() throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();

    doReturn(true).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
         isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "A1",
      "A",
      "ABC",
      "ABC123",
      "ABC-Hello",
      "ABC-Hello"
  })
  public void updateCustomer_region_type_success(String region) throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();

    doReturn(true).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
         isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());
    updateCustomerRequest.getAddress().get(0).setRegion(region);
    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "A1",
      "A",
      "ABC",
      "ABC 123",
      "ABC- Hello",
      "ABC-   Hello"
  })
  public void updateCustomer_addressline_success(String addressLine) throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();

    doReturn(true).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());
    updateCustomerRequest.getAddress().get(0).setAddress1(addressLine);
    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  @ParameterizedTest
  @ValueSource(strings = {
      " ABC",
      "   ABC",
      "ABC ",
      "ABC   ",
      "    ABC    "
  })
  public void updateCustomer_addressline_bad_fail(String addressLine) {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());
    updateCustomerRequest.getAddress().get(0).setAddress1(addressLine);
    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(CustomerTestUtil.INVALID_INPUT, errResponseEntity.getBody().getError().get(0).getCode());
  }


  @Test
  public void updateCustomer_serviceAccountSuspended_success() throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();

    doReturn(true).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        isA(String.class));

    serviceAccountResponse.setStatus(StatusEnum.SUSPENDED);
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);
  }

  /**
   * The test use bad customer data to call updateCustomer to trigger the validation exception
   */
  @Test
  public void updateCustomer_failed() throws Exception {

    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(true);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest_BadData(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NOT_FOUND);
  }

  /**
   * The test use inactive customer data to call updateCustomer to trigger the REQUEST_PROHIBITED exception
   */
  @Test
  public void updateCustomer_inactive_failed() throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(false);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);

    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, Void.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NOT_FOUND);
  }

  @Test
  public void updateCustomer_nonETransferProduct_failed() throws Exception {

    productList.get(0).setExternalRefId("nonETransfer");
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(false);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(productList).when(productsRepository).findAll();
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.NOT_FOUND);
    assertEquals(CustomerTestUtil.RESOURCE_NOT_FOUND, errResponseEntity.getBody().getError().get(0).getCode());

  }

  @Test
  public void updateCustomer_productDisabled_failed() throws Exception {
    productList.get(0).setActive(false);
    Customers customers = CustomerTestUtil.createTestCustomerNoEnrollment(false);
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(productList).when(productsRepository).findAll();
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(CustomerTestUtil.PRODUCT_DISABLED, errResponseEntity.getBody().getError().get(0).getCode());

  }

  @Test
  public void updateCustomer_inactiveEnrollment_failed() throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    for (Enrollments enrollments : customers.getEnrollments()) {
      enrollments.setActive(false);
    }

    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);
    assertEquals(CustomerTestUtil.PRODUCT_DISABLED, errResponseEntity.getBody().getError().get(0).getCode());

  }

  @Test
  public void updateCustomer_nonETransferEnrollment_failed() throws Exception {
    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    for (Enrollments enrollments : customers.getEnrollments()) {
      enrollments.getProducts().setExternalRefId("NonETRANSFER");
    }

    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(productList).when(productsRepository).findAll();
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity responseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT);

  }

  @Test
  public void updateCustomer_invalidPhoneNumberAreaCode_failed() throws Exception {

    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    doReturn(productList).when(productsRepository).findAll();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("379");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        nullable(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest_BadData(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity<ErrorResponse> errorResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errorResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(CustomerTestUtil.INVALID_PHONE_AREA_CODE, errorResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void updateCustomer_invalidPhoneNumberPhone_failed() throws Exception {

    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    doReturn(productList).when(productsRepository).findAll();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("380");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        nullable(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest_BadData(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    ResponseEntity<ErrorResponse> errorResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errorResponseEntity.getStatusCode() == HttpStatus.BAD_REQUEST);

    assertEquals(CustomerTestUtil.INVALID_PHONE_NUMBER, errorResponseEntity.getBody().getError().get(0).getCode());
  }

  @Test
  public void updateCustomer_generalResponseException_failed() throws Exception {

    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    doReturn(productList).when(productsRepository).findAll();

    ResponseException responseException = new ResponseException();
    responseException.setResponseCode("300");
    responseException.setHttpStatusCode(400);

    doThrow(responseException).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        nullable(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest_BadData(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    assertEquals(CustomerTestUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());

  }

  @Test
  public void updateCustomer_illegalArgumentException_failed() throws Exception {

    Customers customers = CustomerTestUtil.createTestCustomer();
    customers.setServiceAccountRefId(CustomerTestUtil.SERVICE_ACCOUNT_REF_ID);
    doReturn(java.util.Optional.of(customers)).when(customersRepository).findByCustomerIdAndServiceAccountRefId(isA(String.class),
        isA(String.class));
    doReturn(serviceAccountResponse).when(serviceAccountAdapter).retrieveServiceAccountByApiToken(isA(String.class));

    doReturn(productList).when(productsRepository).findAll();

    doThrow(IllegalArgumentException.class).when(customerAdapter).updateCustomer(isA(String.class), isA(Customer.class),
        nullable(String.class));

    UpdateCustomerRequest updateCustomerRequest = CustomerTestUtil.createTestUpdateCustomerRequest_BadData(customers.getTypeCd());

    HttpHeaders headers = getHttpHeader();

    String finalUrl = base.toString() + "/" + customers.getExternalRefId();

    HttpEntity<UpdateCustomerRequest> requestUpdate = new HttpEntity<>(updateCustomerRequest, headers);

    errResponseEntity = template.exchange(finalUrl, HttpMethod.PUT, requestUpdate, ErrorResponse.class);

    assert (errResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    assertEquals(CustomerTestUtil.UNEXPECTED_ERROR, errResponseEntity.getBody().getError().get(0).getCode());

  }
}
