package com.peoples.banking.api.customer.v1.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException;
import com.peoples.banking.api.customer.v1.config.CustomerConstant;
import com.peoples.banking.api.customer.v1.dto.CustomerAliasDto;
import com.peoples.banking.api.customer.v1.dto.CustomerDto;
import com.peoples.banking.api.customer.v1.dto.EnrollmentDto;
import com.peoples.banking.api.customer.v1.mapper.AliasEntityMapper;
import com.peoples.banking.api.customer.v1.mapper.CreateCustomerAliasMapper;
import com.peoples.banking.api.customer.v1.mapper.CreateCustomerMapper;
import com.peoples.banking.api.customer.v1.mapper.CustomerEntityMapper;
import com.peoples.banking.api.customer.v1.mapper.RetrieveCustomerAliasMapper;
import com.peoples.banking.api.customer.v1.mapper.RetrieveCustomerAliasesMapper;
import com.peoples.banking.api.customer.v1.mapper.RetrieveCustomerMapper;
import com.peoples.banking.api.customer.v1.mapper.RetrieveCustomerProductMapper;
import com.peoples.banking.api.customer.v1.mapper.UpdateCustomerMapper;
import com.peoples.banking.api.customer.v1.validator.constraint.BusinessDateOfBirth;
import com.peoples.banking.api.customer.v1.validator.constraint.CustomerNameTypeMatch;
import com.peoples.banking.api.customer.v1.validator.constraint.IndividualDateOfBirth;
import com.peoples.banking.api.customer.v1.validator.constraint.NotFutureDate;
import com.peoples.banking.api.customer.v1.validator.constraint.OneEnableNotification;
import com.peoples.banking.api.customer.v1.validator.constraint.OneOrLessEnableNotification;
import com.peoples.banking.api.customer.v1.validator.constraint.SMSPhoneTypeMatch;
import com.peoples.banking.api.customer.v1.validator.constraint.UniqueEmail;
import com.peoples.banking.api.customer.v1.validator.constraint.UniquePhone;
import com.peoples.banking.api.customer.v1.validator.constraint.UniqueProfileInformationId;
import com.peoples.banking.domain.customer.model.AliasStatus;
import com.peoples.banking.domain.customer.model.CreateCustomerRequest;
import com.peoples.banking.domain.customer.model.RegisterAliasRequest;
import com.peoples.banking.domain.customer.model.RegisterAliasResponse;
import com.peoples.banking.domain.customer.model.RetrieveAliasResponse;
import com.peoples.banking.domain.customer.model.RetrieveAliasesResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerAliasResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerProductResponse;
import com.peoples.banking.domain.customer.model.RetrieveCustomerResponse;
import com.peoples.banking.domain.customer.model.UpdateCustomerRequest;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.adapter.interac.customer.CustomerAdapter;
import com.peoples.banking.partner.adapter.interac.registration.RegistrationAdapter;
import com.peoples.banking.partner.domain.interac.customer.model.AddCustomerRequest;
import com.peoples.banking.partner.domain.interac.registration.model.AccountAliasRegistration;
import com.peoples.banking.partner.domain.interac.registration.model.CreateAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.GetAccountAliasRegistrationResponse;
import com.peoples.banking.partner.domain.interac.registration.model.ViewAccountAliasRegistration;
import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Enrollments;
import com.peoples.banking.persistence.customer.entity.Products;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.customer.repository.EnrollmentsRepository;
import com.peoples.banking.persistence.customer.repository.ProductsRepository;
import com.peoples.banking.util.api.common.IdGeneratorUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.config.NetworkErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.ServiceException;
import com.peoples.banking.util.api.common.exception.ValidationException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.service.InteracService;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * A service class for processing business logic validation, mapping and calling Interac service.
 */
@Log4j2
@Service
public class CustomerService extends InteracService {

  @Autowired
  private Validator validator;

  @Autowired
  private CustomerAdapter customerAdapter;

  @Autowired
  private CustomersRepository customersRepository;

  @Autowired
  private ProductsRepository productsRepository;

  @Autowired
  private EnrollmentsRepository enrollmentsRepository;

  @Autowired
  private CreateCustomerMapper createCustomermapper;

  @Autowired
  private CustomerEntityMapper customerEntityMapper;

  @Autowired
  private RetrieveCustomerMapper retrieveCustomerMapper;

  @Autowired
  private RetrieveCustomerProductMapper retrieveCustomerProductMapper;

  @Autowired
  private UpdateCustomerMapper updateCustomerMapper;

  @Autowired
  private CreateCustomerAliasMapper createCustomerAliasMapper;

  @Autowired
  private RetrieveCustomerAliasMapper retrieveCustomerAliasMapper;

  @Autowired
  private RetrieveCustomerAliasesMapper retrieveCustomerAliasesMapper;

  @Autowired
  private AliasEntityMapper aliasEntityMapper;

  @Autowired
  private AliasesRepository aliasesRepository;

  @Autowired
  private RegistrationAdapter registrationAdapter;


  /**
   * the create customer service to validate the request, convert objects, invoke external service and persist customer profile in DB
   *
   * @param request                the CreateCustomerRequest
   * @param serviceAccountResponse the service account information
   * @return String the customer id
   * @throws Exception the exception
   */
  @PerfLogger
  public String createCustomer(CreateCustomerRequest request, ServiceAccountResponse serviceAccountResponse) throws Exception {
    CustomerDto customerDto = null;

    try {
      // validate product
      validateProduct(APICommonUtilConstant.ETRANSFER);

      // validate customer account
      String customerId = null;
      boolean custExist = false;

      if (request.getId() == null || request.getId().isBlank()) {
        // PTC generate customer id for business partner
        customerId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(
            customersRepository.getNextCustomerSequence(),
            CustomerConstant.CUSTOMER_ID_LENGTH,
            CustomerConstant.CUSTOMER_ID_SEQUENCE_RADIX_MAX_LENGTH);
        log.debug("generated customerId={}", customerId);
      } else {
        // validate the customer has registered or not
        customerId = request.getId();
        custExist = customersRepository.checkCustomerIdExist(customerId, serviceAccountResponse.getRefId());
        if (custExist) {
          log.warn("customer={} exists", customerId);
          throw new ValidationException(ErrorProperty.RESOURCE_EXISTS.name());
        }
      }

      String enrollmentRefId;
      // generate enrollment reference id

      enrollmentRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(enrollmentsRepository.getNextEnrollmentSequence(),
          CustomerConstant.ENROLLMENT_ID_LENGTH, CustomerConstant.ENROLLMENT_ID_SEQUENCE_RADIX_MAX_LENGTH).toUpperCase();
      log.debug("generated enrollmentId={}", enrollmentRefId);

      // call partner adapter get the limits group Id
      String limitsGroupId = serviceAccountResponse.getLimitGroupId();
      log.debug("limitGroupId={} found for serviceAccountId={}", limitsGroupId, serviceAccountResponse.getRefId());

      // convert to Dto
      customerDto =
          createCustomermapper
              .createCustomerRequestToCustomerDto(request, serviceAccountResponse.getRefId(), customerId, enrollmentRefId, limitsGroupId);
      validateDto(customerDto);

      log.debug("building customer record for customerId=={}", customerId);
      // map dto to interac AddCustomerRequest
      AddCustomerRequest interacReq = createCustomermapper.customerDtoToAddCustomerRequest(customerDto);

      // invoke interac
      customerAdapter.addCustomer(enrollmentRefId, interacReq, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

      Customers customers = createNewCustomer(customerDto);

      // TODO: split the logic into 1. save customer for customer registration and 2. save
      // enrollments for customer product enrollment. Business check need to be updated as well.
      customersRepository.save(customers);
      log.debug("customer persisted");

    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException) {
        // Connection timeout or adapter error causes the application to proceed the next step
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

      } else if (e instanceof ResponseException) {
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        log.debug("network service error code={}", ((ResponseException) e).getResponseCode());
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("converts network service error code to {}", errorCode);
          throw new ValidationException(errorCode, e);
        } else {
          throw e;
        }
      } else {
        // TBDException, read timeout; external service has received the request and they may
        // have processed it but delay in response. In this case, we want to continue the process on
        // our end.
        log.warn("network service read timeout, continue to persist customer with externalRefId={}", customerDto.getExternalId());

        Customers customers = createNewCustomer(customerDto);

        customersRepository.save(customers);
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return customerDto.getExternalId();
  }


  /**
   * The get customer service to validate the request, retrieve customer data from DB and return the customer profile in response.
   *
   * @param customerId          the customer id
   * @param serviceAccountResponse the service account response
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  public RetrieveCustomerResponse retrieveCustomer(String customerId, ServiceAccountResponse serviceAccountResponse) throws Exception {
    RetrieveCustomerResponse retrieveCustomerResponse = null;

    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);

      Customers oneCustomer = null;
      if (customersOption.isPresent()) {

        log.debug("customerId={} with serviceAccountId={} located", customerId, serviceAccountRefId);

        oneCustomer = customersOption.get();
        String enrolmentId = findNetworkEnrollmentId(oneCustomer);

        if (enrolmentId == null) {
          log.warn("customerId={} with serviceAccountId={} not enrolled for eTransfer",
              oneCustomer.getExternalRefId(),
              oneCustomer.getServiceAccountRefId());
        }

        log.debug("retrieve customerId={}", customerId);
        // map customer to Dto
        CustomerDto customerDto = customerEntityMapper.customersToCustomerDto(oneCustomer);

        // Check if the service Account matched with DB records
        if (!serviceAccountRefId.equals(customerDto.getServiceAccount())) {
          log.warn("service account id={} does not match to the customer record", serviceAccountRefId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }

        // map dto to customer RetrieveCustomerResponse
        retrieveCustomerResponse = retrieveCustomerMapper.customerDtoToRetrieveCustomerResponse(customerDto);

        log.debug("customer retrieved, status={}", retrieveCustomerResponse.getActive());
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveCustomerResponse;
  }

  /**
   * The get customer service to validate the request, retrieve customer enrollment for one product data from DB and return the customer
   * profile in response.
   *
   * @param customerId          the customer id
   * @param productRefId        the customer id
   * @param serviceAccountRefId the service account id
   * @return RetrieveCustomerProductResponse the get customer product response
   */
  @PerfLogger
  public RetrieveCustomerProductResponse retrieveCustomerProduct(String customerId, String productRefId, String serviceAccountRefId) {
    // TODO because we don't let them deactivate hte product, lets just use product not enabled and rely
    // on interac to tell us that
    // once we implement ability to disable a product, then we can use our own status and use that
    // status to update interac
    RetrieveCustomerProductResponse retrieveCustomerProductResponse;

    try {
      // we will first check see if product is active or not, this way it will save DB performance
      validateProduct(productRefId);

      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);

      if (customersOption.isPresent()) {
        log.debug("customer={} with service account id {} found", customerId, serviceAccountRefId);

        Customers oneCustomer = null;
        oneCustomer = customersOption.get();

        // if customer active is false, "resource does not exist" is returned
        if (!oneCustomer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }

        Enrollments oneEnrollment = null;
        for (Enrollments enrollments : oneCustomer.getEnrollments()) {
          // need improve this part later deserialize string to object check if this is etransfer
          if (enrollments.getProducts().getExternalRefId().equals(productRefId)) {
            oneEnrollment = enrollments;
            break;
          }
        }

        // if enrollment active is false, "resource does not exist" is returned
        if (oneEnrollment == null || !oneEnrollment.isActive()) {
          log.warn("customer not enrolled for product [{}] or the enrollment is disabled", productRefId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }

        log.debug("Prepare to retrieve enrollment for customer={}", customerId);

        EnrollmentDto oneEnrollmentDto = customerEntityMapper.enrollmentsToEnrollmentDto(oneEnrollment);

        // map dto to customer retrieveCustomerProductResponse
        retrieveCustomerProductResponse = retrieveCustomerProductMapper.enrollmentDtoToRetrieveCustomerProductResponse(oneEnrollmentDto);

        log.debug("enrollment retrieved");
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveCustomerProductResponse;
  }

  /**
   * The update customer service to validate the request, convert objects, invoke external service and update customer profile in DB
   *
   * @param customerId             the customer id
   * @param serviceAccountResponse the ServiceAccountResponse
   * @param request                the update customer request
   * @return boolean the result of update status
   * @throws Exception the exception
   */
  @PerfLogger
  public boolean updateCustomer(String customerId, ServiceAccountResponse serviceAccountResponse, UpdateCustomerRequest request)
      throws Exception {
    boolean result = false;
    CustomerDto customerDto = null;
    Customers customer = null;

    try {
      // validate product is still valid
      validateProduct(APICommonUtilConstant.ETRANSFER);

      // validation the customer exist in DB
      Optional<Customers> customersOption = customersRepository
          .findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountResponse.getRefId());
      if (customersOption.isPresent()) {

        log.debug("customer={} with service account id={} found", customerId, serviceAccountResponse.getRefId());
        customer = customersOption.get();

        // if customer is inactive status, we will throw request prohibited exception
        if (!customer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }

        String enrollmentId = null;
        for (Enrollments enrollments : customer.getEnrollments()) {
          Products products = enrollments.getProducts();
          // need improve this part later deserialize string to object check if this is etransfer
          if (products.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
            if (enrollments.isActive()) {
              enrollmentId = enrollments.getNetworkEnrollmentId();
            } else {
              // ET-597 returned product disabled when enrollment status is false
              throw new ValidationException(ErrorProperty.PRODUCT_DISABLED.name());
            }

            break;
          }
        }

        // call partner adapter get the limits group Id
        String limitsGroupId = serviceAccountResponse.getLimitGroupId();
        log.debug("limitGroupId={} found for serviceAccountId={}", limitsGroupId, serviceAccountResponse.getRefId());

        // convert to Dto
        customerDto = updateCustomerMapper.updateCustomerRequestToCustomerDto(request, customer, limitsGroupId);

        // validate customer request data
        validateDto(customerDto);

        log.debug("Prepare to update the customer {}", customerId);
        // now we find the enrollment ID, we are ready to call interac
        if (enrollmentId != null) {
          log.debug("enrollment found, invoke network service to update the customer");
          com.peoples.banking.partner.domain.interac.customer.model.Customer interacReq =
              updateCustomerMapper.customerDtoToCustomer(customerDto);
          // if eTransfer product is not found, we update in our DB but don’t push to Interac
          customerAdapter.updateCustomer(enrollmentId, interacReq, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
        }

        customer = updateCustomer(customerDto, customer);

        customersRepository.save(customer);

        log.debug("customer updated");

        result = true;

      } else {
        // Customer did not exist
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountResponse.getRefId());
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException e) { // application validation exception
      throw e;
    } catch (ResourceNotFoundException e) { // application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (BaseException e) {// Exceptions when calling Adapter
      if (e instanceof TimeoutException || e instanceof AdapterException) {
        // Connection timeout or adapter error causes the application to proceed the next step
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

      } else if (e instanceof ResponseException) {
        // we want to catch certain interac error codes and wrap it into ValidationException. The list of
        // error codes are defined in NetworkErrorProperty
        log.debug("network service error code={}", ((ResponseException) e).getResponseCode());
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());

        // The Interac's error that PTC has to handle is found
        if (errorCode != null) {
          log.debug("converts network service error code to {}", errorCode);
          throw new ValidationException(errorCode);
        } else {
          throw e;
        }
      } else {
        // TBDException, read timeout; external service has received the request and they may
        // have processed it but delay in response. In this case, we want to continue the process on
        // our end.
        log.warn("network service read timeout, continue to persist customer: {}", customerDto.getExternalId());

        customer = updateCustomer(customerDto, customer);

        customersRepository.save(customer);

        result = true;
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return result;
  }

  /**
   * The update customer service to validate the request and change customer active status in DB
   *
   * @param customerId           the customer id
   * @param serviceAccountRefId  the service account id
   * @param alreadyProcessedFlag flag to indicate payment already processed
   * @return boolean the result of update status
   */
  @PerfLogger
  public boolean updateCustomerStatus(String customerId, String serviceAccountRefId, boolean newStatus,
      MutableBoolean alreadyProcessedFlag) {
    boolean result;
    Customers customer;

    try {

      // validation the customer exist in DB
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);
      if (customersOption.isPresent()) {

        log.debug("customer={} with service account id={} found", customerId, serviceAccountRefId);
        customer = customersOption.get();

        // if customer is in this status already, we will set alreadyProcessedFlag to true
        if (customer.isActive() == newStatus) {
          alreadyProcessedFlag.setTrue();
        }

        if (alreadyProcessedFlag.isFalse()) {
          log.debug("Prepare to update the customer {}", customerId);
          customer.setActive(newStatus);
          customersRepository.save(customer);
          log.debug("customer status changed to {}", newStatus);
        }

        result = true;
      } else {
        // Customer did not exist
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception or application resource not found exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return result;
  }


  /**
   * create new customer entity from CustomerDTo
   *
   * @param customerDto the customer DTo
   * @return Customers the customer entity
   */
  private Customers createNewCustomer(CustomerDto customerDto) {
    Products products = null;
    // product list is cached
    List<Products> productsList = productsRepository.findAll();
    for (Products product : productsList) {
      if (product.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
        products = product;
        break;
      }
    }

    // map dto to entity
    Customers customers = customerEntityMapper.customerDtoToCustomers(customerDto);

    // map Dto data to Enrollments collection
    Set<Enrollments> enrollmentsList =
        customerEntityMapper.enrollmentDtoListToEnrollmentsList(customerDto.getEnrollmentDtoList(), customers, products);

    customers.setEnrollments(enrollmentsList);

    return customers;

  }


  /**
   * The get customer service to validate the request, retrieve customer data from DB and return the customer profile in response.
   *
   * @param customerId          the customer id
   * @param serviceAccountResponse the service account response
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  public RegisterAliasResponse createCustomerAlias(String customerId, RegisterAliasRequest registerAliasRequest,
      ServiceAccountResponse serviceAccountResponse) throws Exception {
    RegisterAliasResponse registerAliasResponse;

    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);

      if (customersOption.isPresent()) {

        Customers oneCustomer = customersOption.get();

        if (!oneCustomer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }

        String externalRefId = IdGeneratorUtil.generateIdWithSeedAndRandomFixLength(
            aliasesRepository.getNextAliasSequence(),
            CustomerConstant.ALIAS_ID_LENGTH,
            CustomerConstant.ALIAS_REF_ID_SEQUENCE_RADIX_MAX_LENGTH);

        int enrollmentId = findEnrollmentId(oneCustomer);
        String networkEnrollmentId = findNetworkEnrollmentId(oneCustomer);

        CustomerAliasDto customerAliasDto = createCustomerAliasMapper
            .registerAliasRequestAndCustomersToCustomerAliasDto(registerAliasRequest, oneCustomer, externalRefId, enrollmentId,
                networkEnrollmentId, oneCustomer.getId(), oneCustomer.getServiceAccountRefId());

        // validate customer request data
        validateDto(customerAliasDto);

        AccountAliasRegistration accountAliasRegistration = createCustomerAliasMapper
            .customerAliasDtoToAccountAliasRegistration(customerAliasDto.getAliasDtoList().get(0));

        CreateAccountAliasRegistrationResponse createAccountAliasRegistrationResponse = registrationAdapter
            .registerAlias(customerAliasDto.getNetworkEnrollmentId(), accountAliasRegistration,
                buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
        customerAliasDto = createCustomerAliasMapper
            .createAccountAliasResponseToCustomerAliasDto(customerAliasDto, createAccountAliasRegistrationResponse);

        log.debug("customerId={} with serviceAccountId={} created", customerId, serviceAccountRefId);

        Aliases aliases = aliasEntityMapper.customerDtoToAliases(customerAliasDto);
        aliasesRepository.save(aliases);

        registerAliasResponse = createCustomerAliasMapper
            .customerAliasDtoToRegisterAliasResponse(customerAliasDto.getAliasDtoList().get(0));
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception
      throw e;
    } catch (BaseException e) {// Exceptions when calling RegistrationAdapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);
      } else { //ResponseException
        // Interac return error
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());
        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        if (errorCode != null) {
          throw new ValidationException(errorCode, e);
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return registerAliasResponse;
  }

  private void validateDto(Object objectToValidate) {
    Set<ConstraintViolation<Object>> constraintViolations = validator.validate(objectToValidate);

    if (!constraintViolations.isEmpty()) {
      log.debug("{} error(s) found while validating DTO", constraintViolations.size());
      // populate validation error detail
      List<ErrorEntity> errorList = convertViolationToValidationError(constraintViolations);
      throw new ValidationException(errorList);
    }
  }

  /**
   * The get customer service to validate the request, retrieve customer data from DB and return the customer profile in response.
   *
   * @param customerId          the customer id
   * @param serviceAccountResponse the service account response
   * @param offset              - customer aliases offset
   * @param maxResponseItems    - maximum items in response
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  @Transactional
  public RetrieveAliasesResponse retrieveCustomerAliases(String customerId, ServiceAccountResponse serviceAccountResponse, int offset, int maxResponseItems)
      throws Exception {
    RetrieveAliasesResponse retrieveAliasesResponse;

    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);

      if (customersOption.isPresent()) {
        log.debug("customerId={} with serviceAccountId={} located", customerId, serviceAccountRefId);
        Customers oneCustomer = customersOption.get();
        if (!oneCustomer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }
        String enrollmentId = findNetworkEnrollmentId(oneCustomer);
        List<Aliases> byCustomerIdAndEnrollmentId = aliasesRepository
            .findByCustomerIdAndEnrollmentId(oneCustomer.getId(), findEnrollmentId(oneCustomer));

        if (byCustomerIdAndEnrollmentId.isEmpty()) {
          log.warn("aliases not found in our db enrollment id={}  customer id {}", enrollmentId, customerId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }

        CustomerAliasDto customerAliasDto = retrieveCustomerAliasesMapper
            .customersAndAliasesToCustomerDto(enrollmentId, oneCustomer, byCustomerIdAndEnrollmentId);
        validateDto(customerAliasDto);

        GetAccountAliasRegistrationResponse getAccountAliasRegistrationResponse = registrationAdapter
            .retrieveRegisteredAlias(enrollmentId, offset, maxResponseItems, buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));

        if (getAccountAliasRegistrationResponse != null && getAccountAliasRegistrationResponse.getAccountAliasRegistrations() != null) {

          for (ViewAccountAliasRegistration viewAccountAliasRegistration : getAccountAliasRegistrationResponse
              .getAccountAliasRegistrations()) {
            Optional<Aliases> aliases = byCustomerIdAndEnrollmentId.stream()
                .filter(p -> p.getExternalRefId().equals(viewAccountAliasRegistration.getParticipantAccountAliasReference()))
                .findFirst();
            aliases.ifPresent(value -> updateCustomerInteracInfo(value, viewAccountAliasRegistration.getRegistrationStatus().getValue()));
          }
          customerAliasDto = retrieveCustomerAliasesMapper
              .getAccountAliasRegistrationResponseToCustomerAliasDto(customerAliasDto, getAccountAliasRegistrationResponse);
          retrieveAliasesResponse = retrieveCustomerAliasesMapper.customersDtoToRetrieveAliasesResponse(customerAliasDto);

        } else {
          log.warn("retrieveRegisteredAlias enrollment id={} not found", enrollmentId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception
      throw e;
    } catch (BaseException e) {// Exceptions when calling RegistrationAdapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);
      } else { //ResponseException
        // Interac return error
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());
        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        if (errorCode != null) {
          throw new ValidationException(errorCode, e);
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveAliasesResponse;
  }

  /**
   * The get customer service to validate the request, retrieve customer data from DB and return the customer profile in response.
   *
   * @param customerId          the customer id
   * @param serviceAccountResponse the service account reference id
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  @Transactional
  public RetrieveAliasResponse retrieveCustomerAlias(String customerId, String aliasId, ServiceAccountResponse serviceAccountResponse) throws Exception {
    RetrieveAliasResponse retrieveAliasResponse;

    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);
      if (customersOption.isPresent()) {
        Customers oneCustomer = customersOption.get();
        String enrollmentId = findNetworkEnrollmentId(oneCustomer);
        log.debug("customerId={} with serviceAccountId={} located", customerId, serviceAccountRefId);
        if (!oneCustomer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }
        Optional<Aliases> aliasesOptional = aliasesRepository.findByCustomerIdAndExternalRefId(oneCustomer.getId(), aliasId);
        if (aliasesOptional.isPresent()) {
          log.debug("alias with customerId located {}", oneCustomer.getId());
          Aliases aliases = aliasesOptional.get();
          CustomerAliasDto customerAliasDto = retrieveCustomerAliasMapper.aliasesToCustomerAliasDto(aliases, enrollmentId, oneCustomer);
          validateDto(customerAliasDto);
          ViewAccountAliasRegistration viewAccountAliasRegistration = registrationAdapter
              .retrieveRegisteredAlias(customerAliasDto.getNetworkEnrollmentId(), aliases.getNetworkAliasRefId(),
                  buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
          customerAliasDto = retrieveCustomerAliasMapper
              .viewAccountAliasRegistrationToCustomerAccountDto(customerAliasDto, viewAccountAliasRegistration);
          updateCustomerInteracInfo(aliases, customerAliasDto.getAliasDtoList().get(0).getStatus());
          if (viewAccountAliasRegistration != null && StringUtils.isNotBlank(viewAccountAliasRegistration.getAccountAliasReference())) {
            retrieveAliasResponse = retrieveCustomerAliasMapper.customerAliasDtoToRetrieveAliasResponse(customerAliasDto);
          } else {
            log.warn("retrieveAliasResponse is empty for enrollment id={}  and aliasId {}", enrollmentId, aliasId);
            throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
          }
        } else {
          log.warn("aliasId {} for enrollment id {} not found", aliasId, enrollmentId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }

      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception
      throw e;
    } catch (BaseException e) {// Exceptions when calling RegistrationAdapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);
      } else { //ResponseException
        // Interac return error
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());
        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        if (errorCode != null) {
          throw new ValidationException(errorCode, e);
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveAliasResponse;
  }

  /**
   * The get customer service to validate the request, retrieve customer data from DB and return the customer profile in response.
   *
   * @param aliasId        external ref alias id
   * @param aliasNetworkId network alias id
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  @Transactional
  public RetrieveCustomerAliasResponse retrieveCustomerAlias(String aliasId, String aliasNetworkId, String indirectConnectorId) {
    RetrieveCustomerAliasResponse retrieveCustomerAliasResponse;

    try {
      Optional<Aliases> byNetworkAliasId = aliasesRepository.findByNetworkAliasRefIdAndExternalRefId(aliasNetworkId, aliasId);
      if (byNetworkAliasId.isPresent()) {
        Aliases aliases = byNetworkAliasId.get();
        Optional<Customers> customersOptional = customersRepository.findById(aliases.getCustomerId());
        if (customersOptional.isPresent()) {
          Customers customers = customersOptional.get();
          if (!customers.isActive()) {
            throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
          }
          String enrollmentId = findNetworkEnrollmentId(customers);
          CustomerAliasDto customerAliasDto = retrieveCustomerAliasMapper.aliasesToCustomerAliasDto(aliases, enrollmentId, customers);

          validateDto(customerAliasDto);

          retrieveCustomerAliasResponse = retrieveCustomerAliasMapper.customerAliasDtoToRetrieveCustomerAliasResponse(customerAliasDto);
        } else {
          log.warn("customer={} with id not found", aliases.getCustomerId());
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }
      } else {
        log.warn("customer={} with aliasId and aliasNetwork id={} not found", aliasId, aliasNetworkId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception
      throw e;
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    return retrieveCustomerAliasResponse;
  }

  private void updateCustomerInteracInfo(Aliases aliases, String currentStatus) {
    if (aliases.isActive() && AliasStatus.NOT_ACTIVE.getValue().equals(currentStatus)) {
      disableCustomerAlias(aliases);
    }
  }

  /**
   * remove specified customer alias from interac, and on success - from our aliases table
   *
   * @param customerId          the customer id
   * @param serviceAccountResponse the service account response
   * @return RetrieveCustomerResponse the get customer response
   */
  @PerfLogger
  @Transactional
  public boolean removeCustomerAlias(String customerId, ServiceAccountResponse serviceAccountResponse, String aliasId) throws Exception {
    try {
      String serviceAccountRefId = serviceAccountResponse.getRefId();
      Optional<Customers> customersOption = customersRepository.findByCustomerIdAndServiceAccountRefId(customerId, serviceAccountRefId);

      if (customersOption.isPresent()) {
        Customers oneCustomer = customersOption.get();
        String enrollmentId = findNetworkEnrollmentId(oneCustomer);
        if (!oneCustomer.isActive()) {
          throw new ValidationException(ErrorProperty.CUSTOMER_DISABLED.name());
        }
        Optional<Aliases> aliases = aliasesRepository.findByCustomerIdAndExternalRefId(oneCustomer.getId(), aliasId);
        if (aliases.isPresent()) {
          Aliases alias = aliases.get();
          if (!alias.isActive()) {
            log.warn("alias is present in db, but not active id= {}", alias.getId());
            throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
          }
          String networkAliasRefId = alias.getNetworkAliasRefId();
          boolean result = registrationAdapter.removeRegisteredAlias(enrollmentId, networkAliasRefId,
              buildConnectorId(serviceAccountResponse.getConnectorType(), serviceAccountResponse.getIndirectConnectorId()));
          log.debug("Result of removing alias at interac {}", result);
          if (result) {
            log.debug("Removing alias from local repository for customerId {} and networkAliasId {}", oneCustomer.getId(),
                networkAliasRefId);
            disableCustomerAlias(alias);
          }
          return result;
        } else {
          log.warn("customer alias with customer id ={} with alias id={} not found", oneCustomer.getId(), aliasId);
          throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
        }
      } else {
        log.warn("customer={} with service account id={} not found", customerId, serviceAccountRefId);
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (ValidationException | ResourceNotFoundException e) { // application validation exception
      throw e;
    } catch (BaseException e) {// Exceptions when calling RegistrationAdapter
      if (e instanceof TimeoutException || e instanceof AdapterException || e instanceof TBDException) {
        // Connection timeout or adapter error causes the application to proceed the next step, wrap it to
        // ServiceException
        throw new ServiceException(e.getErrorCode().toString(), e, ErrorProperty.SERVICE_UNAVAILABLE.name(), null);
      } else { //ResponseException
        // Interac return error
        String errorCode = NetworkErrorProperty.getByErrorCode(((ResponseException) e).getResponseCode());
        log.debug("Network Service return error {}", ((ResponseException) e).getResponseCode());
        if (errorCode != null) {
          throw new ValidationException(errorCode, e);
        } else {
          log.debug("PTC does not convert the Network Service Error Code");
          throw e;
        }
      }
    } catch (JpaSystemException e) {// Database exception
      throw new ServiceException(e.getMessage(), e, ErrorProperty.SERVICE_UNAVAILABLE.name());

    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

  }

  private void disableCustomerAlias(Aliases alias) {
    alias.setActive(false);
    aliasesRepository.save(alias);
    log.debug("Disabled customer alias {}", alias.getCustomerId());
  }

  private int findEnrollmentId(Customers oneCustomer) {
    int result = 0;
    for (Enrollments enrollments : oneCustomer.getEnrollments()) {
      Products products = enrollments.getProducts();
      // need improve this part later deserialize string to object check if this is etransfer
      if (products.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
        result = enrollments.getId();
        break;
      }
    }
    return result;
  }

  private String findNetworkEnrollmentId(Customers oneCustomer) {
    String result = null;
    for (Enrollments enrollments : oneCustomer.getEnrollments()) {
      Products products = enrollments.getProducts();
      // need improve this part later deserialize string to object check if this is etransfer
      if (products.getExternalRefId().equals(APICommonUtilConstant.ETRANSFER)) {
        result = enrollments.getNetworkEnrollmentId();
        break;
      }
    }
    return result;
  }

  /**
   * updated the existing customer entity from customerDto
   *
   * @param customerDto the customer DTO
   * @param customers   the customer entity
   * @return Customers the udpated customer
   * @throws JsonProcessingException
   */
  private Customers updateCustomer(CustomerDto customerDto, Customers customers) throws JsonProcessingException {

    return customerEntityMapper.updateCustomersFromCustomerDtoAndCustomers(customerDto, customers);
  }

  /**
   * validate product is exist and is active
   *
   * @param productRefId product reference id
   */
  private void validateProduct(String productRefId) {
    Products products = null;
    // product list is cached
    List<Products> productsList = productsRepository.findAll();
    for (Products product : productsList) {
      if (product.getExternalRefId().equals(productRefId)) {
        products = product;
        break;
      }
    }

    if (products == null) {
      log.warn("product={} not found", productRefId);
      throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
    } else {
      // if product is disabled, return PRODUCT_DISABLED error
      if (!products.isActive()) {
        throw new ValidationException(ErrorProperty.PRODUCT_DISABLED.name());
      }
    }
  }

  /**
   * implement the customer validation error and assign corresponding error code
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    ErrorEntity error = new ErrorEntity();

    if (annotation instanceof NotNull) {
      error.setErrorCode(ErrorProperty.MISSING_FIELD.name());

    } else if (annotation instanceof NotBlank) {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());

    } else if (annotation instanceof CustomerNameTypeMatch) {
      error.setErrorCode(ErrorProperty.MISMATCH_CUSTOMER_TYPE.name());

    } else if (annotation instanceof OneEnableNotification) {
      error.setErrorCode(ErrorProperty.INVALID_NOTIFICATION_SELECTION.name());

    } else if (annotation instanceof OneOrLessEnableNotification) {
      error.setErrorCode(ErrorProperty.INVALID_NOTIFICATION_SELECTION.name());

    } else if (annotation instanceof SMSPhoneTypeMatch) {
      error.setErrorCode(ErrorProperty.INVALID_PHONE_TYPE.name());

    } else if (annotation instanceof UniqueProfileInformationId) {
      error.setErrorCode(ErrorProperty.DUPLICATE_ID.name());

    } else if (annotation instanceof UniqueEmail) {
      error.setErrorCode(ErrorProperty.DUPLICATE_EMAIL.name());

    } else if (annotation instanceof UniquePhone) {
      error.setErrorCode(ErrorProperty.DUPLICATE_PHONE.name());

    } else if (annotation instanceof IndividualDateOfBirth) {
      error.setErrorCode(ErrorProperty.INVALID_DATE_OF_BIRTH.name());

    } else if (annotation instanceof BusinessDateOfBirth) {
      error.setErrorCode(ErrorProperty.NOT_APPLICABLE.name());

    } else if (annotation instanceof NotFutureDate) {
      error.setErrorCode(ErrorProperty.INVALID_DATE.name());

    } else {
      error.setErrorCode(ErrorProperty.INVALID_INPUT.name());
    }
    return error;
  }

}
