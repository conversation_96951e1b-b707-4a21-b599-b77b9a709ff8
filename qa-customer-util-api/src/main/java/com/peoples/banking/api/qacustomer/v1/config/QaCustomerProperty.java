package com.peoples.banking.api.qacustomer.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class QaCustomerProperty {

  /**
   * QA::Customer API System Name.
   */
  @Value("${qa.customer.api.system.name}")
  private String systemName;

  /**
   * QA::Customer API time to live value.
   */
  @Value("${qa.customer.api.timetolive:30000}")
  private int timeToLive;

  /**
   * QA::Customer expiry days
   */
  @Value("${qa.customer.api.request.expiry.days:45}")
  private int expiryDays;

  /**
   * QA::Customer maximum limit
   */
  @Value("${qa.customer.api.payment.max.limit:10000}")
  private int paymentMaxLimit;

}
