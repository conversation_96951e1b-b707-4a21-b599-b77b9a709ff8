package com.peoples.banking.api.qacustomer.v1.config;

import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.util.api.common.config.APICommonUtilProperty;
import com.peoples.banking.util.api.common.filter.LoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextListener;

/**
 * Request Payment API Filter Configuration
 * 
 *
 */
@Configuration
public class QaCustomerFilterConfig {

  @Autowired(required = false)
  private ServiceAccountAdapter serviceAccountAdapter;

  @Autowired
  private APICommonUtilProperty apiCommonUtilProperty;
  
  @Autowired
  private QaCustomerProperty qaCustomerProperty;
  
  /**
   * add logging filter
   * 
   * @return FilterRegistrationBean
   */
  @Bean
  public FilterRegistrationBean<LoggingFilter> qaCustomerAPILoggingFilter() {
    LoggingFilter qaCustomerAPILoggingFilter = new LoggingFilter();
    qaCustomerAPILoggingFilter.setApiCommonUtilProperty(apiCommonUtilProperty);
    qaCustomerAPILoggingFilter.setServiceAccountAdapter(serviceAccountAdapter);
    qaCustomerAPILoggingFilter.setSystemName(qaCustomerProperty.getSystemName());
    FilterRegistrationBean<LoggingFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(qaCustomerAPILoggingFilter);
    registrationBean.addUrlPatterns("/*");

    return registrationBean;
  }


  /**
   * create request context listener. It is required for Request Context Holder
   * 
   * @return RequestContextListener
   */
  @Bean
  public RequestContextListener requestContextListener() {
    return new RequestContextListener();
  }


}
