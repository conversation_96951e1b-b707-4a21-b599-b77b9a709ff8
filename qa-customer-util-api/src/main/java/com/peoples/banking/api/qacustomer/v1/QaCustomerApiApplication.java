package com.peoples.banking.api.qacustomer.v1;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.peoples.banking"})
@EntityScan("com.peoples.banking.persistence.customer")
public class QaCustomerApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(QaCustomerApiApplication.class, args);
	}

}
