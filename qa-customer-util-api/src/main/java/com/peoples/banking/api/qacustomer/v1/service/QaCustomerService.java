package com.peoples.banking.api.qacustomer.v1.service;


import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.entity.Enrollments;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.util.api.common.config.ErrorProperty;
import com.peoples.banking.util.api.common.exception.ApplicationException;
import com.peoples.banking.util.api.common.exception.ResourceNotFoundException;
import com.peoples.banking.util.api.common.exception.domain.ErrorEntity;
import com.peoples.banking.util.api.common.service.APIService;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.util.List;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * The qa customer service class for retrieve payment from DB for QA quick and easy test
 */
@Service
@Log4j2
public class QaCustomerService extends APIService {

  @Autowired
  private CustomersRepository customerRepository;

  @Autowired
  private AliasesRepository aliasesRepository;


  /**
   * Retrieve all registered customer aliases, including our alias ID as well as the network alias ID
   *
   * @param customerId the customer identifier
   * @return
   */
  @PerfLogger
  public List<Aliases> retrieveCustomerAliases(String customerId) {
    Customers customer;
    try {
      Optional<Customers> customersOption = customerRepository.findByExternalRefId(customerId);

      if (customersOption.isPresent()) {
        customer = customersOption.get();
      } else {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    List<Aliases> aliases = null;
    try {
      aliases = aliasesRepository.findByCustomerId(customer.getId());
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    if (aliases == null || aliases.isEmpty()) {
      throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
    }

    return aliases;
  }

  /**
   * Retrieve customer enrolments details
   *
   * @param customerId the customer identifier
   * @return
   */
  @PerfLogger
  public String retrieveCustomerNetworkEnrolmentId(String customerId) {

    String networkEnrolmentId = null;
    Customers customer;

    try {
      Optional<Customers> customersOption = customerRepository.findByExternalRefId(customerId);

      if (customersOption.isPresent()) {
        customer = customersOption.get();
      } else {
        throw new ResourceNotFoundException(ErrorProperty.RESOURCE_NOT_FOUND.name());
      }
    } catch (Exception e) { // general exception
      throw new ApplicationException(e.getMessage(), e, ErrorProperty.UNEXPECTED_ERROR.name());
    }

    for (Enrollments enrollments : customer.getEnrollments()) {
      if (enrollments.getProducts().getExternalRefId().equalsIgnoreCase("ETRANSFER")) {
        networkEnrolmentId = enrollments.getNetworkEnrollmentId();
      }
    }

    return networkEnrolmentId;
  }

  /**
   * implementation of constructing the request payment's validation error
   */
  @Override
  protected ErrorEntity constructValidationError(Object annotation) {
    return new ErrorEntity();
  }


}
