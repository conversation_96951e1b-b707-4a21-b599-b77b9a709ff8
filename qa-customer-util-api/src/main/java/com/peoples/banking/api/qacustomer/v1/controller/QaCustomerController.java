package com.peoples.banking.api.qacustomer.v1.controller;

import com.peoples.banking.api.qacustomer.v1.config.QaCustomerConstant;
import com.peoples.banking.api.qacustomer.v1.service.QaCustomerService;
import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.time.Instant;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * The Rest controller for QA Customer retrieve.
 */

@RestController
public class QaCustomerController {

  private static final Logger perf = LogManager.getLogger(APICommonUtilConstant.LOGGER_NAME_PERFORMANCE);

  private static final String OPERATION_GET_ALIAS_BY_CUSTOMER_REF_ID = "retrieveAliasByCustomerId()";
  private static final String OPERATION_GET_ENROLLMENT_BY_CUSTOMER_REF_ID = "retrieveCustomerEnrolmentByCustomerId()";

  @Autowired
  private QaCustomerService service;

  /**
   * get alias by customer ID
   *
   * @param interactionId the unique id per request
   * @param customerId    the ptc request Payment ref id
   * @return Payments in DB
   */
  @PerfLogger
  @GetMapping(value = QaCustomerConstant.OPERATION_GET_ALIAS_BY_CUSTOMER_REF_ID, produces = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<List<Aliases>> retrieveAliasByCustomerId(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId, @PathVariable String customerId) {

    Instant startTime = Instant.now();

    List<Aliases> aliases = service.retrieveCustomerAliases(customerId);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId);

    Instant endTime = Instant.now();

    // log performance
    perf.info("{} - start [{}], end [{}], total [{}]ms", OPERATION_GET_ALIAS_BY_CUSTOMER_REF_ID,
        startTime, endTime, endTime.toEpochMilli() - startTime.toEpochMilli());

    return new ResponseEntity<>(aliases, headers, HttpStatus.OK);
  }

  /**
   * get customer enrollment details
   */
  @PerfLogger
  @GetMapping(value = QaCustomerConstant.OPERATION_GET_ENROLMENT_BY_CUSTOMER_REF_ID, produces = {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<String> retrieveCustomerEnrolmentByCustomerId(
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId, @PathVariable String customerId) {

    Instant startTime = Instant.now();

    String enrolmentId = service.retrieveCustomerNetworkEnrolmentId(customerId);

    // create response headers after the successful service call
    HttpHeaders headers = createResponseHttpHeaders(interactionId);

    Instant endTime = Instant.now();

    // log performance
    perf.info("{} - start [{}], end [{}], total [{}]ms", OPERATION_GET_ENROLLMENT_BY_CUSTOMER_REF_ID,
        startTime, endTime, endTime.toEpochMilli() - startTime.toEpochMilli());

    return new ResponseEntity<>(enrolmentId, headers, HttpStatus.OK);
  }

  /**
   * construct http headers to include interaction id sent in the request and application generated correspondent id
   *
   * @param interactionId - interaction id to use in headers
   * @return HttpHeaders - constructed http headers object
   */
  protected HttpHeaders createResponseHttpHeaders(String interactionId) {
    HttpHeaders headers = new HttpHeaders();

    // correspondentId is generated in the logger filter and stored in request context holder
    String correspondentId = (String) RequestContextHolder.getRequestAttributes().getAttribute(
        APICommonUtilConstant.LOGGING_FIELD_GUIID, RequestAttributes.SCOPE_REQUEST);

    headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.set(APICommonUtilConstant.HEADER_CORRESPONDENT_ID, correspondentId);

    return headers;
  }
}
