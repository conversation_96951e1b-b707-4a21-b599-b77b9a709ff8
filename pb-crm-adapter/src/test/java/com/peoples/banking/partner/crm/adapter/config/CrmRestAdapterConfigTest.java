package com.peoples.banking.partner.crm.adapter.config;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.partner.crm.adapter.CrmRestAdapter;
import com.peoples.banking.partner.crm.adapter.util.CrmTestUtil;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

@ContextConfiguration(classes = {CrmAdapterProperty.class}, loader=AnnotationConfigContextLoader.class)
@SpringBootTest(classes = CrmRestAdapter.class)
public class CrmRestAdapterConfigTest {

  @SpyBean
  private ObjectMapper objectmapper;

  private CreateSafCaseRequest request;

  private static final String CREATE_FAS_CASE_REQUEST_EXAMPLE = "{\"data\":{\"fi_fraud_status\":\"Confirmed_Legit_By_FI\",\"payment_status\":\"ACCEPTED\",\"sender_acct_num\":\"67890\",\"sender_phone_num\":\"*********\",\"sender_name\":\"Tester user\",\"sender_email\":\"<EMAIL>\",\"sender_ip\":\"***********\",\"sender_fin_inst_id\":\"fin_inst_id\",\"recipient_acct_num\":\"67890\",\"recipient_phone_num\":\"*********\",\"recipient_name\":\"Tester user\",\"recipient_email\":\"<EMAIL>\",\"recipient_ip\":\"***********\",\"recipient_fin_inst_id\":\"fin_inst_id\",\"amount\":\"500\",\"date_initiated\":\"2021-09-14\",\"date_expires\":\"2021-09-24\",\"date_deposited\":\"2021-09-15\",\"ptc_customer_id\":\"1111\",\"previous_fraud_status\":\"SUSPICIOUS\",\"ptc_payment_id\":\"111111\",\"interac_payment_id\":\"1sfsaaa\",\"ptc_associated_id\":\"asdfasdf\",\"interac_associated_id\":\"\",\"service_acct_ref_id\":\"service_account_ref_id\",\"customer_type\":\"INDIVIDUAL\",\"payment_type\":\"ALIAS_AUTODEPOSIT\",\"fraud_type\":\"FP_FRAUD\",\"notes\":null,\"crm_unique_id\":\"*********\",\"req_money_contact_name\":null,\"req_money_contact_email\":null,\"req_money_contact_phone\":null}}";
  private static final String CREATE_FAS_CASE_REQUEST_EXAMPLE2 = "{\"data\":{\"fi_fraud_status\":\"Confirmed_Legit_By_FI\",\"payment_status\":\"ACCEPTED\",\"sender_email\":\"<EMAIL>\",\"sender_ip\":\"***********\",\"amount\":\"500\",\"date_initiated\":\"2021-09-14\",\"date_expires\":\"2021-09-14\",\"date_deposited\":\"2021-09-14\",\"ptc_customer_id\":\"1111\",\"ptc_customer_id\":\"2222\",\"ptc_payment_id\":\"111111\",\"interac_payment_id\":\"\",\"ptc_associated_id\":\"\",\"interac_associated_id\":\"\",\"crm_unique_id\":\"*********\"}}";
  
  @BeforeEach
  private void setup() {
    request = CrmTestUtil.buildCreateSafCaseRequest();
  }


  @Test
  public void createFasCaseConversion_success() {

    String requestAsString = assertDoesNotThrow(() -> objectmapper.writeValueAsString(request));
    assertEquals(CREATE_FAS_CASE_REQUEST_EXAMPLE, requestAsString);

  }

  @Test
  public void createFasCase_fail() {
    assertNotNull(request.getData());
    //check that by setting null for any field will break request as json with empty string
    request.getData().setRecipientAcctNum(null);
    String requestAsString = assertDoesNotThrow(() -> objectmapper.writeValueAsString(request));
    assertNotEquals(CREATE_FAS_CASE_REQUEST_EXAMPLE, requestAsString);
  }

  @Test
  public void createFasCaseBackwardConversion_success() {
    CreateSafCaseRequest requestFromString = assertDoesNotThrow(() -> objectmapper.readValue(CREATE_FAS_CASE_REQUEST_EXAMPLE, CreateSafCaseRequest.class));
    assertEquals(request, requestFromString);
  }

  @Test
  public void createFasCaseBackwardConversion_fail() {

    CreateSafCaseRequest requestFromString = assertDoesNotThrow(() -> objectmapper.readValue(CREATE_FAS_CASE_REQUEST_EXAMPLE2, CreateSafCaseRequest.class));
    //check that by setting null for any field will break request as json with empty string
    assertNotEquals(request, requestFromString);
  }


}
