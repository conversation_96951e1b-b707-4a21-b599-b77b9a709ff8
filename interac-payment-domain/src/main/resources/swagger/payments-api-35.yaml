---
# Generated-By: OA3 <PERSON>er <PERSON>l
# Input-File: generated/payments-api-35-tmp.yaml
# Options: --encoding=utf-8; --overwrite
# -----
# Generated-By: OA3 Linker Tool
# Input-File: /dev/stdin
# Options: --encoding=utf-8; --oa3fix; --overwrite; --prune; --stdin
# -----
# Generated-By: OA3 Schema Definition Merge Tool
# Base-File: send-receive-payments-api-35.yaml
# Merged-Files: payment-details-and-history-api-35.yaml, cancel-api-35.yaml, remittance-api-35.yaml, refund-fulfilment-api-35.yaml
# Options: <none>
# -----
openapi: 3.0.0
info:
  description: Payment Processing APIs - ISO20022
  version: "3.5.0"
  title: Payment Processing APIs include send, receive, details, reclaims and remitance
    informations.
  contact:
    name: eTransfer Support
    url: https://www.interac.ca/en/contact-us-2.html
    email: <EMAIL>
  termsOfService: https://www.interac.ca/en/terms-and-conditions.html
  x-last-updated-date: "16-Mar-2020"

tags:
- name: send payment
  description: send payment services
- name: receive payment
  description: receive payment services
- name: payment details
  description: payment details and history services
- name: payment history
  description: payment history services
- name: cancel
  description: REST-based operations to cancel in-flight transfers
  externalDocs:
    url: https://tempuri.org/
- name: remittance
  description: remittance data services
- name: refund
  description: The domain of operations to request the return of previously completed
    transfers to the sender
  externalDocs:
    url: https://tempuri.org/
paths:
 ### START OF ISO Send Payment  ###
  /payments:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - send payment
      description: >-
        This service can be used by a Participant to send a new Interac e-Transfer
        to an existing Recipient using an ISO 20022 pacs.008 message format.
        This service works as a single step operation (as opposed to the initiate/submit
        services which require 2 steps).
      summary: Send e-Transfer payment (ISO 20022 Message format).
      operationId: sendIsoPayment
      parameters:
      - $ref: "#/components/parameters/RetryIndicator"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendPaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: Payment sent successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendPaymentResponse'
        '200':
          description: Payment deposited successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendPaymentResponse'
    get:
      tags:
      - send payment
      summary: Get all sent payments
      description: >-
        This service can be used by Participants to obtain from Interac the list of
        all received transfers for one of their Customers.
        The service can be used in several ways depending on which request filter
        parameters are specified.
        The returned list contains transfers sorted by received date in descending
        order.
      operationId: getOutgoingTransfers
      parameters:
      - in: query
        name: from_date
        description: Start date/time in UTC. Required only if participant_reference
          and payment_schedule_reference are absent.
        schema:
          $ref: "#/components/schemas/CustomDateTime"
      - in: query
        name: to_date
        description: End date/time in UTC, Present only if participant_reference and
          payment_schedule_reference are absent.
        schema:
          $ref: "#/components/schemas/CustomDateTime"
      - in: query
        name: participant_reference
        description: payment reference number at Participant FI.
        schema:
          $ref: "#/components/schemas/TransactionId"
      - in: query
        name: payment_schedule_reference
        description: Schedule reference number associated with the payment, if the
          payment was created as a result of a payment schedule
        schema:
          $ref: "#/components/schemas/ScheduleReference"
      - in: query
        name: exclude_bulk_initiated_payments
        description: >-
          Indicator used to exclude bulk initated transfers. <br/>
          false - No <br/>
          true - Yes <br/>
          By default it would be false, bulk initiated transfers are included in the
          response.
        schema:
          type: boolean
      - in: query
        name: payment_state
        description: >-
          Transfers to be returned in the response based on the transfer state <br/>
          ALL - All transfers included (default) <br/>
          PENDING - Outstanding (transfers not cancelled or completed) <br/>
          FINALIZED - Finalized ( transfers cancelled or completed)
        schema:
          $ref: '#/components/schemas/PaymentState'
      - in: query
        name: offset
        description: offset is starting point of payments request filter; if offset
          is not provided it would be defaulted to zero;
        required: false
        schema:
          type: integer
      - in: query
        name: max_response_items
        description: Maximum number of response items to be returned. All items are
          returned if this field is absent.
        required: false
        schema:
          type: integer
          maximum: 999
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Outgoing payments retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-page-size-limit:
              $ref: "#/components/headers/x-et-page-size-limit"
            x-et-page-total-records:
              $ref: "#/components/headers/x-et-page-total-records"
            x-et-page-next-offset:
              $ref: "#/components/headers/x-et-page-next-offset"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OutgoingPayment'
  /payments/details:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - payment details
      summary: Retrieve payment details (ISO 20022 Message format).
      description: >-
        This service can be used by a Participant to request specific payment details.
      operationId: getPaymentStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentStatusRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Payment successfully retrieved.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentStatusResponse'
 ### START OF NON-ISO Send Payment  ###
  /payments/interrupted:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - payment details
      summary: Get Interrupted Transfers
      description: >-
        To obtain a list of all the transfers submitted using the two-way commit option
        (begin, commit, rollback) for a specific Customer,
        the Participant FI can use the GET INTERRUPTED TRANSFERS message. Further,
        if more details (such as status) are needed for a particular interrupted transfer,
        the Participant can use the GET FINANCIAL TRANSACTION message.
      operationId: getInterruptedTransfer
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Interrupted Transfer
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InterruptedPayment'
  /payments/options:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - send payment
      summary: Payment options
      description: >-
        This service can be used by a Participant to look up a registered service
        and to inquire about the transfer options available for a specific Contact/Recipient
        belonging to a Customer.
        It is normally invoked before creating and submitting a transfer to that Contact/Recipient.
      operationId: getPaymentOptions
      parameters:
      - $ref: "#/components/parameters/ProductCode"
      - $ref: "#/components/parameters/AccountAliasType"
      - $ref: "#/components/parameters/AccountAliasHandle"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Payment options
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentOptionResponse'
 ### START OF NON-ISO Receive Payment  ###
  /payments/receive:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - receive payment
      summary: Get Received Payments
      description: -> This service can be used by Participants to obtain from Interac
        the list of all received transfers for one of their Customers. The service
        can be used in several ways depending on which request filter parameters are
        specified. The returned list contains transfers sorted by received date in
        descending order.
      operationId: getReceivedTransfers
      parameters:
      - in: query
        name: from_date
        description: Start date/time in UTC.
        schema:
          $ref: "#/components/schemas/CustomDateTime"
      - in: query
        name: to_date
        description: End date/time in UTC..
        schema:
          $ref: "#/components/schemas/CustomDateTime"
      - in: query
        name: offset
        description: offset is starting point of received payment filter; if offset
          is not provided it would be defaulted to zero;
        required: false
        schema:
          type: integer
      - in: query
        name: max_response_items
        description: Maximum number of response items to be returned. All items are
          returned if this field is absent.
        required: false
        schema:
          type: integer
          maximum: 999
      - in: query
        name: account_number
        description: Bank account number. Canadian bank account format is aaa-bbbbb-cccccccccccccccccccc
          where 'aaa' is the Financial Institution Identifier 'bbbbb' is the Transit
          Number 'cccccccc...' is the Account Number.
        required: false
        schema:
          $ref: "#/components/schemas/AccountNumber"
      - in: query
        name: id
        description: Payment reference number at Interac.
        schema:
          $ref: "#/components/schemas/ClearingSystemReference"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Received Payments Retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-page-size-limit:
              $ref: "#/components/headers/x-et-page-size-limit"
            x-et-page-total-records:
              $ref: "#/components/headers/x-et-page-total-records"
            x-et-page-next-offset:
              $ref: "#/components/headers/x-et-page-next-offset"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReceivedPayment'
  /payments/transaction:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - send payment
      summary: Initiate Payment (2-phase send e-Transfer payment in ISO 20022 Message
        format).
      description: >-
        This service is used by a Participant to set up or to initiate a new
        e-Transfer. This service does not send the actual transfer. It is only
        the first step in a 2-step process, and it must be followed either by a
        Submit Payment call (to actually send the payment) or by a Reverse
        Payment call (to stop the payment).
      operationId: initiatePayment
      parameters:
      - $ref: "#/components/parameters/RetryIndicator"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiatePaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: Payment transaction created successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitiatePaymentResponse'
    put:
      tags:
      - send payment
      summary: Submit Payment (2-phase send e-Transfer payment in ISO 20022 Message
        format).
      description: >-
        This service is used by a Participant to submit a payment that was
        initiated (set up) using an Initiate Payment service.
      operationId: submitPayment
      parameters:
      - $ref: "#/components/parameters/RetryIndicator"
      - $ref: "#/components/parameters/PaymentTransactionToken"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitPaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: Payment sent successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitPaymentResponse'
        '200':
          description: Payment deposited successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitPaymentResponse'
    delete:
      tags:
      - send payment
      summary: Stop Payment ( 2-phase send e-Transfer payment in ISO 20022 Message
        format).
      description: >-
        This service is used to stop a payment that was initiated (set up) using
        an Initiate Payment service.
      operationId: reversePayment
      parameters:
      - $ref: "#/components/parameters/ParticipantTransactionId"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Payment reversed successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReversePaymentResponse'
    get:
      tags:
      - payment details
      summary: Get Payment Transaction Details
      description: This service is used by the Participant to obtain from Interac
        the status of a particular financial transaction. The financial transaction
        can be identifies by one and only one of the following, participantTransactionId,
        or by participantReferenceNumber or by beginTransactionId.
      operationId: getFinancialTransaction
      parameters:
      - $ref: "#/components/parameters/PaymentTransactionToken"
      - $ref: "#/components/parameters/ParticipantReference"
      - $ref: "#/components/parameters/ParticipantTransactionId"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: payment transaction details
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransactionDetails'
  /payments/{id}:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - send payment
      summary: Get sent payment by Interac Reference Number
      description: >-
        This service can be used by Participants to obtain from Interac a specific
        transfer for one of their Customers.
      operationId: getOutgoingTransferById
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Outgoing payments retrieved
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OutgoingPayment'
  /payments/{id}/authenticate:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    patch:
      tags:
      - receive payment
      summary: Authenticate payment
      description: This service can be used by a Participant to submit to Interac
        the authentication information provided by their Recipient Customer for one
        of their incoming Transfers. It is essentially used to authenticate the Recipient.
        This operation is required only if the authenticationRequired flag for the
        incoming Transfer was set to 1 (i.e. required).
      operationId: authenticatePayment
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticatePayment'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Payment Authenticated
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/cancel:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - cancel
      summary: Stop an in-flight transfer.
      description: |
        The sender can use this function to stop an in-flight transfer (i.e. a transfer not yet completed) and ensure
        that the funds are transferred back into the sender's account.
      operationId: transferCancel
      parameters:
      - $ref: "#/components/parameters/PaymentReference"
      - $ref: "#/components/parameters/RetryIndicator"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentsCancelPostRequestModel'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          $ref: "#/components/responses/204-Transfer-Cancelled"
  /payments/{id}/cancel/transaction:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - cancel
      summary: Begin two-phase cancellation of an in-flight transfer.
      description: |
        This function is used by the sender to initiate the cancellation of a specific outgoing transfer.
      operationId: transferCancelBegin
      parameters:
      - $ref: "#/components/parameters/PaymentReference"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentsCancelTransactionPostRequestModel'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: The transfer was successfully removed from the in-flight queue.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CancelPaymentTransaction"
    put:
      tags:
      - cancel
      summary: Commit the pending two-phase cancellation.
      description: |
        This function is used by the sender to indicate that the outgoing transfer can be removed safely from the
        in-flight transfer queue.
      operationId: transferCancelCommit
      parameters:
      - $ref: "#/components/parameters/PaymentReference"
      - $ref: "#/components/parameters/CancelPaymentTransactionToken"
      - $ref: "#/components/parameters/RetryIndicator"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentsCancelTransactionPutRequestModel'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          $ref: "#/components/responses/204-Transfer-Cancelled"
    delete:
      tags:
      - cancel
      summary: Rollback the two-phase cancellation request.
      description: |
        This function is used by the sender to indicate the funds transfer operation was not successful, and that the
        outgoing transfer should resume.
      operationId: transferCancelRollback
      parameters:
      - $ref: "#/components/parameters/PaymentReference"
      - $ref: "#/components/parameters/ParticipantCancelPaymentTransactionToken"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: The cancellation request was itself cancelled.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"

  /payments/{id}/history:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - payment history
      summary: Get Payment Status History
      description: For a Customer wishing to view the full status history of a particular
        outgoing Transfer, the Customer's Participant can use the GET TRANSFER STATUS
        HISTORY service. The result includes every status the Transfer has passed
        through, with a date stamp for each status change.
      operationId: getTransferStatusHistory
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Payment status history
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                type: array
                minItems: 1
                items:
                  $ref: '#/components/schemas/StatusHistory'
  /payments/{id}/receive:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - receive payment
      summary: Receive e-Transfer Payment
      description: This service is used to complete an incoming transfer. The request
        either fails or succeeds and no rollback is possible.
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      - $ref: "#/components/parameters/RetryIndicator"
      operationId: completeTransfer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceivePaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Transfer successfully completed
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
    get:
      tags:
      - receive payment
      summary: Get Incoming Payment
      description: >-
        This service allows a Participant to obtain the details of an incoming Transfer
        for one of their Recipient Customers.
        It is normally invoked by the Participant after the Customer has received
        the incoming Transfer notice and has given indication that he/she wants to
        proceed with accepting it.
      operationId: getIncomingTransfer
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Incoming Payment Information
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IncomingPayment'
  /payments/{id}/receive/decline:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - receive payment
      summary: Decline Transfer
      description: Decline transfer
      operationId: declineTransfer
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeclinePaymentRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Decline Payment.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"

  /payments/{id}/receive/transaction:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - receive payment
      summary: 2-Phase Receive e-Transfer Payment Begin
      description: This function is used to initiate the completion of an incoming
        transfer.
      operationId: completeTransferBegin
      parameters:
      - $ref: "#/components/parameters/RetryIndicator"
      - $ref: "#/components/parameters/ClearingSystemReference"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceivePaymentBeginRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Transfer successfully completed
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceivePaymentBeginResponse'
    put:
      tags:
      - receive payment
      summary: Receive e-Transfer Payment Commit
      description: This function is used to commit the complete transfer begin, once
        the funds are successfully transferred by the Participant.
      operationId: completeTransferCommit
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      - $ref: "#/components/parameters/ReceivePaymentTransactionToken"
      - $ref: "#/components/parameters/RetryIndicator"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceivePaymentCommitRequest'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Commit Complete Transfer Success
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
    delete:
      tags:
      - receive payment
      summary: 2-Phase Stop receive e-Transfer Payment
      description: If the funds transfer operation is not successful, this function
        is used to rollback the complete transfer begin.
      operationId: completeTransferRollback
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      - $ref: "#/components/parameters/ParticipantTransactionId"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: Rollback Complete Transfer Success
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/refunds/send:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - refund
      summary: Returns a payment to the sender
      description: |
        This function is used to refund payments which were submitted or paid previously.
      operationId: sendPaymentReturn
      parameters:
      - $ref: '#/components/parameters/ClearingSystemReferenceNumber'
      requestBody:
        content:
          application/json:
            schema:
              description: Encapsulates a PaymentReturnV## (pacs.004.001.##) request
                message.
              type: object
              required:
              - payment_return
              properties:
                payment_return:
                  $ref: "#/components/schemas/PaymentReturnV09"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: The payment was returned successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsRefundsSendPost200ResponseModel'
  /payments/{id}/refunds/send/transaction:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - refund
      summary: Initiate a payment return to the sender
      description: |
        This 'InitiatePaymentReturn' function is used to start the process of refunding payments which were submitted or paid previously.
        The actual payment will not be sent at this stage.
      operationId: initiatePaymentReturn
      parameters:
      - $ref: '#/components/parameters/ClearingSystemReferenceNumber'
      requestBody:
        content:
          application/json:
            schema:
              description: Encapsulates a PaymentReturnV## (pacs.004.001.##) request
                message.
              type: object
              required:
              - payment_return
              properties:
                payment_return:
                  $ref: "#/components/schemas/PaymentReturnV09"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '201':
          description: The system is ready to return the payment to the sender.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsRefundsSendTransactionPost201ResponseModel'
    put:
      tags:
      - refund
      summary: Commit a payment return to the sender
      description: |
        This function is used by a participant to submit/commit a payment return that was previously initiated using
        the 'InitiatePaymentReturn' message.
      operationId: submitPaymentReturn
      parameters:
      - $ref: '#/components/parameters/ClearingSystemReferenceNumber'
      - $ref: "#/components/parameters/RefundTransactionToken"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitPaymentReturn"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: The payment was returned successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsRefundsSendTransactionPut200ResponseModel'
    delete:
      tags:
      - refund

      summary: Rollback a payment return to the sender
      description: |
        This function is used to revoke/cancel a payment return that was previously initiated using
        the 'InitiatePaymentReturn' message.
      operationId: reversePaymentReturn
      parameters:
      - $ref: '#/components/parameters/ClearingSystemReferenceNumber'
      - $ref: "#/components/parameters/RefundTransactionToken"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: The pending refund was revoked successfully.
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsRefundsSendTransactionDelete200ResponseModel'
  /payments/{id}/resend-notification:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    post:
      tags:
      - send payment
      summary: Reissue transfer notification
      description: >-
        The service also offers a way for the Sender to reissue the notification for
        a Transfer.
        For example in case this reissue is needed because incorrect or invalid Contact
        information, the Sender Participant's FI can also make use of the UPDATE CONTACT
        message to update the Contact information.
      operationId: reissuePaymentNotification
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: re issue of notification
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /payments/{id}/update-authentication:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    patch:
      tags:
      - send payment
      summary: Update security question and answer
      description: >-
        If a Customer (Sender) needs to update the security question and answer for
        one of his/her outgoing Transfers,
        the Sender's Participant FI can use the UPDATE TRANSFER AUTHENTICATION message
        to submit to the e-Transfer system the updated information.
      operationId: updatePaymentAuthentication
      parameters:
      - $ref: "#/components/parameters/ClearingSystemReference"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePaymentAuthentication'
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '204':
          description: updated authentication
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
  /remittance-advice:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - remittance
      description: >-
        This service can be used by Participants to retrieve from Interac the
        remittance data associated with a specific payment or payment request
        for one of their Customers.
      summary: Remittance details
      operationId: getRemittanceAdvice
      parameters:
      - $ref: "#/components/parameters/RemittanceApiClearingSystemReference"
      - $ref: "#/components/parameters/RemittanceApiRemittanceIdentification"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Remittance Advice Information
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemittanceAdviceResponse'
    # removed as out scope items 
    # post:
    #   tags:
    #     - remittance
    #   description: >-
    #     This service can be used by Participants to send remittance data associated with a specific payment to Interac
    #     or payment request for one of their Customers.
    #   summary: Create Remittance details
    #   operationId: createRemittanceAdvice
    #   requestBody:
    #     content:
    #       application/json:
    #         schema:
    #           $ref: '#/components/schemas/RemittanceAdviceRequest'
    #   responses:
    #     $ref: "../common/v3.5/responses.yaml#/x-linker-objects/standard-errors"
    #     '204':
    #       description: Remittance Advice Information Created
    #       headers:
    #         $ref: "../common/v3.5/headers.yaml#/x-linker-objects/rate-limiting"
    #         x-et-response-code:
    #           $ref: "../common/v3.5/headers.yaml#/x-et-response-code"
  /remittance-location:
    parameters:
    - $ref: "#/components/parameters/ParticipantId"
    - $ref: "#/components/parameters/ParticipantUserId"
    - $ref: "#/components/parameters/IndirectConnectorId"
    - $ref: "#/components/parameters/Authorization"
    - $ref: "#/components/parameters/RequestId"
    - $ref: "#/components/parameters/ChannelIndicator"
    - $ref: "#/components/parameters/Signature"
    - $ref: "#/components/parameters/SignatureType"
    - $ref: "#/components/parameters/TransactionTime"
    get:
      tags:
      - remittance
      description: >-
        This service can be used by Participants to retrieve from Interac the remittance
        location data associated with a specific payment or payment request.
      summary: Remittance location details
      operationId: getRemittanceLocation
      parameters:
      - $ref: "#/components/parameters/RemittanceApiClearingSystemReference"
      - $ref: "#/components/parameters/RemittanceApiRemittanceIdentification"
      responses:
        "400": {"$ref": "#/components/responses/400-bad-request"}
        "401": {"$ref": "#/components/responses/401-unauthorized"}
        "403": {"$ref": "#/components/responses/403-forbidden"}
        "404": {"$ref": "#/components/responses/404-not-found"}
        "429": {"$ref": "#/components/responses/429-too-many-requests"}
        "500": {"$ref": "#/components/responses/500-internal-server-error"}
        "503": {"$ref": "#/components/responses/503-service-unavailable"}
        '200':
          description: Remittance Location Information
          headers:
            x-et-rate-limit-window:
              $ref: "#/components/headers/x-et-rate-limit-window"
            x-et-rate-limit-ceiling:
              $ref: "#/components/headers/x-et-rate-limit-ceiling"
            x-et-rate-limit-remaining:
              $ref: "#/components/headers/x-et-rate-limit-remaining"
            x-et-response-code:
              $ref: "#/components/headers/x-et-response-code"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemittanceLocationResponse'
    # post:
    #   tags:
    #     - remittance
    #   description: >-
    #     This service can be used by Participants to send remittance location data associated with a specific payment to Interac.
    #   summary: Create Remittance Location details
    #   operationId: createRemittanceLocation
    #   requestBody:
    #     content:
    #       application/json:
    #         schema:
    #           $ref: '#/components/schemas/RemittanceLocationRequest'
    #   responses:
    #     $ref: "../common/v3.5/responses.yaml#/x-linker-objects/standard-errors"
    #     '204':
    #       description: Remittance Location Information Created
    #       headers:
    #         $ref: "../common/v3.5/headers.yaml#/x-linker-objects/rate-limiting"
    #         x-et-response-code:
    #           $ref: "../common/v3.5/headers.yaml#/x-et-response-code"
components:
  headers:
    x-et-page-next-offset:
      description: >
        Pagination parameter - the offset of the 1st record in the next requested
        record set
        against the total records in backend
      schema:
        type: integer
        example: 0
    x-et-page-size-limit:
      description: Pagination parameter - the number of record on each returned page
      schema:
        type: integer
        example: 10
    x-et-page-total-records:
      description: Pagination parameter - the total number of record in the backend
        system
      schema:
        type: integer
        example: 100
    x-et-rate-limit-ceiling:
      description: >
        The rate limit ceiling for that given endpoint in the given window.
      schema:
        type: integer
        format: int64
        example: 120
    x-et-rate-limit-remaining:
      description: >
        The number of requests left for the current window. Since the eTransfer is
        calculating the rate limit based on GMT in HOUR window, the timezones that
        have 30-minute shift should realize the remaining count is based on the clock
        in the hourly-shift zones.
      schema:
        type: integer
        format: int64
        example: 60
    x-et-rate-limit-window:
      description: >
        The rate limit gauging and resetting window. To simplify the complexity of
        the impact of timezone on the rate limiting, eTransfer will use GMT for rate
        limiting window. MINUTE is the default setting unless specified otherwise.
      schema:
        type: string
        enum: ['HOUR', 'MINUTE']
        example: MINUTE
    x-et-response-code:
      description: >
        A numeric response code specifying the outcome of the message. A successful
        call will
        return a response code of 0, along with any additional response data.
      schema:
        type: integer
        example: 0
  parameters:
    AccountAliasHandle:
      in: query
      name: deposit_handle
      description: >-
        If serviceType is EMAIL - Customer's email address used to uniquely identify
        the account alias  registration across the system. <br/>
        If serviceType is UUID - Customer's UUID used to uniquely identify the account
        alias  registration across the system. <br/>
        If serviceType is PHONE - Customer's phone number is used to uniquely identify
        the account alias  registration across the system. <br/>
        If serviceType is ACCOUNT_DEPOSIT - Recipient's/Contact's account
      schema:
        $ref: "#/components/schemas/AccountAliasHandle"
      required: true
    AccountAliasType:
      in: query
      name: deposit_type
      description: >-
        Flag indicating the type of the Account Alias  registration
        <br/> EMAIL - means Email based account alias registration
        <br/> UUID  - means UUID based account alias registration. Multiple types
        of unique identifiers are supported.
        <br/> PHONE - means Phone based account alias registrations.
        <br/> ACCOUNT_DEPOSIT - Account based Direct Deposit.
      schema:
        $ref: '#/components/schemas/AccountAliasType'
      required: true
    Authorization:
      in: header
      name: Authorization
      description: >-
        Standard HTTP Header used to implement OAuth 2.0 bearer scheme.
      schema:
        type: string
      required: false
      example: 12345
    CancelPaymentTransactionToken:
      in: header
      name: x-cancel-txn-token
      description: The one-time token received from the previous 'CancelTransferBegin'
        response.
      required: true
      schema:
        $ref: "#/components/schemas/CancelPaymentTransactionToken"
    ChannelIndicator:
      in: header
      name: x-et-channel-indicator
      description: see components/schemas/ChannelIndicator
      schema:
        $ref: '#/components/schemas/ChannelIndicator'
      required: true
      example: ONLINE
    ClearingSystemReference:
      description: The Interac-generated transfer reference number, also known as
        the clearing system reference number.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    ClearingSystemReferenceNumber:
      description: The Interac-generated Clearing System Reference Number.
      in: path
      name: id
      required: true
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    IndirectConnectorId:
      in: header
      name: x-et-indirect-connector-id
      description: >-
        Financial Institution/Debtor Agent Identifier (not Direct Connector) as defined
        in e-Transfer system.
      schema:
        type: string
        maxLength: 35
      required: false
      example: 1
    ParticipantCancelPaymentTransactionToken:
      in: header
      name: x-participant-cancel-txn-token
      description: The participant txn-id provided in received from the previous 'CancelTransferBegin'
        request.
      required: true
      schema:
        $ref: "#/components/schemas/CancelPaymentTransactionToken"
    ParticipantId:
      in: header
      name: x-et-participant-id
      description: >-
        Direct Participant Identifier as defined in e-Transfer system. Participant
        must ensure conformity to the following pattern before transmitting this data
        - CA000xxx where xxx is the Financial Institution Identifier as defined by
        the Canadian Payment Association. If customer's FI connects indirectly through
        a Participant, the participant-id field identifies the direct connector.
      schema:
        type: string
        minLength: 8
        maxLength: 8
      required: true
      example: CA000001
    ParticipantReference:
      description: >-
        TransactionIdentification reference of the payment or payment request. (
        participant ReferenceNumber ), Present if ClrSysRef is missing.
      in: query
      name: participant_reference
      required: true
      schema:
        $ref: "#/components/schemas/TransactionId"
    ParticipantTransactionId:
      description: >-
        TransactionIdentification reference of the payment or payment request. (
        participant transaction id ), Present if ClrSysRef is missing.
      in: query
      name: participant_transaction_id
      required: true
      schema:
        $ref: "#/components/schemas/TransactionId"
    ParticipantUserId:
      description: Present for all API calls initiated on behalf of a customer. Customer
        ID provided as defined in the Participant system and Customer must be registered
        in the e-Transfer system.
      in: header
      name: x-et-participant-user-id
      schema:
        type: string
        maxLength: 35
      required: true
      example: CA000001-user-123
    PaymentReference:
      in: path
      name: id
      description: The payment reference or ID at Interac.
      schema:
        type: string
      required: true
    PaymentTransactionToken:
      in: header
      name: x-payment-transaction-token
      description: >-
        The one-time token received from the previous Payment initiate (sent / receive/
        cancel ) response.
      required: true
      schema:
        $ref: "#/components/schemas/BeginTransactionId"
    ProductCode:
      in: query
      name: product_code
      description: >-
        Identifies an Interac e-Transfer product by code. The valid code values are
        -
        <br/> DOMESTIC - e-Transfer domestic
      schema:
        $ref: '#/components/schemas/ProductCode'
      required: true
    ReceivePaymentTransactionToken:
      in: header
      name: x-receive-payment-transaction-token
      description: Interac transaction token from the previous 'ReceivePaymentBegin'
        response.
      required: true
      schema:
        $ref: "#/components/schemas/BeginTransactionId"
    RefundTransactionToken:
      description: The one-time token received from the previous 'InitiatePaymentReturn'
        response.
      in: header
      name: x-refund-txn-token
      required: true
      schema:
        $ref: "#/components/schemas/RefundTransactionToken"
    RemittanceApiClearingSystemReference:
      description: The Interac-generated transfer reference number, also known as
        the clearing system reference number.
      in: query
      name: clearing_system_reference
      required: false
      schema:
        $ref: "#/components/schemas/ClearingSystemReference"
    RemittanceApiRemittanceIdentification:
      description: Remittance identification reference. Conditional usage; either
        the clearingSystemReference or the remittanceIdentification is required, but
        not both.
      in: query
      name: id
      required: false
      schema:
        $ref: "#/components/schemas/RemittanceIdentification"
    RequestId:
      in: header
      name: x-et-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes. In
        case of a request retry use the same ID as in the original message.
      schema:
        type: string
        maxLength: 36
      required: true
      example: 12345
    RetryIndicator:
      in: header
      name: x-et-retry-indicator
      description: >-
        Flag indicating if the operation is a retry of a previous request (true/false).
        Always to be specified for requests where it is important to avoid the cases
        where the same operation is accidentally performed twice.
      schema:
        type: string
      required: true
      example: false
    Signature:
      in: header
      name: x-et-api-signature
      description: >-
        JWS detached signature of the payload (body only), required for all API calls.
      schema:
        type: string
      required: true
      example: 12345
    SignatureType:
      in: header
      name: x-et-api-signature-type
      description: >-
        The type of the JWT. Required. Allowed values are "PAYLOAD_DIGEST_SHA256"
      schema:
        $ref: '#/components/schemas/SignatureType'
      required: true
      example: PAYLOAD_DIGEST_SHA256
    TransactionTime:
      in: header
      name: x-et-transaction-time
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      schema:
        $ref: '#/components/schemas/TransactionTime'
      required: true
      example: '2020-01-23T12:34:56.123Z'
  responses:
    204-Transfer-Cancelled:
      description: The transfer was cancelled successfully.
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
        x-et-response-code:
          $ref: "#/components/headers/x-et-response-code"
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    401-unauthorized:
      description: Unauthorized
    403-forbidden:
      description: Forbidden
    404-not-found:
      description: Resources Not Found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    429-too-many-requests:
      description: Too many requests; blocked due to rate limiting.
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
    500-internal-server-error:
      description: Internal Server Error
    503-service-unavailable:
      description: Service Unavailable - The server cannot handle the request for
        a service due to temporary maintenance.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorModel"
      headers:
        x-et-rate-limit-window:
          $ref: "#/components/headers/x-et-rate-limit-window"
        x-et-rate-limit-ceiling:
          $ref: "#/components/headers/x-et-rate-limit-ceiling"
        x-et-rate-limit-remaining:
          $ref: "#/components/headers/x-et-rate-limit-remaining"
  schemas:
    AccountAliasHandle:
      description: >-
        If serviceType is EMAIL - Customer's email address used to uniquely identify
        the account alias  registration across the system. <br/>
        If serviceType is UUID - Customer's UUID used to uniquely identify the account
        alias  registration across the system. <br/>
        If serviceType is PHONE - Customer's phone number is used to uniquely identify
        the account alias  registration across the system. </br>
        If serviceType is ACCOUNT_DEPOSIT - Recipient's/Contact's account </br>  In
        case of add/update we will restrict field length at 64 now, for backward compatibility
        until FIs implement 3.5 APIs.
      type: string
      maxLength: 64
      example: <EMAIL>
    AccountAliasRegistrationDetails:
      description: >-
        Direct Deposit Data required to deposit a transfer directly into Recipient's/Payee's
        account. If present the Contact is eligible to receive Direct Deposit e-Transfer
        domestic transfers.
      type: object
      required:
      - account_alias_handle
      - service_type
      properties:
        account_alias_handle:
          description: >-
            UUID associated with the Payee/Recipient.  The UUID must exist in the
            system and it uniquely identifies the Payee's/Recipient's direct deposit
            registration.
          type: string
          maxLength: 64
          example: <EMAIL>
        service_type:
          $ref: '#/components/schemas/ServiceType'
        sender_account_identifier:
          description: >-
            Sender's/Customer's account identifier in Payee's/Recipient's system.
          type: string
          minLength: 1
          maxLength: 40
          example: ********9
    AccountAliasType:
      description: >-
        Flag indicating the type of the Account Alias  registration
        <br/> EMAIL - means Email based account alias registration
        <br/> UUID  - means UUID based account alias registration. Multiple types
        of unique identifiers are supported.
        <br/> PHONE - means Phone based account alias registrations.
        <br/> ACCOUNT_DEPOSIT - Account based Direct Deposit.
      type: string
      enum: ['EMAIL', 'UUID', 'PHONE', 'ACCOUNT_DEPOSIT']
      example: EMAIL

   ## Custom ##
    AccountIdentification4Choice:
      type: object
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'
    AccountNumber:
      description: Bank account number. Canadian bank account format is aaa-bbbbb-cccccccccccccccccccc
        where 'aaa' is the Financial Institution Identifier 'bbbbb' is the Transit
        Number 'cccccccc...' is the Account Number.
      type: string
      minLength: 1
      maxLength: 34
      example: 123-12345-********90********90
    AccountSchemeName1Choice:
      type: object
      oneOf:
      - type: object
        properties:
          code:
            $ref: '#/components/schemas/ExternalAccountIdentification1Code'
      - type: object
        properties:
          proprietary:
            description: >-
              Proprietary scheme name used in the identification of the account. Accepted
              values are
              'ALIAS_ACCT_NO' - Identification scheme used by Interac to identify
              the Account alias registration number of an e-Transfer customer
              'BANK_ACCT_NO' - Unique and unambiguous assignment made by a specific
              bank or similar financial institution to identify a relationship as
              defined between the bank and its client
            type: string
            enum: ['ALIAS_ACCT_NO', 'BANK_ACCT_NO']
            example: ALIAS_ACCT_NO
    ActiveCurrencyAndAmount:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveCurrencyCode'
    ActiveCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 44.44
    ActiveCurrencyCode:
      type: string
      enum: ['CAD', 'USD']
      example: CAD
      x-code-notNull: 317
      x-code-enum: 344
    ActiveOrHistoricCurrencyAndAmount:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyCode'
    ActiveOrHistoricCurrencyAndAmount_SimpleType:
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 55.55
    ActiveOrHistoricCurrencyCode:
      type: string
      enum: ['CAD', 'USD']
      example: CAD
      x-code-notNull: 317
      x-code-enum: 344
    AddressType2Code:
      type: string
      enum: ['ADDR', 'PBOX', 'HOME', 'BIZZ', 'MLTO', 'DLVY']
      x-code-enum: 2031
      example: 'ADDR'
    AddressType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/AddressType2Code'
    AmountType3Choice:
      type: object
      properties:
        instructed_amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
   # REMT 002 #
    AmountType4Choice:
      type: object
      properties:
        instructed_amount:
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    AmountWithCurrency:
      type: object
      required:
      - amount
      - currency
      properties:
        amount:
          description: Transaction amount, with the specified currency.
          type: number
          multipleOf: 0.01
          x-multipleOf: 0.01
          example: 66.66
      #maxLength: 19
      #format: decimal #totalDigits 18 fractionDigits 5
          x-code-invalid: 346
        currency:
          description: ISO 4217 currency code
          type: string
          enum: ['CAD', 'USD']
          example: CAD
          x-code-enum: 344
          x-code-notNull: 317
    AuthenticatePayment:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      required:
      - security_answer
      properties:
        security_answer:
          description: "Answer to the security question (as provided by the customer)\
            \ with leading and trailing whitespace trimmed, uppercase, postfixed with\
            \ hashSalt if present, hashed using the algorithm identified by hashType\
            \ and then Base64 encoded. ISO-8859-1encoding to be used when the hash\
            \ is generated."
          type: string
          maxLength: 64
   ## Common Messages
    AuthenticationType:
      description: >-
        'Flag indicating the type of authentication required to complete the Transfer.
        <br/>
          CONTACT_LEVEL - Security question and answer defined at contact level. <br/>
          PAYMENT_LEVEL - Security question and answer defined at payment level. <br/>
          NOT_REQUIRED - No authentication. </br> '
      type: string
      enum: ['CONTACT_LEVEL', 'PAYMENT_LEVEL', 'NOT_REQUIRED']
      example: CONTACT_LEVEL
    Authorisation1Choice:
      type: object
      oneOf:
      - type: object
        properties:
          code:
            $ref: '#/components/schemas/Authorisation1Code'
      - type: object
        properties:
          proprietary:
            $ref: '#/components/schemas/Max128Text'
    Authorisation1Code:
      type: string
      enum: ['AUTH', 'FDET', 'FSUM', 'ILEV']
      example: 'AUTH'
    BICFIDec2014Identifier:
      type: string
      pattern: '[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}'
      example: AAAADEBBXXX
    BankAccountIdentifier:
      type: object
      required:
      - type
      - account
      properties:
        type:
          description: Bank Account Identifier (CANADIAN - Canadian bank account)
          type: string
          enum: ['CANADIAN']
          example: CANADIAN
        account:
          description: Bank account number. Canadian bank account format is aaa-bbbbb-cccccccccccccccccccc
            where 'aaa' is the Financial Institution Identifier 'bbbbb' is the Transit
            Number 'cccccccc...' is the Account Number.
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-********90********90
    BatchBookingIndicator:
      type: boolean
    BeginTransactionId:
      description: If available this must contain the Interac-generated begin transaction
        Id (i.e. clrSysBgnTxId) that was provided in the Initiate Payment response
        message.
      type: string
      maxLength: 35
      example: ********
    BranchAndFinancialInstitutionIdentification6:
      type: object
      required:
      - financial_institution_identification
      properties:
        financial_institution_identification:  # FinInstnId
          $ref: '#/components/schemas/FinancialInstitutionIdentification18'
    BusinessName:
      description: Business name. This is required for type 1 (small business) or
        2 (corporation)
      type: object
      properties:
        company_name:
          description: Business/company name, is required while creating customer
            profile.
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
          x-code-notNull: 424
        trade_name:
          description: Trade name
          type: string
          minLength: 1
          maxLength: 100
          example: Interac
      x-code-notNull: 423
    CancelPaymentReason:
      type: object
      properties:
        reason_memo:
          description: Human-readable cancellation reason
          type: string
          maxLength: 400
    CancelPaymentTransaction:
      description: Cancel Pending Transaction Model
      type: object
      required:
      - cancel_txn_id
      properties:
        cancel_txn_id:
          $ref: "#/components/schemas/CancelPaymentTransactionToken"
    CancelPaymentTransactionToken:
      description: A one-time token generated specifically to commit or rollback a
        specific cancel transaction.
      type: string
    CashAccount38:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'
        proxy: # Prxy
          $ref: '#/components/schemas/ProxyAccountIdentification1'
    CategoryPurpose1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalCategoryPurpose1Code'
    ChannelIndicator:
      description: >-
        Method Sender accessed the servicedescription:  Identifies the channel that
        the customer is using when making the
        request if the request is initiated by the customer. For requests
        initiated by an e- Transfer system or a participants system component it
        identifies the system that makes the request. Integer, values <br/>
        ONLINE = Customer online initiated transaction <br/>
        MOBILE = Customer mobile initiated transaction <br/>
        PARTICIPANT_BULK_PAYMENT = Participant payment system initiated Bulk file
        transaction (*) <br/>
        PARTICIPANT_ETRANSFER_SYSTEM = Participant payment system initiated transaction
        <br/>
        PARTICIPANT_FRAUD_SYSTEM = Participant fraud detection system initiated transaction
        <br/>
        ETRANSFER_SYSTEM = e-Transfer system initiated transaction (*) <br/>
        ETRANSFER_FRAUD = e-Transfer fraud detection system initiated transaction
        (*) <br/>
        EXTERNAL_APPS = External API initiated transaction (*) <br/>
        INTERAC_SDK = Interac Proximity SDK initiated transaction <br/> (*) - values
        not accepted
        through Participant initiated requests
      type: string
      enum: ['ONLINE', 'MOBILE', 'PARTICIPANT_BULK_PAYMENT', 'PARTICIPANT_ETRANSFER_SYSTEM',
        'PARTICIPANT_FRAUD_SYSTEM', 'ETRANSFER_SYSTEM', 'ETRANSFER_FRAUD', 'EXTERNAL_APPS',
        'INTERAC_SDK']
      example: ONLINE
    ChargeBearerType1Code:
      type: string
      description: This identifies which party(ies) will pay changer due for processing
        this transaction. Value is set to "SLEV", which means Following Service Level"
      enum: ['SLEV']
      example: SLEV
      x-code-enum: 2008
    ClearingSystemIdentification3Choice:
      type: object
      properties:
        proprietary:
          type: string
          enum: ['ETR']
          example: ETR
    ClearingSystemMemberIdentification2:
      type: object
      required:
      - member_identification
      properties:
        member_identification: # MmbId
          description: >-
            Identification of the Instructed/Instructing/Debtor/Creditor Agent.
            The value 'NOTPROVIDED' in the MemberIdentification element can be used
            when this information is not provided or is not available.
          type: string
          minLength: 1
          maxLength: 35
    ClearingSystemReference:
      description: The Interac-generated payment or request for payment reference
        numbers, also known as the clearing system reference in ISO 20022.
      type: string
      maxLength: 35
      example: ********
    Contact4:
      type: object
      properties:
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        phone_number: # PhneNb
          $ref: '#/components/schemas/PhoneNumber'
        mobile_number: # MobNb
          $ref: '#/components/schemas/PhoneNumber'
        fax_number: # FaxNb
          $ref: '#/components/schemas/PhoneNumber'
        email_address: # EmailAdr
          type: string
          maxLength: 2048
          format: email
          example: <EMAIL>
    ContactDetails:
      description: Recipient's/Contact's details
      type: object
      properties:
        contact_id:
          description: Recipient's/Contact's Interac User ID
          type: string
          maxLength: 35
        contact_type:
          $ref: "#/components/schemas/ContactType"
        alias_name:
          description: Recipient/Contact Name/Alias, as known by the customer. Recipient/Contact
            Name/Alias has to be unique for the customer unless the Recipient/Contact
            is a one time contact.
          type: string
          maxLength: 80
        legal_name:
          $ref: "#/components/schemas/LegalName"
        notification_preference:
          type: array
          items:
            $ref: "#/components/schemas/NotificationPreference"
    ContactType:
      description: >-
        'Flag indicating whether the Contact is a business or not. Required if legal
        name is required. (e.g. for for e-Transfer International Remittance) <br/>
        retail <br/> small business <br/> corporation'
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      example: RETAIL
    CountryCode:
      type: string
      description: Only ISO 3166 Alpha-2 codes are allowed.
      enum: ['AA', 'AB', 'AD', 'AE', 'AF', 'AG', 'AI', 'AL', 'AM', 'AN', 'AO', 'AQ',
        'AR', 'AS', 'AT', 'AU', 'AW', 'AX', 'AZ', 'BA', 'BB', 'BD', 'BE', 'BF', 'BG',
        'BH', 'BI', 'BJ', 'BL', 'BM', 'BN', 'BO', 'BQ', 'BR', 'BS', 'BT', 'BV', 'BW',
        'BY', 'BZ', 'C2', 'CA', 'CC', 'CD', 'CF', 'CG', 'CH', 'CI', 'CK', 'CL', 'CM',
        'CN', 'CO', 'CR', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ', 'DE', 'DJ', 'DK', 'DM',
        'DO', 'DZ', 'EC', 'EE', 'EG', 'EH', 'ER', 'ES', 'ET', 'FI', 'FJ', 'FK', 'FM',
        'FO', 'FR', 'GA', 'GB', 'GD', 'GE', 'GF', 'GG', 'GH', 'GI', 'GL', 'GM', 'GN',
        'GP', 'GQ', 'GR', 'GS', 'GT', 'GU', 'GW', 'GY', 'HK', 'HM', 'HN', 'HR', 'HT',
        'HU', 'ID', 'IE', 'IL', 'IM', 'IN', 'IO', 'IQ', 'IR', 'IS', 'IT', 'JE', 'JM',
        'JO', 'JP', 'K1', 'KE', 'KG', 'KH', 'KI', 'KM', 'KN', 'KP', 'KR', 'KW', 'KY',
        'KZ', 'LA', 'LB', 'LC', 'LI', 'LK', 'LR', 'LS', 'LT', 'LU', 'LV', 'LY', 'MA',
        'MC', 'MD', 'ME', 'MF', 'MG', 'MH', 'MK', 'ML', 'MM', 'MN', 'MO', 'MP', 'MQ',
        'MR', 'MS', 'MT', 'MU', 'MV', 'MW', 'MX', 'MY', 'MZ', 'NA', 'NC', 'NE', 'NF',
        'NG', 'NI', 'NL', 'NO', 'NP', 'NR', 'NU', 'NZ', 'OM', 'PA', 'PE', 'PF', 'PG',
        'PH', 'PK', 'PL', 'PM', 'PN', 'PR', 'PS', 'PT', 'PW', 'PY', 'QA', 'QM', 'QN',
        'QO', 'QP', 'QQ', 'QR', 'QS', 'QT', 'QU', 'QV', 'QW', 'QX', 'QY', 'QZ', 'RE',
        'RO', 'RS', 'RU', 'RW', 'S1', 'SA', 'SB', 'SC', 'SD', 'SE', 'SG', 'SH', 'SI',
        'SJ', 'SK', 'SL', 'SM', 'SN', 'SO', 'SR', 'SS', 'ST', 'SV', 'SX', 'SY', 'SZ',
        'TC', 'TD', 'TF', 'TG', 'TH', 'TJ', 'TK', 'TL', 'TM', 'TN', 'TO', 'TP', 'TR',
        'TT', 'TV', 'TW', 'TZ', 'UA', 'UG', 'UM', 'US', 'UY', 'UZ', 'VA', 'VC', 'VE',
        'VG', 'VI', 'VN', 'VU', 'WF', 'WS', 'XA', 'XB', 'XD', 'XE', 'XF', 'XG', 'XN',
        'XP', 'XQ', 'XR', 'XS', 'XT', 'XU', 'XV', 'XW', 'XY', 'XZ', 'YE', 'YT', 'YU',
        'ZA', 'ZM', 'ZW']
      example: CA
      x-code-enum: 2009
    CreditDebitCode:
      type: string
      enum: ['CRDT', 'DBIT']
      example: 'CRDT'
    CreditTransferTransaction39:
      type: object
      required:
      - payment_identification
      - interbank_settlement_amount
      - charge_bearer
      - debtor
      - debtor_agent
      - creditor_agent
      - creditor
      properties:
        payment_identification:  # PmtId
          $ref: '#/components/schemas/PaymentIdentification7'
        payment_type_information: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation28'
        interbank_settlement_amount: # IntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbank_settlement_date: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        charge_bearer: # ChrgBr
          $ref: '#/components/schemas/ChargeBearerType1Code'
        previous_instructing_agent_1: # PrvsInstgAgt1
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/PartyIdentification135'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
        debtor: # Dbtr
          $ref: '#/components/schemas/PartyIdentification135'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/PartyIdentification135'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/PartyIdentification135'
        related_remittance_information: # RltdRmtInf
          description: >-
            Elements in this data block can be used to specify details related to
            the handling of the remittance information such as remittance location
            and/or
            the unique identification of the remittance information if it was sent
            separately from the payment instruction.
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocation7'
          maxItems: 1
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
    CreditorReferenceInformation2:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/CreditorReferenceType2'
        reference: # Ref
          $ref: '#/components/schemas/Max35Text'
    CreditorReferenceType1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType3Code'
    CreditorReferenceType2:
      type: object
      required:
      - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/CreditorReferenceType1Choice'
    CustomDate:
      description: >-
        A particular point in the progression of time in a calendar year
        expressed in the YYYY-MM-DD format. This representation is defined in
        XML Schema Part 2 Datatypes Second Edition - W3C Recommendation 28
        October 2004 which is aligned with ISO 8601.
      type: string
      format: date
      example: '2020-01-23'
    CustomDateTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.sssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.123Z'
    CustomerAccount:
      type: object
      required:
      - account_holder_name
      - bank_account_identifier
      properties:
        fi_account_id:
          description: Unique FI Identifier for the customer account (tokenized account).
          type: string
          minLength: 1
          maxLength: 50
          example: FI Account
        account_holder_name:
          description: Account holder name.
          type: string
          minLength: 1
          maxLength: 80
          example: FI Account holder name
          x-code-notNull: 441
        bank_account_identifier:
          $ref: "#/components/schemas/BankAccountIdentifier"
    CustomerName:
      description: Customer's name
      type: object
      required:
      - legal_name
      - registration_name
      properties:
        registration_name:
          description: Customer's primary registration name or alias
          type: string
          maxLength: 80
        legal_name:
          $ref: "#/components/schemas/LegalName"

    CustomerType:
      description: >-
        Identifies the customer type based on the following codes - <br/>  retail
        <br/> small business <br/> corporation
      type: string
      enum: ['RETAIL', 'SMALL_BUSINESS', 'CORPORATION']
      example: RETAIL
    DateAndDateTime2Choice:
      $ref: '#/components/schemas/ISODateTime'
    DecimalNumber:
      type: number
      #format: decimal  # fractionDigits=17, totalDigits=18
    DeclinePaymentRequest:
      description: Reason for decline
      properties:
        decline_reason:
          description: Reason for decline
          type: string
          maxLength: 400
    DocumentAdjustment1:
      type: object
      required:
      - amount
      properties:
        amount:  # Amt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        credit_debit_indicator: # CdtDbtInd
          $ref: '#/components/schemas/CreditDebitCode'
        reason: # Rsn
          $ref: '#/components/schemas/Max4Text'
        additional_information: # AddtlInf
          $ref: '#/components/schemas/Max140Text'
    DocumentType3Code:
      type: string
      enum: ['RADM', 'RPIN', 'FXDR', 'DISP', 'PUOR', 'SCOR']
      example: 'RADM'
    DocumentType6Code:
      type: string
      enum: ['MSIN', 'CNFA', 'DNFA', 'CINV', 'CREN', 'DEBN', 'HIRI', 'SBIN', 'CMCN',
        'SOAC', 'DISP', 'BOLD', 'VCHR', 'AROI', 'TSUT', 'PUOR']
      example: 'MSIN'
    EmailNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via Email
        required:
        - email
        properties:
          email:
            type: string
            maxLength: 2048
            format: email
            description: The email of the contact
            example: <EMAIL>
    ErrorModel:
      type: object
      required:
      - code
      properties:
        code:
          type: string
          description: |
            Error list for all the APIs, please refer to the example for API-specific error codes.
          maxLength: 80
          example: 999
        text:
          description: Short error explanation.
          type: string
          maxLength: 2000
          example: Unknown error
    ExternalAccountIdentification1Code:
      type: string
      enum: ['AIIN', 'BBAN', 'CUID', 'UPIC']
      example: 'AIIN'
    ExternalCategoryPurpose1Code:
      type: string
      description: This includes iso codes which are 4 letter characters and custom
        which are 3 digit numbers.
      enum: ['BONU', 'CASH', 'CBLK', 'CCRD', 'CORT', 'DCRD', 'DIVI', 'DVPM', 'EPAY',
        'FCOL', 'GOVT', 'HEDG', 'ICCP', 'IDCP', 'INTC', 'INTE', 'LOAN', 'MP2B', 'MP2P',
        'OTHR', 'PENS', 'RPRE', 'RRCT', 'RVPM', 'SALA', 'SECU', 'SSBE', 'SUPP', 'TAXS',
        'TRAD', 'TREA', 'VATX', 'WHLD', '240', '260', '330', '370', '400', '430',
        '460', '480', '452', '308', '311', '318']
      example: BONU
    ExternalFinancialInstitutionIdentification1Code:
      type: string
      minLength: 1
      maxLength: 4
    ExternalOrganisationIdentification1Code:
      type: string
      enum: ['BANK', 'CBID', 'CHID', 'CINC', 'COID', 'CUST', 'DUNS', 'EMPL', 'GS1G',
        'SREN', 'SRET', 'TXID']
      x-code-enum: 2024
      example: 'BANK'
    ExternalPaymentTransactionStatus1Code:
      type: string
      enum: ['ACTC', 'PDNG', 'ACSP', 'RJCT']
      description: >-
        This indicates the transaction processing ISO status. Acceptable codes
        ACTC - AcceptedTechnicalValidation
        PDNG - Pending
        ACSP - Accepted Settlement In progress - indicates transaction was processed
        successfully,
        RJCT - indicates transaction processing has been cancelled
      example: ACTC
    ExternalReturnReason1Code:
      type: string
      enum: ['AC01', 'AC03', 'AC04', 'AC06', 'AC13', 'AC14', 'AC15', 'AC16', 'AC17',
        'AG01', 'AG02', 'AM01', 'AM02', 'AM03', 'AM04', 'AM05', 'AM06', 'AM07', 'AM09',
        'AM10', 'ARDT', 'BE01', 'BE04', 'BE05', 'BE06', 'BE07', 'BE08', 'CN01', 'CNOR',
        'CURR', 'CUST', 'DNOR', 'DS28', 'DT01', 'ED01', 'ED03', 'ED05', 'EMVL', 'ERIN',
        'FF05', 'FOCR', 'FR01', 'FRTR', 'MD01', 'MD02', 'MD06', 'MD07', 'MS02', 'MS03',
        'NARR', 'NOAS', 'NOCM', 'NOOR', 'PINL', 'RC01', 'RC07', 'RF01', 'RR01', 'RR02',
        'RR03', 'RR04', 'RUTA', 'SL01', 'SL02', 'SL11', 'SL12', 'SL13', 'SL14', 'SP01',
        'SP02', 'SVNR', 'TM01', 'TRAC', 'UPAY']
      x-code-enum: 2058
      example: 'AC01'
    ExternalStatusReason1Code:
      type: string
      enum: ['AB01', 'AB02', 'AB03', 'AB04', 'AB05', 'AB06', 'AB07', 'AB08', 'AB09',
        'AB10', 'AC01', 'AC02', 'AC03', 'AC04', 'AC05', 'AC06', 'AC07', 'AC08', 'AC09',
        'AC10', 'AC11', 'AC12', 'AC13', 'AC14', 'AC15', 'AG01', 'AG02', 'AG03', 'AG04',
        'AG05', 'AG06', 'AG07', 'AG08', 'AG09', 'AG10', 'AG11', 'AGNT', 'AM01', 'AM02',
        'AM03', 'AM04', 'AM05', 'AM06', 'AM07', 'AM09', 'AM10', 'AM11', 'AM12', 'AM13',
        'AM14', 'AM15', 'AM16', 'AM17', 'AM18', 'AM19', 'AM20', 'AM21', 'AM22', 'AM23',
        'BE01', 'BE04', 'BE05', 'BE06', 'BE07', 'BE08', 'BE09', 'BE10', 'BE11', 'BE12',
        'BE13', 'BE14', 'BE15', 'BE16', 'BE17', 'BE18', 'BE19', 'BE20', 'BE21', 'BE22',
        'CERI', 'CH03', 'CH04', 'CH07', 'CH09', 'CH10', 'CH11', 'CH12', 'CH13', 'CH14',
        'CH15', 'CH16', 'CH17', 'CH19', 'CH20', 'CH21', 'CH22', 'CNOR', 'CURR', 'CUST',
        'DNOR', 'DS01', 'DS02', 'DS03', 'DS04', 'DS05', 'DS06', 'DS07', 'DS08', 'DS09',
        'DS0A', 'DS0B', 'DS0C', 'DS0D', 'DS0E', 'DS0F', 'DS0G', 'DS0H', 'DS0K', 'DS10',
        'DS11', 'DS12', 'DS13', 'DS14', 'DS15', 'DS16', 'DS17', 'DS18', 'DS19', 'DS20',
        'DS21', 'DS22', 'DS23', 'DS24', 'DS25', 'DS26', 'DS27', 'DT01', 'DT02', 'DT03',
        'DT04', 'DT05', 'DT06', 'DU01', 'DU02', 'DU03', 'DU04', 'DU05', 'DUPL', 'ED01',
        'ED03', 'ED05', 'ED06', 'ERIN', 'FF01', 'FF02', 'FF03', 'FF04', 'FF05', 'FF06',
        'FF07', 'FF08', 'FF09', 'FF10', 'FF11', 'G000', 'G001', 'G002', 'G003', 'G004',
        'G005', 'G006', 'ID01', 'MD01', 'MD02', 'MD05', 'MD06', 'MD07', 'MS02', 'MS03',
        'NARR', 'NERI', 'RC01', 'RC02', 'RC03', 'RC04', 'RC05', 'RC06', 'RC07', 'RC08',
        'RC09', 'RC10', 'RC11', 'RC12', 'RCON', 'RF01', 'RR01', 'RR02', 'RR03', 'RR04',
        'RR05', 'RR06', 'RR07', 'RR08', 'RR09', 'RR10', 'RR11', 'RR12', 'S000', 'S001',
        'S002', 'S003', 'S004', 'SL01', 'SL02', 'SL11', 'SL12', 'SL13', 'SL14', 'TA01',
        'TD01', 'TD02', 'TD03', 'TM01', 'TS01', 'TS04']
      example: 'AB01'
    FIToFICustomerCreditTransferV08:
      type: object
      required:
      - group_header
      - credit_transfer_transaction_information
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader93'
        credit_transfer_transaction_information: # CdtTrfTxInf
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransaction39'
          minItems: 1
          description: >-
            Elements used to provide information about payments.
          maxItems: 1
    FIToFIPaymentStatusReportV10:
      type: object
      required:
      - group_header
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader91'
        transaction_information_and_status: # TxInfAndSts
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransaction110'
          description: >-
            Data block that contains information concerning the original transaction,
            to which the response/status report message refers
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
    FIToFIPaymentStatusReportV10-Definition:
      type: object
      required:
      - fi_to_fi_payment_status_report
      properties:
        fi_to_fi_payment_status_report: # FIToFIPmtStsRpt
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
    FIToFIPaymentStatusRequestV03:
      type: object
      required:
      - group_header
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader91'
        transaction_information: # TxInf
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransaction113'
          description: >-
            Data block that contains information concerning the original transaction,
            to which the response/status report message refers
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
    FinancialIdentificationSchemeName1Choice:
      type: object
      oneOf:
      - type: object
        properties:
          code:
            $ref: '#/components/schemas/ExternalFinancialInstitutionIdentification1Code'
      - type: object
        properties:
          proprietary:
            $ref: '#/components/schemas/Max35Text'
    FinancialInstitutionIdentification18:
      type: object
      properties:
        bicfi:  # BICFI
          $ref: '#/components/schemas/BICFIDec2014Identifier'
        clearing_system_member_identification: # ClrSysMmbId
          $ref: '#/components/schemas/ClearingSystemMemberIdentification2'
        name: # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        other: # Othr
          $ref: '#/components/schemas/GenericFinancialIdentification1'
    FraudCaseId:
      description: Unique Interac-generated reference number associated with this
        fraud operations.
      type: string
      maxLength: 35
      example: ********
    FraudCheckResult:
      description: Fraud check results
      type: object
      required:
      - action
      properties:
        score:
          description: Numeric value between 0 and 999 qualifying Fraud Detection
            System assessment of fraud risk.
          type: integer
          minimum: 0
          maximum: 999
          example: 10
        reason:
          description: Fraud Reason from Fraud Detection System
          type: string
          maxLength: 256
          example: Fraud reason
        action:
          description: Description of action to be performed to mitigate fraud.  (Allow
            Transfer, Block Transfer, Delay Transfer, User Input Required, No Check
            Performed)
          type: string
          enum: ['ALLOW', 'BLOCK', 'DELAY', 'USER_INPUT_REQUIRED', 'NO_CHECK_PERFORMED']
          example: ALLOW
    FraudStatusModel:
      type: object
      required:
      - fraud_status
      properties:
        fraud_status:
          $ref: "#/components/schemas/FraudCheckResult"
    GenericAccountIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          description: >-
            if scheme name is ALIAS_ACCT_NO then this element must contain the Account
            Alias Reference Number (i.e. Autodeposit reference number) generated by
            Interac
            if scheme name is BANK_ACCT_NO then this element must contain the actual
            bank account number. Valid format aaa-bbbbb-cccccccccccccccccccccccc where
              aaa is the Institution Id (fixed length 3 digits)
              bbbbb is the Transit Number (fixed length 5 digits)
              cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: 123-12345-********90********90
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/AccountSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericFinancialIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          $ref: '#/components/schemas/Max35Text'
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/FinancialIdentificationSchemeName1Choice'
        issuer: # Issr
          $ref: '#/components/schemas/Max35Text'
    GenericOrganisationIdentification1:
      type: object
      required:
      - identification
      properties:
        identification:  # Id
          description: participant user ID at their financial institution (creditor
            / debtor/ initiating party ).
          type: string
          minLength: 1
          maxLength: 35
        scheme_name: # SchmeNm
          $ref: '#/components/schemas/OrganisationIdentificationSchemeName1Choice'
    GetPaymentStatusRequest:
      description: Wrapper element that encompasses the whole structure of the request
        message.
      type: object
      required:
      - fi_to_fi_payment_status_request
      properties:
        fi_to_fi_payment_status_request:
          # pacs.028 business payload block
          $ref: '#/components/schemas/FIToFIPaymentStatusRequestV03'
    GetPaymentStatusResponse:
      description: Get Payment Response wrapper
      type: object
      required:
      - fi_to_fi_payment_status_report
      - payment_status
      - account_holder_name
      properties:
        fi_to_fi_payment_status_report:
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
        payment_status:
          $ref: '#/components/schemas/PaymentStatus'
        payment_expiry_date:
          $ref: "#/components/schemas/CustomDateTime"
        payment_authentication:
          $ref: '#/components/schemas/ViewPaymentAuthentication'
        account_holder_name:
          description: Account Holder Name.
          type: string
          minLength: 1
          maxLength: 80
        initiating_party_id:
          description: The id of the initiating party for this payment. Conditional
            - this element is present only if it was present in the original payment
            submission and also if this response is provided to the debtor/sender
            side of the payment (NOT the creditor/recipient side).
          type: string
          minLength: 1
          maxLength: 35
        initiating_party_name:
          description: The name of the initiating party for this payment. Present
            if it was provided in the original payment submission.
          type: string
          minLength: 1
          maxLength: 140
        language:
          $ref: "#/components/schemas/Language"
   ## Non ISO Payment API Messages ##
    GroupHeader79:
      type: object
      required:
      - message_identification
      - creation_datetime
      - initiating_party
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        initiating_party: # InitgPty
          $ref: '#/components/schemas/PartyIdentification135'
    GroupHeader90:
      type: object
      required:
      - message_identification
      - creation_datetime
      - number_of_transactions
      - settlement_information
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        authorisation: # Authstn
          type: array
          items:
            $ref: '#/components/schemas/Authorisation1Choice'
          maxItems: 2
        batch_booking: # BtchBookg
          $ref: '#/components/schemas/BatchBookingIndicator'
        number_of_transactions: # NbOfTxs
          $ref: '#/components/schemas/Max15NumericText'
        control_sum: # CtrlSum
          $ref: '#/components/schemas/DecimalNumber'
        group_return: # GrpRtr
          $ref: '#/components/schemas/TrueFalseIndicator'
        total_returned_interbank_settlement_amount: # TtlRtrdIntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbank_settlement_date: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        settlement_information: # SttlmInf
          $ref: '#/components/schemas/SettlementInstruction7'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
    GroupHeader91:
      type: object
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
      required:
      - message_identification
      - creation_datetime
      - instructing_agent
      - instructed_agent
    GroupHeader93:
      type: object
      properties:
        message_identification:  # MsgId
          description: >-
            The reference number for this request message. It must be unique (within
            each FI / Interac system) for every request.
          type: string
          minLength: 1
          maxLength: 35
        creation_datetime: # CreDtTm
          $ref: '#/components/schemas/ISODateTime'
        number_of_transactions: # NbOfTxs
          $ref: '#/components/schemas/Max15NumericText'
        settlement_information: # SttlmInf
          $ref: '#/components/schemas/SettlementInstruction7'
        instructing_agent: # InstgAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        instructed_agent: # InstdAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
      required:
      - message_identification
      - creation_datetime
      - number_of_transactions
      - settlement_information
      - instructing_agent
      - instructed_agent
    HashType:
      description: Algorithm used to hash the security answers, passwords examples
        - SHA2.
      type: string
      enum: ['SHA2']
      example: SHA2
    ISODate:
      type: string
      format: date
      description: Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      example: '2020-01-23'
    ISODateTime:
      type: string
      description: >-
        "A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601. "
      format: date-time
      example: '2020-01-23T12:34:56.123Z'
    IncomingPayment:
      allOf:
      - $ref: "#/components/schemas/Payment"
      - type: object
        required:
        - payment_reference
        - transfer_status
        - originated_participant_id
        - language
        - sender_registration_name
        - authentication_required
        - additional_remittance_info
        properties:
          payment_reference:
              #description: Interac Transfer Reference Number
            $ref: "#/components/schemas/ClearingSystemReference"
          transfer_status:
            $ref: '#/components/schemas/PaymentStatus'
          originated_participant_id:
            description: ID of participant where the transfer has originated
            type: string
            maxLength: 35
          sender_memo:
            description: Sender's message to Recipient
            type: string
            maxLength: 420
          language:
            $ref: "#/components/schemas/Language"
          sender_registration_name:
            description: Sender's Primary Registration Name/Alias
            type: string
            maxLength: 80
          authentication_required:
            description: >-
              Transfer authentication required or Not <br/>
              false - Not Required <br/>
              true - Required <br/>
            type: boolean
          additional_remittance_info:
            description: >-
              This element indicates if there is additional remittance information
              available for this transfer, as follows:
                NOT_PROVIDED - indicates that there is no additional remittance information
              available for this transaction other than the senderMemo (unstructured
              remittance)
                ADVICE_DETAILS - indicates that there is additional structured remittance
              information available for this transaction in the form or remittance
              advice
                LOCATION_DETAILS - indicates that there is additional structured remittance
              information available for this transaction in the form or remittance
              location
                ADVICE_AND_LOCATION_DETAILS - indicates that there is additional remittance
              information available for this transaction in the form of remittance
              advice and remittance location.
            type: string
            enum: ['NOT_PROVIDED', 'ADVICE_DETAILS', 'LOCATION_DETAILS', 'ADVICE_AND_LOCATION_DETAILS']
            example: NOT_PROVIDED
      - $ref: "#/components/schemas/ViewIncomingPaymentAuthentication"
    InitiatePaymentRequest:
      description: Initiate Payment Request wrapper
      type: object
      required:
      - fi_to_fi_customer_credit_transfer
      - product_code
      - fraud_supplementary_info
      - account_holder_name
      properties:
        fi_to_fi_customer_credit_transfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'
        product_code:
          $ref: '#/components/schemas/ProductCode'
        expiry_date:
          $ref: "#/components/schemas/CustomDateTime"
        fi_account_id:
          description: Unique FI Identifier for the customer account (tokenized account).
          type: string
          minLength: 1
          maxLength: 50
        account_holder_name:
          description: Account Holder Name.
          type: string
          minLength: 1
          maxLength: 80
        sender_account_identifier:
          description: Account Holder Name.
          type: string
          minLength: 1
          maxLength: 40
        fraud_supplementary_info:
          $ref: "#/components/schemas/SupplementaryInfo"
        payment_authentication:
          $ref: '#/components/schemas/PaymentAuthentication'
        language:
          $ref: "#/components/schemas/Language"
        fraud_case_id:
          $ref: "#/components/schemas/FraudCaseId"
    InitiatePaymentResponse:
      description: Initiate Payment Response wrapper
      type: object
      required:
      - fi_to_fi_payment_status_report
      - fraud_check_result
      - clearing_system_begin_transaction_id
      properties:
        fi_to_fi_payment_status_report:
          $ref: "#/components/schemas/FIToFIPaymentStatusReportV10"
        payment_transaction_token:
          $ref: "#/components/schemas/BeginTransactionId"
        fraud_check_result:
          $ref: "#/components/schemas/FraudCheckResult"
    InterruptedPayment:
      allOf:
      - $ref: "#/components/schemas/PaymentInfo"
      - type: object
        required:
        - begin_transaction_id
        - transaction_status
        - transaction_date
        properties:
          originating_channel_indicator:
            $ref: "#/components/schemas/ChannelIndicator"
          begin_transaction_id:
            description: Interac Transaction Id returned in a Transfer Begin response
            type: string
          participant_transaction_id:
            description: Unique Participant Transaction ID for tracking and recovery
              purposes sent in a Transfer Begin request
            type: string
            # receiving_channel_indicator:
            #   description: Method Recipient accessed the service
            #   type: string
          transaction_status:
            $ref: '#/components/schemas/TransactionStatus'
          transaction_date:
              #description: Interac Transaction Date in UTC
            $ref: "#/components/schemas/CustomDateTime"
    Language:
      description: Recipient's Language Preference as defined by Sender
      type: string
      enum: ['EN', 'FR']
      default: EN
      x-code-enum: 2012
      example: 'EN'
    LegalName:
      description: Legal name
      type: object
      oneOf:
      - type: object
        properties:
          retail_name:
            $ref: "#/components/schemas/RetailName"
      - type: object
        properties:
          business_name:
            $ref: "#/components/schemas/BusinessName"
      x-code-notNull: 436
      x-mutually-exclusive:
        code: 400
        notNull: 436
        allowed:
        - 'retail_name'
        - 'business_name'
    LocalInstrument2Choice:
      type: object
      properties:
        proprietary:
          description: >-
            REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
            FULFILL_REQUEST_FOR_PAYMENT      - Fulfill Money Request payment </br>
            ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
            REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
            ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
            REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>
          type: string
          enum: ['REGULAR_PAYMENT', 'FULFILL_REQUEST_FOR_PAYMENT', 'ACCOUNT_ALIAS_PAYMENT',
            'REALTIME_ACCOUNT_ALIAS_PAYMENT', 'ACCOUNT_DEPOSIT_PAYMENT', 'REALTIME_ACCOUNT_DEPOSIT_PAYMENT']
          example: REGULAR_PAYMENT
    Max105Text:
      type: string
      minLength: 1
      maxLength: 105
    Max128Text:
      type: string
      minLength: 1
      maxLength: 128
    Max140Text:
      type: string
      minLength: 1
      maxLength: 140
    Max15NumericText:
      type: string
      pattern: '[0-9]{1,15}'
    Max16Text:
      type: string
      minLength: 1
      maxLength: 16
    Max2048Text:
      type: string
      minLength: 1
      maxLength: 2048
    Max35Text:
      type: string
      minLength: 1
      maxLength: 35
    Max4Text:
      type: string
      minLength: 1
      maxLength: 4
    Max70Text:
      type: string
      minLength: 1
      maxLength: 70
    NotificationActive:
      description: >-
        'false - Notifications will NOT be sent <br/> true -Notifications will be
        sent'
      type: boolean
      example: true
    NotificationPreference:
      description: List of customer's notifications. Sender's method to notify the
        recipient.
      type: object
      discriminator:
        propertyName: type
        mapping:
          EMAIL: "#/components/schemas/EmailNotification"
          SMS: "#/components/schemas/SMSNotification"
          RN: "#/components/schemas/RoutedNotification"
      required:
      - type
      - active
      properties:
        type:
          $ref: "#/components/schemas/NotificationType"
        active:
          $ref: "#/components/schemas/NotificationActive"
    NotificationStatus:
      description: >-
        Notification Status shows the state of notification sent to the recipient/contact.
        It is returned if the transfer had a notification sent to the recipient/contact
        to complete the transfer (regular domestic eTransfer). <br/>
        SENT = Sent <br/>
        PENDING  = Pending <br/>
        SEND_FAILURE  = Send Failure <br/>
        DELIVERY_FAILURE = Bounced back
      type: string
      enum: ['SENT', 'PENDING', 'SEND_FAILURE', 'DELIVERY_FAILURE']
      example: SENT
    NotificationType:
      type: string
      description: Email <br/> SMS <br/> RN ( Participant_Routed_Notification ) channel
      enum: ['EMAIL', 'SMS', 'RN']
      example: EMAIL
      x-code-enum: 546
    OrganisationIdentification29:
      type: object
      properties:
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
    OrganisationIdentificationSchemeName1Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalOrganisationIdentification1Code'
    OriginalGroupInformation29:
      type: object
      required:
      - original_message_identification
      - original_message_name_identification
      properties:
        original_message_identification:  # OrgnlMsgId
          $ref: '#/components/schemas/Max35Text'
        original_message_name_identification: # OrgnlMsgNmId
          $ref: '#/components/schemas/Max35Text'
    OriginalPaymentInformation8:
      type: object
      properties:
        references:  # Refs
          $ref: '#/components/schemas/TransactionReferences5'
        amount: # Amt
          $ref: '#/components/schemas/AmountType3Choice'
        requested_execution_date: # ReqdExctnDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
      required:
      - references
      - amount
    OriginalTransactionReference28:
      type: object
      properties:
        amount: # Amt
          $ref: '#/components/schemas/AmountType4Choice'
        remittance_information: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        ultimate_debtor: # UltmtDbtr
          $ref: '#/components/schemas/Party40Choice'
        debtor: # Dbtr
          $ref: '#/components/schemas/Party40Choice'
        debtor_account: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        debtor_agent: # DbtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor_agent: # CdtrAgt
          $ref: '#/components/schemas/BranchAndFinancialInstitutionIdentification6'
        creditor: # Cdtr
          $ref: '#/components/schemas/Party40Choice'
        creditor_account: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        ultimate_creditor: # UltmtCdtr
          $ref: '#/components/schemas/Party40Choice'
    OutgoingPayment:
      allOf:
      - $ref: "#/components/schemas/PaymentInfo"
      - type: object
        required:
        - expiry_date
        - originating_channel_indicator
        - participant_reference
        - contact_outgoing_details
        - customer_account
        - payment_status
        - authentication_type
        properties:
          expiry_date:
            $ref: "#/components/schemas/CustomDateTime"
          participant_reference:
            $ref: "#/components/schemas/TransactionId"
          sender_memo:
            description: Message to Recipient
            type: string
            maxLength: 420
          originating_channel_indicator:
            $ref: "#/components/schemas/ChannelIndicator"
          request_for_payment_reference:
            description: Interac Money Request Reference Number to fulfill. Required
              if transferType is FULFILL_REQUEST_FOR_PAYMENT.
            type: string
            maxLength: 35
          payment_schedule_reference:
              #description: Interac Transfer Schedule Reference Number present if the transfer was created as a result of a transfer schedule
            $ref: "#/components/schemas/ScheduleReference"
          notification_status:
            $ref: "#/components/schemas/NotificationStatus"
          contact_outgoing_details:
            $ref: '#/components/schemas/ContactDetails'
          account_alias_reg_details:
            $ref: '#/components/schemas/AccountAliasRegistrationDetails'
          authentication_type:
            $ref: "#/components/schemas/AuthenticationType"
          customer_account:
            $ref: "#/components/schemas/CustomerAccount"
    ParticipantFraudInstruction:
      description: >-
        INTERAC_TO_SCORE -> instructing Interac to proceed and score the payment like
        it does today (fraudCaseId can still be submitted but it will not have an
        effect since Interac will still score)
        INTERAC_TO_ALLOW -> instructing Interac to simply trust the Participant FI
        and let the payment through (fraudCaseId can still be submitted but it will
        not have any override effect on this allow instruction)
        INTERAC_TO_BLOCK -> only for non-real time payments, instructing Interac to
        initate the payment but to keep in a blocked state (fraudCaseId can still
        be submitted but it will not have any override effect on this block instruction)
      type: string
      example: INTERAC_TO_SCORE
      enum: ['INTERAC_TO_SCORE', 'INTERAC_TO_ALLOW', 'INTERAC_TO_BLOCK']
    ParticipantUserDetails:
      description: Participant User Details, present only for Account Deposit Payment
        types where we have Interac generated user id and indirect connector returned
        from FIs.
      type: object
      required:
      - participant_user_id
      properties:
        participant_user_id:
          $ref: "#/components/schemas/ParticipantUserId"
        indirect_connector_id:
          type: string
          maxLength: 35
          example: ********
        participant_id:
          type: string
          minLength: 8
          maxLength: 8
          example: CA000099
    ParticipantUserId:
      description: Participant user ID in the participant's system.
      type: string
      maxLength: 35
      example: user-123
    Party38Choice:
      type: object
      properties:
        organisation_identification:
          $ref: '#/components/schemas/OrganisationIdentification29'
    Party40Choice:
      type: object
      properties:
        party:
          $ref: '#/components/schemas/PartyIdentification135'
   # PACS 028 #
    PartyIdentification135:
      type: object
      properties:
        name:  # Nm
          $ref: '#/components/schemas/Max140Text'
        postal_address: # PstlAdr
          $ref: '#/components/schemas/PostalAddress24'
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        country_of_residence: # CtryOfRes
          $ref: '#/components/schemas/CountryCode'
        contact_details: # CtctDtls
          $ref: '#/components/schemas/Contact4'
    Payment:
      description: Send etransfer Request
      type: object
      required:
      - product_code
      - payment_type
      - amount
      - expiry_date
      properties:
        product_code:
          $ref: '#/components/schemas/ProductCode'
        payment_type:
          $ref: "#/components/schemas/PaymentType"
        amount:
          $ref: "#/components/schemas/AmountWithCurrency"
        expiry_date:
          $ref: "#/components/schemas/CustomDateTime"
        sender_memo:
          description: Message to Recipient
          type: string
          maxLength: 420
    PaymentAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      required:
      - authentication_type
      properties:
        authentication_type:
          $ref: "#/components/schemas/AuthenticationType"
        security_question:
          description: Question text. Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 40
        hash_type:
          $ref: "#/components/schemas/HashType"
        hash_salt:
          description: Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 44
        security_answer:
          description: >-
            'Answer to the security question (as provided by the customer) with leading
            and trailing whitespace trimmed, uppercase, postfixed with hashSalt if
            present, hashed using the algorithm identified by hashType and then Base64
            encoded. ISO-8859-1encoding to be used when the hash is generated. Required
            if authenticationType is PAYMENT_LEVEL.'
          type: string
          maxLength: 64
    PaymentIdentification7:
      type: object
      required:
      - end_to_end_identification
      properties:
        instruction_identification:  # InstrId
          description: >-
            Reference identification as assigned by the instructing party. Usage -
            if the credit transfer is used to fulfill a request for payment (i.e.
            if PmtTpInf.LclInstrm.Prtry is FULFILL_REQUEST_FOR_PAYMENT ) then this
            element is mandatory and must be populated as follows;
            if the request for payment was originally submitted to Interac via a pain.13
            then this element must match the Interac generated reference identification
            of the request for payment (i.e. clearingSystemReference element from
            the request body of ISO 20022 - Get Incoming Request for Payment)
            if the request for payment was submitted via a custom non-ISO Money Request
            then this element must match the Interac generated reference identification
            of the request for payment (i.e. requestReferenceNumber)
            if the credit transfer is not used to fulfill a request for payment then
            this element is optional
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            Reference identification as assigned by the customer/debtor. This remains
            unchanged in the whole end to end flow between the end customers, Usage;
            if the credit transfer is used to fulfill a request for payment (i.e.
            if PmtTpInf.LclInstrm.Prtry is FULFILL_REQUEST_FOR_PAYMENT) then,
                if the request for payment was submitted via a pain.13 then this element
            must match the EndToEndId reference identification from that pain.013
            request for payment
                if the request for payment was submitted via a custom non-ISO Money
            Request then this element should be set to 'NOTPROVIDED'
            if the credit transfer is not used to fulfill a request for payment then,
                if a reference is provided by the Debtor then this element should
            contain that value
                if a reference is not provided by the Debtor then this element should
            be set to 'NOTPROVIDED'
          type: string
          minLength: 1
          maxLength: 35
        transaction_identification: # TxId
          description: >-
            Transaction identification set by the first instructing agent (typically
            the Debtor Agent). This remains unchanged in the end to end flow between
            various agents, Usage;
            Reference identification as assigned by Debtor FI. This element is mandatory
            and must be unique for that FI
          type: string
          minLength: 1
          maxLength: 35
        clearing_system_reference: # ClrSysRef
          $ref: '#/components/schemas/Max35Text'
    PaymentInfo:
      description: Payment Request
      type: object
      required:
      - payment_reference
      - request_date
      - product_code
      - payment_type
      - amount
      properties:
        payment_reference:
          #description: Interac Transfer Reference Number
          $ref: "#/components/schemas/ClearingSystemReference"
        product_code:
          $ref: '#/components/schemas/ProductCode'
        payment_type:
          $ref: "#/components/schemas/PaymentType"
        request_date:
          #description: Transfer Requested Date
          $ref: "#/components/schemas/CustomDateTime"
        amount:
          $ref: "#/components/schemas/AmountWithCurrency"
        receiving_channel_indicator:
          $ref: "#/components/schemas/ChannelIndicator"
        payment_status:
          #description: Payment Status list.
          $ref: "#/components/schemas/PaymentStatus"
    PaymentOption:
      description: Payment Option
      type: object
      required:
      - payment_type
      - sender_account_identifier_required
      - max_payment_outgoing_amount
      properties:
        payment_type:
          $ref: "#/components/schemas/PaymentType"
        account_alias_reference:
          description: Account alias Registration reference number generated by interac,
            for using at payment initiation
          type: string
          minLength: 8
          maxLength: 35
        sender_account_identifier_required:
          description: Flag indicating if Sender/Customer has to provide an account
            identifier <br/> false - Not Required <br/> true - Required'
          type: boolean
        sender_account_identifier_format:
          description: This element is used to specify the accepted format of the
            sender account identifier.
          type: string
          minLength: 1
          maxLength: 35
        sender_account_identifier_description:
          description: Provide a brief description about the meaning, purpose or location
            (on the bill) of the sender/subscriber account.
          type: string
          minLength: 1
          maxLength: 420
        max_payment_outgoing_amount:
          $ref: "#/components/schemas/AmountWithCurrency"
        customer_type:
          $ref: "#/components/schemas/CustomerType"
        customer_name:
          $ref: '#/components/schemas/CustomerName'
    PaymentOptionResponse:
      description: Payment Option Response
      type: object
      required:
      - payment_option_token
      properties:
        payment_options:
          type: array
          items:
            $ref: "#/components/schemas/PaymentOption"
    PaymentReturnReason6:
      type: object
      properties:
        reason: # Rsn
          $ref: '#/components/schemas/ReturnReason5Choice'
        additional_information: # AddtlInf
          type: array
          items:
            $ref: '#/components/schemas/Max105Text'
          description: >-
            Further details on the return reason. Conditional Usage,
            If ReturnReasonInformation.Code is FOCR (meaning FollowCancellationRequest)
            then this element must contain the Cancellation Identification from the
            camt.056 payment cancellation request message.
            Otherwise this element can contain the additional information for the
            return reason in a free text format (up to a total of 420 characters),
            as needed.
          maxItems: 4
    PaymentReturnStatus:
      description: >-
        Interac defined payment status  <br/>
        INITIATED <br/>
        DIRECT_DEPOSIT_PENDING <br/>
        DIRECT_DEPOSIT_FAILED <br/>
        COMPLETED  <br/>
      type: string
      enum: ['INITIATED', 'DIRECT_DEPOSIT_PENDING', 'DIRECT_DEPOSIT_FAILED', 'COMPLETED']
      example: INITIATED
    PaymentReturnV09:
      type: object
      required:
      - group_header
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader90'
        transaction_information: # TxInf
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransaction112'
          description: >-
            Data block that contains information concerning to payment refunds
          maxItems: 1
    PaymentState:
      description: >-
        Transfers to be returned in the response based on the transfer state <br/>
        ALL - All transfers included (default) <br/>
        PENDING - Outstanding (transfers not cancelled or completed) <br/>
        FINALIZED - Finalized ( transfers cancelled or completed)
      type: string
      enum: ['ALL', 'PENDING', 'FINALIZED']
      example: ALL
    PaymentStatus:
      description: >-
        Interac defined payment status  <br/>
        INITIATED <br/>
        AVAILABLE <br/>
        AUTHENTICATION_FAILURE <br/>
        AUTHENTICATION_SUCCESSFUL <br/>
        DECLINED <br/>
        EXPIRED <br/>
        DIRECT_DEPOSIT_PENDING <br/>
        DIRECT_DEPOSIT_FAILED <br/>
        CANCELLED  <br/>
        COMPLETED  <br/>
        REALTIME_INITIATED <br/>
        REALTIME_DEPOSIT_COMPLETED <br/>
        REALTIME_DEPOSIT_FAILED <br/>
      type: string
      enum: ['INITIATED', 'AVAILABLE', 'AUTHENTICATION_FAILURE', 'AUTHENTICATION_SUCCESSFUL',
        'DECLINED', 'EXPIRED', 'DIRECT_DEPOSIT_PENDING', 'DIRECT_DEPOSIT_FAILED',
        'CANCELLED', 'COMPLETED', 'REALTIME_INITIATED', 'REALTIME_DEPOSIT_COMPLETED',
        'REALTIME_DEPOSIT_FAILED']
      example: AVAILABLE
    PaymentStatusModel:
      type: object
      required:
      - payment_status
      properties:
        payment_status:
          $ref: "#/components/schemas/PaymentReturnStatus"
    PaymentStatusReportModel:
      type: object
      required:
      - payment_status_report
      properties:
        payment_status_report:
          $ref: "#/components/schemas/FIToFIPaymentStatusReportV10-Definition"
    PaymentTransaction110:
      type: object
      properties:
        original_group_information: # OrgnlGrpInf
          $ref: '#/components/schemas/OriginalGroupInformation29'
        original_instruction_identification: # OrgnlInstrId
          description: >-
            InstructionIdentification (if available) for the original transaction,
            to which the status request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_end_to_end_identification: # OrgnlEndToEndId
          description: >-
            EndToEndIdentification for the original transaction, to which the status
            request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_identification: # OrgnlTxId
          description: >-
            TransactionIdentification for the original transaction, to which the status
            request message refers.
          type: string
          minLength: 1
          maxLength: 35
        transaction_status: # TxSts
          $ref: '#/components/schemas/ExternalPaymentTransactionStatus1Code'
        status_reason_information: # StsRsnInf
          type: array
          items:
            $ref: '#/components/schemas/StatusReasonInformation12'
          description: >-
            Wrapper element for data block that contains the Interac-defined response
            code for this message.
            Usage - only one occurrence of this data block is allowed in this response
            message.
          maxItems: 1
        acceptance_datetime: # AccptncDtTm
          $ref: '#/components/schemas/ISODateTime'
        effective_interbank_settlement_date: # FctvIntrBkSttlmDt
          $ref: '#/components/schemas/DateAndDateTime2Choice'
        account_servicer_reference: # AcctSvcrRef
          $ref: '#/components/schemas/Max35Text'
        clearing_system_reference: # ClrSysRef
          description: >-
            Interac-generated transfer reference number. Present if the request was
            processed successfully and the transaction was found.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_reference: # OrgnlTxRef
          $ref: '#/components/schemas/OriginalTransactionReference28'
      required:
      - clearing_system_reference
    PaymentTransaction112:
      type: object
      properties:
        return_identification:  # RtrId
          $ref: '#/components/schemas/Max35Text'
        original_instruction_identification: # OrgnlInstrId
          description: >-
            This element must contain the InstrId of the original pacs.008 credit
            transfer message that is being returned.
            Conditional - this value must be provided if it was provided in the original
            pacs.008 credit transfer message.
          type: string
          minLength: 1
          maxLength: 35
        original_end_to_end_identification: # OrgnlEndToEndId
          description: >-
            This element must contain the EndToEndId of the original pacs.008 credit
            transfer message that is being returned.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_identification: # OrgnlTxId
          description: >-
            This element must contain the TxId of the original pacs.008 credit transfer
            message that is being returned.
          type: string
          minLength: 1
          maxLength: 35
        original_clearing_system_reference: # OrgnlClrSysRef
          description: >-
            This element must contain the ClrSysRef of the original pacs.008 credit
            transfer message that is being returned.
          type: string
          minLength: 1
          maxLength: 35
        returned_interbank_settlement_amount: # RtrdIntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        returned_instructed_amount: # RtrdInstdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        clearing_system_reference: # ClrSysRef
          $ref: '#/components/schemas/Max35Text'
        return_reason_information: # RtrRsnInf
          type: array
          items:
            $ref: '#/components/schemas/PaymentReturnReason6'
          description: >-
            Provides detailed information on the return reason.
          maxItems: 1
          minItems: 1
      required:
      - return_identification
      - original_end_to_end_identification
      - original_transaction_identification
      - original_clearing_system_reference
      - returned_interbank_settlement_amount
      - return_reason_information
    PaymentTransaction113:
      type: object
      properties:
        original_instruction_identification: # OrgnlInstrId
          description: >-
            InstructionIdentification (if available) for the original transaction,
            to which the status request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_end_to_end_identification: # OrgnlEndToEndId
          description: >-
            EndToEndIdentification for the original transaction, to which the status
            request message refers.
          type: string
          minLength: 1
          maxLength: 35
        original_transaction_identification: # OrgnlTxId
          description: >-
            TransactionIdentification for the original transaction, to which the status
            request message refers.
            Conditional - either the OriginalTransactionIdentification or the ClearingSystemReference
            must be provided, but not both
          type: string
          minLength: 1
          maxLength: 35
        clearing_system_reference: # ClrSysRef
          description: >-
            ClearingSystemReference (if available) for the original transaction, to
            which the status request message refers
            Conditional - either the OriginalTransactionIdentification or the ClearingSystemReference
            must be provided, but not both
          type: string
          minLength: 1
          maxLength: 35
   # REMT 001 #
    PaymentTransactionDetails:
      allOf:
      - $ref: "#/components/schemas/PaymentInfo"
      - type: object
        required:
        - originating_channel_indicator
        - transaction_status
        - transaction_date
        - begin_transaction_id
        - expiry_date
        properties:
          originating_channel_indicator:
              #description: Method Sender accessed the service
            $ref: "#/components/schemas/ChannelIndicator"
          transaction_status:
            $ref: '#/components/schemas/TransactionStatus'
          transaction_date:
            $ref: "#/components/schemas/CustomDateTime"
          participant_transaction_date:
            $ref: "#/components/schemas/CustomDateTime"
          participant_reference:
              #description: Participant Transaction Reference Number associated with SendTransfer. Field present if transactionStatus is 2, 5 or 8
            $ref: "#/components/schemas/TransactionId"
          participant_transaction_id:
            description: Unique Participant Transaction ID for tracking and recovery
              purposes sent in a Transfer Begin request
            type: string
            maxLength: 35
          begin_transaction_id:
              #description: Interac Transaction Id returned in a Transfer Begin response
            $ref: "#/components/schemas/BeginTransactionId"
          expiry_date:
              #description: Transfer Expiry Date in UTC
            $ref: "#/components/schemas/CustomDateTime"
          request_for_payment_reference:
            description: Interac Request Reference Number to fulfill. Present if transferType
              is FULFILL_REQUEST_FOR_PAYMENT.
            type: string
    PaymentType:
      description: >-
        REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
        FULFILL_REQUEST_FOR_PAYMENT     - Fulfill Money Request payment </br>
        ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
        REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
        ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
        REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>      
      type: string
      enum: ['REGULAR_PAYMENT', 'FULFILL_REQUEST_FOR_PAYMENT', 'ACCOUNT_ALIAS_PAYMENT',
        'REALTIME_ACCOUNT_ALIAS_PAYMENT', 'ACCOUNT_DEPOSIT_PAYMENT', 'REALTIME_ACCOUNT_DEPOSIT_PAYMENT']
      example: REGULAR_PAYMENT
    PaymentTypeInformation28:
      type: object
      properties:
        local_instrument: # LclInstrm
          $ref: '#/components/schemas/LocalInstrument2Choice'
        category_purpose: # CtgyPurp
          $ref: '#/components/schemas/CategoryPurpose1Choice'
      required:
      - local_instrument
    PaymentsCancelPostRequestModel:
      description: Cancel Payment Request Body
      allOf:
      - $ref: "#/components/schemas/CancelPaymentReason"
      - $ref: "#/components/schemas/TransactionDateModel"
    PaymentsCancelTransactionPostRequestModel:
      description: Cancel Payment Request Body
      required:
      - txn_id
      allOf:
      - type: object
        properties:
          txn_id:
            $ref: "#/components/schemas/TransactionIdentifier"
      - $ref: "#/components/schemas/CancelPaymentReason"
    PaymentsCancelTransactionPutRequestModel:
      description: Cancel Payment Commit Request Body
      allOf:
      - $ref: "#/components/schemas/TransactionDateModel"
    PaymentsRefundsSendPost200ResponseModel:
      allOf:
      - $ref: "#/components/schemas/PaymentStatusReportModel"
      - $ref: "#/components/schemas/PaymentStatusModel"
      - $ref: '#/components/schemas/FraudStatusModel'
    PaymentsRefundsSendTransactionDelete200ResponseModel:
      allOf:
      - $ref: "#/components/schemas/PaymentStatusReportModel"
    PaymentsRefundsSendTransactionPost201ResponseModel:
      allOf:
      - $ref: "#/components/schemas/PaymentStatusReportModel"
      - $ref: "#/components/schemas/RefundTransactionModel"
      - $ref: '#/components/schemas/FraudStatusModel'
    PaymentsRefundsSendTransactionPut200ResponseModel:
      allOf:
      - $ref: "#/components/schemas/PaymentStatusReportModel"
      - $ref: "#/components/schemas/PaymentStatusModel"
    PhoneNumber:
      description: >-
        The collection of information which identifies a specific phone or FAX number
        as defined by telecom services.
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).
      type: string
      maxLength: 30
      format: '\+[0-9]{1,3}-[0-9()+\-]{1,30}'
      example: ******-555-1212
      x-code-notNull: 309
    PostalAddress24:
      type: object
      properties:
        address_type:  # AdrTp
          $ref: '#/components/schemas/AddressType3Choice'
        department: # Dept
          $ref: '#/components/schemas/Max70Text'
        sub_department: # SubDept
          $ref: '#/components/schemas/Max70Text'
        street_name: # StrtNm
          $ref: '#/components/schemas/Max70Text'
        building_number: # BldgNb
          $ref: '#/components/schemas/Max16Text'
        post_code: # PstCd
          $ref: '#/components/schemas/Max16Text'
        town_name: # TwnNm
          $ref: '#/components/schemas/Max35Text'
        country_sub_division: # CtrySubDvsn
          $ref: '#/components/schemas/Max35Text'
        country: # Ctry
          $ref: '#/components/schemas/CountryCode'
        address_line: # AdrLine
          type: array
          items:
            $ref: '#/components/schemas/Max70Text'
          maxItems: 7
          x-code-notNull: 319
          x-code-invalid: 324
    ProductCode:
      description: >-
        Identifies an Interac e-Transfer product by code. The valid code values are
        -
        <br/> DOMESTIC - e-Transfer domestic
      type: string
      enum: ['DOMESTIC']
      example: DOMESTIC
    ProxyAccountIdentification1:
      type: object
      required:
      - identification
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ProxyAccountType1Choice'
        identification: # Id
          $ref: '#/components/schemas/Max2048Text'
      description: >-
        The elements in this data block can be used to specify the handle type and
        the handle value that were used during the account alias registration.
        Conditional, If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_ALIAS_PAYMENT
        or REALTIME_ACCOUNT_ALIAS_PAYMENT , then this block is mandatory.
        In all other cases this block is not required
    ProxyAccountType1Choice:
      type: object
      properties:
        proprietary:
          description: >-
            Proprietary proxy handle type used in the identification of the creditor
            account. The following values are currently supported and accepted - EMAIL,
            UUID or PHONE
          type: string
          enum: ['EMAIL', 'UUID', 'PHONE']
          example: EMAIL
    ReceivePayment:
      description: Receive Payment
      type: object
      required:
      - customer_account
      - supplementary_info
      properties:
        recipient_memo:
          description: Message for SenderNote - If a Recipient enters a memo, it appears
            on the 'Complete Transfer' email to the Sender.
          type: string
          maxLength: 420
        customer_account:
          $ref: "#/components/schemas/CustomerAccount"
        supplementary_info:
          $ref: "#/components/schemas/SupplementaryInfo"
    ReceivePaymentBeginRequest:
      allOf:
      - $ref: "#/components/schemas/ReceivePayment"
      - type: object
        required:
        - participant_transaction_id
        properties:
          participant_transaction_id:
              #description: Unique Participant Transaction ID for tracking and recovery purposes
            $ref: "#/components/schemas/TransactionId"
    ReceivePaymentBeginResponse:
      description: Receive Payment Begin Response
      type: object
      required:
      - receive_payment_transaction_token
      properties:
        receive_payment_transaction_token:
          description: Interac transaction token from 'ReceivePaymentBegin'
          type: string
          minLength: 1
          maxLength: 35
    ReceivePaymentCommitRequest:
      description: receive commit etransfer Request
      type: object
      required:
      - participant_reference
      - participant_transaction_date
      properties:
        participant_reference:
          #description: Participant Transaction Reference Number
          $ref: "#/components/schemas/TransactionId"
        participant_transaction_date:
          $ref: "#/components/schemas/CustomDateTime"
    ReceivePaymentRequest:
      allOf:
      - $ref: "#/components/schemas/ReceivePayment"
      - type: object
        required:
        - participant_reference
        - participant_transaction_date
        properties:
          participant_reference:
              #description: Participant Transaction Reference Number
            $ref: "#/components/schemas/TransactionId"
          participant_transaction_date:
            $ref: "#/components/schemas/CustomDateTime"
    ReceivedPayment:
      allOf:
      - $ref: "#/components/schemas/PaymentInfo"
      - type: object
        required:
        - payment_status
        - participant_reference
        - received_date
        - sender_registration_name
        - customer_account
        - receiving_channel_indicator
        properties:
          participant_reference:
            description: Participant Transaction Reference Number associated with
              CompleteTransfer transaction.
            type: string
            maxLength: 35
          received_date:
              #description: Transfer Received Date in UTC
            $ref: "#/components/schemas/CustomDateTime"
          sender_memo:
            description: Message to Recipient
            type: string
            maxLength: 420
          sender_registration_name:
            description: Sender's Name/Alias
            type: string
            maxLength: 80
          request_for_payment_reference:
            description: Interac Money Request Reference Number to fulfill. Required
              if transferType is FULFILL_REQUEST_FOR_PAYMENT.
            type: string
            maxLength: 35
          additional_remittance_info:
            description: >-
              This element indicates if there is additional remittance information
              available for this transfer, as follows,
              NOT_PROVIDED - indicates that there is no additional remittance information
              available for this transaction other than the senderMemo (unstructured
              remittance)
              ADVICE_DETAILS - indicates that there is additional structured remittance
              information available for this transaction in the form or remittance
              advice
              LOCATION_DETAILS - indicates that there is additional structured remittance
              information available for this transaction in the form or remittance
              location
              ADVICE_AND_LOCATION_DETAILS - indicates that there is additional remittance
              information available for this transaction in the form of remittance
              advice and remittance location.
            type: string
            enum: ['NOT_PROVIDED', 'ADVICE_DETAILS', 'LOCATION_DETAILS', 'ADVICE_AND_LOCATION_DETAILS']
            example: NOT_PROVIDED
          account_alias_reg_details:
            $ref: '#/components/schemas/AccountAliasRegistrationDetails'
          customer_account:
            $ref: "#/components/schemas/CustomerAccount"
          participant_user_details:
            $ref: "#/components/schemas/ParticipantUserDetails"
    ReferredDocumentInformation7:
      type: object
      properties:
        type:  # Tp
          $ref: '#/components/schemas/ReferredDocumentType4'
        number: # Nb
          $ref: '#/components/schemas/Max35Text'
        related_date: # RltdDt
          $ref: '#/components/schemas/ISODate'
    ReferredDocumentType3Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/DocumentType6Code'
    ReferredDocumentType4:
      type: object
      required:
      - code_or_proprietary
      properties:
        code_or_proprietary:  # CdOrPrtry
          $ref: '#/components/schemas/ReferredDocumentType3Choice'
    RefundTransactionModel:
      type: object
      required:
      - refund_txn_token
      properties:
        refund_txn_token:
          $ref: "#/components/schemas/RefundTransactionToken"
    RefundTransactionToken:
      description: A one-time token generated specifically to commit or rollback a
        specific refund transaction.
      type: string
    RemittanceAdviceResponse:
      description: Remittance Advice Response wrapper
      type: object
      properties:
        remittance_advice:
          $ref: '#/components/schemas/RemittanceAdviceV04'
    RemittanceAdviceV04:
      type: object
      required:
      - group_header
      - remittance_information
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader79'
        remittance_information: # RmtInf
          type: array
          items:
            $ref: '#/components/schemas/RemittanceInformation19'
          minItems: 1
          description: >-
            Remittance information data block.
            Usage -Notwithstanding the ISO 20022 multiplicity for this element only
            one single occurrence of this data block is allowed.
          maxItems: 1
    RemittanceAmount2:
      type: object
      properties:
        due_payable_amount:  # DuePyblAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
        adjustment_amount_and_reason: # AdjstmntAmtAndRsn
          type: array
          items:
            $ref: '#/components/schemas/DocumentAdjustment1'
          description: >-
            Wrapper element for detailed information on the adjusted amount and reason
            of the adjustment.
          maxItems: 1
        remitted_amount: # RmtdAmt
          $ref: '#/components/schemas/ActiveOrHistoricCurrencyAndAmount'
    RemittanceIdentification:
      description: Remittance identification reference at participant systems.
      type: string
      maxLength: 35
      example: ********
    RemittanceInformation16:
      type: object
      properties:
        unstructured:  # Ustrd
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
          x-code-size: 2001
        structured: # Strd
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: Remittance information in a structured form. Up to 5 block
            allowed.
          maxItems: 5
          x-code-size: 2002
    RemittanceInformation19:
      type: object
      required:
      - original_payment_information
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        unstructured: # Ustrd
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters.
          maxItems: 3
        structured: # Strd
          type: array
          items:
            $ref: '#/components/schemas/StructuredRemittanceInformation16'
          description: Remittance information in a structured form. Up to 5 block
            allowed.
          maxItems: 5
        original_payment_information: # OrgnlPmtInf
          $ref: '#/components/schemas/OriginalPaymentInformation8'
    RemittanceLocation5:
      type: object
      required:
      - remittance_location_details
      - references
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        remittance_location_details: # RmtLctnDtls
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocationData1'
          minItems: 1
          description: >-
            Elements used to provide information on the location and/or delivery of
            the remittance information.
          maxItems: 1
        references: # Refs
          $ref: '#/components/schemas/TransactionReferences5'
    RemittanceLocation7:
      type: object
      properties:
        remittance_identification:  # RmtId
          description: >-
            Unique identification, assigned by the originator, to unambiguously identify
            the remittance information within the message.
          type: string
          minLength: 1
          maxLength: 35
        remittance_location_details: # RmtLctnDtls
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocationData1'
          description: Set of elements used to provide information on the location
            and/or delivery of the remittance information.
          maxItems: 1
    RemittanceLocationAdviceV02:
      type: object
      required:
      - group_header
      - remittance_location
      properties:
        group_header:  # GrpHdr
          $ref: '#/components/schemas/GroupHeader79'
        remittance_location: # RmtLctn
          type: array
          items:
            $ref: '#/components/schemas/RemittanceLocation5'
          minItems: 1
          description: >-
            Elements used to provide information on the location and/or delivery of
            the remittance information.
          maxItems: 1
    RemittanceLocationData1:
      type: object
      properties:
        method:  # Mtd
          $ref: '#/components/schemas/RemittanceLocationMethod2Code'
        electronic_address: # ElctrncAdr
          $ref: '#/components/schemas/Max2048Text'
      required:
      - method
      - electronic_address
    RemittanceLocationMethod2Code:
      type: string
      description: >-
        Method used to deliver the remittance advice information. Restriction - only
        value URID is accepted.
      enum: ['URID']
      example: URID
      x-code-enum: 2000
    RemittanceLocationResponse:
      description: Remittance Advise Response wrapper
      type: object
      properties:
        remittance_location:
          $ref: '#/components/schemas/RemittanceLocationAdviceV02'

    # RemittanceAdviceRequest:
    #   description: Remittance Advice Request wrapper
    #   type: object
    #   required:
    #     - remittance_advice
    #     - clearing_system_reference
    #   properties:
    #     remittance_advice:
    #       $ref: '../../iso20022/RemittanceAdviceV04.yaml#/components/schemas/RemittanceAdviceV04'
    #     clearing_system_reference:
    #       #description: Payment or Request for Payment Reference
    #       $ref: "../common/v3.5/types.yaml#/ClearingSystemReference"

    # RemittanceLocationRequest:
    #   description: Remittance Advice Request wrapper
    #   type: object
    #   required:
    #   - remittance_location_advice
    #   - clearing_system_reference
    #   properties:
    #     remittance_location_advice:
    #       $ref: '../../iso20022/RemittanceLocationAdviceV02.yaml#/components/schemas/RemittanceLocationAdviceV02'
    #     clearing_system_reference:
    #       #description: Payment or Request for Payment Reference
    #       $ref: "../common/v3.5/types.yaml#/ClearingSystemReference"
    RetailName:
      description: Retail name. This is required for type 0 (retail)
      type: object
      required:
      - first_name
      - last_name
      properties:
        first_name:
          description: First name
          type: string
          minLength: 1
          maxLength: 100
          example: first_name
          x-code-notNull: 420
        middle_name:
          description: Middle name
          type: string
          minLength: 1
          maxLength: 100
          example: middle_name
        last_name:
          description: Last name
          type: string
          minLength: 1
          maxLength: 100
          example: last_name
          x-code-notNull: 421
      x-code-notNull: 422
    ReturnReason5Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalReturnReason1Code'
   # PAIN 013 #
    ReversePaymentResponse:
      description: Reverse Payment Response wrapper
      type: object
      properties:
        fi_to_fi_payment_status_report:
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
    RoutedNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via Participant Routing system
        properties:
          handle:
            type: string
            maxLength: 64
            description: The handle of the notification registration at the participant.
    SMSNotification:
      allOf:
      - $ref: '#/components/schemas/NotificationPreference'
      - type: object
        description: Notification via SMS
        required:
        - phone_number
        properties:
          phone_number:
            $ref: "#/components/schemas/PhoneNumber"
    ScheduleReference:
      description: The Interac-generated schedule reference number, also known as
        the clearing system reference.
      type: string
      maxLength: 35
      example: ********
    SendPaymentRequest:
      description: Send Payment Request wrapper
      required:
      - fi_to_fi_customer_credit_transfer
      - product_code
      - fraud_supplementary_info
      - account_holder_name
      properties:
        fi_to_fi_customer_credit_transfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'
        product_code:
          $ref: '#/components/schemas/ProductCode'
        expiry_date:
          $ref: "#/components/schemas/CustomDateTime"
        fi_account_id:
          description: Unique FI Identifier for the customer account (tokenized account).
          type: string
          minLength: 1
          maxLength: 50
        account_holder_name:
          description: Account Holder Name.
          type: string
          minLength: 1
          maxLength: 80
        sender_account_identifier:
          description: Sender's/Customer's account identifier as defined in the Creditor/Recipient
            system.
          type: string
          minLength: 1
          maxLength: 40
        fraud_supplementary_info:
          $ref: "#/components/schemas/SupplementaryInfo"
        payment_authentication:
          $ref: "#/components/schemas/PaymentAuthentication"
        language:
          $ref: "#/components/schemas/Language"
        fraud_case_id:
          $ref: "#/components/schemas/FraudCaseId"
        participant_fraud_instruction:
          $ref: "#/components/schemas/ParticipantFraudInstruction"
    SendPaymentResponse:
      description: Send Payment Response wrapper
      type: object
      required:
      - fi_to_fi_payment_status_report
      - payment_status
      - fraud_check_result
      properties:
        fi_to_fi_payment_status_report:
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
        payment_status:
          $ref: '#/components/schemas/PaymentStatus'
        fraud_check_result:
          $ref: "#/components/schemas/FraudCheckResult"
    ServiceType:
      description: >-
        Flag indicating the type of the Account Alias  registration
        <br/> EMAIL - means Email based account alias registration
        <br/> UUID  - means UUID based account alias registration. Multiple types
        of unique identifiers are supported.
        <br/> PHONE - means Phone based account alias registrations
      type: string
      enum: ['EMAIL', 'UUID', 'PHONE']
      example: EMAIL
    SettlementInstruction7:
      type: object
      required:
      - settlement_method
      properties:
        settlement_method:  # SttlmMtd
          $ref: '#/components/schemas/SettlementMethod1Code'
        clearing_system: # ClrSys
          $ref: '#/components/schemas/ClearingSystemIdentification3Choice'
    SettlementMethod1Code:
      type: string
      enum: ['CLRG']
      example: CLRG
    SignatureType:
      description: >-
        The type of the JWT. Required. Allowed values are "PAYLOAD_DIGEST_SHA256"
      type: string
      enum: ['PAYLOAD_DIGEST_SHA256']
      example: PAYLOAD_DIGEST_SHA256
    StatusHistory:
      description: status history
      type: object
      required:
      - transaction_date
      - payment_status
      properties:
        transaction_date:
          #description: Interac Transaction Timestamp associated with the status change
          $ref: "#/components/schemas/CustomDateTime"
        payment_status:
          $ref: "#/components/schemas/PaymentStatus"
        notification_status:
          # Notification Status shows the state of notification sent to the recipient/contact. It is returned if the money request had a notification sent to the recipient/contact to fulfill the money request.
          $ref: "#/components/schemas/NotificationStatus"
        status_memo:
          description: Status comment (e.g. cancellation reason)
          type: string
          maxLength: 420
        participant_reference:
          description: >-
            Participant Transaction Reference Number. This field only contains a value
            if a financial transaction has taken place, i.e. Send Transfer, Cancel
            Transfer or Complete Transfer.
          type: string
    StatusReason6Choice:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/ExternalStatusReason1Code'
        proprietary:
          $ref: '#/components/schemas/Max35Text'
    StatusReasonInformation12:
      type: object
      properties:
        reason: # Rsn
          $ref: '#/components/schemas/StatusReason6Choice'
        additional_information: # AddtlInf
          description: >-
            Additional status reason information in a free text format,
            Conditional - if Reason.Code above is NARR then this element is mandatory.
            Otherwise this element it is optional.
          type: array
          items:
            $ref: '#/components/schemas/Max105Text'
          maxItems: 1
    StructuredRemittanceInformation16:
      type: object
      properties:
        referred_document_information:  # RfrdDocInf
          type: array
          items:
            $ref: '#/components/schemas/ReferredDocumentInformation7'
          description: >-
            Provides detailed information on the cancellation status reason.
          maxItems: 1
        referred_document_amount: # RfrdDocAmt
          $ref: '#/components/schemas/RemittanceAmount2'
        creditor_reference_information: # CdtrRefInf
          $ref: '#/components/schemas/CreditorReferenceInformation2'
        invoicer: # Invcr
          $ref: '#/components/schemas/PartyIdentification135'
        invoicee: # Invcee
          $ref: '#/components/schemas/PartyIdentification135'
        additional_remittance_information: # AddtlRmtInf
          description: >-
            This element is used to provide additional information, in free text form.
            (e.g. invoice description. etc.).
          type: array
          items:
            $ref: '#/components/schemas/Max140Text'
          maxItems: 1
    SubmitPaymentRequest:
      type: object
      required:
      - transaction_id
      - clearing_system_reference
      - participant_transaction_date
      properties:
        transaction_id:
          #description: Must contain the transaction_id from the Initiate Payment request message. ( participantReferenceNumber ).
          $ref: "#/components/schemas/TransactionId"
        clearing_system_reference:
          #description: Interac-generated transfer reference number (i.e. ClrSysRef) that was provided in the Initiate Payment response message.
          $ref: "#/components/schemas/ClearingSystemReference"
        participant_transaction_date:
          #description: Participant Transaction Timestamp in UTC.
          $ref: "#/components/schemas/CustomDateTime"
    SubmitPaymentResponse:
      description: Submit Payment Response wrapper
      type: object
      required:
      - fi_to_fi_payment_status_report
      - payment_status
      properties:
        fi_to_fi_payment_status_report:
          $ref: '#/components/schemas/FIToFIPaymentStatusReportV10'
        payment_status:
          # This provides the status of the payment using the Interac proprietary codes. The following section contains the list of the Interac proprietary payment status codes and their mapping to the ISO 20022 transaction status codes.
          $ref: '#/components/schemas/PaymentStatus'
    SubmitPaymentReturn:
      type: object
      required:
      - participant_reference
      - participant_transaction_date
      properties:
        participant_reference:
          $ref: "#/components/schemas/TransactionId"
        clearing_system_reference:
          #description: Interac-generated transfer reference number (i.e. ClrSysRef) that was provided in the Initiate Payment return response message.
          $ref: "#/components/schemas/ClearingSystemReference"
        participant_transaction_date:
          #description: Participant Transaction Timestamp in UTC.
          $ref: "#/components/schemas/CustomDateTime"
    SupplementaryInfo:
      description: Additional data block required for Fraud or Compliance reasons.
        All these 5 elements are required. Any conditional exceptions must be discussed/established
        with the Interac Fraud team prior to implementation.
      type: object
      properties:
        customer_ip_address:
          description: Public IP Address used by the Customer during payment initiation.
          type: string
          minLength: 7
          maxLength: 64
          example: *******
        customer_card_number:
          description: >-
            Customer's (Sender's) card number associated with the account, either
            clear value or hashed value. Supported hash algorithm - SHA256
          type: string
          minLength: 1
          maxLength: 64
          example: ********90123456
        account_creation_date:
          $ref: '#/components/schemas/CustomDate'
        customer_device_finger_print:
          description: >-
            Unique device fingerprint. The following options/types are supported in
            this preferred priority. They must start with a prefix (ITM or FTM or
            UID or CID) followed by the value of the ************* session id/device
            id/cookie id
              Interac ThreatMetrix profiling session Id        - ITM*************
              FI ThreatMetrix profiling Session Id             - FTM*************
              Unique Device Identifier of device               - UID*************
              Cookie Id Placed at customers computer or device - CID**************
          type: string
          minLength: 1
          maxLength: 256
          example: ITM********90123
        customer_authentication_method:
          description: >-
            Authentication method option used to authenticate the customer (sender)
            prior to payment initiation.
            The following values are currently supported.
          type: string
          enum: ['PASSWORD', 'PVQ', 'FINGERPRINT', 'BIO_METRICS', 'OTP', 'NONE', 'OTHER']
          example: PASSWORD
    TransactionDateModel:
      description: Participant transaction timestamp in UTC
      type: object
      required:
      - txn_id
      - txn_date
      properties:
        txn_id:
          $ref: "#/components/schemas/TransactionIdentifier"
        txn_date:
          $ref: "#/components/schemas/CustomDateTime"
    TransactionId:
      description: TransactionIdentification reference of the payment. ( participantReferenceNumber
        )
      type: string
      maxLength: 35
      example: ********
    TransactionIdentifier:
      description: Unique Participant Transaction ID or Participant Reference for
        tracking and recovery purposes
      type: string
      maxLength: 35
    TransactionReferences5:
      type: object
      required:
      - end_to_end_identification
      properties:
        instruction_identification: # InstrId
          description: >-
            The InstructionIdentification reference of the transaction to which this
            remittance refers.
          type: string
          minLength: 1
          maxLength: 35
        end_to_end_identification: # EndToEndId
          description: >-
            The EndToEndIdentification reference of the transaction to which this
            remittance refers.
          type: string
          minLength: 1
          maxLength: 35
        transaction_identification: # TxId
          description: >-
            The TransactionIdentification reference of the transaction to which this
            remittance refers.
          type: string
          minLength: 1
          maxLength: 35
    TransactionStatus:
      description: >-
        Financial Transaction Status.<br/>
        SEND_PAYMENT_BEGIN  - Send Transfer Begin <br/>
        SEND_PAYMENT_COMMIT - Send Transfer Commit <br/>
        SEND_PAYMENT_ROLLBACK - Send Transfer Rollback <br/>
        CANCEL_PAYMENT_BEGIN - Cancel Transfer Begin <br/>
        CANCEL_PAYMENT_COMMIT -Cancel Transfer Commit <br/>
        CANCEL_PAYMENT_ROLLBACK -Cancel Transfer Rollback <br/>
        COMPLETE_PAYMENT_BEGIN -Complete Transfer Begin <br/>
        COMPLETE_PAYMENT_COMMIT -Complete Transfer Commit <br/>
        COMPLETE_PAYMENT_ROLLBACK -Complete Transfer Rollback <br/>
      type: string
      enum: ['SEND_PAYMENT_BEGIN', 'SEND_PAYMENT_COMMIT', 'SEND_PAYMENT_ROLLBACK',
        'CANCEL_PAYMENT_BEGIN', 'CANCEL_PAYMENT_COMMIT', 'CANCEL_PAYMENT_ROLLBACK',
        'COMPLETE_PAYMENT_BEGIN', 'COMPLETE_PAYMENT_COMMIT', 'COMPLETE_PAYMENT_ROLLBACK']
      example: SEND_PAYMENT_BEGIN
    TransactionTime:
      description: |
        A particular point in the progression of time defined and expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.ssssssZ).
      type: string
      format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
      example: '2020-01-23T12:34:56.123Z'
    TrueFalseIndicator:
      type: boolean
    UpdatePaymentAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      required:
      - authentication_type
      - security_question
      - hash_type
      - security_answer
      properties:
        authentication_type:
          $ref: "#/components/schemas/AuthenticationType"
        security_question:
          description: Question text. Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 40
        hash_type:
          $ref: "#/components/schemas/HashType"
        hash_salt:
          description: Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 44
        security_answer:
          description: >-
            'Answer to the security question (as provided by the customer) with leading
            and trailing whitespace trimmed, uppercase, postfixed with hashSalt if
            present, hashed using the algorithm identified by hashType and then Base64
            encoded. ISO-8859-1encoding to be used when the hash is generated. Required
            if authenticationType is 0.'
          type: string
          maxLength: 64
    ViewIncomingPaymentAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      properties:
        security_question:
          description: Question text. This will only be present if the authenticationRequired
            field indicates that payment authentication is required (authenticationRequired
            field is true).
          type: string
          maxLength: 40
        hash_type:
          $ref: "#/components/schemas/HashType"
        hash_salt:
          description: Required if authenticationRequired is true
          type: string
          maxLength: 44
    ViewPaymentAuthentication:
      description: Transfer authentication method to be used unless the transfer is
        created with another type of transfer authentication
      type: object
      required:
      - authentication_type
      - security_question
      properties:
        authentication_required:
          description: >-
            Transfer authentication required or Not <br/>
            false - Not Required <br/>
            true - Required <br/>
          type: boolean
        authentication_type:
          $ref: "#/components/schemas/AuthenticationType"
        security_question:
          description: Question text. Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 40
        hash_type:
          $ref: "#/components/schemas/HashType"
        hash_salt:
          description: Required if authenticationType is PAYMENT_LEVEL
          type: string
          maxLength: 44
servers:
- description: Production Environment
  url: https://etransfer-services.interac.ca/payment-api/v3.5.0
- description: FI Test Environment
  url: https://etransfer-services.fit.interac.ca/payment-api/v3.5.0
- description: Beta Test Environment
  url: https://etransfer-services.beta.interac.ca/payment-api/v3.5.0
