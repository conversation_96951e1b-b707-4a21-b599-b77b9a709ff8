package com.peoples.banking.api.fraud.notification.listener.v1.mapper;

import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.ACCOUNT_NUMBER;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.AMOUNT;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.CUSTOMER_NAME;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.CUSTOMER_REF_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.EMAIL_RECIPIENT;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.EMAIL_SENDER;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.EXTERNAL_REF_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.FINANCIAL_INSTITUTION_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.FIRST_NAME;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.FULLFILL_REQUEST_MOBILE;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.FULLFILL_REQUIEST_FOR_PAYMENT_EMAIL;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.INTERAC_ASSOCIATED_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.IP_ADDRESS;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.LAST_NAME;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.NOTES;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.PAYMENT_REFERENCE;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.REGISTRATION_NAME;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.RELATED_PAYMENT_REFERENCE;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.SENDER_FINANCIAL_INSTITUTION_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.SERVICE_ACCOUNT_REF_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.SMS_RECIPIENT;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.SMS_SENDER;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.buildDeviceInfo;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.buildNotificationDetails;
import static com.peoples.banking.util.api.common.mapper.converter.DateConverter.CRM_DATE_FORMAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequestData;
import com.peoples.banking.partner.domain.interac.notification.model.BusinessName;
import com.peoples.banking.partner.domain.interac.notification.model.Contact;
import com.peoples.banking.partner.domain.interac.notification.model.ContactType;
import com.peoples.banking.partner.domain.interac.notification.model.FraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.FraudInformation;
import com.peoples.banking.partner.domain.interac.notification.model.FraudStatus;
import com.peoples.banking.partner.domain.interac.notification.model.FraudType;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle.TypeEnum;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentFraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentStatus;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentType;
import com.peoples.banking.partner.domain.interac.notification.model.RetailName;
import com.peoples.banking.partner.domain.interac.notification.model.SenderData;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {CreateSafCaseRequestMapperImpl.class, DateConverter.class})
public class CreateSafCaseRequestMapperTest {

  public static final String REFERENCE_ID = "referenceId";
  private static final String INITIATED_DATE = "2021-01-02";
  private final OffsetDateTime currentUTCDateTime = DateUtil.getCurrentUTCDateTime();
  private final String crmDateString = currentUTCDateTime.format(CRM_DATE_FORMAT);

  @Autowired
  private CreateSafCaseRequestMapper mapper;

  @Test
  public void notificationDetailsAndPaymentDtoToCreateSafCaseRequest_outboundPayment_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), EXTERNAL_REF_ID, PaymentCdType.OUTBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(REFERENCE_ID, caseRequestData.getCrmUniqueId());
    assertEquals(AMOUNT.toString(), caseRequestData.getAmount());
    assertEquals(crmDateString, caseRequestData.getDateDeposited());
    assertEquals(crmDateString, caseRequestData.getDateExpires());
    assertEquals(INITIATED_DATE, caseRequestData.getDateInitiated());
    assertEquals(FraudStatus.CONFIRM_FRAUD.getValue(), caseRequestData.getFiFraudStatus());

    assertEquals(RELATED_PAYMENT_REFERENCE, caseRequestData.getPtcAssociatedId());
    assertEquals(EXTERNAL_REF_ID, caseRequestData.getPtcPaymentId());
    assertEquals(CUSTOMER_REF_ID, caseRequestData.getPtcCustomerId());
    assertEquals(PAYMENT_REFERENCE, caseRequestData.getInteracPaymentId());
    assertEquals(INTERAC_ASSOCIATED_ID, caseRequestData.getInteracAssociatedId());
    assertEquals(SERVICE_ACCOUNT_REF_ID, caseRequestData.getServiceAcctRefId());
    assertEquals("ACCOUNT_DEPOSIT_REGULAR", caseRequestData.getPaymentType());
    assertEquals(PaymentStatus.DECLINED.name(), caseRequestData.getPaymentStatus());
    assertEquals(FraudStatus.SUSPICIOUS.getValue(), caseRequestData.getPreviousFraudStatus());
    assertEquals(FraudType.FF_FRAUD.name(), caseRequestData.getFraudType());
    assertEquals(NOTES, caseRequestData.getNotes());
    assertEquals("INDIVIDUAL", caseRequestData.getCustomerType());
    //recipient data check
    assertEquals(FIRST_NAME + " " + LAST_NAME , caseRequestData.getRecipientName());
    assertEquals(EMAIL_RECIPIENT, caseRequestData.getRecipientEmail());
    assertEquals(SMS_RECIPIENT, caseRequestData.getRecipientPhoneNum());
    assertNull(caseRequestData.getRecipientAcctNum());
    assertNull(caseRequestData.getRecipientIp());
    assertEquals(FINANCIAL_INSTITUTION_ID, caseRequestData.getRecipientFinInstId());
    //sender data check
    assertEquals(REGISTRATION_NAME , caseRequestData.getSenderName());
    assertEquals(EMAIL_SENDER, caseRequestData.getSenderEmail());
    assertEquals(SMS_SENDER, caseRequestData.getSenderPhoneNum());
    assertEquals(ACCOUNT_NUMBER, caseRequestData.getSenderAcctNum());
    assertEquals(IP_ADDRESS, caseRequestData.getSenderIp());
    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());

  }

  @Test
  public void notificationDetailsAndPaymentDtoToCreateSafCaseRequest_inboundPayment_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), EXTERNAL_REF_ID, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(REFERENCE_ID, caseRequestData.getCrmUniqueId());
    assertEquals(AMOUNT.toString(), caseRequestData.getAmount());
    assertEquals(crmDateString, caseRequestData.getDateDeposited());
    assertEquals(crmDateString, caseRequestData.getDateExpires());
    assertEquals(INITIATED_DATE, caseRequestData.getDateInitiated());
    assertEquals(FraudStatus.CONFIRM_FRAUD.getValue(), caseRequestData.getFiFraudStatus());
    assertEquals(RELATED_PAYMENT_REFERENCE, caseRequestData.getPtcAssociatedId());
    assertEquals(EXTERNAL_REF_ID, caseRequestData.getPtcPaymentId());
    assertEquals(CUSTOMER_REF_ID, caseRequestData.getPtcCustomerId());
    assertEquals(PAYMENT_REFERENCE, caseRequestData.getInteracPaymentId());
    assertEquals(INTERAC_ASSOCIATED_ID, caseRequestData.getInteracAssociatedId());
    assertEquals(SERVICE_ACCOUNT_REF_ID, caseRequestData.getServiceAcctRefId());
    assertEquals("ACCOUNT_DEPOSIT_REGULAR", caseRequestData.getPaymentType());
    assertEquals(PaymentStatus.DECLINED.name(), caseRequestData.getPaymentStatus());
    assertEquals(FraudStatus.SUSPICIOUS.getValue(), caseRequestData.getPreviousFraudStatus());
    assertEquals(FraudType.FF_FRAUD.name(), caseRequestData.getFraudType());
    assertEquals(NOTES, caseRequestData.getNotes());
    assertEquals("INDIVIDUAL", caseRequestData.getCustomerType());
    //recipient data check
    assertEquals(FIRST_NAME + " " + LAST_NAME, caseRequestData.getRecipientName());
    String recipientEmail = findSentToNotification(notificationDetails, TypeEnum.EMAIL);
    String recipientPhone = findSentToNotification(notificationDetails, TypeEnum.SMS);
    assertEquals(recipientEmail, caseRequestData.getRecipientEmail());
    assertEquals(recipientPhone, caseRequestData.getRecipientPhoneNum());
    assertEquals(ACCOUNT_NUMBER, caseRequestData.getRecipientAcctNum());
    assertEquals(IP_ADDRESS, caseRequestData.getRecipientIp());
    assertEquals(FINANCIAL_INSTITUTION_ID, caseRequestData.getRecipientFinInstId());
    //sender data check
    assertEquals( REGISTRATION_NAME, caseRequestData.getSenderName());
    String senderEmail = findSenderInfoNotification(notificationDetails, TypeEnum.EMAIL);
    assertEquals(senderEmail, caseRequestData.getSenderEmail());
    String senderPhone = findSenderInfoNotification(notificationDetails, TypeEnum.SMS);
    assertEquals(senderPhone, caseRequestData.getSenderPhoneNum());
    assertNull(caseRequestData.getSenderAcctNum());
    assertNull(caseRequestData.getSenderIp());
    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());

  }

  private String findSentToNotification(NotificationDetails notificationDetails, TypeEnum type) {
    return notificationDetails.getEventPayload().getFraud().getFraudInfoPayload()
        .getPaymentFraudDetails().getPayment().getSentTo().getNotificationPreference()
        .stream().filter(p -> p.getType() == type).findFirst().map(NotificationHandle::getValue).orElse(null);
  }

  private String findSenderInfoNotification(NotificationDetails notificationDetails, TypeEnum type) {
    return notificationDetails.getEventPayload().getFraud().getFraudInfoPayload()
        .getPaymentFraudDetails().getPayment().getSenderInfo().getNotificationPreference()
        .stream().filter(p -> p.getType() == type).findFirst().map(NotificationHandle::getValue).orElse(null);
  }

  @Test
  public void notificationDetailsAndPaymentDtoToCreateSafCaseRequest_no_payment_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails,
        null, null, null, null, null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertNull(caseRequestData.getCrmUniqueId());
    assertEquals(AMOUNT.toString(), caseRequestData.getAmount());
    assertNull(caseRequestData.getDateDeposited());
    assertEquals(crmDateString, caseRequestData.getDateExpires());
    assertEquals(INITIATED_DATE, caseRequestData.getDateInitiated());
    assertEquals(FraudStatus.CONFIRM_FRAUD.getValue(), caseRequestData.getFiFraudStatus());
    assertNull(caseRequestData.getPtcAssociatedId());
    assertNull(caseRequestData.getPtcPaymentId());
    assertNull(caseRequestData.getPtcCustomerId());
    assertEquals(PAYMENT_REFERENCE, caseRequestData.getInteracPaymentId());
    assertEquals(INTERAC_ASSOCIATED_ID, caseRequestData.getInteracAssociatedId());
    assertNull(caseRequestData.getServiceAcctRefId());
    assertEquals("ACCOUNT_DEPOSIT_REGULAR", caseRequestData.getPaymentType());
    assertEquals(PaymentStatus.DECLINED.name(), caseRequestData.getPaymentStatus());
    assertEquals(FraudStatus.SUSPICIOUS.getValue(), caseRequestData.getPreviousFraudStatus());
    assertEquals(FraudType.FF_FRAUD.name(), caseRequestData.getFraudType());
    assertEquals(NOTES, caseRequestData.getNotes());
    assertEquals("INDIVIDUAL", caseRequestData.getCustomerType());
    //recipient data check
    assertEquals(FIRST_NAME + " " + LAST_NAME, caseRequestData.getRecipientName());
    assertEquals(EMAIL_RECIPIENT, caseRequestData.getRecipientEmail());
    assertEquals(SMS_RECIPIENT, caseRequestData.getRecipientPhoneNum());
    assertNull(caseRequestData.getRecipientAcctNum());
    assertNull(caseRequestData.getRecipientIp());
    assertEquals(FINANCIAL_INSTITUTION_ID, caseRequestData.getRecipientFinInstId());
    //sender data check
    assertEquals(REGISTRATION_NAME , caseRequestData.getSenderName());
    assertEquals(EMAIL_SENDER, caseRequestData.getSenderEmail());
    assertEquals(SMS_SENDER, caseRequestData.getSenderPhoneNum());
    assertNull(caseRequestData.getSenderAcctNum());
    assertNull(caseRequestData.getSenderIp());
    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());
  }

  @Test
  public void notificationDetailsAndPaymentDtoToCreateSafCaseRequest_fullfill_request_for_payment_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildFulfillRequestForPaymentNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(REGISTRATION_NAME, caseRequestData.getSenderName());
    assertEquals(EMAIL_SENDER, caseRequestData.getSenderEmail());
    assertEquals(SMS_SENDER, caseRequestData.getSenderPhoneNum());

    assertEquals(CUSTOMER_NAME, caseRequestData.getReqMoneyContactName());
    assertEquals(FULLFILL_REQUIEST_FOR_PAYMENT_EMAIL, caseRequestData.getReqMoneyContactEmail());
    assertEquals(FULLFILL_REQUEST_MOBILE, caseRequestData.getReqMoneyContactPhone());

    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());

  }

  @Test
  public void notificationDetailsAndPaymentNullToCreateSafCaseRequest_fullfill_request_for_payment_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildFulfillRequestForPaymentNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        null, RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(REGISTRATION_NAME, caseRequestData.getSenderName());
    assertEquals(EMAIL_SENDER, caseRequestData.getSenderEmail());
    assertEquals(SMS_SENDER, caseRequestData.getSenderPhoneNum());

    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());
  }

  @Test
  public void notificationDetailsAndPaymentToCreateSafCaseRequest_fullfill_request_for_payment_wrong_recipient_name_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildFulfillRequestForPaymentNotificationDetails();
    assertNotNull(mapper);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        null, RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertNull(caseRequestData.getPtcPaymentId());

    assertEquals(SENDER_FINANCIAL_INSTITUTION_ID , caseRequestData.getSenderFinInstId());
  }

  @Test
  public void notificationDetailsAndPaymentDtoToCreateSafCaseRequest_empty_date_success() {
    OffsetDateTime currentUTCDateTime = DateUtil.getCurrentUTCDateTime();
    NotificationDetails notificationDetails = NotificationTestUtil.buildFulfillRequestForPaymentNotificationDetails();
    assertNotNull(mapper);
    notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().setExpiryDate(null);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);

    assertEquals(crmDateString, caseRequestData.getDateDeposited());

  }

  @Test
  public void recipientName_empty_success() {
    OffsetDateTime currentUTCDateTime = DateUtil.getCurrentUTCDateTime();
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    assertNotNull(mapper);
    Contact sentTo = notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment()
        .getSentTo();
    String companyName = "Anjali";
    sentTo.setCustomerType(ContactType.SMALL_BUSINESS);
    BusinessName businessName = new BusinessName();
    businessName.setCompanyName(companyName);
    sentTo.getLegalName().setBusinessName(businessName);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(companyName, caseRequestData.getRecipientName());

  }

  @Test
  public void recipientName_empty_second_sample_success() {
    OffsetDateTime currentUTCDateTime = DateUtil.getCurrentUTCDateTime();
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    assertNotNull(mapper);
    Contact sentTo = notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment()
        .getSentTo();
    sentTo.setCustomerType(ContactType.RETAIL);
    RetailName retailName = new RetailName();
    String firstName = "Rafael";
    String lastName = "Frami";
    retailName.setFirstName(firstName);
    retailName.setLastName(lastName);
    sentTo.getLegalName().setRetailName(retailName);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.INBOUND);
    assertNotNull(safCaseRequest);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(firstName + " " +  lastName, caseRequestData.getRecipientName());
  }


  @Test
  public void checkPaymentStatusMapping_success() {
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    FraudDetails fraud = notificationDetails.getEventPayload().getFraud();
    assertNotNull(fraud);
    FraudInformation fraudInfoPayload = fraud.getFraudInfoPayload();
    assertNotNull(fraudInfoPayload);
    PaymentFraudDetails paymentFraudDetails = fraudInfoPayload.getPaymentFraudDetails();
    assertNotNull(paymentFraudDetails);
    paymentFraudDetails.setCurrentFraudStatus(FraudStatus.SUSPICIOUS);

    assertNotNull(mapper);

    checkPaymentStatusMapping(notificationDetails, "ALIAS_REGULAR", PaymentType.REGULAR_PAYMENT);
    checkPaymentStatusMapping(notificationDetails, "ALIAS_AUTODEPOSIT", PaymentType.ACCOUNT_ALIAS_PAYMENT);
    checkPaymentStatusMapping(notificationDetails, "REQUEST_FULFILLMENT", PaymentType.FULFILL_REQUEST_FOR_PAYMENT);
    checkPaymentStatusMapping(notificationDetails, "ALIAS_REALTIME", PaymentType.REALTIME_ACCOUNT_ALIAS_PAYMENT);
    checkPaymentStatusMapping(notificationDetails, "ACCOUNT_DEPOSIT_REGULAR", PaymentType.ACCOUNT_DEPOSIT_PAYMENT);
    checkPaymentStatusMapping(notificationDetails, "ACCOUNT_DEPOSIT_REALTIME", PaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT);

  }

  @Test
  protected void checkJsonSampleMapping () {
    NotificationDetails notificationDetails = buildNotificationDetails();
    assertNotNull(notificationDetails);
    String senderName = "Smith, Ziemann and Bartoletti";
    String senderEmail = "<EMAIL>";
    String senderPhone = "******-662-9603";
    String recipientEmail = "<EMAIL>";
    SenderData senderInfo = notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment()
        .getSenderInfo();
    senderInfo.setRegistrationName(senderName);
    senderInfo.getNotificationPreference().get(0).setValue(senderEmail);
    senderInfo.getNotificationPreference().get(1).setValue(senderPhone);
    Contact contact = notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment()
        .getSentTo();
    String recipientFirstName = "Theron";
    contact.getLegalName().getRetailName().setFirstName(recipientFirstName);
    String recipientLastName = "Russel";
    contact.getLegalName().getRetailName().setLastName(recipientLastName);
    String recipientPhone = "******-357-8978";
    contact.getNotificationPreference().get(0).setValue(recipientEmail);
    contact.getNotificationPreference().get(1).setValue(recipientPhone);

    CreateSafCaseRequest safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails,
        null, null, null, null, null, PaymentCdType.INBOUND);
    CreateSafCaseRequestData safCaseRequestData = safCaseRequest.getData();
    assertEquals(senderName, safCaseRequestData.getSenderName());
    assertEquals(senderEmail, safCaseRequestData.getSenderEmail());
    assertEquals(senderPhone, safCaseRequestData.getSenderPhoneNum());


    assertEquals(recipientFirstName + " " + recipientLastName, safCaseRequestData.getRecipientName());
    assertEquals(recipientEmail, safCaseRequestData.getRecipientEmail());
    assertEquals(recipientPhone, safCaseRequestData.getRecipientPhoneNum());
  }


  private void checkPaymentStatusMapping(NotificationDetails notificationDetails, String expectedStatus, PaymentType paymentType) {
    OffsetDateTime currentUTCDateTime = DateUtil.getCurrentUTCDateTime();
    CreateSafCaseRequest safCaseRequest;
    FraudDetails fraudDetails = notificationDetails.getEventPayload().getFraud();
    assertNotNull(fraudDetails);
    PaymentFraudDetails paymentFraudDetails = fraudDetails.getFraudInfoPayload().getPaymentFraudDetails();
    assertNotNull(paymentFraudDetails);
    paymentFraudDetails.getPayment().setPaymentType(paymentType);
    safCaseRequest = mapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(REFERENCE_ID, notificationDetails,
        NotificationTestUtil.buildPayments(), RELATED_PAYMENT_REFERENCE, currentUTCDateTime, buildDeviceInfo(), null, PaymentCdType.OUTBOUND);
    CreateSafCaseRequestData caseRequestData = safCaseRequest.getData();
    assertNotNull(caseRequestData);
    assertEquals(expectedStatus, caseRequestData.getPaymentType());
  }


}