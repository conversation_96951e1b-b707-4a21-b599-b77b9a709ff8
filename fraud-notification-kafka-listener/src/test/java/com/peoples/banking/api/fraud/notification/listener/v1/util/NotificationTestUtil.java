package com.peoples.banking.api.fraud.notification.listener.v1.util;

import static com.peoples.banking.partner.domain.interac.notification.model.PaymentType.FULFILL_REQUEST_FOR_PAYMENT;

import com.peoples.banking.adapter.base.type.ServiceAccountEndpointType;
import com.peoples.banking.domain.serviceaccount.model.ApiEndpoint;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse.OutboundApiAuthEnum;
import com.peoples.banking.partner.domain.interac.notification.model.AmountWithCurrency;
import com.peoples.banking.partner.domain.interac.notification.model.Contact;
import com.peoples.banking.partner.domain.interac.notification.model.ContactType;
import com.peoples.banking.partner.domain.interac.notification.model.CustomerType;
import com.peoples.banking.partner.domain.interac.notification.model.EventDetails;
import com.peoples.banking.partner.domain.interac.notification.model.FraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.FraudInformation;
import com.peoples.banking.partner.domain.interac.notification.model.FraudStatus;
import com.peoples.banking.partner.domain.interac.notification.model.FraudType;
import com.peoples.banking.partner.domain.interac.notification.model.LegalName;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationIntendedForEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationTriggerTypeEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle.TypeEnum;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentFraudDetails;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentStatus;
import com.peoples.banking.partner.domain.interac.notification.model.PaymentType;
import com.peoples.banking.partner.domain.interac.notification.model.RetailName;
import com.peoples.banking.partner.domain.interac.notification.model.SenderData;
import com.peoples.banking.persistence.payment.entity.DeviceInfo;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.DateUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NotificationTestUtil {

  public static final String PAYMENT_REFERENCE = "AABBC123";
  public static final String RELATED_PAYMENT_REFERENCE = "AABBC123_RELATED";
  public static final String CUSTOMER_EXTERNAL_ID = "ASDF";
  public static final String CUSTOMER_REF_ID = "CUSTOMER_REF_ID";
  public static final String EXTERNAL_REF_ID = "external_ref_id";
  public static final String NETWORK_PAYMENT_REF_ID = "network_payment_ref_id";
  public static final FraudStatus CURRENT_FRAUD_STATUS = FraudStatus.CONFIRM_FRAUD;
  public static final String IP_ADDRESS = "*********";
  public static final BigDecimal AMOUNT = BigDecimal.TEN;
  public static final String PARTICIPANT_PAYMENT_REFERENCE = "participant_payment_reference";
  public static final String CUSTOMER_NAME = "Fullfill_Jasonwang";
  public static final String ACCOUNT_NUMBER = "1111";
  public static final String EMAIL_RECIPIENT = "<EMAIL>";
  public static final String SMS_RECIPIENT = "123-456-78";
  public static final String EMAIL_SENDER = "<EMAIL>";
  public static final String SMS_SENDER = "123-456-780";
  public static final String NETWORK_ENROLLMENT_ID = "network_enrollment_id";
  public static final String SERVICE_ACCOUNT_REF_ID = "SERVICE_ACCOUNT_REF_ID";
  public static final String INTERAC_ASSOCIATED_ID = "INTERAC_ASSOCIATED_ID";
  public static final String NOTES = "Boogey-man will be back and he will find you";
  public static final String FIRST_NAME = "John";
  public static final String MIDDLE_NAME = "Boogey-man";
  public static final String LAST_NAME = "Wick";
  public static final String REGISTRATION_NAME = "King of homeless";
  public static final String FINANCIAL_INSTITUTION_ID = "FINANCIAL_INSTITUTION_ID";
  public static final String SENDER_FINANCIAL_INSTITUTION_ID = "SENDER_FINANCIAL_INSTITUTION_ID";
  public static final String NAME_AS_JSON =
      "{\"displayName\":\"" + CUSTOMER_NAME + "\",\"individualNameDetail\":{\"firstName\":\"John\",\"lastName\":\"Antman\"}}";

  public static final String EMAIL_AS_JSON =
      "[{\"id\":\"e01\",\"type\":\"PRIMARY\",\"email\":\"" + EMAIL_SENDER + "\",\"enableNotification\":true},{\"id\":\"e02\",\"type\":\"SECONDARY\",\"email\":\"<EMAIL>\",\"enableNotification\":false}]";
  public static final String FULLFILL_REQUIEST_FOR_PAYMENT_EMAIL = "<EMAIL>";
  public static final String FULLFILL_REQUEST_MOBILE = "0074165551234";


  public static NotificationDetails  buildNotificationDetails() {
    NotificationDetails notificationDetails = new NotificationDetails();
    notificationDetails.setNotificationTriggerType(NotificationTriggerTypeEnum.PARTICIPANT_MANDATED);
    //event details
    notificationDetails.setEvent(EventEnum.FRAUD_PAYMENT_REPORTED);
    notificationDetails.setEventTimestamp(DateUtil.getCurrentUTCDateTime());

    //intended for
    notificationDetails.setNotificationIntendedFor(NotificationIntendedForEnum.PARTICIPANT_FI);

    notificationDetails.setEventPayload(buildEventPayload());

    //Notification handle part
    NotificationHandle contactNotificationHandle = new NotificationHandle();
    contactNotificationHandle.setType(TypeEnum.EMAIL);
    contactNotificationHandle.setValue("<EMAIL>");
    notificationDetails.setContactNotificationHandle(contactNotificationHandle);
    return notificationDetails;
  }

  public static NotificationDetails  buildFulfillRequestForPaymentNotificationDetails() {
    NotificationDetails notificationDetails = buildNotificationDetails();
    notificationDetails.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().setPaymentType(FULFILL_REQUEST_FOR_PAYMENT);

    return notificationDetails;
  }

  private static EventDetails buildEventPayload() {
    EventDetails eventDetails = new EventDetails();
    eventDetails.setPayment(buildPaymentDetails());
    FraudDetails fraud = buildFraud();
    eventDetails.setFraud(fraud);
    return eventDetails;
  }

  private static FraudDetails buildFraud() {
    FraudDetails fraud = new FraudDetails();
    FraudInformation fraudInfoPayload = new FraudInformation();
    PaymentFraudDetails paymentFraudDetails = new PaymentFraudDetails();
    paymentFraudDetails.setPayment(buildPaymentDetails());
    paymentFraudDetails.setCurrentFraudStatus(CURRENT_FRAUD_STATUS);
    paymentFraudDetails.setPreviousFraudStatus(FraudStatus.SUSPICIOUS);
    paymentFraudDetails.setCurrentFraudType(FraudType.FF_FRAUD);
    paymentFraudDetails.setAdditionalNotes(NOTES);
    paymentFraudDetails.setReceivingParticipantId(FINANCIAL_INSTITUTION_ID);
    paymentFraudDetails.setSendingParticipantId(SENDER_FINANCIAL_INSTITUTION_ID);
    fraudInfoPayload.setPaymentFraudDetails(paymentFraudDetails);
    fraud.setFraudInfoPayload(fraudInfoPayload);
    fraud.setCustomerType(CustomerType.RETAIL);
    return fraud;
  }

  private static PaymentDetails buildPaymentDetails() {
    PaymentDetails paymentDetails = new PaymentDetails();
    paymentDetails.setPaymentType(PaymentType.ACCOUNT_DEPOSIT_PAYMENT);
    paymentDetails.setPaymentReference(PAYMENT_REFERENCE);
    paymentDetails.setStatus(PaymentStatus.DECLINED);
    paymentDetails.setRequestForPaymentReference(INTERAC_ASSOCIATED_ID);
    paymentDetails.setExpiryDate(DateUtil.getCurrentUTCDateTime());
    paymentDetails.setCreationDate(OffsetDateTime.of(2021, 1, 2, 3,4,5, 6, ZoneOffset.UTC));
    paymentDetails.setParticipantPaymentReference(PARTICIPANT_PAYMENT_REFERENCE);
    paymentDetails.sentTo(buildContact());
    paymentDetails.senderInfo(buildSenderData());
    AmountWithCurrency amount = new AmountWithCurrency();
    amount.setAmount(AMOUNT);
    paymentDetails.setAmount(amount);
    return paymentDetails;
  }

  private static SenderData buildSenderData() {
    SenderData senderData = new SenderData();
    senderData.notificationPreference(buildNotificationHandle(EMAIL_SENDER, SMS_SENDER));
    senderData.setRegistrationName(REGISTRATION_NAME);
    return senderData;
  }

  private static Contact buildContact() {
    Contact contact = new Contact();
    LegalName legalName = new LegalName();
    RetailName retailName = new RetailName();
    retailName.setFirstName(FIRST_NAME);
    retailName.setLastName(LAST_NAME);
    retailName.setMiddleName(MIDDLE_NAME);
    legalName.setRetailName(retailName);
    contact.setLegalName(legalName);
    contact.setNotificationPreference(buildNotificationHandle(EMAIL_RECIPIENT, SMS_RECIPIENT));
    contact.setCustomerType(ContactType.RETAIL);
    return contact;
  }

  private static List<NotificationHandle> buildNotificationHandle(String emailAddress, String phone) {
    List<NotificationHandle> notificationHandles = new ArrayList<>();
    NotificationHandle notificationHandleEmail = new NotificationHandle();
    notificationHandleEmail.setType(TypeEnum.EMAIL);
    notificationHandleEmail.setValue(emailAddress);
    notificationHandles.add(notificationHandleEmail);

    NotificationHandle notificationHandleSms = new NotificationHandle();
    notificationHandleSms.setType(TypeEnum.SMS);
    notificationHandleSms.setValue(phone);
    notificationHandles.add(notificationHandleSms);
    return notificationHandles;
  }

  public static Payments buildPayments() {
    Payments payments = new Payments();
    DeviceInfo deviceInfo = new DeviceInfo();
    deviceInfo.setIpAddress(IP_ADDRESS);
    payments.setDeviceInfo(deviceInfo);
    payments.setAccountNumber(ACCOUNT_NUMBER);
    payments.setExternalRefId(EXTERNAL_REF_ID);
    payments.setNetworkPaymentRefId(NETWORK_PAYMENT_REF_ID);
    payments.setServiceAccountRefId(SERVICE_ACCOUNT_REF_ID);
    payments.setCustomerRefId(CUSTOMER_REF_ID);
    payments.setContactJson("{\"name\": \"" + CUSTOMER_NAME + "\", \"email\": \"" + FULLFILL_REQUIEST_FOR_PAYMENT_EMAIL
        + "\", \"mobile\": \"" + FULLFILL_REQUEST_MOBILE + "\"}");
    payments.setExpiryDate(LocalDateTime.now());
    return payments;
  }

  public static ServiceAccountResponse buildServiceAccountResponse(String serviceAccountApiToken, String outApiToken,
      String serviceAccountRefId) {
    ServiceAccountResponse serviceAccountResponse = new ServiceAccountResponse();
    serviceAccountResponse.setRefId(serviceAccountRefId);
    serviceAccountResponse.setInboundApiToken(serviceAccountApiToken);
    serviceAccountResponse.setOutboundApiToken(outApiToken);
    serviceAccountResponse.setName("Test ServiceAccount");
    serviceAccountResponse.settlementAccountNumber("001-12345-*************");
    serviceAccountResponse.setAccountNumberRange("621-16001-88[\\d]{10,22}");
    serviceAccountResponse.setLimitGroupId("PRODVAL");
    serviceAccountResponse.setOutboundApiAuth(OutboundApiAuthEnum.BASIC_AUTH);
    serviceAccountResponse.setOutboundApiAuthToken("U0EyOmJhbmFuYQ==");

    Map<String, ApiEndpoint> apiEndpointMap = new HashMap<>();
    ApiEndpoint elgty = new ApiEndpoint();
    elgty.setUrl("http://localhost:8080/account/{account_num}/eligibility");
    apiEndpointMap.put(ServiceAccountEndpointType.ELIGIBILITY.toString(), elgty);

    ApiEndpoint trns = new ApiEndpoint();
    trns.setUrl("http://localhost:8080/account/{account_num}/transaction");
    apiEndpointMap.put(ServiceAccountEndpointType.TRANSACTION.toString(), trns);

    ApiEndpoint rvs = new ApiEndpoint();
    rvs.setUrl("http://localhost:8080/account/{account_num}/{transaction_id}/reversal");
    apiEndpointMap.put(ServiceAccountEndpointType.REVERSAL.toString(), rvs);

    serviceAccountResponse.setApiEndpoints(apiEndpointMap);
    serviceAccountResponse.setStatus(ServiceAccountResponse.StatusEnum.ENABLED);
    serviceAccountResponse.setCreatedDate(DateUtil.getCurrentUTCDateTime());
    serviceAccountResponse.setUpdatedDate(DateUtil.getCurrentUTCDateTime());
    return serviceAccountResponse;
  }

  public static DeviceInfo buildDeviceInfo() {
    DeviceInfo deviceInfo = new DeviceInfo();
    deviceInfo.setIpAddress(IP_ADDRESS);
    return deviceInfo;
  }
}
