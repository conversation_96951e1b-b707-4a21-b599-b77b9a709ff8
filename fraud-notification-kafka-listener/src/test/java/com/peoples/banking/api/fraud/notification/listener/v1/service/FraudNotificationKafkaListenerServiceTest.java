package com.peoples.banking.api.fraud.notification.listener.v1.service;

import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.EXTERNAL_REF_ID;
import static com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil.SERVICE_ACCOUNT_REF_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerNotRetryableException;
import com.peoples.banking.api.fraud.notification.listener.v1.mapper.CreateSafCaseRequestMapper;
import com.peoples.banking.api.fraud.notification.listener.v1.util.NotificationTestUtil;
import com.peoples.banking.partner.crm.adapter.CrmRestAdapter;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequestData;
import com.peoples.banking.partner.domain.interac.notification.model.*;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.DeviceInfoRepository;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FraudNotificationKafkaListenerServiceTest {

  @InjectMocks
  private FraudNotificationKafkaListenerService fraudNotificationKafkaListenerService;

  @Mock
  private PaymentsRepository paymentsRepository;
  @Mock
  private CreateSafCaseRequestMapper createSafCaseRequestMapper;

  @Mock
  private CrmRestAdapter crmRestAdapter;
  @Mock
  private DeviceInfoRepository deviceInfoRepository;
  @Mock
  private ServiceAccountAdapter serviceAccountAdapter;
  @Mock
  private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
  @Mock
  private CustomersRepository customersRepository;
  @Mock
  private AliasesRepository aliasesRepository;

  @Captor
  private ArgumentCaptor<CreateSafCaseRequest> captor;

  private final NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
  private final CreateSafCaseRequest createSafCaseRequest = new CreateSafCaseRequest();
  private final Payments payments = NotificationTestUtil.buildPayments();

  @BeforeEach
  public void setup() throws Exception {
    when(paymentsRepository.findByNetworkPaymentRefIdAndNetworkPaymentTypeAndTypeCd(anyString(), anyString(), any())).thenReturn(
        Optional.of(payments));

    when(serviceAccountAdapter.retrieveServiceAccountByRefId(SERVICE_ACCOUNT_REF_ID)).thenReturn(
        NotificationTestUtil.buildServiceAccountResponse("serviceAccountApiToken",
            "outApiToken", "serviceAccountRefId"));

    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails, payments,
        payments.getExternalRefId(),
        null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails, payments, null,
        null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);

  }

  @Test
  void consumeMessage_emptyDepositedDate_success() throws Exception {
    fraudNotificationKafkaListenerService.consumeMessage(notificationDetails);
    verify(crmRestAdapter).createFasCase(captor.capture());
    assertEquals(createSafCaseRequest, captor.getValue());
  }

  @Test
  void consumeMessage_payment_not_found_success() throws Exception {
    when(paymentsRepository.findByNetworkPaymentRefIdAndNetworkPaymentTypeAndTypeCd(anyString(), anyString(), any())).thenReturn(
        Optional.empty());
    NotificationDetails notificationDetails = NotificationTestUtil.buildNotificationDetails();
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails, null,
        null, null, null, null, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);

    fraudNotificationKafkaListenerService.consumeMessage(notificationDetails);

    verify(crmRestAdapter).createFasCase(captor.capture());
    assertEquals(createSafCaseRequest, captor.getValue());
  }


  @Test
  void consumeMessage_fulfillPaymentRequest_success() throws Exception {
    when(paymentsRepository.findByNetworkPaymentRefIdAndNetworkPaymentTypeAndTypeCd(anyString(), anyString(), any())).thenReturn(
        Optional.empty());
    NotificationDetails notificationDetails = NotificationTestUtil.buildFulfillRequestForPaymentNotificationDetails();
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, notificationDetails, null,
        null, null, null, null, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);

    fraudNotificationKafkaListenerService.consumeMessage(notificationDetails);

    verify(crmRestAdapter).createFasCase(captor.capture());
    assertEquals(createSafCaseRequest, captor.getValue());
  }

  @Test
  void consumeMessage_Empty_fail() {
    KafkaListenerNotRetryableException exception = assertThrows(KafkaListenerNotRetryableException.class, () -> {
      fraudNotificationKafkaListenerService.consumeMessage(new NotificationDetails());
    });
    assertNotNull(exception);
  }

  @Test
  void consumeMessage_emptyEvent_fail() {
    KafkaListenerNotRetryableException exception = assertThrows(KafkaListenerNotRetryableException.class, () -> {
      NotificationDetails message = new NotificationDetails();
      message.setEventPayload(new EventDetails());
      fraudNotificationKafkaListenerService.consumeMessage(message);
    });
    assertNotNull(exception);
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "fraud.payment.fraud_status_updated",
      "fraud.payment.funds_recovered",
      "fraud.payment.funds_recovery_request",
      "fraud.payment.reported"
  })
  void consumeMessage_differentEventTypes_success(String eventType) throws Exception {
    NotificationDetails message = NotificationTestUtil.buildNotificationDetails();
    message.setEvent(EventEnum.fromValue(eventType));
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, message, payments,
        null, null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);
    checkProcessingOfNotificationDetails(message);
  }

  private void checkProcessingOfNotificationDetails(NotificationDetails message)
      throws ResponseException, AdapterException, TBDException, TimeoutException {
    FraudInformation fraudInformation = message.getEventPayload().getFraud().getFraudInfoPayload();
    AccountAliasRegistrationFraudDetails accountAliasFraudDetails = new AccountAliasRegistrationFraudDetails();
    AccountAliasRegistrationDetails accountAliasRegistrations = new AccountAliasRegistrationDetails();

    accountAliasRegistrations.setAccountAliasReference("account_alias_reference");
    accountAliasFraudDetails.setAccountAliasRegistrations(accountAliasRegistrations);
    fraudInformation.setAccountAliasFraudDetails(accountAliasFraudDetails);
    fraudInformation.getAccountAliasFraudDetails().getAccountAliasRegistrations().setAccountAliasReference("aliasRefId");

    RequestforPaymentFraudDetails requestForPaymentFraudDetails = new RequestforPaymentFraudDetails();
    RequestForPaymentDetails requestForPayments = new RequestForPaymentDetails();
    requestForPayments.setRequestReference("request_reference");
    requestForPaymentFraudDetails.setRequestForPayment(requestForPayments);
    fraudInformation.setRequestForPaymentFraudDetails(requestForPaymentFraudDetails);
    fraudInformation.getRequestForPaymentFraudDetails().getRequestForPayment().setRequestReference("requestPaymentRefId");
    message.getEventPayload().setAccountAliasRegistrations(accountAliasRegistrations);
    message.getEventPayload().setRequestForPayments(requestForPayments);
    PaymentScheduleDetails paymentSchedule = new PaymentScheduleDetails();
    paymentSchedule.setScheduleReference("scheduled_reference");
    message.getEventPayload().setPaymentSchedule(paymentSchedule);
    RequestForPaymentScheduleDetails requestToPaySchedule = new RequestForPaymentScheduleDetails();
    requestToPaySchedule.setScheduleReference("schedule_reference");
    message.getEventPayload().setRequestToPaySchedule(requestToPaySchedule);

    fraudNotificationKafkaListenerService.consumeMessage(message);
    verify(crmRestAdapter).createFasCase(captor.capture());
    assertEquals(createSafCaseRequest, captor.getValue());
  }

  @ParameterizedTest
  @EnumSource(FraudType.class)
  void consumeMessage_differentFraudTypes_success(FraudType fraudType) throws Exception {
    NotificationDetails message = NotificationTestUtil.buildNotificationDetails();
    message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().setCurrentFraudType(fraudType);
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, message, payments,
        null, null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);
    checkProcessingOfNotificationDetails(message);
  }

  @Test
  void consumeMessage_enhanceFasServiceAccountRefIdByIdentification_success() throws Exception {
    createSafCaseRequest.setData(new CreateSafCaseRequestData());

    NotificationDetails message = NotificationTestUtil.buildNotificationDetails();
    FraudInformation fraudInfoPayload = message.getEventPayload().getFraud().getFraudInfoPayload();
    PaymentFraudDetails paymentFraudDetails = new PaymentFraudDetails();
    PaymentDetails payment = new PaymentDetails();
    FIToFIPaymentStatusReportV10 fiToFiPaymentStatusReport = getFiToFIPaymentStatusReportV10();
    payment.setFiToFiPaymentStatusReport(fiToFiPaymentStatusReport);
    payment.setPaymentReference("payment_reference");
    paymentFraudDetails.setPayment(payment);
    fraudInfoPayload.setPaymentFraudDetails(paymentFraudDetails);

    Aliases aliases = new Aliases();
    int customerId = 9999;
    aliases.setCustomerId(customerId);
    when(aliasesRepository.findByNetworkAliasRefId(anyString())).thenReturn(List.of(aliases));
    Customers customers = new Customers();
    customers.setServiceAccountRefId("SERVICE_ACCOUNT_REF_ID");
    when(customersRepository.findById(customerId)).thenReturn(Optional.of(customers));

    message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().setPaymentType(PaymentType.ACCOUNT_ALIAS_PAYMENT);
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, message, payments,
        null, null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);
    checkProcessingOfNotificationDetails(message);
  }

  @Test
  void consumeMessage_enhanceFasServiceAccountRefIdByCustomerEmail_success() throws Exception {
    createSafCaseRequest.setData(new CreateSafCaseRequestData());

    NotificationDetails message = NotificationTestUtil.buildNotificationDetails();
    FraudInformation fraudInfoPayload = message.getEventPayload().getFraud().getFraudInfoPayload();
    PaymentFraudDetails paymentFraudDetails = new PaymentFraudDetails();
    PaymentDetails payment = new PaymentDetails();
    payment.setPaymentReference("payment_reference");

    Contact sentTo = new Contact();
    NotificationHandle notificationHandle = new NotificationHandle();
    String notificationEmail = "<EMAIL>";
    notificationHandle.setValue(notificationEmail);
    sentTo.setNotificationPreference(List.of(notificationHandle));
    payment.setSentTo(sentTo);
    paymentFraudDetails.setPayment(payment);
    fraudInfoPayload.setPaymentFraudDetails(paymentFraudDetails);
    when(customersRepository.findServiceAccountByEmail(notificationEmail)).thenReturn("SERVICE_ACCOUNT_REF_ID");

    message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().setPaymentType(PaymentType.ACCOUNT_ALIAS_PAYMENT);
    when(createSafCaseRequestMapper.notificationDetailsAndPaymentsToCreateSafCaseRequest(null, message, payments,
        null, null, null, EXTERNAL_REF_ID, PaymentCdType.OUTBOUND)).thenReturn(createSafCaseRequest);
    checkProcessingOfNotificationDetails(message);
  }

  private static FIToFIPaymentStatusReportV10 getFiToFIPaymentStatusReportV10() {
    FIToFIPaymentStatusReportV10 fiToFiPaymentStatusReport = new FIToFIPaymentStatusReportV10();
    PaymentTransaction110 paymentTransaction110 = new PaymentTransaction110();
    OriginalTransactionReference28 originalTransactionReference = new OriginalTransactionReference28();
    CashAccount38 creditorAccount = new CashAccount38();
    AccountIdentification4Choice identification = new AccountIdentification4Choice();
    GenericAccountIdentification1 other = new GenericAccountIdentification1();
    other.setIdentification("CUSTOMER_IDENTIFICATION");
    identification.setOther(other);
    creditorAccount.setIdentification(identification);
    originalTransactionReference.setCreditorAccount(creditorAccount);
    paymentTransaction110.setOriginalTransactionReference(originalTransactionReference);
    fiToFiPaymentStatusReport.setTransactionInformationAndStatus(List.of(paymentTransaction110));
    return fiToFiPaymentStatusReport;
  }
}