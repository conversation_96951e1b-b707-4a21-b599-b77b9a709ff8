package com.peoples.banking.api.fraud.notification.listener.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class FraudNotificationKafkaListenerProperty {

  @Value("${push.notification.api.timetolive}")
  private int timeToLive;
  @Value("${kafka.notificationTopic}")
  private String kafkaNotificationTopic;
  @Value("${kafka.fraud.group.id}")
  private String fraudConsumerGroup;
}
