package com.peoples.banking.api.fraud.notification.listener.v1.service;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.pb.serviceaccount.ServiceAccountAdapter;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerNotRetryableException;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerRetryableException;
import com.peoples.banking.api.fraud.notification.listener.v1.mapper.CreateSafCaseRequestMapper;
import com.peoples.banking.domain.serviceaccount.model.ServiceAccountResponse;
import com.peoples.banking.partner.crm.adapter.CrmRestAdapter;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.interac.notification.model.*;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.NotificationIntendedForEnum;
import com.peoples.banking.persistence.customer.entity.Aliases;
import com.peoples.banking.persistence.customer.entity.Customers;
import com.peoples.banking.persistence.customer.repository.AliasesRepository;
import com.peoples.banking.persistence.customer.repository.CustomersRepository;
import com.peoples.banking.persistence.payment.entity.DeviceInfo;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.PaymentStatusHistory;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.persistence.payment.repository.DeviceInfoRepository;
import com.peoples.banking.persistence.payment.repository.PaymentStatusHistoryRepository;
import com.peoples.banking.persistence.payment.repository.PaymentsRepository;
import com.peoples.banking.util.api.common.DateUtil;
import com.peoples.banking.util.api.common.config.APICommonUtilConstant;
import com.peoples.banking.util.api.common.type.PaymentStatus;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import static com.peoples.banking.partner.domain.interac.notification.model.PaymentType.*;

@Service
@Log4j2
public class FraudNotificationKafkaListenerService {

    @Autowired
    private ServiceAccountAdapter serviceAccountAdapter;
    @Autowired
    private PaymentsRepository paymentsRepository;

    @Autowired
    private CreateSafCaseRequestMapper createSafCaseRequestMapper;
    @Autowired
    private CrmRestAdapter crmAdapter;
    @Autowired
    private PaymentStatusHistoryRepository paymentStatusHistoryRepository;
    @Autowired
    private DeviceInfoRepository deviceInfoRepository;
    @Autowired
    private CustomersRepository customersRepository;
    @Autowired
    private AliasesRepository aliasesRepository;

    /**
     * Main method to process the notification.
     *
     * @param message notification event.
     */
    @Transactional(readOnly = true, noRollbackFor = Exception.class)
    public void consumeMessage(NotificationDetails message) {
        if (message.getEventPayload() == null || message.getEvent() == null) {
            log.warn("empty event details or event type");
            throw new KafkaListenerNotRetryableException("empty event payload or event type");
        } else {
            log.info("processing event_type={}", message.getEvent());
        }

        try {
            process(message);
        } catch (KafkaListenerRetryableException e) {
            throw e;
        } catch (Exception e) {
            log.error("error while processing push notification", e);
            throw new KafkaListenerNotRetryableException("error while processing push notification", e);
        }
    }

    /**
     * Event handler method to determine action based on event type.
     *
     * @param message notification event.
     */

    private void process(NotificationDetails message) {
//TODO: optimize the code
        switch (message.getEvent()) {
            case PAYMENT_AUTHENTICATION_FAILURE:
            case PAYMENT_AUTO_RECLAIM_SUCCESSFUL:
            case PAYMENT_AVAILABLE_FOR_PICKUP:
            case PAYMENT_CANCELLED:
            case PAYMENT_COMPLETED:
            case PAYMENT_CREATED:
            case PAYMENT_DECLINED:
            case PAYMENT_DEPOSIT_FAILURE:
            case PAYMENT_DEPOSIT_INTERRUPTED:
            case PAYMENT_EXPIRED:
            case PAYMENT_EXPIRY_REMINDER:
            case PAYMENT_FRAUD_BLOCKED:
            case PAYMENT_FRAUD_UNBLOCKED:
            case PAYMENT_FRAUD_DELAY:
            case PAYMENT_MANUAL_RECLAIM:
            case PAYMENT_MANUAL_RECLAIM_REMINDER:
            case PAYMENT_NOTIFICATION_FAILURE:
            case PAYMENT_RESEND_AVAILABLE_FOR_PICKUP:
            case PAYMENT_INTERRUPTED_CANCELLATION_COMMITTED:
            case PAYMENT_INTERRUPTED_CANCELLATION_ROLLEDBACK:
            case PAYMENT_INTERRUPTED_INITIATION_ROLLEDBACK:
            case PAYMENT_INTERRUPTED_COMPLETION_ROLLEDBACK:
                processPaymentPushNotification(message);
                break;
            case FRAUD_PAYMENT_FRAUD_STATUS_UPDATED:
            case FRAUD_PAYMENT_FUNDS_RECOVERED:
            case FRAUD_PAYMENT_FUNDS_RECOVERY_REQUEST:
            case FRAUD_PAYMENT_REPORTED:
                processFraudPaymentPushNotification(message);
                break;
            case FRAUD_REGISTRATION_ACCOUNT_ALIAS_REPORTED:
                processFraudAliasRegistrationPushNotification(message);
                break;
            case FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED:
            case FRAUD_REQUEST_FOR_PAYMENT_REPORTED:
                processFraudRequestForPaymentPushNotification(message);
                break;
            case REGISTRATION_ACCOUNT_ALIAS_BLOCKED:
            case REGISTRATION_ACCOUNT_ALIAS_COMPLETE:
            case REGISTRATION_ACCOUNT_ALIAS_DEACTIVATED:
            case REGISTRATION_ACCOUNT_ALIAS_EXPIRED:
            case REGISTRATION_ACCOUNT_ALIAS_LINK_EXPIRED:
            case REGISTRATION_ACCOUNT_ALIAS_UNBLOCKED:
            case REGISTRATION_ACCOUNT_ALIAS_UPDATED:
            case REGISTRATION_ROUTED_NOTIFICATION_COMPLETE:
            case REGISTRATION_ROUTED_NOTIFICATION_DEACTIVATED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRED:
            case REGISTRATION_ROUTED_NOTIFICATION_EXPIRY_REMINDER:
                processAliasRegistrationPushNotification(message);
                break;
            case REQUEST_FOR_PAYMENT_AVAILABLE:
            case REQUEST_FOR_PAYMENT_FRAUD_BLOCKED:
            case REQUEST_FOR_PAYMENT_CANCELLED:
            case REQUEST_FOR_PAYMENT_DECLINED:
            case REQUEST_FOR_PAYMENT_DEPOSIT_FAILURE:
            case REQUEST_FOR_PAYMENT_EXPIRED:
            case REQUEST_FOR_PAYMENT_FULFILLED:
            case REQUEST_FOR_PAYMENT_MANUAL_REMINDER:
            case REQUEST_FOR_PAYMENT_MODIFIED:
            case REQUEST_FOR_PAYMENT_NOTIFICATION_FAILURE:
            case REQUEST_FOR_PAYMENT_OUTSTANDING_REQUEST_REMINDER:
            case REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_ALL_REQUESTER:
            case REQUEST_FOR_PAYMENT_RESPONDER_BLOCKS_REQUESTER:
            case REQUEST_FOR_PAYMENT_RESPONDER_UNBLOCKS_REQUESTER:
            case REQUEST_FOR_PAYMENT_FRAUD_UNBLOCKED:
                processRequestForPaymentPushNotification(message);
                break;
            case PAYMENT_SCHEDULE_CANCELLED:
            case PAYMENT_SCHEDULE_CREATED:
            case PAYMENT_SCHEDULE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_CANCELLED:
            case PAYMENT_SCHEDULE_INSTANCE_FAILED:
            case PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case PAYMENT_SCHEDULE_RECIPIENT_DEREGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_RECIPIENT_REGISTERED_FOR_AUTODEPOSIT:
            case PAYMENT_SCHEDULE_UPDATE_FAILURE:
            case PAYMENT_SCHEDULE_UPDATED:
                processPaymentSchedulePushNotification(message);
                break;
            case REQUEST_FOR_PAYMENT_SCHEDULE_CREATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_REMINDER:
            case REQUEST_FOR_PAYMENT_SCHEDULE_UPDATED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_CANCELLED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_FAILED:
            case REQUEST_FOR_PAYMENT_SCHEDULE_INSTANCE_FAILED:
                processRequestForPaymentSchedulePushNotification(message);
                break;
            case REQUEST_FOR_PAYMENT_RETURN_AVAILABLE:
            case REQUEST_FOR_PAYMENT_RETURN_DECLINED:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_FAILURE_MANUAL_RECLAIM:
            case REQUEST_FOR_PAYMENT_RETURN_DEPOSIT_SUCCESSFUL:
            case REQUEST_FOR_PAYMENT_RETURN_EXPIRED:
                processRequestForPaymentReturnPushNotification(message);
                break;
            case CUSTOMER_REQUEST_FOR_PAYMENT_UNSUBSCRIBE:
                processCustomerUnsubscribePushNotification(message);
                break;
            default:
                log.warn("Invalid Push Notification Event {}", message);
                throw new KafkaListenerNotRetryableException("payment reference is empty for notification details");
        }
    }

    /**
     * Processes <i>Payment Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processPaymentPushNotification(NotificationDetails message) {
        String paymentRefId = message.getEventPayload().getPayment().getPaymentReference();
        if (StringUtils.isEmpty(paymentRefId)) {
            log.warn("payment reference number not found for event {}", message.getEvent());
            throw new KafkaListenerNotRetryableException("payment reference is empty");
        }

        //TODO: implement the business logic
        log.info("{} for event {} received", paymentRefId, message.getEvent());
    }

    /**
     * Processes <i>Fraud Payment Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processFraudPaymentPushNotification(NotificationDetails message){
        log.info("processing fraud payment push notification");

        FraudInformation fraudInformation = message.getEventPayload().getFraud().getFraudInfoPayload();

        // mandatory fields as per schema and conditional schema elements by type == FRAUD
        String paymentRefId = fraudInformation.getPaymentFraudDetails().getPayment().getPaymentReference();

        if (StringUtils.isEmpty(paymentRefId)) {
            log.warn("empty payment reference number supplied for event_type={}", message.getEvent());
            throw new KafkaListenerNotRetryableException("payment reference is empty");
        }

        PaymentCdType paymentType =
                NotificationIntendedForEnum.CREDITOR_AGENT.equals(
                        message.getNotificationIntendedFor()) ? PaymentCdType.INBOUND : PaymentCdType.OUTBOUND;

        String networkPaymentType = fraudInformation.getPaymentFraudDetails().getPayment().getPaymentType().getValue();

        log.debug("processing");
        processFraudPushNotification(paymentRefId, message, paymentType, networkPaymentType);
    }

    /**
     * Processes <i>Fraud Alias Registration Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processFraudAliasRegistrationPushNotification(NotificationDetails message) {
        log.info("processing fraud alias registration push notification");

        FraudInformation fraudInformation = message.getEventPayload().getFraud().getFraudInfoPayload();
        String aliasRefId = fraudInformation.getAccountAliasFraudDetails().getAccountAliasRegistrations().getAccountAliasReference();

        if (StringUtils.isEmpty(aliasRefId)) {
            log.warn("empty alias reference number supplied for event_type={}", message.getEvent());
            throw new KafkaListenerNotRetryableException("alias reference is empty");
        }

        //TODO: implement the business logic
        log.info("event for alias_ref_id={} received, for event_type={}, no action taken", aliasRefId, message.getEvent());
    }

    /**
     * Processes <i>Fraud Request for Payment Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processFraudRequestForPaymentPushNotification(NotificationDetails message) {
      log.info("processing fraud request for payment push notification");

        FraudInformation fraudInformation = message.getEventPayload().getFraud().getFraudInfoPayload();

        String requestPaymentRefId = fraudInformation.getRequestForPaymentFraudDetails().getRequestForPayment().getRequestReference();

        if (StringUtils.isEmpty(requestPaymentRefId)) {
            log.warn("empty request payment reference number supplied for event_type={}", message.getEvent());
            throw new KafkaListenerNotRetryableException("request for payment reference is empty");
        }

        log.info("event for request_ref_id={} received, for event_type={}, no action taken", requestPaymentRefId, message.getEvent());
    }

    /**
     * Handles processing of FRAUD push notification, relaying the notification to CRM for actioning.
     *
     * @param refId              payment reference ID (Interac)
     * @param message            event payload
     * @param paymentType        payment type
     * @param networkPaymentType payment type (Interac)
     */
    private void processFraudPushNotification(String refId, NotificationDetails message, PaymentCdType paymentType, String networkPaymentType) {

        // search for the payment record
        Optional<Payments> paymentsOptional;
        String relatedPaymentExternalRefId = null;
        String ptcPaymentId = null;
        Payments payments = null;
        if (FULFILL_REQUEST_FOR_PAYMENT.getValue().equals(networkPaymentType) && PaymentCdType.INBOUND.equals(paymentType)) {
            String requestPaymentRefId = message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().getRequestForPaymentReference();
            paymentsOptional = paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(requestPaymentRefId, PaymentCdType.REQUEST);
            if (paymentsOptional.isPresent()) {
                relatedPaymentExternalRefId = paymentsOptional.get().getExternalRefId();
            }

            String paymentReference = message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment().getPaymentReference();
            //related payment search - should be fulfilled payment
            if (StringUtils.isNotBlank(paymentReference)) {
                Optional<Payments> optionalPayments = paymentsRepository.findByNetworkPaymentRefIdAndTypeCd(paymentReference, PaymentCdType.INBOUND);
                if (optionalPayments.isPresent()) {
                    ptcPaymentId = optionalPayments.get().getExternalRefId();
                }
            }
        } else {
            paymentsOptional = paymentsRepository.findByNetworkPaymentRefIdAndNetworkPaymentTypeAndTypeCd(refId, networkPaymentType, paymentType);
            if (paymentsOptional.isPresent()) {
                ptcPaymentId = paymentsOptional.get().getExternalRefId();
            }
        }

        if (paymentsOptional.isEmpty()) {
            log.warn("no payment record located, payment_ref_id={} for event_type={}", refId, message.getEvent());
        } else {
            payments = paymentsOptional.get();
        }

        ServiceAccountResponse serviceAccountsResponse = null;
        String serviceAccountRefId = null;
        if (payments != null) {
            serviceAccountRefId = payments.getServiceAccountRefId();

            // search for associated service account record
            serviceAccountsResponse = getServiceAccount(serviceAccountRefId);
        } else {
            if (PaymentCdType.INBOUND.equals(paymentType)
                    && (ACCOUNT_ALIAS_PAYMENT.getValue().equals(networkPaymentType) || REALTIME_ACCOUNT_ALIAS_PAYMENT.getValue().equals(networkPaymentType))) {
                PaymentDetails payment = message.getEventPayload().getFraud().getFraudInfoPayload().getPaymentFraudDetails().getPayment();
                String identification = fetchMessageIdentification(payment);
                if (StringUtils.isNotBlank(identification)) {
                    List<Aliases> alias = aliasesRepository.findByNetworkAliasRefId(identification);
                    if (alias!= null && !alias.isEmpty()) {
                        Optional<Customers> customer = customersRepository.findById(alias.get(0).getCustomerId());
                        if (customer.isPresent()) {
                            serviceAccountRefId = customer.get().getServiceAccountRefId();
                        }
                    }
                } else {
                    try {
                        serviceAccountRefId = customersRepository.findServiceAccountByEmail(payment.getSentTo().getNotificationPreference()
                                .get(0).getValue());
                    } catch (Exception e) {
                        log.info("Multiple Customers records found for payment {}", payment.getPaymentReference());
                    }
                }
                if (serviceAccountRefId != null) {
                    serviceAccountsResponse = getServiceAccount(serviceAccountRefId);
                } else {
                    log.info("service accountRef id is null for payment {}", refId);
                }
            }
        }

        // Deposited Date
        OffsetDateTime depositedDate = null;
        if (payments != null && PaymentStatus.COMPLETE.getValue().equals(payments.getStatus())) {
            List<PaymentStatusHistory> paymentStatusHistories = paymentStatusHistoryRepository.findByPaymentsOrderById(payments);
            depositedDate = findDepositedDate(paymentStatusHistories);
        }

        DeviceInfo deviceInfo = null;
        if (payments != null) {
            Optional<DeviceInfo> optionalDeviceInfo = deviceInfoRepository.findByPayments(payments);
            deviceInfo = optionalDeviceInfo.orElse(null);
        }
        // CRM Reference ID for service account
        String crmRefId = serviceAccountsResponse == null ? null : serviceAccountsResponse.getCrmRefId();
        if (payments == null && !PaymentType.ACCOUNT_DEPOSIT_PAYMENT.getValue().equals(networkPaymentType) && !PaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT.getValue()
                .equals(networkPaymentType)) {
            try {
                crmRefId = Optional.ofNullable(message)
                        .map(NotificationDetails::getEventPayload)
                        .map(EventDetails::getFraud)
                        .map(FraudDetails::getFraudInfoPayload)
                        .map(FraudInformation::getPaymentFraudDetails)
                        .map(PaymentFraudDetails::getPayment)
                        .map(PaymentDetails::getSentTo)
                        .map(Contact::getNotificationPreference)
                        .filter(l -> !CollectionUtils.isEmpty(l))
                        .flatMap(l -> l.stream().filter(n -> n.getType() == NotificationHandle.TypeEnum.EMAIL).findFirst())
                        .map(NotificationHandle::getValue)
                        .map(customersRepository::findServiceAccountByEmail)
                        .map(this::getServiceAccount)
                        .map(ServiceAccountResponse::getCrmRefId)
                        .orElse(null);
            }
            catch (Exception e) {
                log.warn("{} : Got exception when fetching crm ref id for payment {}", APICommonUtilConstant.INVESTIGATE, refId, e);
            }
        }

        log.info("Sending fraud notification request of type {}, Interac Payment Id {} for SA {} to crm", message.getEvent(),
                Optional.ofNullable(payments).map(Payments::getNetworkPaymentRefId).orElse(null), crmRefId);
        CreateSafCaseRequest createSafCaseRequest = createSafCaseRequestMapper
                .notificationDetailsAndPaymentsToCreateSafCaseRequest(crmRefId, message, payments, relatedPaymentExternalRefId, depositedDate,
                    deviceInfo, ptcPaymentId, paymentType);

        // Generate case on CRM
        try {
            crmAdapter.createFasCase(createSafCaseRequest);
        } catch (ResponseException re) {
            if (re.getHttpStatusCode() == HttpStatus.BAD_REQUEST.value()) {
                log.warn("call to crm failed with bad request, will not retry anymore");
                throw new KafkaListenerNotRetryableException(re);
            } else {
                throw new KafkaListenerRetryableException(re);
            }
        } catch (AdapterException | TBDException | TimeoutException e) {
            log.warn("Failed to send request to crm", e);
            throw new KafkaListenerRetryableException(e);
        }
    }

    private static String fetchMessageIdentification(PaymentDetails payment) {
        if (payment == null || payment.getFiToFiPaymentStatusReport() == null) {
            return null;
        }
        List<PaymentTransaction110> transactionInformationAndStatus = payment.getFiToFiPaymentStatusReport().getTransactionInformationAndStatus();
        if (transactionInformationAndStatus == null || transactionInformationAndStatus.isEmpty()) {
            return null;
        }

        OriginalTransactionReference28 originalTransactionReference = transactionInformationAndStatus.get(0).getOriginalTransactionReference();
        if (originalTransactionReference == null || originalTransactionReference.getCreditorAccount() == null) {
            return null;
        }
        CashAccount38 creditorAccount = originalTransactionReference.getCreditorAccount();
        if (creditorAccount.getIdentification() == null || creditorAccount.getIdentification().getOther() == null) {
            return null;
        }
        return creditorAccount.getIdentification().getOther().getIdentification();
    }

    private ServiceAccountResponse getServiceAccount(String serviceAccountRefId) {
        try {
            return serviceAccountAdapter.retrieveServiceAccountByRefId(serviceAccountRefId);
        } catch (ResponseException e) {
            if (e.getHttpStatusCode() == HttpStatus.NOT_FOUND.value()) {
                log.warn("service account={} is invalid, will not retry anymore", serviceAccountRefId);
                throw new KafkaListenerNotRetryableException(e);
            } else {
                throw new KafkaListenerRetryableException(e);
            }
        } catch (TBDException | TimeoutException e) {
            throw new KafkaListenerRetryableException(e);
        } catch (Exception e) {
            log.warn("during processing we got exception we can't handle, skip processing this notification", e);
            throw new KafkaListenerNotRetryableException(e);
        }
    }

    /**
     * Processes <i>Alias Registration Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processAliasRegistrationPushNotification(NotificationDetails message) {
        String aliasRefId = message.getEventPayload().getAccountAliasRegistrations().getAccountAliasReference();

        if (StringUtils.isEmpty(aliasRefId)) {
            log.warn("alias reference number not found for event {}", message.getEvent());
            throw new KafkaListenerNotRetryableException("alias reference is empty");
        }

        //TODO: implement the business logic
        log.info("event for alias_ref_id={} received, for event_type={}, no action taken", aliasRefId, message.getEvent());
    }

    /**
     * Processes <i>Request for Payment Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processRequestForPaymentPushNotification(NotificationDetails message) {
        String paymentRefId = message.getEventPayload().getRequestForPayments().getRequestReference();

        if (StringUtils.isEmpty(paymentRefId)) {
            log.warn("payment reference number not found for event {}", message.getEvent());
            throw new KafkaListenerNotRetryableException("payment reference is empty");
        }

        //TODO: implement the business logic
        log.info("event for request payment_ref_id={} received, for event_type={}, no action taken", paymentRefId, message.getEvent());
    }

    /**
     * Processes <i>Payment Schedule Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processPaymentSchedulePushNotification(NotificationDetails message) {
        String scheduleRefId = message.getEventPayload().getPaymentSchedule().getScheduleReference();

        if (StringUtils.isEmpty(scheduleRefId)) {
            log.warn("payment schedule reference number not found for event {}", message.getEvent());
            throw new KafkaListenerNotRetryableException("payment schedule reference is empty");
        }

        //TODO: implement the business logic
        log.info("event for schedule_ref_id={} received, for event_type={}, no action taken", scheduleRefId, message.getEvent());
    }

    /**
     * Processes <i>Request for Payment Schedule Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processRequestForPaymentSchedulePushNotification(NotificationDetails message) {
        String scheduleRefId = message.getEventPayload().getRequestToPaySchedule().getScheduleReference();

        if (StringUtils.isEmpty(scheduleRefId)) {
            log.warn("request for payment schedule reference number not found for event {}", message.getEvent());
            throw new KafkaListenerNotRetryableException("request for payment schedule reference is empty");
        }

        //TODO: implement the business logic
        log.info("event for schedule_ref_id={} received, for event_type={}, no action taken", scheduleRefId, message.getEvent());
    }

    /**
     * Processes <i>Request for Payment Return Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processRequestForPaymentReturnPushNotification(NotificationDetails message) {

        //TODO: implement the business logic
        log.info("event for request for payment return received, for event_type={}, no action taken", message.getEvent());
    }

    /**
     * Processes <i>Customer Unsubscribe Push Notification</i> events.
     *
     * @param message notification event
     */
    private void processCustomerUnsubscribePushNotification(NotificationDetails message) {

        //TODO: implement the business logic
        log.info("event for customer unsubscribe received, for event_type={}, no action taken", message.getEvent());
    }

    /**
     * Helper utility to determine deposited date.
     *
     * @param paymentStatusHistories history of payment
     * @return deposit date
     */
    private OffsetDateTime findDepositedDate(List<PaymentStatusHistory> paymentStatusHistories) {
        if (paymentStatusHistories == null) {
            log.warn("Failed to found payment history, return current utc date time");
            return DateUtil.getCurrentUTCDateTime();
        }
        Optional<LocalDateTime> localDateTime = paymentStatusHistories.stream()
                .filter(p -> PaymentStatus.COMPLETE.getValue().equals(p.getStatus()))
                .findFirst().map(PaymentStatusHistory::getCreatedOn);
        if (localDateTime.isEmpty()) {
            log.warn("Failed to found payment history with status completed, return null as deposit date");
            return null;
        }
        return DateUtil.toOffsetDateTime(localDateTime.get());
    }
}
