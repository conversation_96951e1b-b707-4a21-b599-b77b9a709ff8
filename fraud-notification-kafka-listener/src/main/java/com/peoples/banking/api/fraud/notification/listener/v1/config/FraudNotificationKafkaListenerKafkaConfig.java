package com.peoples.banking.api.fraud.notification.listener.v1.config;

import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaConsumerGroupDetails;
import com.peoples.banking.api.common.kafka.listener.v1.config.KafkaListenerConfigProperty;
import com.peoples.banking.api.fraud.notification.listener.v1.service.FraudNotificationKafkaListenerService;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Collections;
import java.util.function.Consumer;

@Configuration
@EnableKafka
@Log4j2
public class FraudNotificationKafkaListenerKafkaConfig {

    @Autowired
    private FraudNotificationKafkaListenerProperty fraudNotificationKafkaListenerProperty;
    @Autowired
    private FraudNotificationKafkaListenerService fraudNotificationKafkaListenerService;

    @Bean
    public KafkaListenerConfigProperty config() {
        return new KafkaListenerConfigProperty(Collections.singletonList(
                new KafkaConsumerGroupDetails(NotificationDetails.class,
                                              fraudNotificationKafkaListenerProperty.getKafkaNotificationTopic(),
                                              fraudNotificationKafkaListenerProperty.getFraudConsumerGroup(),
                                              (Consumer<NotificationDetails>) this::listenKafkaFraudNotifications)));
    }

    @PerfLogger
    public void listenKafkaFraudNotifications(NotificationDetails message) {
        fraudNotificationKafkaListenerService.consumeMessage(message);
    }

//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.payment.group.id}")
//    public void listenKafkaPaymentNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.request-for-payment.group.id}")
//    public void listenKafkaRequestForPaymentNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.fraud-registration.group.id}")
//    public void listenKafkaFraudRegistrationNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.fraud-request-for-payment.group.id}")
//    public void listenKafkaFraudRequestForPaymentNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.registration.group.id}")
//    public void listenKafkaRegistrationNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.payment-schedule.group.id}")
//    public void listenKafkaPaymentScheduleNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.request-for-payment-schedule.group.id}")
//    public void listenKafkaRequestForPaymentScheduleNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.request-for-payment-return.group.id}")
//    public void listenKafkaRequestForPaymentReturnNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }
//
//    @KafkaListener(topics = "${kafka.notificationTopic}", groupId = "${kafka.customer-request-for-payment.group.id}")
//    public void listenKafkaCustomerNotifications(NotificationDetails message) throws Exception {
//        pushNotificationKafkaListenerService.consumeMessage(message);
//    }


}
