package com.peoples.banking.api.fraud.notification.listener.v1.mapper;

import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequest;
import com.peoples.banking.partner.domain.crm.fraud.model.CreateSafCaseRequestData;
import com.peoples.banking.partner.domain.interac.notification.model.*;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationDetails.EventEnum;
import com.peoples.banking.partner.domain.interac.notification.model.NotificationHandle.TypeEnum;
import com.peoples.banking.persistence.payment.entity.DeviceInfo;
import com.peoples.banking.persistence.payment.entity.PaymentCdType;
import com.peoples.banking.persistence.payment.entity.Payments;
import com.peoples.banking.util.api.common.JsonUtil;
import com.peoples.banking.util.api.common.mapper.converter.DateConverter;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;

import static com.peoples.banking.partner.domain.interac.notification.model.PaymentType.*;

/**
 * The mapper class to map objects between Notification from Interac and Crm Request domain
 */
@Mapper(componentModel = "spring", uses = {DateConverter.class}, imports = {PaymentCdType.class})
public abstract class CreateSafCaseRequestMapper {

  /**
   * Map Notification details and payment dto entity to CreateSafCaseRequest
   *
   * @return CreateSafCaseRequest the request to create case
   */
  @Mapping(source = "notificationDetails", target = "data.fiFraudStatus", qualifiedByName = "extractFraudStatus")
  @Mapping(source = "payment.accountNumber", target = "data.recipientAcctNum", qualifiedByName = "extractRecipientAccountNumber")
  @Mapping(source = "payment.accountNumber", target = "data.senderAcctNum", qualifiedByName = "extractSenderAccountNumber")
  @Mapping(source = "notificationDetails", target = "data.recipientName", qualifiedByName = "extractRecipientName")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.sentTo.notificationPreference", target = "data.recipientEmail",
      qualifiedByName = "extractEmailFromNotifications")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.sentTo.notificationPreference", target = "data.recipientPhoneNum",
      qualifiedByName = "extractSmsFromNotifications")
  @Mapping(source = "deviceInfo.ipAddress", target = "data.recipientIp", qualifiedByName = "extractRecipientIpAddress")
  @Mapping(source = "deviceInfo.ipAddress", target = "data.senderIp", qualifiedByName = "extractSenderIpAddress")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.senderInfo.notificationPreference", target = "data.senderEmail",
      qualifiedByName = "extractEmailFromNotifications")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.senderInfo.notificationPreference", target = "data.senderPhoneNum",
      qualifiedByName = "extractSmsFromNotifications")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.sendingParticipantId", target = "data.senderFinInstId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.receivingParticipantId", target = "data.recipientFinInstId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.amount.amount", target = "data.amount")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.creationDate", target = "data.dateInitiated", qualifiedByName = {
      "DateConverter", "toCrmDateString"})
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.expiryDate", target = "data.dateExpires",  qualifiedByName = {
      "DateConverter", "toCrmDateString"})
  @Mapping(source = "depositedDate", target = "data.dateDeposited", qualifiedByName = {
      "DateConverter", "toCrmDateString"})
  @Mapping(source = "payment.customerRefId", target = "data.ptcCustomerId")
  @Mapping(source = "ptcPaymentId", target = "data.ptcPaymentId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.paymentReference", target = "data.interacPaymentId")
  @Mapping(source = "relatedPaymentRefId", target = "data.ptcAssociatedId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.requestForPaymentReference", target = "data.interacAssociatedId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.status", target = "data.paymentStatus")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.previousFraudStatus", target = "data.previousFraudStatus")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.currentFraudType", target = "data.fraudType")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.additionalNotes", target = "data.notes")

  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.senderInfo.registrationName", target = "data.senderName")
  @Mapping(source = "crmRefId", target = "data.crmUniqueId")
  @Mapping(source = "payment.serviceAccountRefId", target = "data.serviceAcctRefId")
  @Mapping(source = "notificationDetails.eventPayload.fraud.fraudInfoPayload.paymentFraudDetails.payment.paymentType", target = "data.paymentType",
      qualifiedByName = "networkPaymentTypeToPaymentType")
  @Mapping(source = "notificationDetails.eventPayload.fraud.customerType", target = "data.customerType",
      qualifiedByName = "networkCustomerTypeToCustomerType")
  public abstract CreateSafCaseRequest notificationDetailsAndPaymentsToCreateSafCaseRequest(String crmRefId, NotificationDetails notificationDetails,
      Payments payment, String relatedPaymentRefId, OffsetDateTime depositedDate, DeviceInfo deviceInfo, String ptcPaymentId, @Context PaymentCdType paymentCdType);

  //TODO - think about extract to common module
  /**
   * Map Dto NetworkPaymentType to RetrievePaymentOptionResponse's PaymentType. While we only support regular and autodeposit, mapping other
   * values for the time being.
   *
   * @param networkPaymentType - network payment type
   * @return PaymentType RetrievePaymentOptionResponse's PaymentType
   */
  @Named("networkPaymentTypeToPaymentType")
  @ValueMappings({
      @ValueMapping(source = "REGULAR_PAYMENT", target = "ALIAS_REGULAR"),
      @ValueMapping(source = "ACCOUNT_ALIAS_PAYMENT", target = "ALIAS_AUTODEPOSIT"),
      @ValueMapping(source = "FULFILL_REQUEST_FOR_PAYMENT", target = "REQUEST_FULFILLMENT"),
      @ValueMapping(source = "REALTIME_ACCOUNT_ALIAS_PAYMENT", target = "ALIAS_REALTIME"),
      @ValueMapping(source = "ACCOUNT_DEPOSIT_PAYMENT", target = "ACCOUNT_DEPOSIT_REGULAR"),
      @ValueMapping(source = "REALTIME_ACCOUNT_DEPOSIT_PAYMENT", target = "ACCOUNT_DEPOSIT_REALTIME")
  })
  protected abstract String networkPaymentTypeToPaymentType(PaymentType networkPaymentType);

  /**
   * Map customer entity json field to customer email field
   * @param json raw json input
   * @return email field value
   */
  @Named("customerEmailJsonToEmail")
  protected String customerEmailJsonToEmail(String json) {
    if (StringUtils.isBlank(json)) {
      return null;
    }
    return extractJsonField(json, "email");
  }

  /**
   * Map customer entity json field to customer name field
   * @param json raw json input
   * @return name field value
   */
  @Named("customerNameJsonToName")
  protected String customerNameJsonToName(String json) {
    return extractJsonField(json, "name");
  }

  /**
   * Map customer entity json field to customer mobile field
   * @param json raw json input
   * @return mobile field value
   */
  @Named("customerNameJsonToName")
  protected String customerMobileJsonToMobile(String json) {
    if (StringUtils.isBlank(json)) {
      return null;
    }
    try {
     return extractJsonField(json, "mobile");
    } catch (Exception e) {
      return "";
    }
  }

  /**
   * Map customer entity json field to customer name field
   * @param json raw json input
   * @return name field value
   */
  private String extractJsonField(String json, String fieldName) {
    if (StringUtils.isBlank(json)) {
      return null;
    }
    try {
      Map<String, Object> map = JsonUtil.toObject(json, Map.class);
      return (String) map.get(fieldName);
    } catch (Exception e) {
      return "";
    }
  }

  @Named("extractFraudStatus")
  protected String extractFraudStatus(NotificationDetails notificationDetails) {
    FraudDetails fraud = notificationDetails.getEventPayload().getFraud();
    if (fraud == null) {
      return null;
    }
    FraudInformation fraudInfoPayload = fraud.getFraudInfoPayload();
    if (EventEnum.FRAUD_REQUEST_FOR_PAYMENT_REPORTED.equals(notificationDetails.getEvent()) || EventEnum.FRAUD_REQUEST_FOR_PAYMENT_FRAUD_STATUS_UPDATED.equals(notificationDetails.getEvent())){
      return fraudInfoPayload.getRequestForPaymentFraudDetails().getCurrentFraudStatus().getValue();
    } else {
      return fraudInfoPayload.getPaymentFraudDetails().getCurrentFraudStatus().getValue();
    }
  }

  @Named("extractRecipientAccountNumber")
  protected String extractRecipientAccountNumber(String accountNumber, @Context PaymentCdType paymentCdType){
    if (paymentCdType != PaymentCdType.OUTBOUND) {
      return accountNumber;
    }
    return null;
  }

  @Named("extractSenderAccountNumber")
  protected String extractSenderAccountNumber(String accountNumber, @Context PaymentCdType paymentCdType){
    if (paymentCdType == PaymentCdType.OUTBOUND) {
      return accountNumber;
    }
    return null;
  }

  @Named("extractSenderIpAddress")
  protected String extractSenderIpAddress(String ipAddress, @Context PaymentCdType paymentCdType){
    if (paymentCdType == PaymentCdType.OUTBOUND) {
      return ipAddress;
    }
    return null;
  }

  @Named("extractRecipientIpAddress")
  protected String extractRecipientIpAddress(String ipAddress, @Context PaymentCdType paymentCdType){
    if (paymentCdType != PaymentCdType.OUTBOUND) {
      return ipAddress;
    }
    return null;
  }

  @Named("extractRecipientName")
  protected String extractRecipientName(NotificationDetails notificationDetails){

    FraudDetails fraud = notificationDetails.getEventPayload().getFraud();
    String result = "";
    if (fraud == null || fraud.getFraudInfoPayload() == null) {
      return result;
    }
    FraudInformation fraudInfoPayload = fraud.getFraudInfoPayload();
    if (fraudInfoPayload.getPaymentFraudDetails() == null || fraudInfoPayload.getPaymentFraudDetails().getPayment() == null) {
      return result;
    }

    return extractCustomerName(fraudInfoPayload);
  }

  private String extractCustomerName(FraudInformation fraudInfoPayload) {

    Contact sentTo = fraudInfoPayload.getPaymentFraudDetails().getPayment().getSentTo();
    LegalName legalName =  sentTo.getLegalName();
    String result = "";
    if (legalName == null) {
      return result;
    }
    ContactType contactType = sentTo.getCustomerType();
    if (contactType == ContactType.RETAIL) {
      if (legalName.getRetailName() == null) {
        return result;
      }
      result = legalName.getRetailName().getFirstName() + " " + legalName.getRetailName().getLastName();
    } else if (contactType == ContactType.SMALL_BUSINESS || contactType == ContactType.CORPORATION) {
      if (legalName.getBusinessName() == null) {
        return result;
      }
      result = legalName.getBusinessName().getCompanyName();
    }

    return result;
  }

  @Named("networkCustomerTypeToCustomerType")
  @ValueMappings({
      @ValueMapping(source = "RETAIL", target = "INDIVIDUAL"),
      @ValueMapping(source = "SMALL_BUSINESS", target = "SMALL_BUSINESS"),
      @ValueMapping(source = "CORPORATION", target = "CORPORATION"),
  })
  protected abstract String networkCustomerTypeToCustomerType(CustomerType customerType);

  @Named("extractEmailFromNotifications")
  protected String extractEmailFromNotifications(List<NotificationHandle> notificationHandles){
    return extractNotificationValueByType(notificationHandles, TypeEnum.EMAIL);
  }

  @Named("extractSmsFromNotifications")
  protected String extractSmsFromNotifications(List<NotificationHandle> notificationHandles){
    return extractNotificationValueByType(notificationHandles, TypeEnum.SMS);
  }

  protected String extractNotificationValueByType(List<NotificationHandle> notificationHandles, TypeEnum requiredType) {
    if (notificationHandles == null || notificationHandles.isEmpty()) {
      return "";
    }

    return notificationHandles.stream()
        .filter(n -> n.getType() == requiredType)
        .findFirst()
        .map(NotificationHandle::getValue)
        .orElse("");
  }

  //adjustments for fulfill_request_for_payment
  @AfterMapping
  public void afterMappingCaseRequest(@MappingTarget CreateSafCaseRequest createSafCaseRequest,
      Payments payments, NotificationDetails notificationDetails) {
    //notification type is fulfillment -
    CreateSafCaseRequestData data = createSafCaseRequest.getData();
    if (data == null) {
      //this shouldn't happen, but ide warn about nullable data
      return;
    }
    if (payments != null &&
            Objects.equals(FULFILL_REQUEST_FOR_PAYMENT, Optional.ofNullable(notificationDetails.getEventPayload().getFraud())
                    .map(FraudDetails::getFraudInfoPayload)
                    .map(FraudInformation::getPaymentFraudDetails)
                    .map(PaymentFraudDetails::getPayment)
                    .map(PaymentDetails::getPaymentType)
                    .orElse(null))) {
      String contactJson = payments.getContactJson();
      data.setReqMoneyContactName(customerNameJsonToName(contactJson));
      data.setReqMoneyContactEmail(customerEmailJsonToEmail(contactJson));
      data.setReqMoneyContactPhone(customerMobileJsonToMobile(contactJson));
    }

    if (data.getDateExpires() == null && payments != null && payments.getExpiryDate() != null) {
      data.setDateExpires(payments.getExpiryDate().format(DateConverter.CRM_DATE_FORMAT));
    }
   }

}
