version: "3.6"
services:
  fraudnotificationkafkalistener:
    build: .
    environment:
      - DATABASE_HOST=interac-db-qa.cluster-cpeag2bxo5g6.ca-central-1.rds.amazonaws.com
      - DATABASE_NAME=postgres
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=wXVTqTWk0Hu3LGBZRxit
      - KAFKA_SERVER=localhost:9092
      - KAFKA_FRAUD_NOTIFICATION_TOPIC=push-notifications_fraud
      - KAFKA_FRAUD_NOTIFICATIONS_CONSUMER_GROUP=push-notifications-group
      - KAFKA_PUSH_NOTIFICATIONS_FRAUD_GROUP=push-payment-group
      - KAFKA_PUSH_NOTIFICATIONS_PAYMENT_GROUP=payment-group
      - KAFKA_PUSH_NOTIFICATIONS_REQUEST_FOR_PAYMENT_GROUP=request-for-payment-group
      - KAFKA_PUSH_NOTIFICATIONS_FRAUD_REGISTRATION_GROUP=fraud-registration-group
      - KAFKA_PUSH_NOTIFICATIONS_FRAUD_REQUEST_FOR_PAYMENT_GROUP=fraud-request-for-payment-group
      - KAFKA_PUSH_NOTIFICATIONS_REGISTRATION_GROUP=registration-group
      - KAFKA_PUSH_NOTIFICATIONS_PAYMENT_SCHEDULE_GROUP=payment-schedule-group
      - KAFKA_PUSH_NOTIFICATIONS_REQUEST_FOR_PAYMENT_SCHEDULE_GROUP=request-for-payment-schedule-group
      - KAFKA_PUSH_NOTIFICATIONS_REQUEST_FOR_PAYMENT_RETURN_GROUP=request-for-payment-return-group
      - KAFKA_PUSH_NOTIFICATIONS_CUSTOMER_REQUEST_FOR_PAYMENT_GROUP=customer-request-for-payment-group
      - KAFKA_SSL_ENABLED=false
      - KAFKA_SSL_TRUSTSTORE_LOCATION=
      - KAFKA_SSL_TRUSTSTORE_PASSWORD=
      - KAFKA_SSL_KEYSTORE_LOCATION=
      - KAFKA_SSL_KEYSTORE_PASSWORD=
      - KAFKA_SSL_KEY=
      - CRM_SUBSCRIPTION_KEY=defaultCrmKey
      - CRM_HOST=https://pttestapp-apim.azure-api.net/api/qa/fascases