package com.peoples.banking.partner.adapter.interac.common.config;

import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;

/**
 * Get Adapter Interac RestTemplate Configuration
 *
 */
@Configuration
public class InteracGetMethodRestTemplateConfig extends InteracRestTemplateConfigBase {

  /**
   * Get Method HTTP client.
   */
  @Autowired
  @Qualifier("getMethodHttpClient")
  CloseableHttpClient getMethodHttpClient;

  /**
   * Instantiate new <i>RestTemplate</i> instance.
   * RestTemplate for HTTP Get Method
   *
   * @return instantiated object.
   */
  @Bean(name = "readRestTemplate")
  public RestTemplate getMethodRestTemplate() {
    return getReadMethodRestTemplate();
  }

  public RestTemplate getReadMethodRestTemplate() {
    RestTemplate restTemplate = new RestTemplate(getGetMethodClientHttpRequestFactory());
    DefaultUriBuilderFactory defaultUriBuilderFactory = new DefaultUriBuilderFactory();
    defaultUriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.NONE);
    restTemplate.setUriTemplateHandler(defaultUriBuilderFactory);
    return restTemplate;
  }

  /**
   * Instantiate new <i>ClientHttpRequestFactory</i> instance.
   *
   * @return instantiated object.
   */
  public HttpComponentsClientHttpRequestFactory getGetMethodClientHttpRequestFactory() {
    HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
    clientHttpRequestFactory.setHttpClient(getGetMethodHttpClient());
    return clientHttpRequestFactory;
  }

  /**
   * @inheritDoc
   */
  public CloseableHttpClient getGetMethodHttpClient() {
    return getMethodHttpClient;
  }

}
