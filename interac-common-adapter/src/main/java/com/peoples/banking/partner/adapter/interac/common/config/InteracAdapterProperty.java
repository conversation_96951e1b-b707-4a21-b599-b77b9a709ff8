package com.peoples.banking.partner.adapter.interac.common.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class InteracAdapterProperty {

  /**
   * Interac system URL (protocol://host:port).
   */
  @Value("${sys.pn.interac.url.base}")
  private String interacUrlBase;

  /**
   * People's Participant ID on Interac.
   */
  @Value("${sys.pn.interac.peoples.fiid}")
  private String fiid;

  /**
   * The key-id assigned to the certificate used by <PERSON> (differs by environment).
   */
  @Value("${sys.pn.interac.peoples.kid}")
  private String kId;

  /**
   * Full path to the Peoples private key file.
   */
  @Value("${sys.pn.interac.peoples.private-key}")
  private String peoplesPrivKeyFile;

  /**
   * Full path to the Intearc public certificate file.
   */
  @Value("${sys.pn.interac.public-cert}")
  private String interacPubKeyFile;

  /**
   * The key-id assigned to the certificate that Interac is using (differs by environment).
   */
  @Value("${sys.pn.interac.kid}")
  private String interacKId;

  /**
   * Time to live (maximum age) of a request (in milliseconds).
   * <pre>defaults to 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pn.interac.time-to-live:30000}")
  private Integer timeToLive;

  /**
   * HTTP connection keepalive interval (in milliseconds).
   * <pre>default is 20 seconds = 20000 ms</pre>
   */
  @Value("${sys.pn.interac.connection.keep-alive:20000}")
  private Integer connectionKeepAlive;

  /**
   * Maximum number of HTTP connections.
   * <pre>default is 25</pre>
   */
  @Value("${sys.pn.interac.connection.max:25}")
  private Integer maxHttpConnections;

  /**
   * Delay before reaper removes idle HTTP connections (in milliseconds).
   * <pre>default is 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pn.interac.connection.wait-time:30000}")
  private Integer waitTimeIdleConnection;

  /**
   * Flag indicating whether requests are traversing the public internet or VPN network. defaults to
   * true.
   */
  @Value("${sys.pn.interac.public-network:true}")
  private Boolean isInteracPublicNetwork;

  /**
   * Connection timeout for requesting a connection from connection manager (in milliseconds).
   * <pre>defaults to 5 seconds = 5000 ms.</pre>
   */
  @Value("${sys.pn.interac.connection-request-timeout:5000}")
  private Integer connectionReqTimeout;

  /**
   * Timeout waiting until a connection is established (in milliseconds).
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pn.interac.connection.timeout:15000}")
  private Integer connectTimeout;

  /**
   * Read timeout for requests to Interac.
   * <pre>defaults to 30 seconds = 30000 ms.</pre>
   */
  @Value("${sys.pn.interac.read.timeout:10000}")
  private Integer readTimeout;

  /**
   * Read timeout for requests to Interac.
   * <pre>defaults to 10 seconds = 10000 ms.</pre>
   */
  @Value("${sys.pn.interac.get.method.read.timeout:10000}")
  private Integer getMethodReadTimeout;

  /**
   * Max connections per route for requests to Interac.
   */
  @Value("${sys.pn.interac.connection.max-per-route:#{null}}")
  private Integer connectionMaxPerRoute;

  /**
   * Default max connections per route for requests to Interac.
   */
  @Value("${sys.pn.interac.connection.default-max-per-route:#{null}}")
  private Integer defaultConnectionMaxPerRoute;

  @Value("#{'${interac.logging.api.exclude.headers:}'.split(',')}")
  private Set<String> excludeLoggingReqHeaders;

}
