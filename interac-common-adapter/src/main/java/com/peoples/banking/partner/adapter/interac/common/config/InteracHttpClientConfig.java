package com.peoples.banking.partner.adapter.interac.common.config;

import org.apache.http.HttpHost;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.peoples.banking.adapter.base.config.HttpClientConfig;
import com.peoples.banking.adapter.base.exception.AdapterException;

/**
 * Interac HttpClient Config- Supports both HTTP and HTTPS - Uses a connection pool to re-use connections and save overhead of creating connections. -
 * Has a custom connection keep-alive strategy (to apply a default keep-alive if one isn't specified) - Starts an idle connection monitor to
 * continuously clean up stale connections.
 */
@Configuration
@EnableScheduling
public class InteracHttpClientConfig extends HttpClientConfig {

  /**
   * Interac adapter properties.
   */
  @Autowired
  private InteracAdapterProperty property;


  /**
   * @inheritDoc
   */
  @Override
  protected Integer getMaxHttpConnections() {
    return property.getMaxHttpConnections();
  }


  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectionKeepAlive() {
    return property.getConnectionKeepAlive();
  }


  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectionReqTimeout() {
    return property.getConnectionReqTimeout();
  }


  /**
   * @inheritDoc
   */
  @Override
  protected Integer getConnectTimeout() {
    return property.getConnectTimeout();
  }


  /**
   * @inheritDoc
   */

  @Override
  protected Integer getReadTimeout() {
    return property.getReadTimeout();
  }


  /**
   * @inheritDoc
   */
  @Override
  protected Integer getWaitTimeIdleConnection() {
    return property.getWaitTimeIdleConnection();
  }

  /**
   * @inheritDoc
   */
  protected Integer getConnectionMaxPerRoute() { return property.getConnectionMaxPerRoute(); }

  /**
   * @inheritDoc
   */
  protected Integer getDefaultConnectionMaxPerRoute() { return property.getDefaultConnectionMaxPerRoute(); }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "interacPoolingConnectionManager")
  public PoolingHttpClientConnectionManager poolingConnectionManager() throws AdapterException {
    PoolingHttpClientConnectionManager interacPoolingConnectionManager = getPoolingConnectionManager();
    if (getConnectionMaxPerRoute() != null) {
      HttpHost host = new HttpHost(property.getInteracUrlBase());
      interacPoolingConnectionManager.setMaxPerRoute(new HttpRoute(host), getConnectionMaxPerRoute());
    }
    if (getDefaultConnectionMaxPerRoute() != null) {
      interacPoolingConnectionManager.setDefaultMaxPerRoute(getDefaultConnectionMaxPerRoute());
    }
    return interacPoolingConnectionManager;
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "interacConnectionKeepAliveStrategy")
  public ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
    return getConnectionKeepAliveStrategy();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "interacHttpClient")
  public CloseableHttpClient httpClient() throws AdapterException {
    return getHttpClient();
  }

  /**
   * @inheritDoc
   */
  @Override
  @Bean(name = "interacIdleConnectionMonitor")
  public Runnable idleConnectionMonitor(@Qualifier("interacPoolingConnectionManager") PoolingHttpClientConnectionManager connectionManager) {
    return getIdleConnectionMonitor(connectionManager);
  }

  @Bean(name = "getMethodHttpClient")
  public CloseableHttpClient getMethodHttpClient() throws AdapterException {
    return getHttpClient(getConnectionReqTimeout(), getConnectTimeout(), getGetMethodReadTimeout());
  }

  public Integer getGetMethodReadTimeout() {
    return property.getGetMethodReadTimeout();
  }
}