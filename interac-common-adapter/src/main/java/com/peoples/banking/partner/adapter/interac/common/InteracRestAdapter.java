package com.peoples.banking.partner.adapter.interac.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.adapter.base.util.IdGeneratorUtil;
import com.peoples.banking.partner.adapter.interac.common.config.InteracAdapterProperty;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jwe.JsonWebEncryption;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

/**
 * Base implementation of <i>Rest</i> for outgoing communication with Interac Service Domains.
 * <pre>includes header construction, JWS header + payload signing, and  payload encryption and
 * decryption</pre>
 * <p>
 * TODO refactor implementation using WebClient as this is the Spring approach going forward.
 */
@Log4j2
public abstract class InteracRestAdapter<T> {

  /**
   * <i>Interac</i> header value for signature type.
   */
  public static final String SIGNATURE_TYPE = "PAYLOAD_DIGEST_SHA256";

  /**
   * <i>Interac</i> header value for channel indicator.
   */
  private static final String CHANNEL_INDICATOR = "ONLINE";

  /**
   * JsonWebSignature instance for Sender (Peoples).
   */
  @Autowired
  protected JsonWebSignature jws;

  /**
   * JsonWebEncryption instance for Sender (Peoples).
   */
  @Autowired
  protected JsonWebEncryption jwe;

  /**
   * JW* private kay for Sender (Peoples).
   */
  @Autowired
  @Qualifier("jwSender")
  protected JsonWeb jwSender;

  /**
   * JW* public certificate for Receiver (Interac).
   */
  @Autowired
  @Qualifier("jwReceiver")
  protected JsonWeb jwReceiver;

  /**
   * REST connection template.
   */
  @Autowired
  @Qualifier("interacRestTemplate")
  protected RestTemplate restTemplate;

  /**
   * REST connection template.
   */
  @Autowired
  @Qualifier("readRestTemplate")
  protected RestTemplate readRestTemplate;

  /**
   * <i>Interac</i> runtime properties.
   */
  @Autowired
  protected InteracAdapterProperty interacProperties;

  @Autowired
  protected ObjectMapper objectMapper;

  /**
   * <i>Interac</i> service domain adapter instantiated for.
   */
  private String serviceDomain;

  /**
   * Initialization.
   */
  @PerfLogger
  @PostConstruct
  public void init() throws AdapterException {
    log.info("starting initialization of JW*");

    // initialize
    log.info("serviceDomain={}", serviceDomain);

    // initialize sender encryption -- private key
    this.initSender();
    // built to fail hard, exception thrown if not initialized.

    // initialize receiver encryption -- public key
    this.initReceiver();
    // built to fail hard, exception thrown if not initialized.

    // Initialize JWS
    jws.setSenderRsaPrivateKey(jwSender);
    jws.setReceiverRsaPublicCert(jwReceiver);

    // Initialize JWE
    jwe.setSenderRsaPrivateKey(jwSender);
    jwe.setReceiverRsaPublicCert(jwReceiver);

    log.info("finished initialization of JW*");

    // TODO add performance logging to this initialization
  }

  /**
   * Initialization of Receiver encryption details.
   */
  private void initSender() throws AdapterException {
    // Sender (private key) initialization
    String senderPrivateKeyPath = interacProperties.getPeoplesPrivKeyFile();

    // sanity check
    if (senderPrivateKeyPath != null && !senderPrivateKeyPath.isBlank()) {
      log.debug("starting initialization of sender private key");
      jwSender.initPrivateKey(senderPrivateKeyPath);
      log.debug("finished initialization of private key");
    } else {
      // properties NOT defined or NULL
      log.error("sender private key properties NOT found");
      throw new AdapterException("sender private key properties NOT found",
          ErrorCode.INVALID_INPUT);
    }
  }

  /**
   * Initialization of Sender encryption details.
   */
  private void initReceiver() throws AdapterException {
    // Sender (public certificate & kid) initialization
    String receiverPublicCertPath = interacProperties.getInteracPubKeyFile();
    String receiverKeyId = interacProperties.getInteracKId();

    // sanity check
    if (receiverPublicCertPath != null && !receiverPublicCertPath.isBlank() &&
        receiverKeyId != null && !receiverKeyId.isBlank()) {
      log.debug("starting initialization of receiver public certificate");
      jwReceiver.initPublicCert(receiverPublicCertPath, receiverKeyId);
      log.debug("finished initialization of public certificate");
    } else {
      // properties NOT defined or NULL
      log.error("receiver public certificate & key id  properties NOT found");
      throw new AdapterException("receiver public certificate & key id  properties NOT found",
          ErrorCode.INVALID_INPUT);
    }
  }

  /**
   * @param endpointUrl        URL to HTTP resource
   * @param httpMethod         HTTP method
   * @param interacRequestType Request type to indicate which headers must be added to the outgoing request
   * @param participantUserId  Interac participant user ID
   * @param request            request to send
   * @param responseType       class instance of the response object
   * @param pathParams         path parameters
   * @param headerParams       header parameters
   * @param <Treq>             the class type of the domain object that the request is
   * @param <Tresp>            the class type of the domain object that the response is converted into
   * @return response object
   * @throws AdapterException  adapter exception
   * @throws ResponseException response exception from Interac
   * @throws TBDException      response exception requiring rollback
   */
  protected <Treq, Tresp> Tresp execute(String endpointUrl, HttpMethod httpMethod, @Nullable InteracRequestType interacRequestType,
      String participantUserId, String indirectConnectorId, @Nullable Treq request, @Nullable Class<Tresp> responseType, @Nullable Map<String, ?> pathParams,
      @Nullable Map<String, ?> headerParams)
      throws AdapterException, ResponseException, TBDException, TimeoutException {
    // sanity check (participantUserId, requestType & request (for GET), and params are optional)
    if (endpointUrl == null || endpointUrl.isBlank() || httpMethod == null || responseType == null) {
      // debug logging (shield these with a isDebugEnabled for efficiency)
      if (log.isDebugEnabled()) {
        log.debug("optional parameters: interacRequestType={}, request={}, participantUserId={}, pathParams={}, headerParams={}",
            interacRequestType, request, participantUserId, pathParams, headerParams);
        log.debug("required parameters: endpointUrl={}, httpMethod={}, responseType={}",
            endpointUrl, httpMethod, responseType);
      }

      throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
    }

    // validate
    if (interacRequestType == null) {
      interacRequestType = InteracRequestType.DEFAULT;
    }

    // step 1 - generate payload (request body)
    log.debug("generating HTTP payload");
    String contentType = null;
    String payload = null;

    // convert response to JSON for encryption (optional) and transmission
    if (request != null) {
      try {
        payload = objectMapper.writeValueAsString(request);
      } catch (JsonProcessingException e) {
        // should NOT be triggered, as we validate this during initialization
        log.error("failed on serializing request payload", e);
        throw new AdapterException("failed on serializing request payload", ErrorCode.UNEXPECTED_EXCEPTION);
      }
      log.info("HTTP payload (raw)={}", payload);
    }

    if (interacProperties.getIsInteracPublicNetwork()) {
      // JWE required on payload, and payload is encrypted
      if (request != null) {
        // encrypt payload when traversing the public network.
        payload = jwe.encrypt(payload);
      }
      contentType = InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE;
    } else {
      // no JWE on payload
      contentType = InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE;
    }

    // step 2 - generate JWS
    log.debug("generating JWS");
    Instant timestamp = Instant.now();
    String signedJws = jws.buildJWS(payload, timestamp, contentType, interacProperties.getKId());

    // step 3a -- populate HTTP headers
    log.debug("populating HTTP headers");
    HttpHeaders httpHeaders = this.buildHeaders(signedJws, timestamp, participantUserId, indirectConnectorId);

    // step 3b -- populate supplementary (operation specific) headers)
    this.supplementaryHeaders(httpHeaders, interacRequestType, headerParams);

    // step 4 -- populate HTTP entity
    log.debug("populating HTTP entity");
    HttpEntity<?> httpEntity = null;
    if (request == null) {
      // no HTTP payload (eg. heartbeat, any GET operation, etc.)
      httpEntity = new HttpEntity<Treq>(httpHeaders);
    } else {
      // with HTTP payload
      httpEntity = new HttpEntity<>(payload, httpHeaders);
    }

    Tresp response = this.rest(endpointUrl, httpMethod, httpEntity, responseType, pathParams);

    return response;
  }

  /**
   * Invokes the REST endpoint with the request payload, and gracefully handles exceptions.
   *
   * @param <Tresp>           the class type of the domain object that the response is
   * @param endpointUrl       URL to HTTP resource
   * @param httpMethod        HTTP method
   * @param httpEntity        HTTP entity instance
   * @param responseClassType the class type of the domain object that the response is converted to
   * @param params            path parameters
   */
  protected <Tresp> Tresp rest(String endpointUrl, HttpMethod httpMethod, HttpEntity<?> httpEntity, Class<Tresp> responseClassType,
      @Nullable Map<String, ?> params) throws ResponseException, TBDException, AdapterException, TimeoutException {
    // no sanity check, trusted function within this class

    ResponseEntity<?> responseEntity;
    Tresp response = null;

    // TODO can we NOT do it, fix this.
    if (params == null) {
      params = Collections.emptyMap();
      // leave empty
    }

    RestTemplate callRestTemplate = restTemplate;
    if (HttpMethod.GET.equals(httpMethod)) {
      log.debug("use readRestTemplate for get http call");
      callRestTemplate = this.readRestTemplate;
    }

    try {
      log.info("initiating request to {}", endpointUrl);
      if (interacProperties.getIsInteracPublicNetwork()) {
        log.debug("start sending request to interac through public network");

        // encrypted response payload returned when over public network
        responseEntity = callRestTemplate.exchange(endpointUrl, httpMethod, httpEntity, String.class, params);
        response = convertEncryptedResponseToDomain((String) responseEntity.getBody(), responseClassType);
        // payload already logged during decryption
      } else {
        // plaintext (with transport encryption only) response
        responseEntity = callRestTemplate.exchange(endpointUrl, httpMethod, httpEntity, responseClassType, params);
        response = (Tresp) responseEntity.getBody();
      }
      // shield expensive call with debug level check
      log.info("RESPONSE | httpStatus={} | headers={} | body={}", responseEntity.getStatusCodeValue(), responseEntity.getHeaders(), objectMapper.writeValueAsString(response));
    } catch (RestClientResponseException e) {
      /*
       * result with no side-effects (transaction did NOT complete)
       * {@code includes HttpClientErrorException (all 4xx series HTTP status codes)}
       * {@code includes HttpServerErrorException (all 5xx series HTTP status codes)}
       * {@code includes UnknownHttpStatusCodeException (undefined HTTP status codes)}
       */
      // throw appropriate exception
      throwResponseException(e);
    } catch (ResourceAccessException e) {
      /* result with potential side-effects (transaction MAY HAVE completed)
       * {@code includes SocketTimeoutException (may have updated record, undetermined)}
       * {@code includes ConnectionPoolTimeoutException (no record updated)}
       * {@code includes ConnectTimeoutException (undefined HTTP status codes)}
       */

      if (e.getCause() instanceof SocketTimeoutException) {
        // Read/socket timeout will result in a java.net.SocketTimeoutException.
        log.error("reason: socket timeout occurred", e);

        // throw  TBDException, as side effects (eg. records MAY have been updated) exist
        throw new TBDException();

      } else if (e.getCause() instanceof ConnectionPoolTimeoutException) {
        // Connection request timeout will result in an org.apache.http.conn.ConnectionPoolTimeoutException being thrown
        log.error("reason: connection (from connection pool) request timeout occurred", e);

        // throw  TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();

      } else if (e.getCause() instanceof ConnectTimeoutException) {
        // Connection timeout will result in an org.apache.http.conn.ConnectTimeoutException being thrown
        log.error("reason: connection timeout occurred", e);

        // throw  TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      } else {
        log.error("generic resource exception received", e);

        // throw  TimeoutException, as no side effects (eg. nothing created on Interac) exist.
        throw new TimeoutException();
      }
    } catch (Exception e) {
      // unexpected exception
      log.error("unexpected exception", e);
      throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
    }

    return response;
  }

  /**
   * Adds supplementary headers specific to an Interac API.
   *
   * @param httpHeaders        HTTP headers associated with the request
   * @param interacRequestType Interac request type of this request
   * @param headerParams       header parameters
   */
  protected abstract void supplementaryHeaders(HttpHeaders httpHeaders, InteracRequestType interacRequestType, Map<String, ?> headerParams)
      throws AdapterException;

  /**
   * Build HTTP reader for the request.
   *
   * @param signedJws     JWS token
   * @param timestamp     timestamp of this instant in time
   * @param participantId participant use ID on <i>Interac</i>
   * @return HTTP header
   */
  private HttpHeaders buildHeaders(String signedJws, Instant timestamp, String participantId, String indirectConnectorId) {
    // no sanity check, trusted function within this class

    // initialize
    HttpHeaders httpHeaders = new HttpHeaders();

    // default HTTP httpHeaders
    httpHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);

    DateTimeFormatter formatter = new DateTimeFormatterBuilder().appendInstant(3).toFormatter();

    // Interac httpHeaders (mandatory)
    httpHeaders.set(InteracRestAdapterConstant.HEADER_PARTICIPANT_ID, interacProperties.getFiid());
    httpHeaders.set(InteracRestAdapterConstant.HEADER_CHANNEL_INDICATOR, CHANNEL_INDICATOR);
    // TODO this should be DB driven to ensure uniqueness, or from a common seed/generator
    httpHeaders.set(InteracRestAdapterConstant.HEADER_REQUEST_ID, IdGeneratorUtil.generateRequestId());
    httpHeaders.set(InteracRestAdapterConstant.HEADER_SIGNATURE, signedJws);
    httpHeaders.set(InteracRestAdapterConstant.HEADER_SIGNATURE_TYPE, SIGNATURE_TYPE);
    // Interac may send all 0s in the milliseconds therefore we need to have the consistent behaviour of
    // having 3 digits in milliseconds even all are 0s
    httpHeaders.set(InteracRestAdapterConstant.HEADER_TRANS_TIME, formatter.format(timestamp.truncatedTo(ChronoUnit.MILLIS)));

    // Interac httpHeaders (optional)
    if (participantId != null && !participantId.isBlank()) {
      httpHeaders.set(InteracRestAdapterConstant.HEADER_PARTICIPANT_USER_ID, participantId);
    }
    if (indirectConnectorId != null && !indirectConnectorId.isBlank()) {
      httpHeaders.set(InteracRestAdapterConstant.HEADER_INDIRECT_CONNECTOR_ID, indirectConnectorId);
    }

    logHeaders(httpHeaders);

    return httpHeaders;
  }

  private void logHeaders(HttpHeaders httpHeaders) {
    log.info("HTTP headers {}", httpHeaders.entrySet().stream().filter(h -> !interacProperties.getExcludeLoggingReqHeaders().contains(h.getKey()))
        .map(Object::toString).collect(Collectors.joining(",")));
  }

  /**
   * Returns the <i>Interac</i> domain this instance is instantiated for.
   *
   * @return service domain identifier
   */
  protected abstract String getServiceDomain();

  /**
   * Returns the <i>Interac</i> domain endpoint URL.
   */
  protected String getBaseUrl() {
    return interacProperties.getInteracUrlBase();
  }

  /**
   * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
   *
   * @throws ResponseException response exception from Interac
   */
  @SneakyThrows
  private void throwResponseException(RestClientResponseException exception) {
    String response = exception.getResponseBodyAsString();
    Integer httpStatusCode = exception.getRawStatusCode();
    // sanity check
    if (!response.isBlank()) {
      Class<T> errorClass = getErrorClass();
      T responseModel = null;
      try {
        if (interacProperties.getIsInteracPublicNetwork()) {
          responseModel = this.convertEncryptedResponseToDomain(response, errorClass);
        } else {
          responseModel = this.convertResponseToDomain(response, errorClass);
        }
      } catch (TBDException e) {
        if (interacProperties.getIsInteracPublicNetwork()) {
          try {
            responseModel = this.convertResponseToDomain(response, errorClass);
          } catch (Exception exc) {}
        }
        if (responseModel == null) {
          log.warn("RESPONSE | httpStatus={} | headers={}", httpStatusCode, exception.getResponseHeaders());
          log.warn("cannot convert Interac error response into something meaningful");
          throw new ResponseException(httpStatusCode, null, null);
        }
      }

      String errorCode = getErrorCodeFunction().apply(responseModel);
      String errorText = getErrorDescriptionFunction().apply(responseModel);
      if ("5002".equals(errorCode)) {
        log.error("RESPONSE | httpStatus={} | headers={} | body={}", httpStatusCode, exception.getResponseHeaders(), objectMapper.writeValueAsString(responseModel));
      } else {
        log.warn("RESPONSE | httpStatus={} | headers={} | body={}", httpStatusCode, exception.getResponseHeaders(), objectMapper.writeValueAsString(responseModel));
      }
      throw new ResponseException(httpStatusCode, errorCode, errorText);
    } else {
      log.warn("exception received, statusCode={}", httpStatusCode);
      throw new ResponseException(httpStatusCode, null, null);
    }
  }

  protected abstract Class<T> getErrorClass();
  protected abstract Function<T, String> getErrorCodeFunction();
  protected abstract Function<T, String> getErrorDescriptionFunction();

  /**
   * Converts encrypted payload into decrypted object representation.
   *
   * @param <T>              the generic object type that the response is converted to
   * @param encryptedPayload encrypted payload to be converted
   * @param classType        the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertEncryptedResponseToDomain(String encryptedPayload, Class<T> classType)
      throws TBDException {
    T response = null;

    // sanity check
    if (encryptedPayload != null && !encryptedPayload.isBlank()) {
      String payload = null;

      try {
        payload = jwe.decrypt(encryptedPayload);
      } catch (AdapterException e) {
        log.debug("failed to decrypt payload, undetermined result", e);

        throw new TBDException();
      }

      try {
        response = objectMapper.readValue(payload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }
    }

    return response;
  }

  /**
   * Converts payload into object representation.
   *
   * @param <T>              the generic object type that the response is converted to
   * @param responsePayload response payload to be converted
   * @param classType        the class type of the domain object that the response is converted to
   * @return T the converted object
   * @throws TBDException response exception requiring rollback
   */
  protected <T> T convertResponseToDomain(String responsePayload, Class<T> classType)
      throws TBDException {
    T response = null;

    try {
        response = objectMapper.readValue(responsePayload, classType);
      } catch (JsonProcessingException e) {
        log.error("processing error converting JSON", e);
        throw new TBDException();
      }

    return response;
  }

  /**
   * API call type.
   */
  public enum InteracRequestType {DEFAULT, INITIATE_PAYMENT, SUBMIT_PAYMENT, REVERSE_PAYMENT, CANCEL_PAYMENT, GET_PAYMENT, AUTHENTICATE_PAYMENT,
    DECLINE_PAYMENT, COMPLETE_PAYMENT_BEGIN, COMPLETE_PAYMENT_COMMIT, UPDATE_PAYMENT_FRAUD_STATUS, BLOCK_PAYMENT_FRAUD, UNBLOCK_PAYMENT_FRAUD}
}
