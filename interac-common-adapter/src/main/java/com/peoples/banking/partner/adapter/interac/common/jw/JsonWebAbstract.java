package com.peoples.banking.partner.adapter.interac.common.jw;

import com.nimbusds.jose.jwk.RSAKey;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import lombok.Getter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public abstract class JsonWebAbstract {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(JsonWebAbstract.class);

  /**
   * Sender's RSA Key.
   */
  protected RSAKey senderRsaKey;

  /**
   * Flag indicating sender RSA key has been initialized.
   */
  @Getter
  protected boolean encryptionInitialized = false;

  /**
   * Receiver's public certificate RSA key.
   */
  protected RSAKey receiverRsaKey;

  /**
   * Flag indicating receiver RSA public certificate.
   */
  @Getter
  protected boolean decryptionInitialized = false;

  /**
   * Initializes with RSA private key.
   *
   * @param jsonWebInstance
   */
  public void setSenderRsaPrivateKey(JsonWeb jsonWebInstance) throws AdapterException {
    if (jsonWebInstance == null || !jsonWebInstance.isPrivateKey()
        || jsonWebInstance.getRsaKey() == null) {
      encryptionInitialized = false;
      LOGGER.error("supplied object is NOT a valid private key");
      throw new AdapterException("supplied object is NOT a valid private key",
          ErrorCode.INVALID_PRIVATE_KEY);
    }

    encryptionInitialized = true;
    senderRsaKey = jsonWebInstance.getRsaKey();
  }

  /**
   * Initializes with RSA public certificate.
   *
   * @param jsonWebInstance
   */
  public void setReceiverRsaPublicCert(JsonWeb jsonWebInstance) throws AdapterException {
    if (jsonWebInstance == null || !jsonWebInstance.isPublicCert()
        || jsonWebInstance.getRsaKey() == null) {
      decryptionInitialized = false;

      LOGGER.error("supplied object is NOT a valid public certificate");
      throw new AdapterException("supplied object is NOT a valid public certificate",
          ErrorCode.INVALID_PUBLIC_CERTIFICATE);
    }

    decryptionInitialized = true;
    receiverRsaKey = jsonWebInstance.getRsaKey();
  }
}
