package com.peoples.banking.partner.adapter.interac.common.jws;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jwt.SignedJWT;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWebAbstract;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.codec.binary.Hex;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * ' RFC 7515 - JSON Web Signature (JWS) represents header and payload signature signing.
 */
@Component
public class JsonWebSignature extends JsonWebAbstract {

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(JsonWebSignature.class);
  private static String SIGNATURE_TIMESTAMP = "signature-timestamp";
  private static String PAYLOAD_DIGEST = "payload-digest";
  private static String MESSAGE_DIGEST_SHA_256 = "SHA-256";

  private static String SIGNATURE_CONTENT_TYPE = "application/json";
  private static String SIGNATURE_ENCRYPTED_CONTENT_TYPE = "application/jose";

  /**
   * Constants.
   */
  @Getter
  @Setter
  private Long timeToLiveMilli = 30000l;

  /**
   * Digitally sign the JWS token.
   *
   * @param httpPayload raw HTTP payload
   * @param timestamp   timestamp of this instant in time
   * @param cty         content type
   * @param kid         key-id
   * @return
   */
  public String buildJWS(String httpPayload, Instant timestamp, String cty, String kid) throws AdapterException {
    // sanity check
    if (!encryptionInitialized) {
      LOGGER.error("sender private key object not yet initialized");
      throw new AdapterException("object not initialized for encryption", ErrorCode.INVALID_STATE);
    }

    // sanity check
    if (cty == null || cty.isBlank() || kid == null || kid.isBlank()) {
      LOGGER.debug("either cty or kid null or blank");
      throw new AdapterException("null or empty inputs to function", ErrorCode.INVALID_INPUT);
    }

    // default timestamp if null or empty
    if (timestamp == null) {
      timestamp = Instant.now();
      LOGGER.debug("initializing timestamp to default, timestamp={}", timestamp);
    }

    DateTimeFormatter formatter = new DateTimeFormatterBuilder().appendInstant(3).toFormatter();
    // step 1 - construct the JWS payload -- function validates null or blank httpPayload
    // Interac may send all 0s in the milliseconds therefore we need to have the consistent behaviour of
    // having 3 digits in milliseconds even all are 0s
    String jwsPayload = this.constructJwsPayload(httpPayload, formatter.format(timestamp.truncatedTo(ChronoUnit.MILLIS)));

    // step 2 - instantiate signer util
    JWSSigner signer = null;
    try {
      signer = new RSASSASigner(senderRsaKey);
    } catch (JOSEException e) {
      // should NOT be triggered, as we validate this during initialization
      LOGGER.error("failed on JWE payload encryption", e);
      throw new AdapterException();
    }

    // step 3 - build jws
    JWSObject jwsObject = new JWSObject(
        // JWS header
        new JWSHeader.Builder(JWSAlgorithm.RS256).keyID(kid).type(JOSEObjectType.JWT).contentType(cty).build(),
        // JWS payload
        new Payload(jwsPayload)
    );

    LOGGER.info("JWS header for signing={}", jwsObject.getHeader());

    // step 5 - RSA signature
    try {
      jwsObject.sign(signer);
    } catch (JOSEException e) {
      // should NOT be triggered, as we validate this during initialization
      LOGGER.error("failed on JWE payload encryption", e);
      throw new AdapterException();
    }

    // step 5 - serialize and compact JWS
    // <header-hash>.<payload-hash>.<signature-hash>
    String signedJws = jwsObject.serialize();

    LOGGER.debug("JWS={}", signedJws);

    return signedJws;
  }

  /**
   * Verify the JWS token.
   *
   * @param httpPayload        raw HTTP payload
   * @param jwsToken           JWS token to verify
   * @param signatureTimestamp signature-timestamp of the request (transaction time)
   * @param keyId              expected kid from header
   * @return
   */
  public JwsVerificationResult validateJWS(String httpPayload, String jwsToken, String signatureTimestamp, String keyId)
      throws AdapterException {
    // default to success
    JwsVerificationResult result = JwsVerificationResult.VERIFIED;
    // sanity check
    if (!decryptionInitialized) {
      LOGGER.error("receiver public key object not yet initialized");
      throw new AdapterException("object not initialized for decryption", ErrorCode.INVALID_STATE);
    }

    // sanity check
    if (jwsToken == null || jwsToken.isBlank() || signatureTimestamp == null || signatureTimestamp.isBlank()) {
      LOGGER.debug("either jws token or timestamp null or blank");
      throw new AdapterException("null or empty inputs to function", ErrorCode.INVALID_INPUT);
    }

    //
    // step 1a - verify TTL
    Instant currentTs = Instant.now();
    Instant transactionTs = Instant.parse(signatureTimestamp);

    // timestamp can be in the past or future, account for both edge cases
    long timeDelta = Math.abs(currentTs.toEpochMilli() - transactionTs.toEpochMilli());

    // determines outcome
    if (timeDelta > timeToLiveMilli) {
      result = JwsVerificationResult.FAILED_TTL;
    }

    // logging
    if (LOGGER.isDebugEnabled()) {
      // expensive call (two .toStrings), wrap with DEBUG check
      LOGGER.debug("JWS requestTimestamp={}, currentTimestamp={}, timeDelta={} ms, TTL=+/-{} ms",
          signatureTimestamp, currentTs.toString(), timeDelta, timeToLiveMilli);
    }

    //
    // step 2 - verify the signature
    SignedJWT signedJwt = null;

    // continue only if still valid signature
    if (JwsVerificationResult.VERIFIED.equals(result)) {
      // first, parse the signature
      try {
        signedJwt = SignedJWT.parse(jwsToken);
      } catch (ParseException e) {
        LOGGER.debug("JWS token cannot be parsed", e);
        throw new AdapterException("JWS token cannot be parsed", ErrorCode.INVALID_JWS);
      }

      // then, verify the signature
      JWSVerifier verifier = null;
      try {
        verifier = new RSASSAVerifier(receiverRsaKey);
      } catch (JOSEException e) {
        // should NOT be triggered, as we validate this during initialization
        LOGGER.error("failed on loading receiver public cert", e);
        throw new AdapterException();
      }

      try {
        if (!signedJwt.verify(verifier)) {
          result = JwsVerificationResult.FAILED_SIGNATURE;
        }
      } catch (JOSEException e) {
        LOGGER.debug("unable to verify JWS token", e);
        result = JwsVerificationResult.FAILED_SIGNATURE;
      }
    } else {
      LOGGER.debug("JWS verification failed [{} ms > {} ms]", timeDelta, timeToLiveMilli);
    }

    // continue only if still valid signature
    if (JwsVerificationResult.VERIFIED.equals(result)) {
      //
      // step 3 - verify the header
      JWSHeader header = signedJwt.getHeader();

      if (!JWSAlgorithm.RS256.equals(header.getAlgorithm())) {
        LOGGER.warn("error in signature alg; expected={}, received={}", JWSAlgorithm.RS256, header.getAlgorithm());
        result = JwsVerificationResult.FAILED_SIGNATURE;
      } else if (!keyId.equals(header.getKeyID())) {
        LOGGER.warn("error in signature kid; expected={}, received={}", keyId, header.getKeyID());
        result = JwsVerificationResult.FAILED_SIGNATURE;
      } else if (!(SIGNATURE_CONTENT_TYPE.equals(header.getContentType()) || SIGNATURE_ENCRYPTED_CONTENT_TYPE.equals(header.getContentType()))) {
        LOGGER.warn("error in signature cty; expected={} or {}, received={}", SIGNATURE_CONTENT_TYPE, SIGNATURE_ENCRYPTED_CONTENT_TYPE, header.getContentType());
        result = JwsVerificationResult.FAILED_SIGNATURE;
      } else if (!header.isBase64URLEncodePayload()) {
        LOGGER.warn("error in signature b64; expected={}, received={}", true, header.isBase64URLEncodePayload());
        result = JwsVerificationResult.FAILED_SIGNATURE;
      } else {
        LOGGER.debug("successfully validated JWS header parameters");
      }

      //
      // step 4 - verify the signed content.
      String signedJwtPayload = signedJwt.getPayload().toString();
      LOGGER.debug("JWS (received) payload={}", signedJwtPayload);

      String expectedJwtPayload = this.constructJwsPayload(httpPayload, signatureTimestamp);
      LOGGER.debug("JWS (expected) payload={}", expectedJwtPayload);

      if (signedJwtPayload != null && signedJwtPayload.equals(expectedJwtPayload)) {
        LOGGER.debug("JWS expected payload matches JWS signed payload");
      } else {
        LOGGER.error("JWS expected payload does NOT match JWS signed payload");
        result = JwsVerificationResult.FAILED_SIGNATURE;
      }
    } else {
      LOGGER.debug("JWS verification failed");
    }

    // return result of verification
    LOGGER.info("JWS={}", result.toString());
    return result;
  }

  /**
   * Build JWS payload from HTTP body payload + timestamp
   *
   * @param httpPayload        HTTP body payload
   * @param signatureTimestamp signatureTimestamp
   * @return
   */
  private String constructJwsPayload(String httpPayload, String signatureTimestamp) throws AdapterException {
    // no sanity check, as it was done in caller within this class

    LOGGER.debug("constructing JWS");
    // construct JWS (efficiently)
    StringBuilder jwsPayload = new StringBuilder("{\"");
    jwsPayload.append(SIGNATURE_TIMESTAMP);
    jwsPayload.append("\":\"");
    jwsPayload.append(signatureTimestamp);
    jwsPayload.append("\"");

    if (httpPayload == null || httpPayload.isBlank()) {
      LOGGER.debug("with no HTTP payload to add");
    } else {
      LOGGER.debug("HTTP payload={}", httpPayload);

      // digest the HTTP payload first
      MessageDigest md = null;
      try {
        md = MessageDigest.getInstance(MESSAGE_DIGEST_SHA_256);
      } catch (NoSuchAlgorithmException e) {
        // should NOT be triggered, as we validate this during initialization
        LOGGER.error("failed on JWS digest", e);
        throw new AdapterException();
      }
      // set encoding and digest
      byte[] hashInBytes = md.digest(httpPayload.getBytes(StandardCharsets.UTF_8));

      // append to JWS in digested & hex-encoded form
      jwsPayload.append(",\"");
      jwsPayload.append(PAYLOAD_DIGEST);
      jwsPayload.append("\":\"");
      jwsPayload.append(Hex.encodeHexString(hashInBytes));
      jwsPayload.append("\"");
    }

    // finish constructing JWS
    jwsPayload.append("}");

    // return
    return jwsPayload.toString();
  }

  /**
   * JWS verification result.
   */
  public enum JwsVerificationResult {VERIFIED, FAILED_TTL, FAILED_SIGNATURE}
}
