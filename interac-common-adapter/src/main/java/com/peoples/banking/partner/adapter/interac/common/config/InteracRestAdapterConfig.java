package com.peoples.banking.partner.adapter.interac.common.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;

/**
 * Configuration class for <i>InteracRestAdapter</i>.
 */
@Configuration
public class InteracRestAdapterConfig {

  /**
   * Instantiate new <i>JsonWeb</i> instance.
   *
   * @return instantiated object.
   */
  @Bean(name = "jwSender")
  public JsonWeb jsonWebSender() {
    return new JsonWeb();
  }

  /**
   * Instantiate new <i>JsonWeb</i> instance.
   *
   * @return instantiated object.
   */
  @Bean(name = "jwReceiver")
  public JsonWeb jsonWebReceiver() {
    return new JsonWeb();
  }

  /**
   * Instantiate new <i>ObjectMapper</i> instance.
   *
   * @return instantiated object.
   */
  @Bean
  public ObjectMapper objectMapper() {
    // initialize ObjectMapper with appropriate modules to correctly convert dates
    SimpleModule simpleModule = new SimpleModule();
    simpleModule.addSerializer(OffsetDateTime.class, new JsonSerializer<OffsetDateTime>() {
      @Override
      public void serialize(OffsetDateTime offsetDateTime, JsonGenerator jsonGenerator,
          SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
        jsonGenerator
            .writeString(DateTimeFormatter.ISO_INSTANT.
                format(offsetDateTime.truncatedTo(ChronoUnit.MILLIS)));
      }
    });
    ObjectMapper objectMapper = new ObjectMapper()
        .registerModule(new ParameterNamesModule())
        .registerModule(new Jdk8Module())
        .registerModule(new JavaTimeModule())
        .registerModule(simpleModule)
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .setSerializationInclusion(Include.NON_NULL);
    return objectMapper;
  }
}