package com.peoples.banking.partner.adapter.interac.common.config;

import com.peoples.banking.adapter.base.config.RestTemplateConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;

/**
 * common Interac RestTemplateConfig
 */
public abstract class InteracRestTemplateConfigBase extends RestTemplateConfig {
  /**
   * HTTP client.
   */
  @Autowired
  @Qualifier("interacHttpClient")
  CloseableHttpClient httpClient;

  /**
   * Instantiate new <i>RestTemplate</i> instance.
   *
   * @return instantiated object.
   */
  @Bean(name = "interacRestTemplate")
  public RestTemplate restTemplate() {
    return getRestTemplate();
  }

  /**
   * Instantiate new <i>ClientHttpRequestFactory</i> instance.
   *
   * @return instantiated object.
   */
  @Bean(name = "interacClientHttpRequestFactory")
  public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() {
    return getClientHttpRequestFactory();
  }

  /**
   * Instantiate new <i>TaskScheduler</i>, for reaping stale HTTP connections from the pool.
   *
   * @return instantiated object.
   */
  @Bean(name = "interacTaskScheduler")
  public TaskScheduler taskScheduler() {
    return getTaskScheduler();
  }

  /**
   * @inheritDoc
   */
  @Override
  public CloseableHttpClient getHttpClient() {
    return this.httpClient;
  }
}
