package com.peoples.banking.partner.adapter.interac.common.jw;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.KeyType;
import com.nimbusds.jose.jwk.RSAKey;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import lombok.Getter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * Implementation of public certificate or private key loader for JW*.
 */
@Component
public class JsonWeb {

  /**
   * Constant.
   */
  public final static String CERTIFICATE_X509 = "X.509";

  /**
   * Logging facility.
   */
  private static final Logger LOGGER = LogManager.getLogger(JsonWeb.class);
  /**
   * RSA Key for encryption or decryption.
   */
  @Getter
  protected RSAKey rsaKey;

  /**
   * Type of JsonWeb object initialized.
   */
  private Type character = Type.NOT_DEFINED;

  /**
   * Convenience function to determine if instance is a private key.
   *
   * @return
   */
  public boolean isPrivateKey() {
    return character == Type.PRIVATE_KEY;
  }

  /**
   * Convenience function to determine if instance is a public certificate.
   *
   * @return
   */
  public boolean isPublicCert() {
    return character == Type.PUBLIC_CERT;
  }

  /**
   * Initialize with a private key (supports PEM-encoded private key).
   *
   * @param pathToFile full path to the file
   */
  public void initPrivateKey(String pathToFile) throws AdapterException {
    // sanity check
    if (pathToFile == null || pathToFile.isBlank()) {
      LOGGER.debug("pathToFile=(null)");
      throw new AdapterException("pathToFile=(null)", ErrorCode.INVALID_INPUT);
    }

    LOGGER.debug("initializing PEM private key from file={}", pathToFile);

    RSAKey privateKey = null;

    // reference to file, will not throw exception as we validate its inputs above
    File file = new File(pathToFile);
    if (!file.isFile()) {
      LOGGER.error("failed to open or read file={}", pathToFile);
      throw new AdapterException("file=" + pathToFile, ErrorCode.FILE_NOT_FOUND);
    }

    byte[] fileAsByes = null;
    // read in PEM file contents as byte[]
    try (
        // read in PEM file contents using try-with-resource to autoclose resources
        FileInputStream fileInputStream = new FileInputStream(pathToFile);
        InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream)
    ) {
      fileAsByes = fileInputStream.readAllBytes();
    } catch (IOException e) {
      LOGGER.error("not a valid RSA (PEM-format) private  key, file={}", pathToFile, e);
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PRIVATE_KEY);
    }

    // construct Nimbus JWK object to validate the private key
    // friendly-name of the key (set as CN name during creation) is set as kid field
    JWK jwk = null;
    try {
      jwk = JWK.parseFromPEMEncodedObjects(new String(fileAsByes));
    } catch (JOSEException e) {
      LOGGER.error("failed to load PEM file", e);
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PRIVATE_KEY);
    }

    // validate the key (only accept RSA keys as per eTransfer specification)
    if (jwk == null || !jwk.getKeyType().equals(KeyType.RSA)) {
      LOGGER.error("invalid key loaded, keyType={}", jwk != null ? jwk.getKeyType() : "null");
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PRIVATE_KEY);
    } else {
      LOGGER.debug("building private RSA JWK");
      privateKey = jwk.toRSAKey();

      // validate
      if (!privateKey.isPrivate()) {
        LOGGER.error("public certificate loaded instead of private key");
        throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PRIVATE_KEY);
      }
    }

    // debug logging
    LOGGER.debug("initialization complete");

    // set its character (public, private or none).
    character = Type.PRIVATE_KEY;

    rsaKey = privateKey;
  }

  /**
   * Initialize with a public certificate (supports PEM-encoded X.509 public certificate).
   *
   * @param pathToFile full path to the file
   */
  public void initPublicCert(String pathToFile, String kId) throws AdapterException {
    // sanity check
    if (pathToFile == null || pathToFile.isBlank()) {
      LOGGER.debug("pathToFile=(null)");
      throw new AdapterException("pathToFile=(null)", ErrorCode.INVALID_INPUT);
    } else if (kId == null || kId.isBlank()) {
      LOGGER.debug("kId=(null)");
      throw new AdapterException("kId=(null)", ErrorCode.INVALID_INPUT);
    }

    LOGGER.debug("initializing X.509 public certificate from file={}", pathToFile);

    RSAKey publicCert = null;

    // reference to file, will not throw exception as we validate its inputs above
    File file = new File(pathToFile);
    if (!file.isFile()) {
      LOGGER.error("failed to open or read file={}", pathToFile);
      throw new AdapterException("file=" + pathToFile, ErrorCode.FILE_NOT_FOUND);
    }

    X509Certificate certificate = null;
    try (
        // read in PEM file contents using try-with-resource to autoclose resources
        FileInputStream fileInputStream = new FileInputStream(pathToFile)
    ) {
      // instance of X.509 certificate factory
      CertificateFactory certFactory = CertificateFactory.getInstance(CERTIFICATE_X509);

      // read in X.509 certificate
      certificate = (X509Certificate) certFactory.generateCertificate(fileInputStream);
    } catch (CertificateException | IOException e) {
      LOGGER.error("not a valid X.509 certificate, file={}", pathToFile, e);
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PUBLIC_CERTIFICATE);
    }

    // validate certificate can be used for digital signatures
    if (!isSignatureKeyPair(certificate)) {
      LOGGER.error("public key does NOT have Digital Signature usage defined");
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PUBLIC_CERTIFICATE);
    }

    // create the Nimbus JWK object from the certificate
    JWK jwk = null;
    try {
      jwk = JWK.parse(certificate);
    } catch (JOSEException e) {
      LOGGER.error("failed to load X.509 file", e);
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PUBLIC_CERTIFICATE);
    }

    // validate the certificate
    if (jwk == null || !jwk.getKeyType().equals(KeyType.RSA)) {
      LOGGER.error("invalid key loaded, keyType={}", jwk != null ? jwk.getKeyType() : "null");
      throw new AdapterException("file=" + pathToFile, ErrorCode.INVALID_PUBLIC_CERTIFICATE);
    } else {
      LOGGER.debug("building public RSA JWK");
      publicCert = new RSAKey.Builder((RSAKey) jwk).keyID(kId).build();
    }

    // debug logging
    LOGGER.debug("initialization complete");

    // set its
    character = Type.PUBLIC_CERT;

    rsaKey = publicCert;
  }

  /**
   * Utility function to detect whether a certificate has the usage of Digital Signature.
   *
   * @param certificate X.509 certificate
   * @return: true/false
   */
  private boolean isSignatureKeyPair(X509Certificate certificate) {
    boolean[] certUse = certificate.getKeyUsage();
    if (certUse == null) {
      // no key usage presented guarantees NOT used for digital signature
      return false;
    }

    //  KeyUsage ::= BIT STRING {
    //     digitalSignature        (0),
    return certUse[0];
  }

  /**
   * Extracts the kid from the certificate/key CN.
   *
   * @return - the first section of the CN will be used as kid in JWT.
   * <pre>e.g. etapi-sign-key-ca000095-001</pre>
   */
  public String extractKeyId() throws AdapterException {
    String result = null;

    // validate
    if (rsaKey != null) {
      String rsaKeyCn = rsaKey.getKeyID();

      if (rsaKeyCn != null) {
        String[] sections = rsaKeyCn.split("\\.");
        result = sections[0];
      } else {
        throw new AdapterException(
            "no kid exists for this instance, likely a private key without a corresponding certificate",
            ErrorCode.INVALID_STATE);
      }
    } else {
      throw new AdapterException("not yet initialized", ErrorCode.INVALID_STATE);
    }

    return result;
  }

  /**
   * Type of RSAKey initialized.
   */
  public enum Type {
    PUBLIC_CERT, PRIVATE_KEY, NOT_DEFINED
  }

}
