package com.peoples.banking.partner.adapter.interac.common.config;

public final class InteracRestAdapterConstant {
  // interac header values

  // Interac headers
  public static final String HEADER_PARTICIPANT_ID = "x-et-participant-id";
  public static final String HEADER_PARTICIPANT_USER_ID = "x-et-participant-user-id";
  public static final String HEADER_INDIRECT_CONNECTOR_ID = "x-et-indirect-connector-id";
  public static final String HEADER_REQUEST_ID = "x-et-request-id";
  public static final String HEADER_RETRY_INDICATOR = "x-et-retry-indicator";
  public static final String HEADER_CHANNEL_INDICATOR = "x-et-channel-indicator";
  public static final String HEADER_SIGNATURE_TYPE = "x-et-api-signature-type";
  public static final String HEADER_SIGNATURE = "x-et-api-signature";
  public static final String HEADER_TRANS_TIME = "x-et-transaction-time";
  public static final String HEADER_PAYMENT_TRANSACTION_TOKEN = "x-payment-transaction-token";
  public static final String HEADER_RECEIVE_PAYMENT_TRANSACTION_TOKEN = "x-receive-payment-transaction-token";
  public static final String HEADER_AUTHORIZATION = "Authorization";
  public static final String HEADER_PRODUCT_CODE = "x-et-product-code";
  public static final String LOGGER_NAME = "APIPayloadLogger";

  public static final String SYSTEM_NAME_INTERAC = "Interac";

  public final static String DEFAULT_CONTENT_TYPE = "application/json";
  public final static String ENCRYPTED_CONTENT_TYPE = "application/jose";

  // TODO rename static constants so a reorg on this class doesn't ruin the flow

  private InteracRestAdapterConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
