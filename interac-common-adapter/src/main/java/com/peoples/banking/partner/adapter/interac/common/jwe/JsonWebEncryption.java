package com.peoples.banking.partner.adapter.interac.common.jwe;

import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSADecrypter;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWebAbstract;
import java.text.ParseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * RFC 7516 - JSON Web Encryption (JWE) represents encrypted content using JSON-based data
 * structures.
 */
@Component
public class JsonWebEncryption extends JsonWebAbstract {

  /**
   * Logging facility.
   */
  private static final Logger log = LogManager.getLogger(JsonWebEncryption.class);
  /**
   * Constants.
   */
  private static final JWEAlgorithm ALGORITHM = JWEAlgorithm.RSA_OAEP_256;
  private static final EncryptionMethod ENCRYPTION_METHOD = EncryptionMethod.A256GCM;


  /**
   * Encrypts the payload using the <i>Receiver's</i> public key.
   *
   * @param plainText decrypted text to encrypt
   * @return
   */
  public String encrypt(String plainText) throws AdapterException {
    // sanity check
    if (plainText == null || plainText.isBlank()) {
      log.warn("nothing to encrypt");
      return null;
    }

    // sanity check
    if (!encryptionInitialized) {
      log.error("sender private key object not yet initialized");
      throw new AdapterException("object not initialized for encryption",
          ErrorCode.INVALID_STATE);
    }

    // Step-01: Build the JWE header
    final JWEHeader.Builder builder = new JWEHeader.Builder(ALGORITHM, ENCRYPTION_METHOD);

    // Specify the key-id based on the eTransfer JWE spec
    builder.keyID(this.extractKeyIdFromCN(receiverRsaKey.getKeyID()));

    // Specify cty parameter in the header (indicate encrypted)
    builder.contentType(InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE);

    // consolidate the header
    final JWEHeader jweHeader = builder.build();

    // debug output
    log.info("JWE Header={}", jweHeader.toString());

    // Step-02: Init JWE envelope
    JWEObject jwe = new JWEObject(jweHeader, new Payload(plainText));

    try {
      // Step-03: Init the encryption with the receiver's public key
      final RSAEncrypter encrypter = new RSAEncrypter(receiverRsaKey.toRSAPublicKey());
      // Step-04: Do the encryption
      jwe.encrypt(encrypter);
    } catch (JOSEException e) {
      // should NOT be triggered, as we validate this during initialization
      log.error("failed on JWE payload encryption", e);
      throw new AdapterException();
    }

    // Step-05: JWE compact serialization
    String jweString = jwe.serialize();

    // debug output
    log.debug("JWE (encrypted payload)={}", jweString);

    return jweString;
  }

  /**
   * Decrypts the payload using the <i>Sender's</i> private key.
   *
   * @param encryptedText encrypted text to decrypt
   * @return
   */
  public String decrypt(String encryptedText) throws AdapterException {
    // sanity check
    if (encryptedText == null || encryptedText.isBlank()) {
      log.warn("nothing to decrypt");
      return null;
    }

    // sanity check
    if (!decryptionInitialized) {
      log.error("receiver public certificate object not yet initialized");
      throw new AdapterException("object not initialized for decryption",
          ErrorCode.INVALID_STATE);
    }

    // Step-01: Loading the encrypted JWE envelope
    JWEObject jwe = null;
    try {
      jwe = JWEObject.parse(encryptedText);
    } catch (ParseException e) {
      log.error("failed to parse encrypted JWE envelope: {}", e.getMessage());
      throw new AdapterException("failed during decryption of JWE envelope",
          ErrorCode.UNEXPECTED_EXCEPTION);
    }

    // Step-02: Init the decrypter with the sender's private key
    final RSADecrypter rsaDecrypter;
    try {
      rsaDecrypter = new RSADecrypter(senderRsaKey.toPrivateKey());
    } catch (JOSEException e) {
      log.error("failed to load JWE private key", e);
      throw new AdapterException();
    }

    // Step-03: Do decryption
    try {
      jwe.decrypt(rsaDecrypter);
    } catch (JOSEException e) {
      log.error("failed to decrypt payload", e);
      throw new AdapterException("failed to decrypt payload", ErrorCode.INVALID_INPUT);
    }

    // Step-04: serialize the clear-text payload
    String response = jwe.getPayload().toString();


    return response;
  }

  /**
   * Split the key alias name from the CN name.
   *
   * @param keyCN - the CN of the keyalias/griendly-name <pre>.g. etapi-sign-key-ca000095-001.devqa.ca000095.internal</pre>
   * @return - the first section of the CN will be used as kid in JWT. <pre>e.g.etapi-sign-key-ca000095-001</pre>
   */
  private String extractKeyIdFromCN(String keyCN) {
    String[] sections = keyCN.split("\\.");
    return sections[0];
  }
}
