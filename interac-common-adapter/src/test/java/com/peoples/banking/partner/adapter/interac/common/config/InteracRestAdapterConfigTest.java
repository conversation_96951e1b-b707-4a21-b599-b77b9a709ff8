package com.peoples.banking.partner.adapter.interac.common.config;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.context.support.AnnotationConfigContextLoader;
import com.fasterxml.jackson.databind.ObjectMapper;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {InteracRestAdapterConfig.class}, loader=AnnotationConfigContextLoader.class)
@SpringJUnitConfig()
public class InteracRestAdapterConfigTest {
  
  @Autowired
  private ObjectMapper objectmapper;
  
  private SamplePaymentDomain payment;
  private String paymentDomainString;
  private String paymentDomainUnrecognizedFieldString;
   
  
  
  @BeforeEach
  private void setup() {
    payment = new SamplePaymentDomain("Luna", 100);
    paymentDomainString = "{\"senderName\":\"Luna\",\"amount\":100}";
    paymentDomainUnrecognizedFieldString = "{\"senderName\":\"Luna\",\"amount\":100,\"recipientName\":\"Helen\"}";
  }
  

  @Test
  public void objectMapper_writeValueAsString_success() {
    
    String paymentString = assertDoesNotThrow(() -> {
      return objectmapper.writeValueAsString(payment);
    });
    
    assertEquals(paymentDomainString, paymentString);
  }
  
  @Test
  public void objectMapper_readValue_success() {
    SamplePaymentDomain convertedPayment = assertDoesNotThrow(() -> {
      return objectmapper.readValue(paymentDomainString, SamplePaymentDomain.class);
    });
    
    assertEquals(payment.getSenderName(), convertedPayment.getSenderName());
    assertEquals(payment.getAmount(), convertedPayment.getAmount());
  }
    
  @Test
  public void objectMapper_readValue_unrecongizedField_success() {
    SamplePaymentDomain convertedPayment = assertDoesNotThrow(() -> {
      return objectmapper.readValue(paymentDomainUnrecognizedFieldString, SamplePaymentDomain.class);
    });
    
    assertEquals(payment.getSenderName(), convertedPayment.getSenderName());
    assertEquals(payment.getAmount(), convertedPayment.getAmount());
  }
}
