package com.peoples.banking.partner.adapter.interac.common.jw;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@TestMethodOrder(OrderAnnotation.class)
@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig()
class JsonWebAbstractTest {

  /**
   * Mocked object.
   */
  @Mock
  JsonWeb mockedJsonWeb;

  /**
   * Unit under test is JsonWebAbstract::setSenderRsaPrivateKey
   */

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   null sender private key object
   * </pre>
   */
  @Order(1)
  @Test
  void setSenderRsaPrivateKey_null() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setSenderRsaPrivateKey(null);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   new but not yet initialized
   * </pre>
   */
  @Order(1)
  @Test
  void setSenderRsaPrivateKey_uninitialized() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);
    JsonWeb jsonWeb = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setSenderRsaPrivateKey(jsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   not a private key
   * </pre>
   */
  @Order(1)
  @Test
  void setSenderRsaPrivateKey_notPrivateKey() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    Mockito
        .when(mockedJsonWeb.isPrivateKey())
        .thenReturn(false);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setSenderRsaPrivateKey(mockedJsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   null RSAKey
   * </pre>
   */
  @Order(1)
  @Test
  void setSenderRsaPrivateKey_nullRSAKey() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    Mockito
        .when(mockedJsonWeb.isPrivateKey())
        .thenReturn(true);

    Mockito
        .when(mockedJsonWeb.getRsaKey())
        .thenReturn(null);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setSenderRsaPrivateKey(mockedJsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   success
   * </pre>
   */
  @Order(1)
  @Test
  void setSenderRsaPrivateKey_successAndThenNull() throws AdapterException {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    // initialize encryption keys (sender)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPrivateKey = classLoader.getResource(TestConstant.VALID_SENDER_PRIVATE_KEY)
        .getFile();

    JsonWeb peoplesJsonWeb = new JsonWeb();
    peoplesJsonWeb.initPrivateKey(absolutePathPrivateKey);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(peoplesJsonWeb);
    Assertions.assertTrue(peoplesJsonWeb.isPrivateKey());

    // unit under test -- add to uut
    String encryptedText = null;
    try {
      jsonWebAbstract.setSenderRsaPrivateKey(peoplesJsonWeb);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertTrue(jsonWebAbstract.isEncryptionInitialized());
    Assertions.assertFalse(jsonWebAbstract.isDecryptionInitialized());

    // unit under test -- then set to null
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setSenderRsaPrivateKey(null);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
    Assertions.assertFalse(jsonWebAbstract.isEncryptionInitialized());
    Assertions.assertFalse(jsonWebAbstract.isDecryptionInitialized());
  }

  /**
   * Unit under test is JsonWebEncryption::setReceiverRsaPublicCert
   */

  /**
   * Unit under test is JsonWebEncryption::setReceiverRsaPublicCert
   * <pre>
   *   null sender private key object
   * </pre>
   */
  @Order(2)
  @Test
  void setReceiverRsaPublicCert_null() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setReceiverRsaPublicCert(null);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   new but not yet initialized
   * </pre>
   */
  @Order(2)
  @Test
  void setReceiverRsaPublicCert_uninitialized() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);
    JsonWeb jsonWeb = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setReceiverRsaPublicCert(jsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setReceiverRsaPublicCert
   * <pre>
   *   not a public certificate key
   * </pre>
   */
  @Order(2)
  @Test
  void setReceiverRsaPublicCert_notPublicKey() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    Mockito
        .when(mockedJsonWeb.isPublicCert())
        .thenReturn(false);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setReceiverRsaPublicCert(mockedJsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setReceiverRsaPublicCert
   * <pre>
   *   null RSAKey
   * </pre>
   */
  @Order(2)
  @Test
  void setReceiverRsaPublicCert_nullRSAKey() {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    Mockito
        .when(mockedJsonWeb.isPublicCert())
        .thenReturn(true);

    Mockito
        .when(mockedJsonWeb.getRsaKey())
        .thenReturn(null);

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setReceiverRsaPublicCert(mockedJsonWeb);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebEncryption::setSenderRsaPrivateKey
   * <pre>
   *   success
   * </pre>
   */
  @Order(2)
  @Test
  void setReceiverRsaPublicCert_successAndThenNull() throws AdapterException {
    // initialize
    JsonWebAbstract jsonWebAbstract = Mockito
        .mock(JsonWebAbstract.class, Mockito.CALLS_REAL_METHODS);

    //  initialize encryption keys (receiver)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPublicCert = classLoader.getResource(TestConstant.VALID_RECEIVER_PUBLIC_CERT)
        .getFile();

    JsonWeb interacJsonWeb = new JsonWeb();
    interacJsonWeb
        .initPublicCert(absolutePathPublicCert, TestConstant.RECEIVER_KEY_ID);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(interacJsonWeb);
    Assertions.assertTrue(interacJsonWeb.isPublicCert());

    // unit under test -- add to uut
    try {
      jsonWebAbstract.setReceiverRsaPublicCert(interacJsonWeb);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertFalse(jsonWebAbstract.isEncryptionInitialized());
    Assertions.assertTrue(jsonWebAbstract.isDecryptionInitialized());

    // unit under test -- then set to null
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      jsonWebAbstract.setReceiverRsaPublicCert(null);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
    Assertions.assertFalse(jsonWebAbstract.isEncryptionInitialized());
    Assertions.assertFalse(jsonWebAbstract.isDecryptionInitialized());
  }
}