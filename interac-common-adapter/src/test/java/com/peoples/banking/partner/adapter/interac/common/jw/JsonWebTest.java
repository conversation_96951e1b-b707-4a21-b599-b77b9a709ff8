package com.peoples.banking.partner.adapter.interac.common.jw;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@TestInstance(Lifecycle.PER_CLASS)
@SpringJUnitConfig()
public class JsonWebTest {
  /**
   * Unit under test is JsonWeb::initPrivateKey
   */

  /**
   * Unit under test is JsonWeb::initPrivateKey
   * <pre>
   *   null and empty arguments
   * </pre>
   *
   * @param filePath inputs for parameterized unit test
   */
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void initPrivateKey_nullAndEmpty(String filePath) {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPrivateKey(filePath);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_INPUT,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPrivateKey
   * <pre>
   *   success
   * </pre>
   */
  @Test
  void initPrivateKey_success() {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(TestConstant.VALID_SENDER_PRIVATE_KEY).getFile();

    // unit under test
    try {
      uut.initPrivateKey(absolutePath);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(uut.getRsaKey());
    Assertions.assertTrue(uut.getRsaKey().isPrivate());
    Assertions.assertTrue(uut.isPrivateKey());
    Assertions.assertFalse(uut.isPublicCert());
  }

  /**
   * Unit under test is JsonWeb::initPrivateKey
   * <pre>
   *   variations of corrupt and empty RSA keys
   * </pre>
   *
   * @param filePath inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {TestConstant.INVALID_SENDER_PRIVATE_KEY_EMPTY_1,
      TestConstant.INVALID_SENDER_PRIVATE_KEY_EMPTY_2,
      TestConstant.INVALID_SENDER_PRIVATE_KEY_CORRUPT_1,
      TestConstant.INVALID_SENDER_PRIVATE_KEY_CORRUPT_2}
  )
  void initPrivateKey_invalidPrivateKeyFile(String filePath) {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(filePath).getPath();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPrivateKey(absolutePath);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPrivateKey
   * <pre>
   *   elliptical curve private key, not RSA
   *   public certificate, not RSA private key
   * </pre>
   */
  @ParameterizedTest
  @ValueSource(strings = {TestConstant.INVALID_SENDER_PRIVATE_KEY_ELLIPTICAL_CURVE,
      TestConstant.INVALID_SENDER_PRIVATE_KEY_PUBLIC_CERT}
  )
  void initPrivateKey_nonRsaPrivateKeyFile(String filePath) {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(filePath)
        .getPath();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPrivateKey(absolutePath);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PRIVATE_KEY,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPrivateKey
   * <pre>
   *   non existent file
   * </pre>
   */
  @Test
  void initPrivateKey_nonExistentPrivateKeyFile() {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPrivateKey("/some/invalid/file/path.rsa.key");
    });

    // assertions
    Assertions.assertEquals(ErrorCode.FILE_NOT_FOUND,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   */

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   null and empty arguments for filePath
   * </pre>
   *
   * @param filePath inputs for parameterized unit test
   */
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void initPublicCert_nullAndEmptyFilePath(String filePath) {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPublicCert(filePath, TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_INPUT,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   null and empty arguments for kId
   * </pre>
   *
   * @param kId inputs for parameterized unit test
   */
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void initPublicCert_nullAndEmptyKid(String kId) {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPublicCert(TestConstant.VALID_RECEIVER_PUBLIC_CERT, kId);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_INPUT,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   success
   * </pre>
   */
  @Test
  void initPublicCert_success() {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(TestConstant.VALID_RECEIVER_PUBLIC_CERT)
        .getFile();

    // unit under test
    try {
      uut.initPublicCert(absolutePath, TestConstant.RECEIVER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(uut.getRsaKey());
    Assertions.assertFalse(uut.getRsaKey().isPrivate());
    Assertions.assertFalse(uut.isPrivateKey());
    Assertions.assertTrue(uut.isPublicCert());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   variations of corrupt and empty RSA keys
   * </pre>
   *
   * @param filePath inputs for parameterized unit test
   */
  @ParameterizedTest
  @ValueSource(strings = {TestConstant.INVALID_RECEIVER_PUBLIC_CERT_EMPTY_1,
      TestConstant.INVALID_RECEIVER_PUBLIC_CERT_EMPTY_2,
      TestConstant.INVALID_RECEIVER_PUBLIC_CERT_CORRUPT_1,
      TestConstant.INVALID_RECEIVER_PUBLIC_CERT_CORRUPT_2,
      TestConstant.INVALID_RECEIVER_PUBLIC_CERT_NO_KEYUSAGE}
  )
  void initPublicCert_invalidPublicCertFile(String filePath) {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(filePath).getPath();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPublicCert(absolutePath, TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   elliptical curve public cert, not RSA
   * </pre>
   */
  @Test
  void initPublicCert_nonRsaPublicCertFile() {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader
        .getResource(TestConstant.INVALID_RECEIVER_PUBLIC_CERT_ELLIPTICAL_CURVE)
        .getPath();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPublicCert(absolutePath, TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_PUBLIC_CERTIFICATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::initPublicCert
   * <pre>
   *   non existent file
   * </pre>
   */
  @Test
  void initPublicCert_nonExistentPublicCertFile() {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.initPublicCert("/some/invalid/file/path.rsa.key",
          TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions.assertEquals(ErrorCode.FILE_NOT_FOUND,
        ((AdapterException) exception).getErrorCode());
  }


  /**
   * Unit under test is JsonWeb::extractKeyId
   * <pre>
   *   initialization of public certificate.
   * </pre>
   */
  @Test
  void extractKeyId_success() {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(TestConstant.VALID_RECEIVER_PUBLIC_CERT)
        .getFile();

    // initialize - pre-requisite setup
    try {
      uut.initPublicCert(absolutePath, TestConstant.RECEIVER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    Assertions.assertNotNull(uut.getRsaKey());
    Assertions.assertFalse(uut.getRsaKey().isPrivate());

    // unit under test
    String keyId = null;
    try {
      keyId = uut.extractKeyId();
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(keyId);
    Assertions.assertEquals(TestConstant.RECEIVER_KEY_ID, keyId);
  }

  /**
   * Unit under test is JsonWeb::extractKeyId
   * <pre>
   *   no initialization executed yet.
   * </pre>
   */
  @Test
  void extractKeyId_nonInitializedRsaKey() {
    // initialize
    JsonWeb uut = new JsonWeb();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.extractKeyId();
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_STATE,
        ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWeb::extractKeyId
   * <pre>
   *   initialization of public certificate.
   * </pre>
   */
  @Test
  void extractKeyId_initializedPrivateKey() {
    // initialize
    JsonWeb uut = new JsonWeb();
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePath = classLoader.getResource(TestConstant.VALID_SENDER_PRIVATE_KEY).getFile();

    // initialize - pre-requisite setup
    try {
      uut.initPrivateKey(absolutePath);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    Assertions.assertNotNull(uut.getRsaKey());
    Assertions.assertTrue(uut.getRsaKey().isPrivate());

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.extractKeyId();
    });

    // assertions
    Assertions.assertEquals(ErrorCode.INVALID_STATE,
        ((AdapterException) exception).getErrorCode());
  }
}