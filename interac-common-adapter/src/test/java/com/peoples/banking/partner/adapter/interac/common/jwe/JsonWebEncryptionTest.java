package com.peoples.banking.partner.adapter.interac.common.jwe;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@TestMethodOrder(OrderAnnotation.class)
@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig()
class JsonWebEncryptionTest {

  /**
   * Constants.
   */
  private static final String PLAIN_TEXT_1 = "Be yourself; everyone else is already taken.";
  private static final String ENCRYPTED_TEXT = "eyJraWQiOiJldGFwaS1zaWduLWtleS1jYTAwMDk5OS0wMDEiLCJjdHkiOiJhcHBsaWNhdGlvblwvanNvbiIsImVuYyI6IkEyNTZHQ00iLCJhbGciOiJSU0EtT0FFUC0yNTYifQ.MAPDlClA38gBs0FDI1N8IafZXtatwZvSgmAvZMYpb3SFky9GUYJRwr4oMgMXI1k1H6N2QDLRQJrJ0jMtHE3EBd2y-p29BQPLspQ_c49Bsjhlc9EPLmQ-UI5BdwmS-TknoLTHe-qp6PPf0s_iDHCUwAjHwLhxGwj7U7JY22uhMzApiKa4oE9xGlRJurKSjZNWnfuPPEq_z5y92xLd8hsnNGdL933gGeBt_xacx1l9iBVljE57EJ1S7NThLsSQgzc3-AL1CYX8Nx3o5O0KOrr5LJUYDsf92Kx8swebQaY1BJo9WivLzWFlhpxBL9Av3bNERd2aBOkcu3Td54ojpuA1aQ.CbH7aOPEweS-xpUn.Xo3naakY--egfLE8rOOnkw0fYKf6YWo0eBPmwDKc0yYLTOEvW8HpYKnJgec.S2ekKFc48s8stX7VFP03Ww";

  /**
   * Mocked object.
   */
  @Mock
  JsonWeb mockedJsonWeb;

  /**
   * Unit under test (UUT).
   */
  @InjectMocks
  JsonWebEncryption uutSender;

  /**
   * Unit under test (UUT).
   */
  @InjectMocks
  JsonWebEncryption uutReceiver;

  /**
   * Initialization for the sender side.
   */
  @BeforeAll
  void initializeSender() throws AdapterException {

    // initialize encryption keys (sender)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPrivateKey = classLoader.getResource(TestConstant.VALID_SENDER_PRIVATE_KEY)
        .getFile();

    JsonWeb peoplesJsonWeb = new JsonWeb();
    peoplesJsonWeb.initPrivateKey(absolutePathPrivateKey);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(peoplesJsonWeb);
    Assertions.assertTrue(peoplesJsonWeb.isPrivateKey());

    // inject into uut
    uutSender.setSenderRsaPrivateKey(peoplesJsonWeb);

    //  initialize encryption keys (receiver)
    String absolutePathPublicCert = classLoader.getResource(TestConstant.VALID_RECEIVER_PUBLIC_CERT)
        .getFile();

    JsonWeb interacJsonWeb = new JsonWeb();
    interacJsonWeb
        .initPublicCert(absolutePathPublicCert, TestConstant.RECEIVER_KEY_ID);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(interacJsonWeb);
    Assertions.assertTrue(interacJsonWeb.isPublicCert());

    // inject into uut
    uutSender.setReceiverRsaPublicCert(interacJsonWeb);
  }

  /**
   * Initialization for the receiver side.
   */
  @BeforeAll
  void initializeReceiver() throws AdapterException {
    /**
     * The sender now becomes the receiver, and the receiver how becomes the sender.
     */

    // initialize encryption keys (sender (aka the receive)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPrivateKey = classLoader.getResource(TestConstant.VALID_RECEIVER_PRIVATE_KEY)
        .getFile();

    JsonWeb interacJsonWeb = new JsonWeb();
    interacJsonWeb.initPrivateKey(absolutePathPrivateKey);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(interacJsonWeb);
    Assertions.assertTrue(interacJsonWeb.isPrivateKey());

    // inject into uut
    uutReceiver.setSenderRsaPrivateKey(interacJsonWeb);

    //  initialize encryption keys (receiver)
    String absolutePathPublicCert = classLoader.getResource(TestConstant.VALID_SENDER_PUBLIC_CERT)
        .getFile();

    JsonWeb peoplesJsonWeb = new JsonWeb();
    peoplesJsonWeb
        .initPublicCert(absolutePathPublicCert, TestConstant.SENDER_KEY_ID);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(peoplesJsonWeb);
    Assertions.assertTrue(peoplesJsonWeb.isPublicCert());

    // inject into uut
    uutReceiver.setReceiverRsaPublicCert(peoplesJsonWeb);
  }

  /**
   * Unit under test is JsonWebEncryption::encrypt
   */

  /**
   * Unit under test is JsonWebEncryption::encrypt
   * <pre>
   *   null and empty arguments
   * </pre>
   *
   * @param plainText inputs for parameterized unit test
   */
  @Order(3)
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void encrypt_nullAndEmpty(String plainText) throws AdapterException {
    // initialize

    // unit under test
    String result = uutSender.encrypt(plainText);

    // assertions
    Assertions.assertNull(result);
  }

  /**
   * Unit under test is JsonWebEncryption::encrypt
   * <pre>
   *   no private key initialized
   * </pre>
   */
  @Order(3)
  @Test
  void encrypt_nullSenderPrivateKey() {
    // initialize
    JsonWebEncryption uutEncryption = new JsonWebEncryption();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutEncryption.encrypt("lorem ipsum");
    });

    // assertions
    AdapterException e = (AdapterException) exception;
    Assertions.assertEquals(ErrorCode.INVALID_STATE, e.getErrorCode());
    Assertions.assertTrue(e.getMessage().contains("encryption"));
  }

  /**
   * Unit under test is JsonWebEncryption::encrypt
   * <pre>
   *   success
   * </pre>
   */
  @Order(3)
  @Test
  void encrypt_success() {
    // initialize
    String plainText = PLAIN_TEXT_1;

    // unit under test
    String encryptedText = null;
    try {
      encryptedText = uutSender.encrypt(plainText);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(encryptedText);
    Assertions.assertNotEquals(plainText, encryptedText);
    Assertions.assertEquals(4, encryptedText.chars().filter(ch -> ch == '.').count());
  }

  /**
   * Unit under test is JsonWebEncryption::decrypt
   */

  /**
   * Unit under test is JsonWebEncryption::decrypt
   * <pre>
   *   null and empty arguments
   * </pre>
   *
   * @param plainText inputs for parameterized unit test
   */
  @Order(4)
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void decrypt_nullAndEmpty(String plainText) throws AdapterException {
    // initialize

    // unit under test
    String result = uutReceiver.decrypt(plainText);

    // assertions
    Assertions.assertNull(result);
  }

  /**
   * Unit under test is JsonWebEncryption::decrypt
   * <pre>
   *   corrupt encrypted text
   * </pre>
   */
  @Order(4)
  @Test
  void decrypt_corruptedText() {
    // initialize
    String encText = ENCRYPTED_TEXT + "lorem ipsum";

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutReceiver.decrypt(encText);
    });

    // assertions
    AdapterException e = (AdapterException) exception;
    Assertions.assertEquals(ErrorCode.INVALID_INPUT, e.getErrorCode());
    Assertions.assertTrue(e.getMessage().contains("decrypt"));
  }

  /**
   * Unit under test is JsonWebEncryption::decrypt
   * <pre>
   *   no public cert initialized
   * </pre>
   */
  @Order(4)
  @Test
  void decrypt_nullReceiverPublicKey() {
    // initialize
    JsonWebEncryption uutReceiver = new JsonWebEncryption();

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutReceiver.decrypt("lorem ipsum");
    });

    // assertions
    AdapterException e = (AdapterException) exception;
    Assertions.assertEquals(ErrorCode.INVALID_STATE, e.getErrorCode());
    Assertions.assertTrue(e.getMessage().contains("decryption"));
  }

  /**
   * Unit under test is JsonWebEncryption::decrypt
   * <pre>
   *   success
   * </pre>
   */
  @Order(4)
  @Test
  void decrypt_success() {
    // initialize
    String encText = ENCRYPTED_TEXT;

    // unit under test
    String plainText = null;
    try {
      plainText = uutReceiver.decrypt(encText);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(plainText);
    Assertions.assertNotEquals(encText, plainText);
    Assertions.assertEquals(PLAIN_TEXT_1, plainText);
  }
}