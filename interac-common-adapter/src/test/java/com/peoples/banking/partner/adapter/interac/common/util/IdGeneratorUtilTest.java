package com.peoples.banking.partner.adapter.interac.common.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.peoples.banking.adapter.base.util.IdGeneratorUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class IdGeneratorUtilTest {

  private static int LENGTH_OF_ID = 36;
  private static int MAX_IDS_TO_GENERATE = 100000;

  @Test
  void generateSingleRequestId() {
    String uniqueId1 = IdGeneratorUtil.generateRequestId();

    assertNotNull(uniqueId1);
    assertEquals(uniqueId1.length(), LENGTH_OF_ID);
  }

  @Test
  void generateMultipleRequestIds() {
    Set<String> uniqueIds = new HashSet<>();

    // generate unique IDs
    for (int i = 0; i < MAX_IDS_TO_GENERATE; ++i) {
      // add to the set
      uniqueIds.add(IdGeneratorUtil.generateRequestId());
    }

    // use set (definition contains unique entries) to confirm # of unique IDs
    assertEquals(uniqueIds.size(), MAX_IDS_TO_GENERATE);
  }
}