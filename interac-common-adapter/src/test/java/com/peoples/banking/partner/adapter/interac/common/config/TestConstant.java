package com.peoples.banking.partner.adapter.interac.common.config;

public final class TestConstant {

  // Customer 1 -- individual sending the funds (debtor)/recipient of the request for payment
  public static final String CUSTOMER_ONE_ID = "JUNITCUST123";
  public static final String CUSTOMER_ONE_FIRST_NAME = "John";
  public static final String CUSTOMER_ONE_LAST_NAME = "Wick";
  public static final String CUSTOMER_ONE_DISPLAY_NAME = "J Wick";
  public static final String CUSTOMER_ONE_ACCOUNT_ONE = "621-00005-**********";
  /**
   * Sender email address to use to avoid SPAMMING any person or mail server.
   * <pre>View email mailbox @ https://maildrop.cc/inbox/dCdPRKYOtnNKlXKCFMsU</pre>
   */
  public static final String CUSTOMER_ONE_EMAIL = "<EMAIL>";

  public static final String INVALID_EMAIL = "Invalid Email";

  public static final String NOT_EXIST = "not exist";

  public static final String Customer = "Customer";

  public static final String REQUEST_NOT_COMPLETED = "REQUEST_NOT_COMPLETED";

  // Customer 2 -- individual receiving the funds (creditor)/sender of the request for payment
  public static final String CUSTOMER_TWO_ID = "JUNITCUST456";
  public static final String CUSTOMER_TWO_FIRST_NAME = "Anna";
  public static final String CUSTOMER_TWO_LAST_NAME = "Wick";
  public static final String CUSTOMER_TWO_DISPLAY_NAME = "A Wick";
  public static final String CUSTOMER_TWO_ACCOUNT_ONE = "621-00005-**********";

  /**
   * receiver Email address to use to avoid SPAMMING any person or mail server.
   * <pre>View email mailbox @ https://maildrop.cc/inbox/q5dbox2aw28dtts2oiqh</pre>
   */
  public static final String CUSTOMER_TWO_EMAIL = "<EMAIL>";

  /**
   * Peoples Group - Member Identifier.
   */
  public static final String PEOPLES_MEMBER_IDENT = "CA000621";
  /**
   * Peoples Group (aka Sender) - Key ID.
   */
  public static final String SENDER_KEY_ID = "etapi-sign-key-ca000621-001";

  /**
   * Interac Corp (aka Receiver) - Key ID.
   */
  public static final String RECEIVER_KEY_ID = "etapi-sign-key-ca000999-001";

  /**
   * Sender (eg. Peoples) private RSA key.
   */
  public static final String VALID_SENDER_PRIVATE_KEY = "sender-private-key.rsa.key";
  /**
   * Sender (eg. Peoples) public certificate (self signed).
   */
  public static final String VALID_SENDER_PUBLIC_CERT = "sender-public-cert.crt";

  /**
   * Receiver (eg. Interac) private RSA key.
   */
  public static final String VALID_RECEIVER_PRIVATE_KEY = "receiver-private-key.rsa.key";

  /**
   * Sender (eg. Peoples) public certificate (self signed).
   */
  public static final String VALID_RECEIVER_PUBLIC_CERT = "receiver-public-cert.crt";

  public static final String PEOPLES_TRUST_PRIVATE_KEY = "INTERAC_BETA_PTC.key";
  public static final String INTERAC_PUBLIC_CERT = "INTERAC_BETA_INTERAC.crt";

  //public static final String PEOPLES_TRUST_PRIVATE_KEY = "INTERAC_FIT_PTC.key";
  //public static final String INTERAC_PUBLIC_CERT = "INTERAC_FIT_INTERAC.crt";

  /**
   * Exception scenarios.
   */
  public static final String INVALID_SENDER_PRIVATE_KEY_EMPTY_1 = "private-key-empty1.rsa.key";
  public static final String INVALID_SENDER_PRIVATE_KEY_EMPTY_2 = "private-key-empty2.rsa.key";
  public static final String INVALID_SENDER_PRIVATE_KEY_CORRUPT_1 = "private-key-corrupt1.rsa.key";
  public static final String INVALID_SENDER_PRIVATE_KEY_CORRUPT_2 = "private-key-corrupt2.rsa.key";
  public static final String INVALID_SENDER_PRIVATE_KEY_ELLIPTICAL_CURVE = "private-key-non-RSA.pem.key";
  public static final String INVALID_SENDER_PRIVATE_KEY_PUBLIC_CERT = VALID_RECEIVER_PUBLIC_CERT;
  public static final String INVALID_RECEIVER_PUBLIC_CERT_EMPTY_1 = "public-cert-empty1.crt";
  public static final String INVALID_RECEIVER_PUBLIC_CERT_EMPTY_2 = "public-cert-empty2.crt";
  public static final String INVALID_RECEIVER_PUBLIC_CERT_CORRUPT_1 = "public-cert-corrupt1.crt";
  public static final String INVALID_RECEIVER_PUBLIC_CERT_CORRUPT_2 = "public-cert-corrupt2.crt";
  public static final String INVALID_RECEIVER_PUBLIC_CERT_NO_KEYUSAGE = "public-cert-invalid-no-keyusage.crt";
  public static final String INVALID_RECEIVER_PUBLIC_CERT_ELLIPTICAL_CURVE = "public-cert-non-RSA.crt";

  // TODO rename static constants so a reorg on this class doesn't ruin the flow

  private TestConstant() {
    /**
     * prevents even the native class from calling the constructor.
     */
    throw new AssertionError();
  }
}
