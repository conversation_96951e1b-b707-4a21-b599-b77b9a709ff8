package com.peoples.banking.partner.adapter.interac.common.jws;

import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.adapter.interac.common.config.InteracRestAdapterConstant;
import com.peoples.banking.partner.adapter.interac.common.config.TestConstant;
import com.peoples.banking.partner.adapter.interac.common.jw.JsonWeb;
import com.peoples.banking.partner.adapter.interac.common.jws.JsonWebSignature.JwsVerificationResult;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@TestMethodOrder(OrderAnnotation.class)
@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig()
class JsonWebSignatureTest {

  /**
   * Validated JWS token, externally validated using jwt.io.
   */
  private static final String JWS_SIGNATURE_PRE_VALIDATED_NULL_PAYLOAD = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  private static final String JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_DEFAULT_CONTENT_TYPE = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  private static final String JWS_SIGNATURE_PRE_VALIDATED_WITH_SIMPLE_PAYLOAD_DEFAULT_CONTENT_TYPE = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  private static final String JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_ENCRYPTED_CONTENT_TYPE = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  private static final String JWS_SIGNATURE_PRE_VALIDATED_WITH_SIMPLE_PAYLOAD_ENCRYPTED_CONTENT_TYPE = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

  private static final String JWS_SIGNATURE_CORRUPT_1 = "ABC.DEF.GHI";
  private static final String JWS_SIGNATURE_CORRUPT_2 = "A.D.G";
  private static final String JWS_SIGNATURE_CORRUPT_3 = "*.*.*";
  private static final String JWS_SIGNATURE_CORRUPT_4 = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  private static final String JWS_SIGNATURE_CORRUPT_SECTION_1 = "eyJraWQiOiJldGaS1zaWduLWtleS1jYTAwMDYyMS0wMDEifLCJjdHkiOiJhcHBsaWNhdGlvblwNvbiIsInR5cCI6IkpXVCIsImFsZyI6IlJTMjU2In0.eyJzaWduYXR1cmUtdGltZXN0YW1wIjoiMjAyMC0wNS0yMVQxNzoyNDo1MS42MDc0NTExMDBaIiwicGF5bG9hZC1kaWdlc3QiOiJiZTg5Zjc2Y2NmMTUwY2JlZWM5OTUxMDIyNjljYTlhZGI5ZDg0NTQ5NGY4YWU3MTA5YWYyNzAzMGY2YjA3OTkyIn0.cyIx1yhpzURey3i1OU2suHZ2i6-y39yQpsafikAnE4ujRV8yE1q1RDs3yFZ-hs__kPAut5TlFEiC0VJR_IePJEcZ2wXzQDOJ0PYzKkedc1DrDq1tarAVNuWW9pqxrxdcZ-PLGlUT2qfcxwJjnHK242Nq79veZIzgeYT5QfuvLK_pABkb1KB-q-DLQ7bcLRky4YqpvmnWu0rDfJgtqUf2sxvDM07aPfbyOCTl1crK2tCB6Rb1BcucFxzomjZdChy82l1G9UW7h1tTXRd_fLwDyIyNFB3MOL238AnN5tbnyYskmnxUWZRkTT0U81yHsCww00DnfxiTSU9pKq_6Gq_FyQ";
  private static final String JWS_SIGNATURE_CORRUPT_SECTION_2 = "eyJraWQiOiJldGFwaS1zaWduLWtleS1jYTAwMDYyMS0wMDEiLCJjdHkiOiJhcHBsaWNhdGlvblwvanNvbiIsInR5cCI6IkpXVCIsImFsZyI6IlJTMjU2In0.eyzaWduYXR1cmUXN0YW163wIjoiMjAyMC0wNS0yMVQxNzoyNDo1MS42MDc0NTExMDBaIiwicGF5bWdlc3QiOiJiZTg5Zjc2Y2NmMTUwY2JlZWM5OTNjljYTlhZGI5ZDg0NTQ5NG4YWU3MTA5YWYyNzAzMGY2YjAOTkyIn0.cyIx1yhpzURey3i1OU2suHZ2i6-y39yQpsafikAnE4ujRV8yE1q1RDs3yFZ-hs__kPAut5TlFEiC0VJR_IePJEcZ2wXzQDOJ0PYzKkedc1DrDq1tarAVNuWW9pqxrxdcZ-PLGlUT2qfcxwJjnHK242Nq79veZIzgeYT5QfuvLK_pABkb1KB-q-DLQ7bcLRky4YqpvmnWu0rDfJgtqUf2sxvDM07aPfbyOCTl1crK2tCB6Rb1BcucFxzomjZdChy82l1G9UW7h1tTXRd_fLwDyIyNFB3MOL238AnN5tbnyYskmnxUWZRkTT0U81yHsCww00DnfxiTSU9pKq_6Gq_FyQ";
  private static final String JWS_SIGNATURE_CORRUPT_SECTION_3 = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

  private static final String JSON_SIMPLE_PAYLOAD = "{ \"value1\"=\"Hello 123\"}";
  private static final String JSON_COMPLEX_PAYLOAD = "{ \"value1\"=\"Hello 123\", \"value2\"=123, \"object1\"= { \"value3\"=123.00 } }";

  /**
   * Mocked object.
   */
  @Mock
  JsonWeb mockedJsonWeb;
  /**
   * Unit under test (UUT).
   */
  @InjectMocks
  JsonWebSignature uutSender;
  /**
   * Unit under test (UUT).
   */
  @InjectMocks
  JsonWebSignature uutReceiver;
  /**
   * Timestamp (at this instant).
   */
  private Instant timestamp;

  /**
   * Initialization for the sender side.
   */
  @BeforeAll
  void initializeSender() throws AdapterException {

    // initialize encryption keys (sender)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPrivateKey = classLoader.getResource(TestConstant.VALID_SENDER_PRIVATE_KEY)
        .getFile();

    JsonWeb peoplesJsonWeb = new JsonWeb();
    peoplesJsonWeb.initPrivateKey(absolutePathPrivateKey);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(peoplesJsonWeb);
    Assertions.assertTrue(peoplesJsonWeb.isPrivateKey());

    // inject into uut
    uutSender.setSenderRsaPrivateKey(peoplesJsonWeb);

    //  initialize encryption keys (receiver)
    String absolutePathPublicCert = classLoader.getResource(TestConstant.VALID_RECEIVER_PUBLIC_CERT)
        .getFile();

    JsonWeb interacJsonWeb = new JsonWeb();
    interacJsonWeb
        .initPublicCert(absolutePathPublicCert, TestConstant.RECEIVER_KEY_ID);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(interacJsonWeb);
    Assertions.assertTrue(interacJsonWeb.isPublicCert());

    // inject into uut
    uutSender.setReceiverRsaPublicCert(interacJsonWeb);
  }

  /**
   * Initialization for the receiver side.
   */
  @BeforeAll
  void initializeReceiver() throws AdapterException {
    /**
     * The sender now becomes the receiver, and the receiver how becomes the sender.
     */

    // initialize encryption keys (sender (aka the receive)
    ClassLoader classLoader = this.getClass().getClassLoader();
    String absolutePathPrivateKey = classLoader.getResource(TestConstant.VALID_RECEIVER_PRIVATE_KEY)
        .getFile();

    JsonWeb interacJsonWeb = new JsonWeb();
    interacJsonWeb.initPrivateKey(absolutePathPrivateKey);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(interacJsonWeb);
    Assertions.assertTrue(interacJsonWeb.isPrivateKey());

    // inject into uut
    uutReceiver.setSenderRsaPrivateKey(interacJsonWeb);

    //  initialize encryption keys (receiver)
    String absolutePathPublicCert = classLoader.getResource(TestConstant.VALID_SENDER_PUBLIC_CERT)
        .getFile();

    JsonWeb peoplesJsonWeb = new JsonWeb();
    peoplesJsonWeb
        .initPublicCert(absolutePathPublicCert, TestConstant.SENDER_KEY_ID);
    // sanity check (trust its own unit test has validated)
    Assertions.assertNotNull(peoplesJsonWeb);
    Assertions.assertTrue(peoplesJsonWeb.isPublicCert());

    // inject into uut
    uutReceiver.setReceiverRsaPublicCert(peoplesJsonWeb);
  }

  /**
   * Initialization for general.
   */
  @BeforeAll
  void initialize() {
    timestamp = Instant.now();
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   */

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   uninitialized JsonWebSignature (with no keys)
   *   with default content type (JSON)
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_uninitializedWithJsonContentType() {
    // initialize
    JsonWebSignature uut = new JsonWebSignature();

    // unit under test -- with content type == DEFAULT_CONTENT_TYPE
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
          TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_STATE, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   uninitialized JsonWebSignature (with no keys)
   *   with encrypted content type (JOSE)
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_uninitializedWithJoseContentType() {
    // initialize
    JsonWebSignature uut = new JsonWebSignature();

    // unit under test -- with content type == DEFAULT_CONTENT_TYPE
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE,
          TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_STATE, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   null and empty arguments for cty (content type)
   * </pre>
   *
   * @param cty content type
   */
  @Order(3)
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void buildJWS_nullAndEmptyCty(String cty) {
    // initialize

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutSender.buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, cty, TestConstant.RECEIVER_KEY_ID);
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_INPUT, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   null and empty arguments for kid (key id)
   * </pre>
   *
   * @param kid key id
   */
  @Order(3)
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void buildJWS_nullAndEmptyKid(String kid) {
    // initialize

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutSender
          .buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
              kid);
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_INPUT, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   null HTTP payload
   *   fixed timestamp
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successNullPayloadWithFixedDate() {
    // initialize
    Instant ts = Instant.parse("2020-05-21T17:24:51.607451100Z");

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(null, ts, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
    Assertions.assertEquals(JWS_SIGNATURE_PRE_VALIDATED_NULL_PAYLOAD, signature);
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   with simple JSON HTTP payload
   *   fixed timestamp
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successWithSimpleJsonPayloadFixedDate() {
    // initialize
    Instant ts = Instant.parse("2020-05-21T17:24:51.607451100Z");

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(JSON_SIMPLE_PAYLOAD, ts, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
    Assertions.assertEquals(JWS_SIGNATURE_PRE_VALIDATED_WITH_SIMPLE_PAYLOAD_DEFAULT_CONTENT_TYPE,
        signature);
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   with complex JSON HTTP payload
   *   fixed timestamp
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successWithComplexJsonPayloadFixedDate() {
    // initialize
    Instant ts = Instant.parse("2020-05-21T17:24:51.607451100Z");

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(JSON_COMPLEX_PAYLOAD, ts, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
    Assertions.assertEquals(JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_DEFAULT_CONTENT_TYPE,
        signature);
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   with simple JSON HTTP payload (fake encrypted)
   *   with JOSE content type
   *   fixed timestamp
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successWithSimpleJosePayloadFixedDate() {
    // initialize
    Instant ts = Instant.parse("2020-05-21T17:24:51.607451100Z");

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(JSON_SIMPLE_PAYLOAD, ts, InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
    Assertions.assertEquals(JWS_SIGNATURE_PRE_VALIDATED_WITH_SIMPLE_PAYLOAD_ENCRYPTED_CONTENT_TYPE,
        signature);
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   with complex JSON HTTP payload (fake encrypted)
   *   with JOSE content type
   *   fixed timestamp
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successWithComplexJosePayloadFixedDate() {
    // initialize
    Instant ts = Instant.parse("2020-05-21T17:24:51.607451100Z");

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(JSON_COMPLEX_PAYLOAD, ts, InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
    Assertions.assertEquals(JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_ENCRYPTED_CONTENT_TYPE,
        signature);
  }

  /**
   * Unit under test is JsonWebSignature::buildJWS
   * <pre>
   *   null timestamp, autogenerated
   *   compare JWS against pre-validated expected result.
   * </pre>
   */
  @Order(3)
  @Test
  void buildJWS_successWithNullTimestamp() {
    // initialize

    // unit under test
    String signature = null;
    try {
      signature = uutSender
          .buildJWS(JSON_COMPLEX_PAYLOAD, null, InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE,
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(signature);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   */

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   uninitialized JsonWebSignature (with no keys)
   * </pre>
   */
  @Order(4)
  @Test
  void validateJWS_uninitialized() {
    // initialize
    JsonWebSignature uut = new JsonWebSignature();

    // unit under test -- with content type == DEFAULT_CONTENT_TYPE
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uut.validateJWS(JSON_SIMPLE_PAYLOAD, JWS_SIGNATURE_PRE_VALIDATED_NULL_PAYLOAD, timestamp.toString(), "someKid");
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_STATE, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   null and empty JWS token
   * </pre>
   */
  @Order(4)
  @ParameterizedTest
  @NullAndEmptySource
  @ValueSource(strings = {" ", "   ", "\t", "\n"})
  void validateJWS_nullAndEmptyJws(String jws) {
    // initialize

    // unit under test -- with content type == DEFAULT_CONTENT_TYPE
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD, jws, timestamp.toString(), "someKid");
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_INPUT, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   null timestamp
   * </pre>
   */
  @Order(4)
  @Test
  void validateJWS_nullTimestamp() {
    // initialize

    // unit under test -- with content type == DEFAULT_CONTENT_TYPE
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD,
          JWS_SIGNATURE_PRE_VALIDATED_WITH_SIMPLE_PAYLOAD_DEFAULT_CONTENT_TYPE, null, "someKid");
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_INPUT, ((AdapterException) exception).getErrorCode());
  }


  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   generate JWS (using buildJws, functionality tested above, guaranteed by @Order)
   *   default content type
   *   validate both simple and complex JSON payloads
   * </pre>
   *
   * @param payload JWS token
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {JSON_SIMPLE_PAYLOAD, JSON_COMPLEX_PAYLOAD})
  void validateJWS_successDefaultContentType(String payload) throws AdapterException {
    // initialize
    Instant timestamp = Instant.now();

    String jws = uutSender
        .buildJWS(payload, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // assertions
    Assertions.assertNotNull(jws);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(payload, jws, timestamp.truncatedTo(ChronoUnit.MILLIS).toString(),
          TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.VERIFIED, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   generate JWS (using buildJws, functionality tested above, guaranteed by @Order)
   *   default content type
   *   validate both simple and complex JSON payloads
   * </pre>
   *
   * @param payload JWS token
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {JSON_SIMPLE_PAYLOAD, JSON_COMPLEX_PAYLOAD})
  void validateJWS_successDefaultContentType_trailingZeros(String payload) throws AdapterException {
    // initialize
    LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC).withNano(0);
    DateTimeFormatter formatter = new DateTimeFormatterBuilder().appendInstant(3).toFormatter();
    Instant timestamp = now.toInstant(ZoneOffset.UTC);
    String jws = uutSender
        .buildJWS(payload, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // assertions
    Assertions.assertNotNull(jws);

    // unit under test
    JwsVerificationResult result = null;
    try {
      //pass 000 milliseconds in the timestamp
      result = uutReceiver.validateJWS(payload, jws, formatter.format(timestamp.truncatedTo(ChronoUnit.MILLIS)),
          TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.VERIFIED, result);
  }


  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   generate JWS (using buildJws, functionality tested above, guaranteed by @Order)
   *   encrypted content type
   *   validate both simple and complex JSON payloads
   * </pre>
   *
   * @param payload JWS token
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {JSON_SIMPLE_PAYLOAD, JSON_COMPLEX_PAYLOAD})
  void validateJWS_successEncryptedContentType(String payload) throws AdapterException {
    // initialize
    Instant timestamp = Instant.now();

    String jws = uutSender
        .buildJWS(payload, timestamp, InteracRestAdapterConstant.ENCRYPTED_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // assertions
    Assertions.assertNotNull(jws);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(payload, jws, timestamp.truncatedTo(ChronoUnit.MILLIS).toString(),
          TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.VERIFIED, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   using existing JWS tokens
   *   TTL far back in the past
   * </pre>
   *
   * @param jws pre-generated JWS
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {JWS_SIGNATURE_PRE_VALIDATED_NULL_PAYLOAD,
      JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_DEFAULT_CONTENT_TYPE,
      JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_ENCRYPTED_CONTENT_TYPE})
  void validateJWS_staticJwsExpiredTtl(String jws) {
    // initialize
    Instant expiredTs = timestamp.minus(uutReceiver.getTimeToLiveMilli() + 1, ChronoUnit.MILLIS);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(jws, jws, expiredTs.toString(), TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.FAILED_TTL, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   generated JWS
   *   TTL expired -- value in the past
   * </pre>
   *
   * @param ttlExceededbyMs milliseconds TTL exceeded by
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(ints = {1, 10, 100, 1000, 10000, 100000})
  void validateJWS_timestampExpired(int ttlExceededbyMs) throws AdapterException {
    // initialize
    long ttl = uutReceiver.getTimeToLiveMilli();
    String jws = uutSender
        .buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // assertions
    Assertions.assertNotNull(jws);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver
          .validateJWS(JSON_SIMPLE_PAYLOAD, jws,
              timestamp.minus(ttl + ttlExceededbyMs, ChronoUnit.MILLIS).toString(), TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.FAILED_TTL, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   generated JWS
   *   TTL expired -- value far in the future
   * </pre>
   *
   * @param ttlExceededbyMs milliseconds TTL exceeded by
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(ints = {100000, 1000000, 10000000})
  void validateJWS_timestampInTheFuture(int ttlExceededbyMs) throws AdapterException {
    // initialize
    long ttl = uutReceiver.getTimeToLiveMilli();
    String jws = uutSender
        .buildJWS(JSON_SIMPLE_PAYLOAD, timestamp, InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // assertions
    Assertions.assertNotNull(jws);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver
          .validateJWS(JSON_SIMPLE_PAYLOAD, jws, timestamp.plus(ttl + ttlExceededbyMs, ChronoUnit.MILLIS).toString(),
              TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.FAILED_TTL, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   invalid (invalid header) or corrupt JWS
   * </pre>
   *
   * @param jws JWS token
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {JWS_SIGNATURE_CORRUPT_1,
      JWS_SIGNATURE_CORRUPT_2,
      JWS_SIGNATURE_CORRUPT_3,
      JWS_SIGNATURE_CORRUPT_4,
      JWS_SIGNATURE_CORRUPT_SECTION_1})
  void validateJWS_invalidJws1(String jws) {
    // initialize

    // unit under test
    Exception exception = Assertions.assertThrows(AdapterException.class, () -> {
      uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD, jws, timestamp.toString(), TestConstant.SENDER_KEY_ID);
    });

    // assertions
    Assertions
        .assertEquals(ErrorCode.INVALID_JWS, ((AdapterException) exception).getErrorCode());
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   invalid (payload data or signature)
   * </pre>
   *
   * @param jws JWS token
   */
  @Order(4)
  @ParameterizedTest
  @ValueSource(strings = {
      JWS_SIGNATURE_CORRUPT_SECTION_2,
      JWS_SIGNATURE_CORRUPT_SECTION_3})
  void validateJWS_invalidJws2(String jws) {
    // initialize

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD, jws, timestamp.toString(), TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.FAILED_SIGNATURE, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   payload does not match signature payload
   * </pre>
   */
  @Order(4)
  @Test
  void validateJWS_invalidPayload() {
    // initialize

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD,
          JWS_SIGNATURE_PRE_VALIDATED_WITH_COMPLEX_PAYLOAD_ENCRYPTED_CONTENT_TYPE, timestamp.toString(), TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.FAILED_SIGNATURE, result);
  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   updating timeToLiveMilli
   * </pre>
   */
  @Order(4)
  @Test
  void validateJWS_updateTimeToLive() {
    final Long NEW_TTL = 123456l;

    // initialize
    Long defaultTimeToLive = uutReceiver.getTimeToLiveMilli().longValue();
    uutReceiver.setTimeToLiveMilli(NEW_TTL);

    Assertions.assertEquals(uutReceiver.getTimeToLiveMilli(), NEW_TTL);

    // restore TTL
    uutReceiver.setTimeToLiveMilli(defaultTimeToLive);

  }

  /**
   * Unit under test is JsonWebSignature::validateJWS
   * <pre>
   *   updating timeToLiveMills, sets timestamp far back enough in the past
   *   to exceed the default, but still be valid  based on the new TTL
   * </pre>
   */
  @Order(4)
  @Test
  void validateJWS_updateTimeToLiveSuccessfulValidation() throws AdapterException {
    // initialize
    long defaultTimeToLive = uutReceiver.getTimeToLiveMilli().longValue();
    uutReceiver.setTimeToLiveMilli(defaultTimeToLive + 15000);

    String jws = uutSender
        .buildJWS(JSON_SIMPLE_PAYLOAD, timestamp.minus(defaultTimeToLive + 10, ChronoUnit.MILLIS),
            InteracRestAdapterConstant.DEFAULT_CONTENT_TYPE,
            TestConstant.SENDER_KEY_ID);

    // unit under test
    JwsVerificationResult result = null;
    try {
      result = uutReceiver.validateJWS(JSON_SIMPLE_PAYLOAD, jws,
          timestamp.minus(defaultTimeToLive + 10, ChronoUnit.MILLIS).truncatedTo(ChronoUnit.MILLIS).toString(), TestConstant.SENDER_KEY_ID);
    } catch (Exception e) {
      Assertions.fail("unexpected exception thrown", e);
    }

    // assertions
    Assertions.assertNotNull(result);
    Assertions.assertEquals(JwsVerificationResult.VERIFIED, result);

    // restore TTL
    uutReceiver.setTimeToLiveMilli(defaultTimeToLive);
  }

  /**
   * Utility function to return specific section of JWS.
   *
   * @param jws   digital signature
   * @param index section to return
   * @return
   */
  private String getSectionFromSignature(String jws, int index) {
    if (jws == null) {
      return null;
    }
    String[] sections = jws.split("\\.");
    return sections[index];
  }
}