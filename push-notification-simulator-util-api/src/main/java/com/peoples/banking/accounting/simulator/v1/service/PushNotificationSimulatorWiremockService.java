package com.peoples.banking.accounting.simulator.v1.service;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.peoples.banking.accounting.simulator.v1.config.PushNotificationSimulatorConstant.ROOT_SERVICE_URL;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.VeryShortIdGenerator;
import com.github.tomakehurst.wiremock.http.ContentTypeHeader;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import com.github.tomakehurst.wiremock.http.HttpHeaders;

import javax.annotation.PostConstruct;

import com.peoples.banking.domain.account.model.Error;
import com.peoples.banking.domain.account.model.ErrorResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.http.entity.ContentType;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service that add push notification stubbing to wiremock server
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class PushNotificationSimulatorWiremockService {

  private final WireMockServer wireMockServer;
  private final ObjectMapper objectMapper;
  private final VeryShortIdGenerator generator = new VeryShortIdGenerator();

  /**
   * add all push notifications stubs using debtor.name or creditor.name patterns
   */
  @PostConstruct
  public void setupPushNotifications() {
    //setup default successfully response
    wireMockServer.stubFor(post(urlEqualTo(ROOT_SERVICE_URL))
            .willReturn(WireMock.noContent()
                    .withHeaders(getResponseHeaders())));

    addResponseWithResultCode("NOTAUTHORIZED", HttpStatus.FORBIDDEN, "NOT_AUTHORIZED");
    addResponseWithResultCode("REQUESTEXPIRED", HttpStatus.BAD_REQUEST, "REQUEST_EXPIRED");
    addResponseWithResultCode("MISSINGHEADER", HttpStatus.BAD_REQUEST, "MISSING_HEADER",
            "x-pg-api-token");
    addResponseWithResultCode("DUPLICATEREQUEST", HttpStatus.BAD_REQUEST, "DUPLICATE_REQUEST");
    addResponseWithResultCode("INVALIDINPUT", HttpStatus.BAD_REQUEST, "INVALID_INPUT",
            "event_date");
    addResponseWithResultCode("MISSINGFIELD", HttpStatus.BAD_REQUEST, "MISSING_FIELD",
            "event_reason");
   addResponseWithResultCode("INVALIDFIELDLENGTH", HttpStatus.BAD_REQUEST, "INVALID_FIELD_LENGTH",
            "ref_id");
   addResponseWithResultCode("INVALIDVALUETYPE", HttpStatus.BAD_REQUEST, "INVALID_VALUE_TYPE",
            "event_type");
  }

  /**
   * add specific eligibility pattern stubbing
   *
//   * @param accountPattern - account number pattern
   * @param resultCode     - result code to use for building response
   */
  private void addResponseWithResultCode(String pattern, HttpStatus resultCode, String errorCode) {
    addResponseWithResultCode(pattern, resultCode, errorCode, null);
  }

  /**
   * add specific eligibility pattern stubbing
   *
   * @param name                  - debtor or creditor name strict matching
   * @param resultCode            - result code to use for building response
   * @param additionalInformation - additional information to add to response
   */
  private void addResponseWithResultCode(String name, HttpStatus resultCode, String errorCode, String additionalInformation) {
    wireMockServer.stubFor(post(urlEqualTo(ROOT_SERVICE_URL))
            //error response with specified result code and additional information
            .withRequestBody(matchingJsonPath("$.debtor[?(@.name == '" + name + "')]")
                    .or(matchingJsonPath("$.creditor[?(@.name == '" + name + "')]")))
            .willReturn(aResponse()
                    .withStatus(resultCode.value())
                    .withHeaders(getResponseHeaders())
                    .withBody(responseWithResultCode(errorCode, additionalInformation))));
  }

  /**
   * Build ErrorResponse with specified error result code and additional information and convert to String using objectMapper
   *
   * @param errorCode - result error code to use
   * @param additionalInformation - additional information to use
   * @return - new ErrorResponse with specified result code in json
   */
  @SneakyThrows
  private String responseWithResultCode(String errorCode, String additionalInformation) {
    Error error = new Error();
    error.setCode(errorCode);
    error.setAdditionalInformation(additionalInformation);
    ErrorResponse response = new ErrorResponse();
    response.setError(List.of(error));
    return objectMapper.writeValueAsString(response);
  }

  /**
   * construct response headers with values from original request and generated random ones
   *
   * @return - wiremock HttpHeaders object
   */
  private HttpHeaders getResponseHeaders() {
    HttpHeader interactionHeader = new HttpHeader("x-pg-interaction-id", "{{request.headers.x-pg-interaction-id}}");
    String correspondentId = generator.generate();
    HttpHeader correspondentHeader = new HttpHeader("x-pg-correspondent-id", correspondentId);

    return new HttpHeaders(new ContentTypeHeader(ContentType.APPLICATION_JSON.getMimeType()),
        interactionHeader, correspondentHeader);
  }
}
