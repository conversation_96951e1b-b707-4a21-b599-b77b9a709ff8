package com.peoples.banking.accounting.simulator.v1.controller;

import com.peoples.banking.domain.account.model.Error;
import com.peoples.banking.domain.account.model.ErrorResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static com.peoples.banking.accounting.simulator.v1.config.PushNotificationSimulatorConstant.ROOT_SERVICE_URL;

@SpringBootTest
@TestPropertySource("classpath:application.properties")
public class PushNotificationSimulatorControllerTest {

  @Value("${server.host:http://localhost:}")
  public String LOCAL_ADDRESS;

  @Value("${server.http.port}")
  protected int LOCAL_PORT;
  public String API_URL;

  private final TestRestTemplate testRestTemplate = new TestRestTemplate();

  @BeforeEach
  public void setup () {
    API_URL = LOCAL_ADDRESS + LOCAL_PORT + ROOT_SERVICE_URL;
  }

  @Test
  public void pushNotification_success() {
    ResponseEntity<String> response = testRestTemplate
        .postForEntity(API_URL, buildRequest("good debtor"), String.class);
    Assertions.assertNotNull(response);

    Assertions.assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
    Assertions.assertNull(response.getBody());
  }

  @Test
  public void pushNotification_not_authorized() {
    ErrorResponse response = testRestTemplate
        .postForObject(API_URL, buildRequest("NOTAUTHORIZED"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("NOT_AUTHORIZED", error.get(0).getCode());
  }

  @Test
  public void pushNotification_request_expired() {
    ErrorResponse response = testRestTemplate
        .postForObject(API_URL, buildRequest("REQUESTEXPIRED"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("REQUEST_EXPIRED", error.get(0).getCode());
  }

  @Test
  public void pushNotification_missing_header() {
    ErrorResponse response = testRestTemplate
        .postForObject(API_URL, buildRequest("MISSINGHEADER"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("MISSING_HEADER", error.get(0).getCode());
    Assertions.assertEquals("x-pg-api-token", error.get(0).getAdditionalInformation());
  }

  @Test
  public void pushNotification_duplicate_request() {
    ErrorResponse response = testRestTemplate
        .postForObject(API_URL, buildRequest("DUPLICATEREQUEST"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("DUPLICATE_REQUEST", error.get(0).getCode());
  }

  @Test
  public void pushNotification_invalid_input() {
    ErrorResponse response = testRestTemplate
            .postForObject(API_URL, buildRequest("INVALIDINPUT"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("INVALID_INPUT", error.get(0).getCode());
    Assertions.assertEquals("event_date", error.get(0).getAdditionalInformation());
  }

  @Test
  public void pushNotification_missing_field() {
    ErrorResponse response = testRestTemplate
            .postForObject(API_URL, buildRequest("MISSINGFIELD"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("MISSING_FIELD", error.get(0).getCode());
    Assertions.assertEquals("event_reason", error.get(0).getAdditionalInformation());
  }

  @Test
  public void pushNotification_invalid_field_length() {
    ErrorResponse response = testRestTemplate
            .postForObject(API_URL, buildRequest("INVALIDFIELDLENGTH"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("INVALID_FIELD_LENGTH", error.get(0).getCode());
    Assertions.assertEquals("ref_id", error.get(0).getAdditionalInformation());
  }

  @Test
  public void pushNotification_invalid_value_type() {
    ErrorResponse response = testRestTemplate
            .postForObject(API_URL, buildRequest("INVALIDVALUETYPE"), ErrorResponse.class);
    Assertions.assertNotNull(response);

    List<Error> error = response.getError();
    Assertions.assertNotNull(error);
    Assertions.assertEquals(1, error.size());

    Assertions.assertEquals("INVALID_VALUE_TYPE", error.get(0).getCode());
    Assertions.assertEquals("event_type", error.get(0).getAdditionalInformation());
  }


  private String buildRequest(String name) {
    return "{ \"debtor\": { \"name\": \"" + name + "\" },  \"creditor\": { \"name\": \"somegood name \" }}\n";
  }


}