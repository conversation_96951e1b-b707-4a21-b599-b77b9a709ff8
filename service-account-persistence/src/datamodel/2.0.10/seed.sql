-- SERVICE ACCOUNTS
INSERT INTO configuration.service_accounts (
	ref_id,
	inbound_api_token,
	outbound_api_token,
	outbound_api_auth,
	outbound_api_auth_token,
	name,
	sett_account_num,
	account_num_range,
	limit_group_id,
	created_on,
	updated_on,
	crm_ref_id
	)
VALUES (
	'PGVAL1',
	'TIQg542Zx',
	'7MG2fXQ6IfMrEztS1xvIl2pdArE44ypX',
	'NONE',
	null,
	'Peoples SA1',
	'001-12345-*************',
	'621-16001-[\d]{12,24}',
	'PRODVAL',
	CURRENT_TIMESTAMP,
	CURRENT_TIMESTAMP,
	'*********'
	);

INSERT INTO configuration.service_accounts (
	ref_id,
	inbound_api_token,
	outbound_api_token,
	outbound_api_auth,
	outbound_api_auth_token,
	name,
	sett_account_num,
	account_num_range,
	limit_group_id,
	created_on,
	updated_on,
	crm_ref_id
	)
VALUES (
	'PGVAL2',
	'Xwu7BmV8O',
	'uvRYvKY24hmT2wnzUs4oZ8p3cYMSY3Jj',
	'BASIC_AUTH',
	'U0EyOmJhbmFuYQ==', -- USER: SA2, PASS: banana
	'Peoples SA2',
  '001-12345-*************',
	'621-16001-88[\d]{10,22}',
	'ABC123',
	CURRENT_TIMESTAMP,
	CURRENT_TIMESTAMP,
	'*********'
	);

INSERT INTO configuration.service_accounts (
	ref_id,
	inbound_api_token,
	outbound_api_token,
	outbound_api_auth,
	outbound_api_auth_token,
	name,
	sett_account_num,
	account_num_range,
	limit_group_id,
	created_on,
	updated_on,
	crm_ref_id
	)
VALUES (
	'PGVAL3',
	'P29dpj4PY',
	'a85YQVm4Q6aKs7Q9tQhigyNkUEM3xhC4',
	'BASIC_AUTH',
	'U0EzOmFscGFjYQ==', -- USER: SA3, PASS: alpaca
	'Peoples SA3',
	'001-12345-*************',
	'621-16001-99[\d]{10,22}',
	'ABC123',
	CURRENT_TIMESTAMP,
	CURRENT_TIMESTAMP,
	'*********'
	);

INSERT INTO configuration.service_accounts (
	ref_id,
	inbound_api_token,
	outbound_api_token,
	outbound_api_auth,
	outbound_api_auth_token,
	name,
	sett_account_num,
	account_num_range,
	limit_group_id,
	created_on,
	updated_on,
	crm_ref_id,
	connector_type,
	indirect_connector_id
	)
VALUES (
	'PGVAL4',
	'GPJmo1615w',
	'qCSU5VzcBfkRPHrQMB4IV7bO2IRucX',
	'NONE',
	null,
	'Peoples Indirect',
	'001-12345-*************',
	'621-16001-[\d]{12,24}',
	'PRODVAL',
	CURRENT_TIMESTAMP,
	CURRENT_TIMESTAMP,
	'*********',
	'INDIRECT',
	'*********'
	);

--
--CONFIGURATIONS
--
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
   ,'AUTO_REVERSE'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
   ,'GL_PROFILE_ID'
   ,'8f1439aa-800c-4afb-bcce-99466ed46c9c'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
   ,'GL_ACCOUNT_ID'
   ,'d7e0df23-92c4-4808-a5a6-c79ce4cfc471'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
   ,'GL_SERVICE_ENROLLMENT'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );


INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
   ,'AUTO_REVERSE'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
   ,'GL_PROFILE_ID'
   ,'0e6806af-d127-4196-864b-ac2fb383d77b'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
   ,'GL_ACCOUNT_ID'
   ,'85eb3436-f439-4bcc-8c29-65de1194d96d'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
   ,'GL_SERVICE_ENROLLMENT'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );

INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
   ,'AUTO_REVERSE'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
   ,'GL_PROFILE_ID'
   ,'0003'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
   ,'GL_ACCOUNT_ID'
   ,'A0003'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );
INSERT INTO configuration.service_account_configurations (
   service_account_id
   ,key
   ,value
   ,created_on
   ,updated_on
   )
VALUES (
   (select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
   ,'GL_SERVICE_ENROLLMENT'
   ,'Y'
   ,CURRENT_TIMESTAMP
   ,CURRENT_TIMESTAMP
   );

--
-- API_ENDPOINTS
--
INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
	,'ELIGIBILITY'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/eligibility'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);

INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
	,'TRANSACTION'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/transaction'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);

INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL1')
	,'REVERSAL'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/{transaction_id}/reversal'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);


INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
	,'ELIGIBILITY'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/eligibility'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);

INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
	,'TRANSACTION'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/transaction'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);

INSERT INTO configuration.api_endpoints (
	service_account_id
	,type_cd
	,url
	,created_on
	,updated_on
	)
VALUES (
	(select ID from "configuration".service_accounts where ref_id = 'PGVAL2')
	,'REVERSAL'
	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/{transaction_id}/reversal'
	,CURRENT_TIMESTAMP
	,CURRENT_TIMESTAMP
	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
 	,'ELIGIBILITY'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/eligibility'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
 	,'TRANSACTION'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/transaction'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL3')
 	,'REVERSAL'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/{transaction_id}/reversal'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL4')
 	,'ELIGIBILITY'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/eligibility'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL4')
 	,'TRANSACTION'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/transaction'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

INSERT INTO configuration.api_endpoints (
 	service_account_id
 	,type_cd
 	,url
 	,created_on
 	,updated_on
 	)
VALUES (
 	(select ID from "configuration".service_accounts where ref_id = 'PGVAL4')
 	,'REVERSAL'
 	,'http://account-sim-util-service.pg-interac-dev.svc.cluster.local:8080/account/{account_num}/{transaction_id}/reversal'
 	,CURRENT_TIMESTAMP
 	,CURRENT_TIMESTAMP
 	);

--
-- SERVICE_ACCOUNT_FEATURES
--PGVAL1, PGVAL2, PGVAL3
INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'RETRIEVE_PAYMENT_OPTIONS', TRUE , CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'INITIATE_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'SUBMIT_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'RETRIEVE_PAYMENT_OPTIONS', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'INITIATE_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'SUBMIT_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--
INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'RETRIEVE_PAYMENT_OPTIONS', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'INITIATE_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'SUBMIT_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--
INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL4'), 'RETRIEVE_PAYMENT_OPTIONS', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL4'), 'INITIATE_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_features(service_account_id, feature, active, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL4'), 'SUBMIT_SEND_PAYMENT', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--
-- SERVICE_ACCOUNT_LIMITS
--
INSERT INTO configuration.service_account_limits (service_account_id, reserve_amount, notification_threshold_amount, suspend_threshold_amount, created_on, updated_on)
SELECT id, 10000, 2500, 3333, CURRENT_TIMESTAMP , CURRENT_TIMESTAMP from configuration.service_accounts;

--
-- FEATURES
--
INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('CREATE_PAYMENT_REQUEST', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('CANCEL_PAYMENT_REQUEST', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('RETRIEVE_PAYMENT_REQUEST', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('RETRIEVE_PAYMENT_OPTIONS', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('INITIATE_SEND_PAYMENT', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('SUBMIT_SEND_PAYMENT', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('REVERSE_SEND_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('CANCEL_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('RETRIEVE_OUTGOING_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('RETRIEVE_INCOMING_PAYMENT', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('AUTHENTICATE_PAYMENT', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('INITIATE_RECEIVE_PAYMENT', true, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('SUBMIT_RECEIVE_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('REVERSE_RECEIVE_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('DECLINE_PAYMENT', false, CURRENT_TIMESTAMP);

INSERT INTO configuration.features (feature, restricted, created_on)
VALUES('INQUIRE_TRANSACTION', false, CURRENT_TIMESTAMP);

-- FINANCIAL_INSTITUTION_ACCOUNTS
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000001', 'BMO', 'Bank Of Montreal EMT', '*********', '4109806', '{ "address1":"1 Woodland St.", "region":"Port Hawkesbury", "subdivision":"NS", "postal_code":"B9A9A3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000002', 'BNS', 'Electronic Mail Payment', '*********', '6478539', '{ "address1":"101 Wood Street", "region":"Steinbach", "subdivision":"MB", "postal_code":"R5G4S3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000003', 'RBC', 'Royal Bank, Royal  Direct   Call Centre', '*********', '********', '{ "address1":"109 Brickyard St.", "region":"Goulds", "subdivision":"LB", "postal_code":"A1S9G7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000004', 'TD', 'Toronto Dominion Bank 1020', '*********', '********', '{ "address1":"113 Amerige Ave.", "region":"Sainte-Julie", "subdivision":"QC", "postal_code":"J3E1T6", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000006', 'NBC', 'BANQUE NATIONALE-COMPENSATION P2P', '*********', '2660071', '{ "address1":"120 Airport St.", "region":"Hanover", "subdivision":"ON", "postal_code":"N4N9K8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000010', 'CIBC P2P Receiving Account', 'CIBC P2P Receiving Account', '*********', '********', '{ "address1":"133 Homewood Drive", "region":"Mauricie", "subdivision":"QC", "postal_code":"G0X8G8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000016', 'HSBC', 'E-TRANSFER IN CLAIMED GL', '*********', '********', '{ "address1":"5 Edgewater Lane", "region":"L Ile-Dorval", "subdivision":"QC", "postal_code":"H9S4T9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000099', 'Bank CA000099', 'Settlement Acct for CA000099', '*********', '********', '{ "address1":"14 Big Rock Cove Ave.", "region":"Lacombe", "subdivision":"AB", "postal_code":"T4L5T5", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000101', 'Bank CA000101', 'Settlement Acct for CA000101', '*********', '********', '{ "address1":"147 West Pin Oak St.", "region":"Montérégie-Est", "subdivision":"QC", "postal_code":"J0J6G7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000103', 'Bank CA000103', 'Settlement Acct for CA000103', '*********', '********', '{ "address1":"20 W. Airport Drive", "region":"Paris", "subdivision":"ON", "postal_code":"N3L9P0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000111', 'Bank CA000111', 'Settlement Acct for CA000111', '*********', '********', '{ "address1":"28B Spruce Road", "region":"Stanley", "subdivision":"NB", "postal_code":"E6B4H7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000123', 'Bank CA000123', 'Settlement Acct for CA000123', '*********', '********', '{ "address1":"3 N. Sussex Ave.", "region":"Windsor", "subdivision":"QC", "postal_code":"J1S5L4", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000124', 'Bank CA000124', 'Settlement Acct for CA000124', '*********', '********', '{ "address1":"37 Shirley Drive", "region":"Nepean", "subdivision":"ON", "postal_code":"K2G5T3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000143', 'Bank CA000143', 'Settlement Acct for CA000143', '*********', '********', '{ "address1":"370 Edgemont Drive", "region":"Canterbury", "subdivision":"NB", "postal_code":"E6H1L8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000145', 'Bank CA000145', 'Settlement Acct for CA000145', '*********', '********', '{ "address1":"380 East Brewery Lane", "region":"Chilcotin", "subdivision":"BC", "postal_code":"V0L2S3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000161', 'Bank CA000161', 'Settlement Acct for CA000161', '*********', '********', '{ "address1":"408 Bald Hill Drive", "region":"Wainwright", "subdivision":"AB", "postal_code":"T9W3Y2", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000198', 'Bank CA000198', 'Settlement Acct for CA000198', '*********', '********', '{ "address1":"45 West Lilac Drive", "region":"Ponoka", "subdivision":"AB", "postal_code":"T4J0G1", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000201', 'Bank CA000201', 'Settlement Acct for CA000201', '*********', '128925', '{ "address1":"49 E. Spruce St.", "region":"Peace River", "subdivision":"AB", "postal_code":"T8S1L5", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000216', 'Bank CA000216', 'Settlement Acct for CA000216', '*********', '********', '{ "address1":"5 Miller St.", "region":"Ville-Marie", "subdivision":"QC", "postal_code":"J9V5J5", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000219', 'ATB', 'E-TRANSFER SETTLEMENT ACCOUNT', '*********', '********', '{ "address1":"51 Beacon Lane", "region":"La Prairie", "subdivision":"QC", "postal_code":"J5R1G6", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000222', 'Bank CA000222', 'Settlement Acct for CA000222', '*********', '********', '{ "address1":"55 Poor House Court", "region":"Aurora", "subdivision":"ON", "postal_code":"L4G4H6", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000233', 'Bank CA000233', 'Settlement Acct for CA000233', '*********', '********', '{ "address1":"556 Garfield Court", "region":"Kanata", "subdivision":"ON", "postal_code":"K2K2G3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000234', 'Bank CA000234', 'Settlement Acct for CA000234', '*********', '********', '{ "address1":"56 Baker Ave.", "region":"Truro", "subdivision":"NS", "postal_code":"B2N2V7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000239', 'Bank CA000239', 'Settlement Acct for CA000239', '*********', '********', '{ "address1":"564 West Baker Ave.", "region":"Black Lake", "subdivision":"QC", "postal_code":"G6H1J0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000320', 'PC Bank', 'President''s Choice Financial', '*********', '338235', '{ "address1":"98 Greenrose Avenue", "region":"Duvernay", "subdivision":"QC", "postal_code":"H7E6M3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000328', 'Citibank NA Canada', 'Citibank NA Canada', '*********', '2715560', '{ "address1":"6 Ramblewood Court", "region":"Ville Émard", "subdivision":"QC", "postal_code":"H4E4T0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000374', 'Motus', 'Motus Bank', '*********', '7919314', '{ "address1":"677 Crescent St.", "region":"Napanee", "subdivision":"ON", "postal_code":"K7R9T6", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000352', 'DC Bank', 'DirectCash ATM Processing Partnership', '*********', '********', '{ "address1":"9890 Mountainview Lane", "region":"Plaster Rock", "subdivision":"NB", "postal_code":"E7G3B8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000456', 'Bank CA000456', 'Settlement Acct for CA000456', '*********', '********', '{ "address1":"70 Gainsway Road", "region":"Alliston", "subdivision":"ON", "postal_code":"L9R0X1", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000506', 'Bank CA000506', 'Settlement Acct for CA000506', '*********', '********', '{ "address1":"72 Newport St.", "region":"La Plaine", "subdivision":"QC", "postal_code":"J7M1H8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000540', 'Manulife', 'Manulife Bank of Canada', '*********', '********', '{ "address1":"7264 Manhattan Drive", "region":"Pierrefonds", "subdivision":"QC", "postal_code":"H8Z8N1", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000556', 'Bank CA000556', 'Settlement Acct for CA000556', '*********', '********', '{ "address1":"7319 Meadowbrook Road", "region":"Burtts Corner", "subdivision":"NB", "postal_code":"E6L9A8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000557', 'Bank CA000557', 'Settlement Acct for CA000557', '*********', '********', '{ "address1":"774 Primrose Drive", "region":"Richibucto", "subdivision":"NB", "postal_code":"E4W7H8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000612', 'Bank CA000612', 'Settlement Acct for CA000612', '*********', '********', '{ "address1":"8 Myrtle Road", "region":"Brantville", "subdivision":"NB", "postal_code":"E9H4M1", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000614', 'Tangerine', 'Tangerine Bank', '*********', '********', '{ "address1":"8 West Oxford Rd.", "region":"Balmoral", "subdivision":"NB", "postal_code":"E8E0G5", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000621', 'Peoples Trust', 'Peoples Trust Company', '*********', '********', '{ "address1":"8034 Lakewood Street", "region":"Courtice", "subdivision":"ON", "postal_code":"L1E6P9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000666', 'Bank CA000666', 'Settlement Acct for CA000666', '*********', '********', '{ "address1":"8159A Wall St.", "region":"Kitimat", "subdivision":"BC", "postal_code":"V8C0X4", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000667', 'Bank CA000667', 'Settlement Acct for CA000667', '*********', '5679536', '{ "address1":"82 Leatherwood Dr.", "region":"Portage la Prairie", "subdivision":"MB", "postal_code":"R1N0N3", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000786', 'Bank CA000786', 'Settlement Acct for CA000786', '*********', '********', '{ "address1":"82 Pilgrim Rd.", "region":"Etobicoke", "subdivision":"ON", "postal_code":"M8V5J0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000797', 'Bank CA000797', 'Settlement Acct for CA000797', '*********', '********', '{ "address1":"830 Spring Dr.", "region":"Bracebridge", "subdivision":"ON", "postal_code":"P1L6B2", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000809', 'CUCC', 'CUCC, EMT Settlement              Account', '*********', '********', '{ "address1":"832 Wintergreen St.", "region":"Langley City", "subdivision":"BC", "postal_code":"V3A2J4", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000815', 'Desjardins', 'FCDQ TRSF DE FONDS PAR COURRIEL', '*********', '********', '{ "address1":"8771 Third St.", "region":"Ladysmith", "subdivision":"BC", "postal_code":"V9G2N7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000837', 'Meridian Credit Union', 'Meridian Credit Union, EMT Settlement              Account', '*********', '********', '{ "address1":"8884 Selby Street", "region":"Iona", "subdivision":"NS", "postal_code":"B2C9R4", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000869', 'CUCC - Prairie', 'CUCC - Prairie, EMT Settlement Account', '*********', '********', '{ "address1":"8923 Grove Lane", "region":"Paradise", "subdivision":"LB", "postal_code":"A1L4P9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000888', 'Bank CA000888', 'Settlement Acct for CA000888', '*********', '6947839', '{ "address1":"9016 Tailwater Dr.", "region":"Kirkland Lake", "subdivision":"ON", "postal_code":"P2N2C7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000899', 'Bank CA000899', 'Settlement Acct for CA000899', '*********', '********', '{ "address1":"9078 Pheasant Avenue", "region":"Meadow Lake", "subdivision":"SK", "postal_code":"S9X4V0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000998', 'Bank CA000998', 'Settlement Acct for CA000998', '*********', '********', '{ "address1":"9095 San Carlos St.", "region":"Chaudière-Nord", "subdivision":"QC", "postal_code":"G0S6R0", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA000999', 'Bank CA000999', 'Settlement Acct for CA000999', '*********', '********', '{ "address1":"9215 Fairway Street", "region":"Scarborough", "subdivision":"ON", "postal_code":"M1B0G9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA001110', 'Bank CA001110', 'Settlement Acct for CA001110', '*********', '********', '{ "address1":"923 Hill Field St.", "region":"Varennes", "subdivision":"QC", "postal_code":"J3X2A1", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA123456', 'Bank CA123456', 'Settlement Acct for CA123456', '*********', '********', '{ "address1":"9281 Cedar Ave.", "region":"Le Fjord", "subdivision":"QC", "postal_code":"G0T7L5", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA131689', 'Bank CA131689', 'Settlement Acct for CA131689', '*********', '********', '{ "address1":"9340 Wellington St.", "region":"Aylmer", "subdivision":"ON", "postal_code":"N5H1P4", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA363404', 'Bank CA363404', 'Settlement Acct for CA363404', '*********', '********', '{ "address1":"94 Applegate Drive", "region":"Saint-Antoine", "subdivision":"NB", "postal_code":"E4V5G8", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA555444', 'Bank CA555444', 'Settlement Acct for CA555444', '*********', '********', '{ "address1":"94 Roehampton St.", "region":"Terrace", "subdivision":"BC", "postal_code":"V8G6M9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA578893', 'Bank CA578893', 'Settlement Acct for CA578893', '*********', '********', '{ "address1":"9469 Lakewood Drive", "region":"Apohaqui", "subdivision":"NB", "postal_code":"E5P3J7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA777777', 'Bank CA777777', 'Settlement Acct for CA777777', '*********', '********', '{ "address1":"9562 West Richardson Drive", "region":"Parc-Extension", "subdivision":"QC", "postal_code":"H3N3R9", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
INSERT INTO configuration.financial_institution_accounts (ref_id, name, account_holder_name, account_routing, account_number, address_json, address_json_ver, created_on) VALUES('CA999999', 'Interarc', 'INTERAC eTransfer Settlement Account', '*********', '********', '{ "address1":"963 Indian Spring St.", "region":"Lavaltrie", "subdivision":"QC", "postal_code":"J5T6E7", "country":"CAN"}', 1, CURRENT_TIMESTAMP);
