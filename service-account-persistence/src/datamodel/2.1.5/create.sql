-- Schema for Banking as a Service
create schema IF NOT EXISTS configuration;

-- FINANCIAL_INSTITUTION_ACCOUNTS
CREATE table IF NOT EXISTS configuration.financial_institution_accounts (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    ref_id  		        CHARACTER VARYING NOT NULL, -- ref id of FI, e.g. CA00001, CA00002 etc
    name                    CHARACTER VARYING NOT NULL, -- Organization name
    account_holder_name     CHARACTER VARYING NOT NULL, -- Name of the beneficiary account holder
    account_routing         CHARACTER VARYING NOT NULL, -- Routing information (FI ID + Branch)
    account_number          CHARACTER VARYING NOT NULL, -- Account number
    address_json            CHARACTER VARYING NOT NULL, -- Beneficiary address
    address_json_ver        SMALLINT NOT NULL, -- JSON object version for POJO conversion
    created_on      	    TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	    TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT      	    uq_fi_ref_id UNIQUE(ref_id) -- guarantee ref_id is unique to FI
);

-- SERVICE_ACCOUNTS table
CREATE table IF NOT EXISTS configuration.service_accounts (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    ref_id  			    CHARACTER VARYING NOT NULL, -- service provider's identifier
    crm_ref_id              CHARACTER VARYING NOT NULL,-- the crm ref id for when we call CRM
    inbound_api_token	    CHARACTER VARYING NOT NULL, -- inbound unique token, used by the business partners who call eTransfer services
    outbound_api_token      CHARACTER VARYING NOT NULL, -- outbound unique token, used by the eTransfer system to call business partners' services
    outbound_api_auth       CHARACTER VARYING NOT NULL, --outbound authentication type (NONE, BASIC)
    outbound_api_auth_token CHARACTER VARYING, -- outbound authentication token (eg. basic authentication token)
    name           		    CHARACTER VARYING NOT NULL, -- service provider's name
    sett_account_num        CHARACTER VARYING NOT NULL, -- settlement account used for end of day settlement
    account_num_range       CHARACTER VARYING NOT NULL, -- account numbers available to the service account
    limit_group_id  	    CHARACTER VARYING NOT NULL, -- service account's limit group id
    connector_type  	    CHARACTER VARYING NOT NULL DEFAULT 'DIRECT', -- service account's connector type (DIRECT/INDIRECT)
    indirect_connector_id  	CHARACTER VARYING , -- service account's indirect connector id in case connector_type = 'INDIRECT')
    status                  CHARACTER VARYING DEFAULT 'ENABLED',
    status_reason           CHARACTER VARYING,
    status_updated_by       CHARACTER VARYING,
    created_on      	    TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	    TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT      	    uq_ref_id UNIQUE(ref_id), -- guarantee ref_id is unique to a service account
    CONSTRAINT			    uq_api_token UNIQUE(inbound_api_token)-- guarantee api_token is unique to a service account
);

-- API_ENDPOINTS table
CREATE table IF NOT EXISTS configuration.api_endpoints (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id      INTEGER NOT NULL REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
    type_cd				    CHARACTER VARYING NOT NULL, -- endpoint type code, i.e ELIGIBILITY
    url					    CHARACTER VARYING NOT NULL, -- endpoint
    created_on      	    TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	    TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT      	    uq_service_account_operation_type UNIQUE(service_account_id, type_cd) -- guarantee type_cd is unique to a service account
);

-- SERVICE_ACCOUNT_FEATURES table
CREATE table IF NOT EXISTS configuration.service_account_features (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id      INTEGER NOT NULL REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
    feature                 CHARACTER VARYING NOT NULL, -- feature name
    active                  BOOLEAN NOT NULL DEFAULT TRUE, -- active record indicator, default to TRUE
    created_on      	    TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	    TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT      	    uq_service_account_id_feature UNIQUE(service_account_id, feature) -- guarantee ref_id is unique to a service account
);

-- FEATURE table
CREATE table IF NOT EXISTS configuration.features (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    feature                 CHARACTER VARYING NOT NULL, -- feature name
    restricted              BOOLEAN NOT NULL DEFAULT FALSE, -- active record indicator, default to FALSE
    created_on      	    TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	    TIMESTAMP  -- date and time record updated on, NULL if never updated
);

-- SERVICE_ACCOUNT_EVENTS
CREATE table IF NOT EXISTS configuration.service_account_events (
    id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id      INTEGER NOT NULL REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
    event				    CHARACTER VARYING NOT NULL, -- event type, possible values - ADDED/UPDATED/DELETED
    updated_by				CHARACTER VARYING NOT NULL, --user who add/update the permission.
    category				CHARACTER VARYING NOT NULL, --event category, possible values PROFILE/FEATURE/INITIATE_SEND_PAYMENT/LIMIT/CONTACT
    scope				    CHARACTER VARYING NOT NULL, --event log scope
    reason_type				CHARACTER VARYING NOT NULL, --type of reason, possible values SYSTEM_INITIATED/AGENT_INITIATED/USER_INITIATED
    description				CHARACTER VARYING, -- description of event happened
    original_event_json     JSONB, -- original values in json format
    original_event_json_ver	SMALLINT NOT NULL, -- original event json value version
    updated_event_json      JSONB, -- updated values in json format
    updated_event_json_ver	SMALLINT NOT NULL, -- new event json value version
    created_on      	    TIMESTAMP NOT NULL -- date and time record was created on
);

CREATE INDEX IF NOT exists ix_service_account_id ON configuration.api_endpoints (service_account_id);

-- SERVICE_ACCOUNT_LIMITS table
CREATE table IF NOT EXISTS configuration.service_account_limits (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id              INTEGER not null REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
    reserve_amount                  NUMERIC(12,2) NOT NULL, -- reserve amount in PTC
    notification_threshold_amount   NUMERIC(12,2) NOT NULL, -- the cumulative amount threshold for sending warning notification
    suspend_threshold_amount        NUMERIC(12,2) NOT NULL, -- the cumulative amount threshold for suspend a service account
    created_on                      TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on                      TIMESTAMP , -- date and time record updated on, NULL if never updated
    CONSTRAINT                      uq_service_account_id_limits UNIQUE(service_account_id) -- guarantee ref_id is unique to a service account
);

-- SERVICE_ACCOUNT_CONTACTS table
CREATE table IF NOT EXISTS configuration.service_account_contacts (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id              INTEGER NOT NULL REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
    type                            CHARACTER VARYING NOT NULL, -- domain type of the contact (OPERATIONAL, TECHNICAL, FRAUD)
    priority                        CHARACTER VARYING NOT NULL, -- priority type of the contact (PRIMARY, SECONDARY)
    first_name                      CHARACTER VARYING, -- first name of the contact
    last_name                       CHARACTER VARYING, -- last name of the contact
    email                           CHARACTER VARYING, -- email of the contact
    phone                           CHARACTER VARYING, -- phone of the contact
    created_on                      TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on                      TIMESTAMP  -- date and time record updated on, NULL if never updated
);

CREATE table IF NOT EXISTS configuration.accounting (
    id              	INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    transaction_date    DATE NOT NULL, -- date of transaction
    due_date      	    TIMESTAMP NOT NULL, -- date for transaction to be posted
    description  		CHARACTER VARYING, -- additional description
    partner_id  		CHARACTER VARYING, -- ref id of service account for EFT or FI id for WIRE
    amount              NUMERIC(19,2) NOT NULL, -- amount of the request
    payment_type        CHARACTER VARYING NOT NULL, -- type of request - EFT/WIRE
    transaction_type    CHARACTER VARYING NOT NULL, -- type of transaction DEBIT/CREDIT
    status              CHARACTER VARYING, -- status of transaction POSTED/REJECTED/FAILED
    transaction_id      CHARACTER VARYING NOT NULL, -- correlation transaction id
    network_reference   CHARACTER VARYING, -- id returned when EFT/WIRE transaction is created
    created_on      	TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	TIMESTAMP  -- date and time record updated on, NULL if never updated
);

CREATE table IF NOT EXISTS configuration.service_account_certificates (
      id              	    INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
      service_account_id      INTEGER NOT NULL REFERENCES configuration.service_accounts(id) , -- FK to service_accounts.id
      key_id  		        CHARACTER VARYING NOT NULL, -- key id of the certificate
      certificate             CHARACTER VARYING NOT NULL, -- pem certificate content
      created_on              TIMESTAMP NOT NULL, -- date and time record was created on
      updated_on              TIMESTAMP,  -- date and time record updated on, NULL if never updated
      expired_on              TIMESTAMP,  -- date and time of certificate expiration
      CONSTRAINT      	    uq_sa_cert_ref_id UNIQUE(key_id) -- guarantee ref_id is unique to FI
);

CREATE INDEX IF NOT exists ix_accounting_due_date ON configuration.accounting (due_date);

-- add the new table service_account_configurations
-- SERVICE_ACCOUNT_CONFIGURATIONS table
CREATE table IF NOT EXISTS configuration.service_account_configurations (
    id                              INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY, -- internal unique identifier for referential integrity
    service_account_id              INTEGER NOT NULL REFERENCES configuration.service_accounts(id), -- FK to service_accounts.id
    key                             CHARACTER VARYING, -- the type of configuration,
    value                           CHARACTER VARYING, -- the status fo the type of configuration,
    created_on      	            TIMESTAMP NOT NULL, -- date and time record was created on
    updated_on      	            TIMESTAMP,  -- date and time record updated on, NULL if never updated
    CONSTRAINT      	            uq_key UNIQUE(service_account_id, key)
);