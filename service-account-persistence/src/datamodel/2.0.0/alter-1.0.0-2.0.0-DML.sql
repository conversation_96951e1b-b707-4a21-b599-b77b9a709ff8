-- Grant SERVICE_ACCOUNT_PERMISSIONS (CREATE_PAYMENT_REQUEST, RETRIEVE_PAYMENT_OPTIONS, INITIATE_SEND_PAYMENT, SUBMIT_SEND_PAYMENT)
-- to PGVAL1, PG<PERSON>L2, <PERSON>G<PERSON>L3

--PGVAL1
INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'CREATE_PAYMENT_REQUEST', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'RETRIEVE_PAYMENT_OPTIONS', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'INITIATE_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL1'), 'SUBMIT_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--PGVAL2
INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'CREATE_PAYMENT_REQUEST', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'RETRIEVE_PAYMENT_OPTIONS', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'INITIATE_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL2'), 'SUBMIT_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

--PGVAL3
INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'CREATE_PAYMENT_REQUEST', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'RETRIEVE_PAYMENT_OPTIONS', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'INITIATE_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO configuration.service_account_permissions(service_account_id, permission, status, created_on, updated_on)
VALUES((SELECT ID FROM configuration.SERVICE_ACCOUNTS WHERE REF_ID = 'PGVAL3'), 'SUBMIT_SEND_PAYMENT', 'ENABLED', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
