package com.peoples.banking.persistence.serviceaccount.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.index.Indexed;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@RedisHash("ServiceAccountTransactions")
public class ServiceAccountTransactions implements Serializable {
  @Indexed
  private String serviceAccountRefId;
  @Indexed
  private String externalPaymentRefId;
  private BigDecimal amount;
  private LocalDateTime transactionDate;
  private LocalDateTime createdOn;
}
