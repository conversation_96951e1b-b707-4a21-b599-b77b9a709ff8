package com.peoples.banking.persistence.serviceaccount.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountCertificates;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;

public interface ServiceAccountCertificatesRepository extends JpaRepository<ServiceAccountCertificates, Integer> {

  @Cacheable(value = "ServiceAccountCertificateByKey", cacheManager = "serviceAccountCacheManager")
  @Query("select c from ServiceAccountCertificates c join fetch c.serviceAccounts sa where sa.inboundApiToken = :apiToken and c.keyId = :keyId")
  Optional<ServiceAccountCertificates> findByApiTokenAndKeyId(@Param("apiToken") String apiToken, @Param("keyId") String keyId);

  Optional<ServiceAccountCertificates> findByKeyId(String keyId);

  List<ServiceAccountCertificates> findAllByServiceAccounts(ServiceAccounts sa);

}
