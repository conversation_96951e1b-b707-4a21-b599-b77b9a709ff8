package com.peoples.banking.persistence.serviceaccount.entity;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "service_account_events"
    , schema = "configuration"

)
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class ServiceAccountEvents implements java.io.Serializable{

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private int id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "service_account_id", nullable = false)
  private ServiceAccounts serviceAccounts;

  @Column(name = "created_on", length = 29)
  private LocalDateTime createdOn;

  @Column(name = "event", nullable = false)
  private String event;

  @Column(name = "updated_by", nullable = false)
  private String updatedBy;

  @Column(name = "category", nullable = false)
  private String category;

  @Column(name = "scope", nullable = false)
  private String scope;

  @Column(name = "reason_type", nullable = false)
  private String reasonType;

  @Column(name = "description", nullable = false)
  private String description;

  @Type(type = "jsonb")
  @Column(name = "original_event_json", columnDefinition = "jsonb")
  private String originalEventJson;

  @Column(name = "original_event_json_ver", nullable = false)
  private short originalEventJsonVersion;

  @Type(type = "jsonb")
  @Column(name = "updated_event_json", columnDefinition = "jsonb")
  private String updatedEventJson;

  @Column(name = "updated_event_json_ver", nullable = false)
  private short updatedEventJsonVersion;

}
