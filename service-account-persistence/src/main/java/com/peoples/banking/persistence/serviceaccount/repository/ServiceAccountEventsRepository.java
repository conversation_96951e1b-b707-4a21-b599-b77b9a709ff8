package com.peoples.banking.persistence.serviceaccount.repository;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountEvents;
import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ServiceAccountEventsRepository extends JpaRepository<ServiceAccountEvents, Integer> {

    @Query(value = "select *  from \"configuration\".service_account_events e \n"
        + " where e.created_on >=?1 and \n"
        + " e.created_on <=?2 \n"
        + " and e.service_account_id=?5\n"
        + " order by created_on desc \n"
        + " offset ?3 \n"
        + " limit ?4 \n;", nativeQuery = true)
    List<ServiceAccountEvents> findServiceAccountEvents(LocalDateTime startDate, LocalDateTime endDate, Integer offset,
        Integer maxResponseItems, Integer serviceAccountId);

    @Query(value = "select *  from \"configuration\".service_account_events e \n"
        + " where e.created_on >=?1 \n"
        + " and e.created_on <=?2 \n"
        + " and e.service_account_id=?5\n"
        + " and e.category=?6\n"
        + " order by created_on desc \n"
        + " offset ?3 \n"
        + " limit ?4 \n;", nativeQuery = true)
    List<ServiceAccountEvents> findServiceAccountEventsByCategory(LocalDateTime startDate, LocalDateTime endDate, Integer offset,
        Integer maxResponseItems, Integer serviceAccountId, String category);

    long deleteByServiceAccounts(ServiceAccounts serviceAccounts);
}