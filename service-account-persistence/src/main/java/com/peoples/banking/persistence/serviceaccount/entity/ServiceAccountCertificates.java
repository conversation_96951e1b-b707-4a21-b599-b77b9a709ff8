package com.peoples.banking.persistence.serviceaccount.entity;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "service_account_certificates"
    , schema = "configuration"
    , uniqueConstraints = {@UniqueConstraint(columnNames = {"key_id"})}
)
public class ServiceAccountCertificates implements java.io.Serializable{

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private int id;

  @ManyToOne(fetch= FetchType.LAZY, optional = false)
  @JoinColumn(name="service_account_id", nullable=false)
  private ServiceAccounts serviceAccounts;

  @Column(name = "key_id", nullable = false)
  private String keyId;

  @Column(name = "certificate", nullable = false)
  private String certificate;

  @Column(name = "created_on")
  private LocalDateTime createdOn;
  @Column(name = "updated_on")
  private LocalDateTime updatedOn;
  @Column(name = "expired_on")
  private LocalDateTime expiredOn;

}
