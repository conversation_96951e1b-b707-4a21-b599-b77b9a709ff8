package com.peoples.banking.persistence.serviceaccount.repository;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccounts;
import java.util.Optional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ServiceAccountsRepository extends JpaRepository<ServiceAccounts, Integer> {

  //used by Service Account API for Admin UI
  Optional<ServiceAccounts> findByRefId(String refId);

  @Query("select s from ServiceAccounts s left join fetch s.apiEndpointsMap a left join fetch s.limits l left join fetch s.features f where s.inboundApiToken=?1")
  Optional<ServiceAccounts> internalFindByInboundApiToken(String inboundApiToken);

  @Cacheable(value = "ServiceAccountInternalByRefId", cacheManager = "serviceAccountCacheManager")
  @Query("select s from ServiceAccounts s left join fetch s.apiEndpointsMap a left join fetch s.limits l left join fetch s.features f left join fetch s.configs where s.refId=?1")
  Optional<ServiceAccounts> internalFindByRefId(String refId);

  @Cacheable(value = "ServiceAccountByRefId", cacheManager = "serviceAccountCacheManager")
  default Optional<ServiceAccounts> internalFindByRefIdWithContacts(String refId){
    return internalFindByRefId(refId).map(sa->{sa.getContacts().size();return sa;});
  }

  @Cacheable(value = "ServiceAccountByInboundAPIToken", cacheManager = "serviceAccountCacheManager")
  default Optional<ServiceAccounts> internalFindByInboundApiTokenWithContacts(String inboundApiToken){
    return internalFindByInboundApiToken(inboundApiToken).map(sa->{sa.getContacts().size();return sa;});
  }

}
