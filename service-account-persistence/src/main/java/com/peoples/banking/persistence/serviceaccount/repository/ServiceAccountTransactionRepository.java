package com.peoples.banking.persistence.serviceaccount.repository;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountTransactions;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Map;

public interface ServiceAccountTransactionRepository {
  public void save(ServiceAccountTransactions serviceAccountTransactions) throws Exception;

  public Map<String, ServiceAccountTransactions> getServiceAccountTransactions(String serviceAccountRefId) throws Exception;

  public void delete(ServiceAccountTransactions serviceAccountTransactions) throws Exception;

  public void expireAt(ServiceAccountTransactions serviceAccountTransactions, LocalDate expiryDate) throws Exception;
}
