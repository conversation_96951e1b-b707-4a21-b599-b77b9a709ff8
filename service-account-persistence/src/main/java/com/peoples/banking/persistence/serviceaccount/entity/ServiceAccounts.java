package com.peoples.banking.persistence.serviceaccount.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MapKeyColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "service_accounts"
    , schema = "configuration"
    , uniqueConstraints = {@UniqueConstraint(columnNames = {"ref_id"}),
    @UniqueConstraint(columnNames = {"inbound_api_token"})}
)
public class ServiceAccounts implements java.io.Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private int id;

  @Column(name = "ref_id", nullable = false)
  private String refId;

  @Column(name = "inbound_api_token", nullable = false)
  private String inboundApiToken;

  @Column(name = "outbound_api_token", nullable = false)
  private String outboundApiToken;

  @Column(name = "outbound_api_auth", nullable = false)
  private String outboundApiAuth;

  @Column(name = "outbound_api_auth_token", nullable = true)
  private String outboundApiAuthToken;

  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "sett_account_num", nullable = false)
  private String settlementAccountNum;

  @Column(name = "account_num_range", nullable = false)
  private String accountNumRange;

  @Column(name = "limit_group_id", nullable = false)
  private String limitGroupId;

  @Column(name = "created_on", nullable = false, length = 29)
  private LocalDateTime createdOn;

  @Column(name = "updated_on", length = 29)
  private LocalDateTime updatedOn;

  @Column(name = "crm_ref_id")
  private String crmId;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  @MapKeyColumn(name = "type_cd")
  private Map<String, ApiEndpoints> apiEndpointsMap = new HashMap<>();

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  private List<ServiceAccountEvents> events = new ArrayList<>();

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  private List<ServiceAccountsContact> contacts = new ArrayList<>();

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  private List<ServiceAccountCertificates> certificates = new ArrayList<>();

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  @MapKeyColumn(name = "key")
  private Map<String, ServiceAccountConfigurations> configs = new HashMap<>();

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL, orphanRemoval = true)
  @MapKeyColumn(name = "feature")
  private Map<String, ServiceAccountFeatures> features = new HashMap<>();

  //TODO: it is oneToONeMapping however, lazy fetch doesn't apply to oneToOne therefore we sacrifice the constraint
  // we'll optimize oneToONe in the future
  @OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceAccounts", cascade = CascadeType.ALL)
  private List<ServiceAccountLimits> limits = new ArrayList<>();

  @Column(name = "status", nullable = false)
  private String status;

  @Column(name = "connector_type", nullable = false)
  private String connectorType;

  @Column(name = "indirect_connector_id")
  private String indirectConnectorId;

  @Column(name = "status_reason")
  private String statusReason;

  @Column(name = "status_updated_by")
  private String statusUpdatedBy;

}
