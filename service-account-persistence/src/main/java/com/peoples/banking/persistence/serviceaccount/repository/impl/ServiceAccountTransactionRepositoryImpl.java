package com.peoples.banking.persistence.serviceaccount.repository.impl;

import com.peoples.banking.persistence.serviceaccount.entity.ServiceAccountTransactions;
import com.peoples.banking.persistence.serviceaccount.repository.ServiceAccountTransactionRepository;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class ServiceAccountTransactionRepositoryImpl implements ServiceAccountTransactionRepository {

  @Autowired
  private RedisTemplate redisTemplate;

  public void save(ServiceAccountTransactions serviceAccountTransactions) throws Exception {
    redisTemplate.opsForHash()
        .put(serviceAccountTransactions.getServiceAccountRefId(), serviceAccountTransactions.getExternalPaymentRefId(),
            serviceAccountTransactions);

  }

  public Map<String, ServiceAccountTransactions> getServiceAccountTransactions(String serviceAccountRefId) throws Exception {
    return redisTemplate.opsForHash().entries(serviceAccountRefId);
  }

  public void delete(ServiceAccountTransactions serviceAccountTransactions) throws Exception {
    redisTemplate.opsForHash()
        .delete(serviceAccountTransactions.getServiceAccountRefId(), serviceAccountTransactions.getExternalPaymentRefId());
  }

  public void expireAt(ServiceAccountTransactions serviceAccountTransactions, LocalDate expiryDate) throws Exception {
    Instant expiryDateTime = expiryDate.atStartOfDay(ZoneId.systemDefault()).toInstant();

    redisTemplate.expireAt(serviceAccountTransactions.getServiceAccountRefId(), expiryDateTime);
  }
}
