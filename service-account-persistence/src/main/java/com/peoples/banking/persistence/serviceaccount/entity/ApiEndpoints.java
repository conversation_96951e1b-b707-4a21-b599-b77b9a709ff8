package com.peoples.banking.persistence.serviceaccount.entity;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "api_endpoints"
    , schema = "configuration"
    , uniqueConstraints = {@UniqueConstraint(columnNames = {"service_account_id", "type_cd"})}
)
public class ApiEndpoints implements java.io.Serializable {

  @Id
  @Column(name="id", unique=true, nullable=false)
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private int id;

  @ManyToOne(fetch= FetchType.EAGER)
  @JoinColumn(name="service_account_id", nullable=false)
  private ServiceAccounts serviceAccounts;

  @Column(name = "type_cd", nullable = false)
  private String typeCd;

  @Column(name = "url", nullable = false)
  private String url;

  @Column(name = "created_on", nullable = false, length = 29)
  private LocalDateTime createdOn;

  @Column(name = "updated_on", length = 29)
  private LocalDateTime updatedOn;

}
