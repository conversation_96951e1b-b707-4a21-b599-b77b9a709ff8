package com.peoples.banking.persistence.serviceaccount.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "service_account_limits"
    , schema = "configuration"
    , uniqueConstraints = {@UniqueConstraint(columnNames = {"service_account_id"})}
)
public class ServiceAccountLimits implements java.io.Serializable{

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private int id;

  @ManyToOne(fetch= FetchType.LAZY, optional = false)
  @JoinColumn(name="service_account_id", nullable=false)
  private ServiceAccounts serviceAccounts;

  @Column(name = "reserve_amount", nullable = false)
  private BigDecimal reserveAmount;

  @Column(name = "notification_threshold_amount", nullable = false)
  private BigDecimal notificationThresholdAmount;

  @Column(name = "suspend_threshold_amount", nullable = false)
  private BigDecimal suspendThresholdAmount;

  @Column(name = "created_on", nullable = false, length = 29)
  private LocalDateTime createdOn;

  @Column(name = "updated_on", length = 29)
  private LocalDateTime updatedOn;

}
