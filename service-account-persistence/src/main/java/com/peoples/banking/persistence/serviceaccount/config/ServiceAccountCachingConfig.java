package com.peoples.banking.persistence.serviceaccount.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableCaching
public class ServiceAccountCachingConfig {

  @Bean
  public CacheManager serviceAccountCacheManager() {
    return new ConcurrentMapCacheManager("ServiceAccountByInboundAPIToken", "ServiceAccountByRefId", "FeaturesAll", "ServiceAccountCertificateByKey", "ServiceAccountInternalByRefId");
  }
}
