package com.peoples.banking.persistence.serviceaccount.repository;

import com.peoples.banking.persistence.serviceaccount.entity.Features;
import java.util.List;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FeaturesRepository extends JpaRepository<Features, Integer> {

  @Cacheable(value = "FeaturesAll", cacheManager = "serviceAccountCacheManager")
  List<Features> findAll();
}
