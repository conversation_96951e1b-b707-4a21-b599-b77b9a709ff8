package com.peoples.banking.partner.gl.adapter.impl;

import com.amazonaws.SdkClientException;
import com.peoples.banking.partner.domain.gl.model.InitiateLedgerTransactionAsyncRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URI;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.amazonaws.DefaultRequest;
import com.amazonaws.Request;
import com.amazonaws.Response;
import com.amazonaws.SdkBaseException;
import com.amazonaws.auth.AWS4Signer;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.http.AmazonHttpClient;
import com.amazonaws.http.HttpMethodName;
import com.amazonaws.http.HttpResponse;
import com.amazonaws.http.HttpResponseHandler;
import com.amazonaws.util.IOUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.adapter.base.exception.AdapterException;
import com.peoples.banking.adapter.base.exception.ResponseException;
import com.peoples.banking.adapter.base.exception.TBDException;
import com.peoples.banking.adapter.base.exception.TimeoutException;
import com.peoples.banking.adapter.base.exception.common.BaseException.ErrorCode;
import com.peoples.banking.partner.domain.gl.model.CommitLedgerTransactionResponse;
import com.peoples.banking.partner.domain.gl.model.InitiateLedgerTransactionRequest;
import com.peoples.banking.partner.domain.gl.model.InitiateLedgerTransactionResponse;
import com.peoples.banking.partner.domain.gl.model.InlineResponse400;
import com.peoples.banking.partner.gl.adapter.GlClientException;
import com.peoples.banking.partner.gl.adapter.GlRestAdapter;
import com.peoples.banking.partner.gl.adapter.config.GlAdapterConstant;
import com.peoples.banking.partner.gl.adapter.config.GlAdapterProperty;
import com.peoples.banking.util.logger.annotation.PerfLogger;

import lombok.extern.log4j.Log4j2;

/**
 * Base implementation of <i>Rest</i> for outgoing communication with GL Service Domains.
 * <pre>includes header construction</pre>
 * <p>
 * TODO refactor implementation using WebClient as this is the Spring approach going forward.
 */
@Log4j2
@Service
public class GlRestAdapterImpl implements GlRestAdapter {

    private final static String SYMBOL_EQUAL = "=";

    @Autowired
    protected AmazonHttpClient client;
    @Autowired
    protected AWS4Signer signer;
    @Autowired
    protected AWSCredentials awsCredentials;

    @Autowired
    protected GlAdapterProperty glProperties;

    @Autowired
    protected ObjectMapper objectMapper;
    private String initiateTransactionEndpoint;
    private String initiateTransactionAsyncEndpoint;
    private String commitTransactionEndpoint;
    private String commitTransactionAsyncEndpoint;
    private String rollbackTransactionEndpoint;
    private String deleteTransactionEndpoint;

    @PostConstruct
    public void init() {
        initiateTransactionEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.INITIATE_TRANSACTION_URL;
        initiateTransactionAsyncEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.INITIATE_TRANSACTION_ASYNC_URL;
        commitTransactionEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.COMMIT_TRANSACTION_URL;
        commitTransactionAsyncEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.COMMIT_TRANSACTION_ASYNC_URL;
        rollbackTransactionEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.ROLLBACK_TRANSACTION_URL;
        deleteTransactionEndpoint = glProperties.getGlUrlBase() + GlAdapterConstant.DELETE_TRANSACTION_URL;
    }

    /**
     * @param endpointUrl  URL to HTTP resource
     * @param httpMethod   HTTP method
     * @param requestData  requestData to send
     * @param responseType class instance of the response object
     * @param pathParams   path parameters
     * @param headers      header parameters
     * @param <Treq>       the class type of the domain object that the requestData is
     * @param <Tresp>      the class type of the domain object that the response is converted into
     * @return response object
     * @throws AdapterException  adapter exception
     * @throws ResponseException response exception from GL
     * @throws TBDException      response exception requiring rollback
     */
    protected <Treq, Tresp> Tresp execute(String endpointUrl, HttpMethodName httpMethod,
                                          @Nullable Treq requestData, @Nullable Class<Tresp> responseType,
                                          @Nullable Map<String, String> pathParams,
                                          @Nullable Map<String, String> headers)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        // sanity check (requestType & requestData (for GET), and params are optional)
        if (endpointUrl == null || endpointUrl.isBlank() || httpMethod == null || responseType == null) {
            // debug logging (shield these with a isDebugEnabled for efficiency)
            if (log.isDebugEnabled()) {
                log.debug("optional parameters: requestData={}, pathParams={}, headers={}", requestData, pathParams, headers);
                log.debug("required parameters: endpointUrl={}, httpMethod={}, responseType={}",
                        endpointUrl, httpMethod, responseType);
            }

            throw new AdapterException("invalid input arguments", ErrorCode.INVALID_INPUT);
        }

        URI uri;
        if (pathParams == null) {
            uri = URI.create(endpointUrl);
        } else {
            String processed = endpointUrl;
            for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                processed = processed.replaceAll("\\{" + entry.getKey() + "\\}", entry.getValue());
            }
            uri = URI.create(processed);
        }

        // step 1 - generate payload (requestData body)
        log.debug("generating HTTP payload");
        byte[] payload = null;

        // convert response to JSON for encryption (optional) and transmission
        if (requestData != null) {
            try {
                payload = objectMapper.writeValueAsBytes(requestData);
            } catch (JsonProcessingException e) {
                // should NOT be triggered, as we validate this during initialization
                log.error("failed on serializing requestData payload", e);
                throw new AdapterException("failed on serializing requestData payload", ErrorCode.UNEXPECTED_EXCEPTION);
            }
            log.info("HTTP payload (raw)={}", new String(payload));
        }

        // step 2 -- populate HTTP headers
        final String headersToLog = headers.entrySet().stream()
                .map(Object::toString).collect(Collectors.joining(","));
        log.info("populating HTTP headers {}", headersToLog);
        // step 3 -- populate HTTP entity
        log.debug("populating HTTP entity");

        Request<Tresp> request = new DefaultRequest<>(glProperties.getGlAwsService());
        request.setHttpMethod(httpMethod);
        request.setHeaders(headers);

        request.setEndpoint(uri);
        if (payload != null) {
            request.setContent(new ByteArrayInputStream(payload));
        }
        signer.sign(request, awsCredentials);

        return this.rest(request, responseType);
    }

    /**
     * Invokes the REST endpoint with the request payload, and gracefully handles exceptions.
     *
     * @param <Tresp>           the class type of the domain object that the response is
     * @param responseClassType the class type of the domain object that the response is converted to
     */
    private <Tresp> Tresp rest(Request request, Class<Tresp> responseClassType) throws ResponseException, AdapterException, TimeoutException {
        Response<Tresp> response = null;
        try {
            log.info("initiating request to {}", request.getEndpoint());
            response = client
                    .requestExecutionBuilder()
                    .request(request)
                    .errorResponseHandler(new HttpResponseHandler<>() {
                        @Override
                        public SdkBaseException handle(HttpResponse httpResponse) throws Exception {
                            final String payload = IOUtils.toString(httpResponse.getContent());
                            String resHeaders = getResHeaders(httpResponse);
                            log.info("RESPONSE | httpStatus={}, headers=[{}], body={}", httpResponse.getStatusCode(), resHeaders.trim(), payload);
                            return new GlClientException(httpResponse.getStatusCode(), payload);
                        }

                        @Override
                        public boolean needsConnectionLeftOpen() {
                            return false;
                        }
                    })
                    .execute(new HttpResponseHandler<>() {
                        @Override
                        public Tresp handle(HttpResponse httpResponse) throws Exception {
                            String resHeaders = getResHeaders(httpResponse);
                            if (httpResponse.getContent() == null) {
                                log.info("RESPONSE | httpStatus={}, headers=[{}], body={}", httpResponse.getStatusCode(), resHeaders.trim(), null);
                                return null;
                            }
                            final String content = IOUtils.toString(httpResponse.getContent());
                            log.info("RESPONSE | httpStatus={}, headers=[{}], body={}", httpResponse.getStatusCode(), resHeaders.trim(), content);

                            if (responseClassType == String.class) {
                                return (Tresp) content;
                            }
                            return objectMapper.readValue(content, responseClassType);
                        }

                        @Override
                        public boolean needsConnectionLeftOpen() {
                            return false;
                        }
                    });
        } catch (GlClientException e) {
            log.warn("exception received: {}", e.getMessage(), e);
            this.throwResponseException(e.getMessage(), e.getStatusCode());
        } catch (SdkClientException e) {
            log.warn("exception received: {}", e.getMessage(), e);
            throw new TimeoutException();
        } catch (Exception e) {
            // unexpected exception
            log.error("unexpected exception", e);
            throw new AdapterException(ErrorCode.UNEXPECTED_EXCEPTION);
        }

        return response.getAwsResponse();
    }

    /**
     * Build HTTP readers for the request.
     *
     * @return HTTP header
     */
    private Map<String, String> buildHeaders(String interactionId) {
        Map<String, String> httpHeaders = new HashMap<>();

        // default HTTP httpHeaders
        httpHeaders.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.put(GlAdapterConstant.HEADER_INTERACTION_ID, interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
        return httpHeaders;
    }

    /**
     * @inheritDoc
     */
    @PerfLogger
    @Override
    public InitiateLedgerTransactionResponse initiateTransaction(String accountId, String profileId, String interactionId,
                                                                 InitiateLedgerTransactionRequest request)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        return execute(initiateTransactionEndpoint, HttpMethodName.POST, request,
                InitiateLedgerTransactionResponse.class, null, httpHeaders);
    }

    /**
     * @inheritDoc
     */
    @PerfLogger
    @Override
    public void initiateTransactionAsync(String accountId, String profileId, String interactionId,
                                         InitiateLedgerTransactionAsyncRequest request)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        execute(initiateTransactionAsyncEndpoint, HttpMethodName.POST, request,
                String.class, null, httpHeaders);
    }

    @PerfLogger
    @Override
    public void commitTransaction(String accountId, String profileId, String instructionRefId, String interactionId)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        execute(commitTransactionEndpoint, HttpMethodName.PATCH, null,
                CommitLedgerTransactionResponse.class, Map.of(GlAdapterConstant.INSTRUCTION_REF_PARAM, instructionRefId), httpHeaders);
    }

    @PerfLogger
    @Override
    public void commitTransactionAsync(String accountId, String profileId, String instructionRefId, String interactionId)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        execute(commitTransactionAsyncEndpoint, HttpMethodName.PATCH, null,
                CommitLedgerTransactionResponse.class, Map.of(GlAdapterConstant.INSTRUCTION_REF_PARAM, instructionRefId), httpHeaders);
    }

    @PerfLogger
    @Override
    public void rollbackTransaction(String accountId, String profileId, String instructionRefId, String interactionId)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        execute(rollbackTransactionEndpoint, HttpMethodName.DELETE, null,
                String.class, Map.of(GlAdapterConstant.INSTRUCTION_REF_PARAM, instructionRefId), httpHeaders);
    }

    @PerfLogger
    @Override
    public void deleteTransaction(String accountId, String profileId, String instructionRefId, String transactionRefId, String interactionId)
            throws AdapterException, ResponseException, TBDException, TimeoutException {
        final Map<String, String> httpHeaders = buildHeaders(interactionId);
        httpHeaders.put(GlAdapterConstant.HEADER_ACCOUNT_ID, accountId);
        httpHeaders.put(GlAdapterConstant.HEADER_PROFILE_ID, profileId);

        final Map<String, String> pathParams = Map.of(
                GlAdapterConstant.INSTRUCTION_REF_PARAM, instructionRefId,
                GlAdapterConstant.TRANSACTION_REF_PARAM, transactionRefId);
        execute(deleteTransactionEndpoint, HttpMethodName.DELETE, null,
                String.class, pathParams, httpHeaders);
    }

    /**
     * Builds a ResponseException and maps fields from the <i>Service Domain</i> specific {@code ErrorModel}.
     *
     * @param response       response body from <i>GL</i>
     * @param httpStatusCode HTTP status code from downstream system
     * @throws ResponseException response exception from GL
     */
    protected void throwResponseException(String response, Integer httpStatusCode)
            throws ResponseException {

        // sanity check
        if (response != null && !response.isBlank()) {
            InlineResponse400 responseModel;
            try {
                responseModel = objectMapper.readValue(response, InlineResponse400.class);
            } catch (IOException e) {
                log.error("cannot convert gl error response into something meaningful");
                throw new ResponseException(httpStatusCode, null, null);
            }
            if (!CollectionUtils.isEmpty(responseModel.getError())) {
                throw new ResponseException(httpStatusCode, responseModel.getError().get(0).getCode(),
                        responseModel.getError().get(0).getAdditionalInformation());
            } else {
                throw new ResponseException(httpStatusCode, null, null);
            }
        } else {
            throw new ResponseException(httpStatusCode, null, null);
        }
    }

    /**
     * To get the list of response headers configured in logging.api.res.headers property to be logged
     *
     * @return String the list of headers combined in a string.
     */
    private String getResHeaders(HttpResponse response) {
        StringBuilder headerBuilder = new StringBuilder();
        for (Map.Entry<String, String> header : response.getHeaders().entrySet()) {
            //header could be empty, therefore add the check
            if (header != null && !header.getKey().isEmpty()) {
                headerBuilder.append(header);
                headerBuilder.append(SYMBOL_EQUAL);
                headerBuilder.append(header.getValue());
                headerBuilder.append(StringUtils.SPACE);
            }
        }

        return headerBuilder.toString();
    }

}
