package com.peoples.banking.partner.gl.adapter.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class GlAdapterProperty {

  /**
   * GL system URL (protocol://host:port).
   */
  @Value("${sys.pn.gl.url.base}")
  private String glUrlBase;

  @Value("${sys.pn.gl.aws.key.value}")
  private String glAwsKey;

  @Value("${sys.pn.gl.aws.secret.value}")
  private String glAwsSecret;

  @Value("${sys.pn.gl.aws.region.value}")
  private String glAwsRegion;

  @Value("${sys.pn.gl.aws.service.value}")
  private String glAwsService;

  /**
   * Time to live (maximum age) of a request (in milliseconds).
   * <pre>defaults to 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pn.gl.time-to-live:30000}")
  private Integer timeToLive;

  /**
   * HTTP connection keepAlive interval (in milliseconds).
   * <pre>default is 20 seconds = 20000 ms</pre>
   */
  @Value("${sys.pn.gl.connection.keep-alive:20000}")
  private Integer connectionKeepAlive;

  /**
   * Maximum number of HTTP connections.
   * <pre>default is 25</pre>
   */
  @Value("${sys.pn.gl.connection.max:25}")
  private Integer maxHttpConnections;

  /**
   * Delay before reaper removes idle HTTP connections (in milliseconds).
   * <pre>default is 30 seconds = 30000 ms</pre>
   */
  @Value("${sys.pn.gl.connection.wait-time:30000}")
  private Integer waitTimeIdleConnection;


  /**
   * Connection timeout for requesting a connection from connection manager (in milliseconds).
   * <pre>defaults to 5 seconds = 5000 ms.</pre>
   */
  @Value("${sys.pn.gl.connection-request-timeout:5000}")
  private Integer connectionReqTimeout;

  /**
   * Timeout waiting until a connection is established (in milliseconds).
   * <pre>defaults to 15 seconds = 15000 ms.</pre>
   */
  @Value("${sys.pn.gl.connection.timeout:15000}")
  private Integer connectTimeout;

  /**
   * Read timeout for requests to GL.
   * <pre>defaults to 1.25 seconds = 1250 ms.</pre>
   */
  @Value("${sys.pn.gl.read.timeout:1250}")
  private Integer readTimeout;
}
