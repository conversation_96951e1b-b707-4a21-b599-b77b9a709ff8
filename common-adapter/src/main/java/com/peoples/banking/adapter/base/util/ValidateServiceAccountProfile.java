package com.peoples.banking.adapter.base.util;

import com.peoples.banking.adapter.base.type.ServiceAccountAuthType;
import com.peoples.banking.adapter.base.type.ServiceAccountProfile;

public class ValidateServiceAccountProfile {
  /**
   * Validates the service account profile.
   *
   * @param profile service account profile
   * @return
   */
  public static boolean isValidServiceAccountProfile(ServiceAccountProfile profile) {
    boolean result = true;

    if (profile == null
        || profile.getApiToken() == null || profile.getApiToken().isBlank()
        || profile.getEndpointUrl() == null || profile.getEndpointUrl().isBlank()
        || profile.getAuthType() == null) {
      // these must be populated
      result = false;
    }

    if (profile.getAuthType() != ServiceAccountAuthType.NO_AUTH) {
      if (profile.getAuthToken() == null || profile.getAuthToken().isBlank()) {
        // auth token MUST be provided if auth type is NOT NO_AUTH
        result = false;
      }
    }

    if (profile.getEndpointUrl() == null || profile.getEndpointUrl().isBlank()) {
      // endpoint url must be populated
      result = false;
    }

    return result;
  }

}
